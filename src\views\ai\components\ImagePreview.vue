<template>
  <!-- 图片预览蒙层 -->
  <div v-if="show" class="preview-overlay" @click="handleClose">
    <div class="preview-content" @click.stop>
      <img
        src="@/assets/images/close-tips.png"
        @click="handleClose"
        alt="关闭"
        style="width: 36px; height: 36px"
        class="close-icon"
      />
      <img :src="imageUrl" class="preview-image" />
      <div class="preview-actions">
        <button class="preview-btn save-btn" @click="handleSave">保存素材</button>
        <button class="preview-btn delete-btn" @click="handleDelete">删除</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean;
  imageUrl: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'save'): void;
  (e: 'delete'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const handleClose = () => {
  emit('close');
};

const handleSave = () => {
  emit('save');
};

const handleDelete = () => {
  emit('delete');
};
</script>

<style scoped lang="less">
// 预览蒙层样式
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.preview-content {
  max-width: 80%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  position: relative;
}

.close-icon {
  position: absolute;
  top: 0px;
  right: -60px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 1001;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.7;
  }
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.preview-actions {
  display: flex;
  gap: 16px;
  align-items: center;

  .preview-btn {
    width: 120px;
    height: 40px;
    border-radius: 4px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &.save-btn {
      background: #2e76ff;
      color: #ffffff;
    }

    &.delete-btn {
      background: #c9453e;
      color: #ffffff;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
