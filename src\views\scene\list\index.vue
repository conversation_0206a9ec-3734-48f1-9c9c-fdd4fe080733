<template>
  <div>
    <search-form
      :form-keys-data="keysData"
      :search-data-event="searchData"
      currentRoute="/scene/list"></search-form>
  </div>
  <div>
    <table-list
      :data="tableData"
      :column-list="columnList"
      :change-page="changePage"
      :delete-content="deleteContent"
      :delete-dataEvent="deleteDataEvent"
      :data-total="pageTotal"
      :page-size="searchForm.pageSize"
      :page-no="searchForm.pageNo"
      :operation-items="operationItems"></table-list>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted, reactive } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import { useRouter } from 'vue-router';
import { getSceneMetaPageForWeb, deleteSceneMeta } from '@/api/index';
import type { OperationItems, TableRow } from '@/types/operation';

const router = useRouter();

const pageTotal = ref(0); // 总计数据条数
const tableData: any = ref([]); // 表格数据
const deleteContent = {
  title: '删除项目',
  content: '是否删除此项目？',
};
const searchForm: any = reactive({
  // 查询对象
  pageSize: 10,
  pageNo: 1,
});

const keysData = reactive([
  {
    key: 'sceneName',
    type: 'input',
    label: '项目名称',
  },
  {
    key: 'sceneId',
    type: 'input',
    label: '项目编号',
  },
  {
    key: 'sceneStatus',
    type: 'select',
    label: '项目状态',
    dataList: [
      {
        name: '全部',
        value: '',
      },
      {
        name: '开启',
        value: '1',
      },
      {
        name: '关闭',
        value: '0',
      },
    ],
  },
]);

const editData = (data: any) => {
  router.push('/scene_edit?sceneid=' + data.id);
};

const deleteDataEvent = (data: any) => {
  deleteSceneMeta({ sceneId: data.id }).then((res: any) => {
    getData();
  });
};
// Operation items configuration
const operationItems = reactive<OperationItems>([
  {
    label: '编辑',
    key: 'edit',
    onClick: (row: TableRow) => {
      editData(row);
    },
  },
]);

const columnList = [
  {
    prop: 'id',
    label: '项目编号',
  },
  {
    prop: 'sceneName',
    label: '项目名称',
  },
  // {
  //   prop: 'stadiumName',
  //   label: '所属场馆'
  // },
  {
    prop: 'scenePic',
    label: '项目照片',
    type: 'image',
  },
  {
    prop: 'spaceCode',
    label: '空间编码',
  },
  {
    prop: 'sceneStatus',
    label: '项目状态',
  },
  {
    prop: 'interactionCount',
    label: '互动区域数量',
    width: 120,
  },
  {
    prop: 'guideRouteCount',
    label: '路线数量',
  },
  {
    prop: 'updateTimeStr',
    label: '更新日期',
  },
  {
    prop: 'operate',
    label: '操作',
    width: 120,
    type: 'operation',
  },
];
const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getData();
};

const searchData = (data: any) => {
  for (const key in data) {
    searchForm[key] = data[key];
  }
  getData();
};

const getData = () => {
  getSceneMetaPageForWeb({ ...searchForm }).then((res: any) => {
    pageTotal.value = res.data.total;
    tableData.value = [...res.data.records].map((e: any) => ({
      sceneName: e.sceneName,
      id: e.id,
      stadiumName: e.stadiumName,
      scenePic: e.scenePic || require('@/assets/images/icon/default_scene.png'),
      spaceCode: e.spaceDto.id,
      interactionCount: e.interactionCount,
      guideRouteCount: e.guideRouteCount,
      updateTimeStr: e.updateTimeStr,
      sceneStatus: e.sceneStatus == 1 ? '开启' : '关闭',
    }));
  });
};

onMounted(() => {
  getData();
});
</script>
