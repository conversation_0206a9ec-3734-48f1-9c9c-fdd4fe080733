<template>
  <div class="canvas-left" :class="hide_side ? 'hide-left-side' : ''">
    <div class="left-tabs" v-if="showRoute">
      <div :class="operateIndex == 0 ? 'active' : ''" @click.stop="operateIndex = 0">
        <img src="@/assets/images/icon/areaA.png" />
        <span>互动区域</span>
      </div>
      <div :class="operateIndex == 1 ? 'active' : ''" @click.stop="operateIndex = 1">
        <img src="@/assets/images/icon/routeA.png" />
        <span>导览路线</span>
      </div>
    </div>
    <div class="left-tabs-content">
      <div v-if="operateIndex == 0" class="add" @click="addArea">
        <img src="@/assets/images/icon/add-list.png" />
        <span>新增互动区域</span>
      </div>
      <div v-if="operateIndex == 1" class="add" @click="addRoute">
        <img src="@/assets/images/icon/add-list.png" />
        <span>新增导览路线</span>
      </div>
      <div v-show="operateIndex == 0" class="area-list-box">
        <div>
          <div class="area-name"
            :class="(activeAreaIndex == -99 ? 'active' : '') + (publicMaterialList.filter((e: any) => e.flag != 'delete').length ? ' has-draw-down' : '') + (!hideList[-99] ? ' hide' : '')"
            @click="handleClick(-99, 'area')">
            <div>
              <img v-if="activeAreaIndex == -99" src="@/assets/images/icon/area-listA.png" />
              <img v-if="activeAreaIndex != -99" src="@/assets/images/icon/area-list.png" />
              <span>公共区域</span>
            </div>
          </div>
          <div class="source-list"
            :class="((hoverSourceName == e.id) || (currentSourceName == e.id) || (currentSourceName == e.uuid)) ? 'hoveActive' : ''"
            v-for="(e, i) in publicMaterialList" @click="handleClickActive(i, -99, e)"
            @mousemove="handleActiveSource(i, -99, true)" @mouseleave="handleActiveSource(i, -99, false)"
            :style="{ display: ((e.groupName && !isPlanStyle) || (hideList[-99] || e.flag == 'delete')) ? 'none' : 'flex' }"
            :key="i">
            <div>{{ e.materialName }}</div>
            <img src="@/assets/images/icon/delete-source.png" @click.stop="handleDeleteSource($event, i, -99)" />
          </div>
        </div>
        <div v-show="item.flag != 'delete'" v-for="(item, index) in areaDataList" :key="index">
          <div class="area-name"
            :class="(activeAreaIndex == index ? 'active' : '') + (item.materialDto.filter((e: any) => e.flag != 'delete').length ? ' has-draw-down' : '') + (!hideList[index] ? ' hide' : '')"
            @click="handleClick(index, 'area')">
            <div>
              <img v-if="activeAreaIndex == index" src="@/assets/images/icon/area-listA.png" />
              <img v-if="activeAreaIndex != index" src="@/assets/images/icon/area-list.png" />
              <span v-if="store.state.stateStore.activeArea != index">{{
                item.title }}</span>
              <el-input class="edit-area-name" v-if="store.state.stateStore.activeArea == index" v-model="areaNameText"
                @blur="changeName('area')" placeholder="请输入互动区域名称" />
            </div>
            <div class="tool-box" v-if="showRoute">
              <div v-show="activeAreaIndex == index" class="delete" @click.stop="selectEditArea(index)">
                <img src="@/assets/images/icon/edit.png" />
              </div>
              <div v-show="activeAreaIndex == index" class="delete" @click.stop="handleDelete($event, index)">
                <img src="@/assets/images/icon/delete.png" />
              </div>
            </div>
          </div>
          <div class="source-list"
            :class="((hoverSourceName == e.id) || (currentSourceName == e.id) || (currentSourceName == e.uuid)) ? 'hoveActive' : ''"
            v-for="(e, i) in item.materialDto" @click="handleClickActive(i, index, e)"
            @mousemove="handleActiveSource(i, index, true)" @mouseleave="handleActiveSource(i, index, false)"
            :style="{ display: ((e.groupName && !isPlanStyle) || (hideList[index] || e.flag == 'delete')) ? 'none' : 'flex' }"
            :key="i">
            <div>{{ e.materialName }}</div>
            <img src="@/assets/images/icon/delete-source.png" @click.stop="handleDeleteSource($event, i, index)" />
          </div>
        </div>
      </div>
      <div v-show="operateIndex == 1" class="area-list-box">
        <div v-show="item.flag != 'delete'" class="area-name route-list-style" v-for="(item, index) in routeList"
          :class="activeRouteIndex == index ? 'active' : ''" :key="index" @click="handleClick(index, 'route')">
          <div>
            <img v-if="activeRouteIndex == index" src="@/assets/images/icon/route-listA.png" />
            <img v-if="activeRouteIndex != index" src="@/assets/images/icon/route-list.png" />
            <span v-if="store.state.stateStore.activeRoute != index">{{
              item.title }}</span>
            <el-input class="edit-route-name" v-if="store.state.stateStore.activeRoute == index" v-model="routeNameText"
              @blur="changeName('route')" placeholder="请输入路线名称" />
          </div>
          <div class="tool-box">
            <div v-show="activeRouteIndex == index" class="delete" @click.stop="selectEditRoute(index)">
              <img src="@/assets/images/icon/edit.png" />
            </div>
            <div v-show="activeRouteIndex == index" class="delete" @click.stop="handleDelete($event, index)">
              <img src="@/assets/images/icon/delete.png" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="left-source-box">
      <div class="add-source">
        <span>素材库</span>
        <div @click.stop="modalShow = true" class="add"></div>
      </div>
      <div class="source-type">
        <span>素材类型</span>
        <el-select v-model="sourceType" clearable placeholder="请选择素材类型" class="select-default" @change="seleteSource"
          popper-class="select-option" :suffix-icon="DropDown">
          <el-option label="全部" value="" />
          <el-option v-for="item in materialType" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
    </div>
    <div class="source-pool-box">
      <div class="source-pool" v-for="(item, index) in sourceList"
        :style="{ marginRight: (index + 1) % 4 == 0 ? '0' : '7px' }" :key="index">
        <div :class="hoverImage == index + '' ? 'hoverSource' : ''"
          @mousedown="handleMouseDown($event, materialUrls[item.materialType].url_a, item)"
          @mousemove="handleMouseMove(index)" @mouseleave="handleMouseLeave">
          <img :src="materialUrls[item.materialType][hoverImage == index + '' ? 'url_a' : 'url']" draggable="false" />
          <div class="preview" @mousedown.stop="handlePreview(item)"></div>
          <div class="default-source" v-if="item.materialAffiliation == 2">默认</div>
        </div>
        <div>{{ item.materialName }}</div>
      </div>
    </div>
    <div class="left-source-box">
      <div class="add-source-combination">
        <span>素材组合</span>
        <div @click.stop="modalShow2 = true" class="add"></div>
      </div>
    </div>
    <div class="view-combination">
      <div class="combination-list" v-for="(item, index) in groupMaterial" :key="index"
        @click.stop="showgroupListIndex = (showgroupListIndex == index ? -1 : index)">
        <div>
          <img src="@/assets/images/icon/group-more.png" />
          <img src="@/assets/images/icon/group-icon.png" />
          <span @click.stop="null" @mousedown="handleMouseDown($event, materialUrls.groups.url_a, item)">{{
            item.groupName }}</span>
        </div>
        <div class="operate-btns">
          <img src="@/assets/images/icon/edit-source.png" @click.stop="modalShow2 = item.id" />
          <img src="@/assets/images/icon/delete-source.png" @click.stop="handleDeleteSourceGroup($event, item.id)" />
        </div>
        <div v-if="showgroupListIndex == index" @click.stop="null">
          <div class="source-pool-box2">
            <div class="source-pool" v-for="(item2, index2) in item.viewGroupArray"
              :style="{ marginRight: (index2 + 1) % 4 == 0 ? '0' : '7px' }" :key="index2">
              <div :class="hoverImage == `${index}${index2}2` ? 'hoverSource' : ''"
                @mousemove="handleMouseMove(index, index2, '2')" @mouseleave="handleMouseLeave">
                <img :src="materialUrls[item2.materialType][hoverImage == `${index}${index2}2` ? 'url_a' : 'url']"
                  draggable="false" />
                <div class="preview" @mousedown.stop="handlePreview(item2)"></div>
                <div class="default-source" v-if="item2.materialAffiliation == 2">默认</div>
              </div>
              <div>{{ item2.materialName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="!hide_side" class="hide-side" @click="hide_side = true">
    <img src="@/assets/images/icon/hide-side.png" />
  </div>
  <div v-if="hide_side" class="show-side" @click="hide_side = false">
    <img src="@/assets/images/icon/show-side.png" />
  </div>
  <create-source-material v-if="modalShow" :handle-hide="handleHide"></create-source-material>
  <material-combination v-if="modalShow2" :groupMaterial="groupMaterial" :modalShow2="JSON.stringify(modalShow2)"
    :handle-hide="handleHide2" :sourceList="sourceList"></material-combination>
  <material-preview :source-type="showSourceType" :handle-hide="closedPreview" :model-type="modelType" v-if="sourceUrl"
    :source-url="sourceUrl"></material-preview>
  <div class="popover-style" v-if="showDeleteType"
    :style="{ left: deleteSpanBoundingClientRect.left - 107 + 'px', top: deleteSpanBoundingClientRect.top - 198 + 'px' }">
    <div class="popover-title">删除</div>
    <div class="popover-content">
      <div>是否确定删除？</div>
      <div class="delete-box">
        <el-button class="btn-cancle el-size" @click="showDeleteType = ''"><span>取消</span></el-button>
        <el-button class="btn-sure el-size" @click="deleteEvent"><span>删除</span></el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, nextTick } from 'vue'
import { Vector2, Vector3 } from 'three'
import DropDown from '@/components/DropDown.vue'
import { getMaterialPageByUser, getOssAccessPath, getDefaultMaterial, selectMaterialGroup, deleteMaterialGroup } from '@/api'
import { materialUrls, materialType } from '@/config'
import { cropNumber, angleToRadians } from '@/utils/index'
import CreateSourceMaterial from '@/views/material/create/CreateSourceMaterial.vue'
import MaterialCombination from '@/views/material/combination/index.vue'
import MaterialPreview from '@/components/MaterialPreview.vue'
import { useStore } from "vuex";
import { ElMessage } from 'element-plus'

const store = useStore();

const props = defineProps({
  sourcePoolMourseDown: {
    default: null,
    type: Function
  },
  handleActiveArea: {
    default: null,
    type: Function
  },
  handleChangeArea: {
    default: null,
    type: Function
  },
  handleActiveRoute: {
    default: null,
    type: Function
  },
  handleChangeRoute: {
    default: null,
    type: Function
  },
  changeActiveIndex: {
    default: null,
    type: Function
  },
  publicMaterialList: {
    default: null,
    type: Object
  },
  deleteData: {
    default: null,
    type: Function
  },
  deleteSource: {
    default: null,
    type: Function
  },
  activeSource: {
    default: null,
    type: Function
  },
  activeSourceMaterial: {
    default: null,
    type: Function
  },
  sceneInfo: {
    default: null,
    type: Object
  },
  interactionId: {
    default: '',
    type: String
  },
  guideRouteId: {
    default: '',
    type: String
  },
  hoverSourceName: {
    default: '',
    type: String
  },
  activeSourceName: {
    default: null,
    type: Object
  },
  showRoute: {
    default: 0,
    type: Number
  },
  isPlanStyle: {
    default: false,
    type: Boolean
  },
  renderSceneData: {
    default: null,
    type: Object
  }
})
const operateIndex = ref(0) // 互动区域和路线的切换
const activeAreaIndex = ref(-99) // 当前高亮的互动区域index
const activeRouteIndex = ref(-1) // 当前高亮的路线index
const sourceType = ref('') // 资源类型
const areaDataList: any = ref([]) // 包含互动区域信息，title为互动区域名称，value为互动区域所绑定的素材数组
const routeList: any = ref([])
const hideList: any = reactive({})
const sourceList: any = ref([])
const areaNameText = ref('') // 互动区域名称修改的输入框绑定
const routeNameText = ref('') // 路线名称修改的输入框绑定
const hide_side = ref(false)
const hoverImage: any = ref('') // 悬浮在照片上
const modalShow = ref(false) // 显示新建
const modalShow2: any = ref(null) // 显示素材组合
const showSourceType = ref('')
const sourceUrl = ref('')
const modelType = ref('')
const deleteSpanBoundingClientRect = reactive({ left: 0, top: 0 })
const currentSourceName = ref('')
const showDeleteType = ref('')
const deleteData: any = ref({})
const groupMaterial: any = ref([])
const showgroupListIndex = ref(-1)
const deleteArea: any = ref({})

// 删除互动区域或者路线
const handleDelete = (event: any, dataIndex: number) => {
  handleDeleteTarget(event, 'classA')
  deleteData.value = {
    dataIndex
  }
}

// 删除素材组合
const handleDeleteSourceGroup = (event: any, dataIndex: number) => {
  handleDeleteTarget(event, 'classC')
  deleteData.value = {
    dataIndex
  }
}

// 确定被删除目标的位置
const handleDeleteTarget = (event: any, type: string) => {
  const getBoundingClientRect = event.target.getBoundingClientRect()

  deleteSpanBoundingClientRect.left = getBoundingClientRect.left - 30;
  deleteSpanBoundingClientRect.top = getBoundingClientRect.top;
  showDeleteType.value = type
}

// 执行删除操作
const deleteEvent = () => {
  if (showDeleteType.value == 'classA') {
    const { dataIndex } = deleteData.value
    if (operateIndex.value == 0) {
      if (!areaDataList.value[dataIndex].id) {
        areaDataList.value.splice(dataIndex, 1)
      } else {
        areaDataList.value[dataIndex].flag = 'delete'
      }
      activeAreaIndex.value = -1
    } else if (operateIndex.value == 1) {
      if (!routeList.value[dataIndex].id) {
        routeList.value.splice(dataIndex, 1)
      } else {
        routeList.value[dataIndex].flag = 'delete'
      }
      activeRouteIndex.value = -1
    }
    props.deleteData && props.deleteData(dataIndex, operateIndex.value)
    deleteArea.value = {
      dataIndex,
      type: "classA"
    }
  } else if (showDeleteType.value == 'classB') {
    const { i, index } = deleteData.value
    props.deleteSource(i, index)
  } else if (showDeleteType.value == 'classC') {
    const { dataIndex } = deleteData.value
    deleteGroup(dataIndex)
  }
  deleteData.value = {}
  showDeleteType.value = ''
}

const deleteGroup = (id: number) => {
  deleteMaterialGroup({ id }).then((res: any) => {
    selectMaterialGroups()
  })
}

const seleteSource = (e: any) => {
  getMaterialTotal(e)
}

const handleHide = (renew?: boolean) => {
  modalShow.value = false

  if (renew) {// 判断是否需要重新渲染
    getMaterialTotal()
    sourceType.value = ''
  }
}

const handleHide2 = (renew?: boolean) => {
  modalShow2.value = false

  if (renew) {// 判断是否需要重新渲染
    selectMaterialGroups()
  }
}

const closedPreview = () => {
  sourceUrl.value = ''
}

const changeName = (name: string) => {
  let { activeArea, activeRoute } = store.state.stateStore
  if (name == 'area') {
    areaDataList.value[activeArea].title = areaNameText.value
    activeArea = -1;
    props.handleChangeArea(areaNameText.value, activeAreaIndex.value)
  } else if (name == 'route') {
    routeList.value[activeRoute].title = routeNameText.value
    activeRoute = -1;
    props.handleChangeRoute(routeNameText.value, activeRouteIndex.value)
  }
  store.state.stateStore = { activeArea, activeRoute }
}

const selectEditArea = (index: number) => {
  store.state.stateStore.activeArea = index
  areaNameText.value = areaDataList.value[index].title
  nextTick(() => {
    (document.querySelector('.edit-area-name input') as any)?.focus();
  })
}

const selectEditRoute = (index: number) => {
  store.state.stateStore.activeRoute = index
  routeNameText.value = routeList.value[index].title
  nextTick(() => {
    (document.querySelector('.edit-route-name input') as any)?.focus();
  })
}

const handleClick = (index: number, name: string) => {
  if (name == 'area') {
    if (activeAreaIndex.value == index) {
      hideList[index] = !hideList[index]
    } else {
      hideList[index] = false
      activeAreaIndex.value = index;
    }

    if (activeAreaIndex.value != -1) {
      props.handleActiveArea([...areaDataList.value], activeAreaIndex.value)
    }
  } else if (name == 'route') {
    activeRouteIndex.value = index;
    props.handleActiveRoute && props.handleActiveRoute([...routeList.value], activeRouteIndex.value)
  }
}

const handleMouseDown = (e: any, url: string, data: any) => {
  if (store.state.isDragLoading || store.state.isRequesting) {
    return ElMessage({ type: 'warning', message: '数据加载中，请勿频繁操作' })
  }
  props.sourcePoolMourseDown(e, url, data)
}

const handlePreview = (data: any) => {
  showSourceType.value = data.materialFormat ? data.materialFormat.split('/')[0] : 'text'
  if (['fbx', 'glb', 'gltf', 'obj'].includes(showSourceType.value)) {
    showSourceType.value = 'model'
    modelType.value = data.materialFormat
  }
  let ossKey = ''
  if (showSourceType.value == 'model') {
    ossKey = data.modelStorageMap?.web?.ossKey || ''
  } else if (showSourceType.value == 'text') {
    sourceUrl.value = data.materialWord
  } else {
    ossKey = data.ossKey
  }
  if (ossKey) {
    getOssAccessPath({ key: ossKey }).then((res1: any) => {
      sourceUrl.value = res1.data
    })
  }
}

const handleMouseMove = (index: number, index2?: number, type?: string) => {
  if (!hoverImage.value) {
    hoverImage.value = index + ''
  }
  if (type) {
    hoverImage.value = `${index}${index2}${type}`
  }

}

const handleMouseLeave = () => {
  hoverImage.value = ''
}

// 删除资源
const handleDeleteSource = (event: any, i: any, index: number) => {
  handleDeleteTarget(event, 'classB')
  deleteData.value = {
    i,
    index
  }
}

const handleActiveSource = (i: any, index: number, isActive: boolean) => {
  props.activeSource && props.activeSource(i, index, isActive)
}

const handleClickActive = (i: any, index: number, object: any) => {
  if (currentSourceName.value == object.id || currentSourceName.value == object.uuid) {
    currentSourceName.value = ''
    props.activeSourceMaterial(i, index, false)
  } else {
    currentSourceName.value = object.uuid || object.id;
    props.activeSourceMaterial(i, index, true)
  }
}

const addArea = (data?: any, index?: number) => {
  if (data.id) {
    const materialDto = data.materialMetaDtoList?.map((e: any) => ({ ...e, ...e.materialDto, point: new Vector3(e.location.x, e.location.y, e.location.z), id: e.id, materialId: e.materialId, materialAffiliation: e.materialAffiliation })) || []
    const materialGroupDto = data.materialGroupDtoList?.map((e: any) => ({ ...e, point: new Vector3(e.location.x, e.location.y, e.location.z), id: e.id, materialId: e.id, materialName: e.groupName })) || []
    areaDataList.value.push({
      title: data.interactionName,
      materialDto: [...materialDto, ...materialGroupDto],
      rotate: angleToRadians(data.rotation.y),
      scale: new Vector2(data.scale.x, data.scale.z),
      location: data.location,
      id: data.id
    })
    props.handleActiveArea([...areaDataList.value], index, true)

    if (props.interactionId && data.id == props.interactionId) {
      activeAreaIndex.value = index || 0;
    }
    return
  }
  if (!areaDataList.value.length) {
    areaDataList.value.push({
      title: '互动区域01',
      materialDto: [],
      flag: 'add',
      id: new Date().getTime() + ''
    })
  } else {
    const nameNumber = +cropNumber(areaDataList.value.slice(-1)[0].title)
    let nameTitle = ''
    if (nameNumber) {
      nameTitle = areaDataList.value.slice(-1)[0].title.slice(0, -((nameNumber + '').length)) + (nameNumber + 1)
    } else {
      nameTitle = areaDataList.value.slice(-1)[0].title + '01'
    }
    areaDataList.value.push({
      title: nameTitle,
      materialDto: [],
      flag: 'add',
      id: new Date().getTime() + ''
    })
  }

  let { activeArea } = store.state.stateStore;
  activeArea = areaDataList.value.length - 1
  areaNameText.value = areaDataList.value[activeArea].title
  activeAreaIndex.value = activeArea;
  store.state.stateStore.activeArea = activeArea;
  nextTick(() => {
    (document.querySelector('.edit-area-name input') as any)?.focus();
  })
  props.handleActiveArea([...areaDataList.value], activeAreaIndex.value, true)

  // 新建互动区域的左右同步操作
  if (props.isPlanStyle) {
    (document.querySelectorAll('.left-tabs-content .add')[1] as any).click()
  } else {
    (document.querySelectorAll('.left-tabs-content .add')[0] as any).click()
  }
}

const addRoute = (data?: any, index?: number) => {
  if (data.id) {
    routeList.value.push({
      title: data.guideRouteName,
      ...data
    })

    props.handleActiveRoute && props.handleActiveRoute([...routeList.value], index, true)

    if (props.guideRouteId && data.id == props.guideRouteId) {
      activeRouteIndex.value = index || 0;
      operateIndex.value = 1
    }
    return
  }
  if (!routeList.value.length) {
    routeList.value.push({
      title: '导览路线01'
    })
  } else {
    const nameNumber = +cropNumber(routeList.value.slice(-1)[0].title)
    let nameText = ''
    if (nameNumber) {
      nameText = routeList.value.slice(-1)[0].title.slice(0, -((nameNumber + '').length)) + (nameNumber + 1)
    } else {
      nameText = routeList.value.slice(-1)[0].title + '01'
    }
    routeList.value.push({
      title: nameText
    })
  }

  let { activeRoute } = store.state.stateStore;
  activeRoute = routeList.value.length - 1
  routeNameText.value = routeList.value[activeRoute].title
  activeRouteIndex.value = activeRoute;
  store.state.stateStore.activeArea = activeRoute;
  nextTick(() => {
    (document.querySelector('.edit-route-name input') as any)?.focus();
  })
  props.handleActiveRoute([...routeList.value], activeRouteIndex.value, true)
}

onMounted(() => {
  getMaterialTotal();
  selectMaterialGroups()
})

const selectMaterialGroups = () => {
  selectMaterialGroup({ pageNo: 1, pageSize: 999 }).then((res) => {
    groupMaterial.value = res.data.records
    groupMaterial.value = groupMaterial.value.map((e: any) => {
      const viewGroupArray = []
      if (e.wordMaterial) {
        viewGroupArray.push({ materialAffiliation: e.wordMaterial.affiliationId, ...e.wordMaterial.baseMaterialDto });
      }
      switch (e.groupType) {
        case 1:
          viewGroupArray.push({ materialAffiliation: e.videoMaterial.affiliationId, ...e.videoMaterial.baseMaterialDto });

          break;
        case 2:
          viewGroupArray.push({ materialAffiliation: e.modelMaterial.affiliationId, ...e.modelMaterial.baseMaterialDto });
          break;
        case 3:
          viewGroupArray.push({ materialAffiliation: e.audioMaterial.affiliationId, ...e.audioMaterial.baseMaterialDto });
          break;
        case 4:
          viewGroupArray.push(...(e.materialSimpleInfoComposeArr.flat()).map((e2: any) => ({ materialAffiliation: e2.affiliationId, ...e2.baseMaterialDto })));
          break;
        case 5:
          viewGroupArray.push(...(e.materialSimpleInfoComposeArr.flat()).map((e2: any) => ({ materialAffiliation: e2.affiliationId, ...e2.baseMaterialDto })));
          break;
      }
      e.viewGroupArray = [...viewGroupArray]
      e.materialName = e.groupName
      return e
    })
  })
}

const getMaterialTotal = (materialType?: string) => {
  getDefaultMaterial({ pageSize: 100, pageNo: 1 }).then((res: any) => {
    const data1 = res.data.records.map((d: any) => {
      d.materialAffiliation = 2;
      return d;
    })
    sourceList.value = [...data1]
    getMaterialPageByUser({ getTotal: true, materialType }).then((res: any) => {
      const data2 = res.data.records.map((d: any) => {
        d.materialAffiliation = 1;
        return d;
      })
      sourceList.value.unshift(...data2)
      sourceList.value = sourceList.value.filter((item: any) => item.materialType != 5)
    })
  })
}

const initData = () => {
  areaDataList.value = []
  routeList.value = []
  activeAreaIndex.value = -99
  activeRouteIndex.value = -1
  operateIndex.value = 0
  store.state.stateStore = {
    activeArea: -1,
    activeRoute: -1
  }
}

watch(operateIndex, (newState) => {
  props.changeActiveIndex(newState)
})

watch(props.activeSourceName, (newState) => {
  currentSourceName.value = newState.text;
})

watch(props.renderSceneData, (newState) => {
  if (newState.reRender) {
    // 初始化数据
    initData()

    if (props.sceneInfo && props.sceneInfo.interactionDtoList) {
      props.sceneInfo.interactionDtoList.forEach((e: any, i: number) => {
        addArea(e, i)
      })
    }
    if (props.sceneInfo && props.sceneInfo.guideRouteDtoList) {
      props.sceneInfo.guideRouteDtoList.forEach((e: any, i: number) => {
        addRoute(e, i)
      })
    }
  }
})

watch(activeAreaIndex, (newState) => {
  if (store.state.activeAreaIndex != newState) {
    store.state.activeAreaIndex = newState
  }
})

watch(store.state, (newState) => {
  if (newState.activeAreaIndex != activeAreaIndex.value) {
    handleClick(newState.activeAreaIndex, 'area');
  }

  if (newState.deleteArea.dataIndex != -1 && newState.deleteArea.dataIndex != deleteArea.value.dataIndex) {
    showDeleteType.value = newState.deleteArea.type;
    deleteData.value.dataIndex = newState.deleteArea.dataIndex;
    deleteEvent()
  }
})

watch(deleteArea, (newState) => {
  if (store.state.deleteArea.dataIndex != newState.dataIndex) {
    store.state.deleteArea.type = newState.type
    store.state.deleteArea.dataIndex = newState.dataIndex;
  }
})
</script>

<style scoped lang="less">
.edit-area-name,
.edit-route-name {
  width: 160px;
  height: 100%;
  box-sizing: border-box;
  display: inline-block;

  ::v-deep(.el-input__inner) {
    color: #fff !important;
    height: 18px;
    line-height: 18px;
    font-weight: 400;

    &::placeholder {
      color: #ddd !important;
    }
  }

  ::v-deep(.el-input__wrapper) {
    background: rgba(0, 0, 0, 0) !important;
    border: none !important;
  }
}

#app .source-type {
  ::v-deep(.el-input__wrapper) {
    height: 34px;
  }

  ::v-deep(.el-input__wrapper .el-input__inner) {
    font-size: 14px;
  }
}


.canvas-left {
  position: fixed;
  left: 0;
  top: 60px;
  width: 317px;
  height: calc(100% - 59px);
  background: url(~@/assets/images/background/side-bg.png);
  background-size: 100% 100%;
  z-index: 10;

  .view-combination {
    height: 560px;
    max-height: calc(100% - 670px);
    background: rgba(214, 218, 222, 0.3);
    margin: -20px 10px 0;
    font-weight: 400;
    font-size: 12px;
    color: #000000;
    text-align: left;
    font-style: normal;
    z-index: 999;
    overflow: hidden;
    overflow-y: auto;

    .combination-list {
      position: relative;
      width: 100%;
      // height: 32px;
      line-height: 32px;
      padding: 0 16px;
      cursor: pointer;
      box-sizing: border-box;
      border-radius: 11px;

      .source-pool {
        text-align: center;

        img {
          margin-top: 8px;
        }
      }

      .operate-btns {
        visibility: hidden;
        position: absolute;
        right: 10px;
        top: 0;
        height: 32px;

        img {
          vertical-align: middle;
          cursor: pointer;
          margin-right: 6px;
        }
      }

      &:hover {
        background-color: rgba(60, 150, 255, 0.1);

        .operate-btns {
          visibility: visible;
        }
      }

      span {
        vertical-align: middle;
      }

      img {
        vertical-align: middle;
        margin-top: 1px;
      }
    }
  }
}

.canvas-left.hide-left-side {
  left: -320px;
}

.hide-side,
.show-side {
  position: absolute;
  top: 50%;
  margin-top: -65px;
  left: 317px;
  width: 16px;
  height: 42px;
  cursor: pointer;
  z-index: 1;

  img {
    width: 100%;
    height: 100%;
  }
}

.show-side {
  left: 0;
}

.left-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 52px;
  font-size: 17px;

  &>div {
    width: 50%;
    height: 100%;
    box-shadow: inset 0px -1px 2px 0px rgba(0, 0, 0, 0.06);
    cursor: pointer;
    padding-left: 32px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    img {
      margin-right: 6px;
    }
  }

  &>div.active {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  }
}

.left-tabs-content {
  height: calc(40% - 120px);
  min-height: 232px;

  .add {
    height: 39px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, #FFFFFF 100%);
    padding-left: 47px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    color: #4A4A4A;
    cursor: pointer;

    span {
      margin-left: 12px;
    }
  }

  .area-list-box {
    padding: 8px 31px 0 23px;
    box-sizing: border-box;
    height: calc(100% - 38px);
    overflow: hidden;
    overflow-y: auto;

    .area-name {
      position: relative;
      width: 263px;
      height: 34px;
      border-radius: 11px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      padding-left: 24px;
      padding-right: 12px;
      color: #414040;
      box-sizing: border-box;
      cursor: pointer;

      &>div {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        img {
          vertical-align: middle;
        }

        span {
          vertical-align: middle;
          display: inline-block;
          height: 18px;
          line-height: 18px;
          margin-left: 11px;
        }
      }

      .tool-box {
        width: 38px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .delete {
        width: 20px;
        height: 20px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        cursor: pointer;
      }
    }

    .route-list-style {
      margin-bottom: 8px;
    }

    .has-draw-down::before {
      content: '';
      width: 8px;
      height: 8px;
      position: absolute;
      left: 11px;
      top: 12px;
      background: url(~@/assets/images/icon/draw-down.png);
      transform: rotate(-90deg);
    }

    .hide.has-draw-down::before {
      transform: rotate(0deg);
    }

    .has-draw-down.active::before {
      background: url(~@/assets/images/icon/draw-downA.png);
    }

    .area-name.active {
      background: linear-gradient(225deg, #3C96FF 0%, #0375FF 100%);
      box-shadow: inset -1px -1px 0px 0px rgba(255, 255, 255, 0.2), inset 1px 1px 0px 0px rgba(11, 91, 225, 0.4);
      color: #FFFFFF;
    }

    .source-list {
      height: 14px;
      font-size: 14px;
      font-weight: 400;
      color: #414040;
      padding: 10px 15px 10px 66px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 11px;
      margin: 4px 0;
      cursor: pointer;

      &:hover {
        background-color: rgba(60, 150, 255, 0.1);
      }

      &.hoveActive {
        background-color: rgba(60, 150, 255, 0.1);
      }

      img {
        width: 14px;
        height: 14px;
        cursor: pointer;
        visibility: hidden;
      }

      &.hoveActive img {
        visibility: visible;
      }
    }
  }
}

.left-source-box {
  padding: 18px 20px;
  border-top: 1px solid rgba(200, 200, 200, 0.6);

  .add-source,
  .add-source-combination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    margin-bottom: 12px;

    .add {
      width: 32px;
      height: 32px;
      background: url(~@/assets/images/icon/add.png);
      background-size: 100% 100%;
      position: relative;
      z-index: 1;
      cursor: pointer;

      &:hover {
        &::before {
          content: '\65b0\589e\7d20\6750';
          position: absolute;
          left: -17px;
          top: 39px;
          font-size: 12px;
          width: 66px;
          text-align: center;
          height: 28px;
          line-height: 28px;
          background-color: #333537;
          letter-spacing: 1px;
          border-radius: 4px;
          color: #fff;
        }

        &::after {
          content: '';
          position: absolute;
          left: 10px;
          top: 27px;
          width: 0;
          height: 0;
          border: 6px solid transparent;
          border-bottom-color: #333537;
        }
      }
    }
  }

  .add-source-combination .add:hover::before {
    content: '\65b0\589e\7d20\6750\7ec4\5408';
    width: 90px;
    left: -29px;
  }
}

.source-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-top: 13px;

  .select-default {
    height: 34px;
  }

  &>span {
    font-size: 12px;
    font-weight: 400;
    color: #000000;
  }
}

.source-pool-box {
  height: calc(40% - 160px);
  min-height: 208px;
}

.source-pool-box,
.source-pool-box2 {
  padding-left: 20px;
  overflow: hidden;
  overflow-y: auto;
}

.source-pool-box2 {
  cursor: default;
}

.source-pool {
  float: left;
  width: 64px;
  height: 84px;
  margin-right: 7px;
  margin-bottom: 20px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  &>div:first-child {
    position: relative;
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    margin-bottom: 5px;

    .default-source {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 14px;
      line-height: 14px;
      background-image: url(~@/assets/images/icon/default-source-icon.png);
      background-size: 100% 100%;
      font-size: 10px;
      font-weight: 600;
      color: #2166EA;
      text-align: center;
    }

    &.hoverSource {
      position: relative;
      background: #D8D8D8 linear-gradient(180deg, #FFFFFF 0%, #A8DDFF 67%, #2C8CFF 100%, #2C8CFF 100%);

      .preview {
        width: 12px;
        height: 12px;
        position: absolute;
        right: 3px;
        top: 3px;
        background-image: url(~@/assets/images/preview.png);
        background-size: 100% 100%;
        cursor: pointer;
      }

      .default-source {
        background-image: url(~@/assets/images/icon/default-source-iconA.png);
        color: #fff;
      }
    }

    img {
      width: 48px;
      height: 48px;
      margin-top: 8px;
    }

    // cursor: pointer;
  }

  &>div:last-child {
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    color: #414040;
  }
}

.delete-box {
  margin-top: 22px;
  margin-bottom: 5px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .el-size {
    width: 43px;
    height: 20px;
    font-weight: 400;
    text-shadow: 0px 5px 10px rgba(200, 200, 200, 0.5);
    padding: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;

    span {
      font-size: 10px;
      transform: scale(0.83333);
      transform-origin: 0 0;
    }

    &.btn-sure {
      background: #E84A4B;
      box-shadow: 0px 5px 10px 0px rgba(200, 200, 200, 0.5), 0px 5px 9px 0px rgba(235, 159, 159, 0.3);
      border-radius: 3px;
      color: #FFFFFF;
      border: none;

      &:hover {
        background: #e84a4b;
        box-shadow: 0px 0px 4px 4px rgba(255, 35, 35, 0.3);
      }

      &:active {
        background: #cf4243;
        box-shadow: 0px 5px 10px 0px rgba(255, 35, 35, 0.5);
      }
    }

    &.btn-cancle {
      background: #F7F8FA;
      box-shadow: 0px 5px 10px 0px rgba(200, 200, 200, 0.5), 0px 5px 10px 0px rgba(185, 203, 225, 0.5);
      border-radius: 3px;
      color: #333333;
      border: none;

      &:hover {
        background: #f7f8fa;
        box-shadow: 0px 0px 4px 4px rgba(185, 203, 225, 0.3);
      }

      &:active {
        background: #edeff2;
        box-shadow: 0px 5px 10px 0px rgba(185, 203, 225, 0.5);
      }
    }
  }
}

.popover-btn {
  color: #F23333 !important;
}

/* 媒体查询，适配ipad端和小电脑页面 */
@media screen and (max-width: 1200px) {
  .canvas-left {
    width: 267px;

    .left-tabs {
      font-size: 14px;

      &>div img {
        width: 18px;
      }
    }

    .left-tabs-content .area-list-box .area-name {
      width: 213px;
    }

    .source-pool {
      width: 52px;
      height: 80px;

      &>div:first-child {
        width: 52px;
        height: 52px;
      }
    }

    .edit-area-name {
      width: 120px;
    }
  }

  .canvas-left.hide-left-side {
    left: -270px;
  }

  .hide-side {
    left: 267px;
  }
}
</style>