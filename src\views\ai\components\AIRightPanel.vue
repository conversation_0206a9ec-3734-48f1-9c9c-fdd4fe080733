<template>
  <div class="right-panel">
    <div class="history-title">
      历史记录
      <span class="history-tip">仅保留10条记录，建议生成后立即保存到素材空间</span>
    </div>
    <div class="history-list">
      <div v-if="loading" class="loading-state">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      <div v-else-if="history.length === 0" class="empty-state">
        <img src="@/assets/images/nolistimg.png" alt="暂无历史记录" class="empty-img" />
        <span>暂无历史记录</span>
      </div>
      <div v-else v-for="item in history" :key="item.id" class="history-card">
        <div v-if="item.status === 'generating'" class="card-progress">
          <img src="@/assets/images/starGif.gif" class="progress-icon" />
          <div v-if="!isFailedStatus(item.taskStatus)" class="progress-bar">
            <div class="progress-fill" :style="{ width: item.progress + '%' }"></div>
          </div>
          <div class="progress-text">{{ item.text }}</div>
          <div class="progress-subtext">{{ item.subText }}</div>
          <el-button
            v-if="
              !isFailedStatus(item.taskStatus) &&
              props.type !== AIModelType.IMAGE &&
              props.type !== AIModelType.VIDEO
            "
            class="cancel-btn"
            size="small"
            @click="handleCancel(item.id)">
            取消生成
          </el-button>
          <el-button
            v-if="isFailedStatus(item.taskStatus)"
            class="cancel-btn"
            size="small"
            @click="openDeleteDialog(item.id)">
            删除
          </el-button>
        </div>
        <div v-else class="card-image-box">
          <img
            :src="
              props.type === AIModelType.AUDIO
                ? require('@/assets/images/Rectangle11.png')
                : item.coverPicPath
                ? item.coverPicPath
                : require('@/assets/images/nolistimg.png')
            "
            class="card-image" />
          <img
            v-if="props.type === AIModelType.AUDIO || props.type === AIModelType.VIDEO"
            :src="require('@/assets/images/play11.png')"
            class="audio-play-btn" />
          <div class="card-overlay">
            <div class="card-actions">
              <button class="action-btn view-btn" @click="() => previewImage(item)">查看</button>
              <button class="action-btn save-btn" @click="openSaveDialog(item.id)">保存素材</button>
              <button class="action-btn delete-btn" @click="openDeleteDialog(item.id)">删除</button>
              <div class="card-time">{{ item.time }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 图片预览组件 -->
  <ImagePreview
    :show="showPreview"
    :image-url="previewImageUrl"
    @close="closePreview"
    @save="handlePreviewSave"
    @delete="handlePreviewDelete" />
  <AudioPreview
    :show="showAudioPreview"
    :audio-url="previewAudioUrl"
    @close="closeAudioPreview"
    @save="handlePreviewSave"
    @delete="handlePreviewDelete" />
  <VideoPreview
    :show="showVideoPreview"
    :video-url="previewVideoUrl"
    @close="closeVideoPreview"
    @save="handlePreviewSave"
    @delete="handlePreviewDelete" />
  <ModelPreview
    :show="showModelPreview"
    :model-url="previewModelUrl"
    @close="closeModelPreview"
    @save="handlePreviewSave"
    @delete="handlePreviewDelete" />

  <!-- 删除确认弹窗 -->
  <el-dialog
    v-model="showDeleteDialog"
    title="删除任务"
    width="450px"
    :close-on-click-modal="false">
    <span class="delete-dialog-title">是否确认删除当前任务？删除后不可恢复。</span>
    <template #footer>
      <el-button @click="showDeleteDialog = false">取消</el-button>
      <el-button type="primary" @click="confirmDelete">确认</el-button>
    </template>
  </el-dialog>
  <el-dialog v-model="showTipsDialog" title="提示" width="450px" :close-on-click-modal="false">
    <span class="tips-dialog-title">操作失败</span>
    <template #footer>
      <el-button type="primary" @click="showTipsDialog = false">知道了</el-button>
    </template>
  </el-dialog>
  <el-dialog v-model="showSaveDialog" title="保存素材" width="450px" :close-on-click-modal="false">
    <div class="save-dialog-text">将该素材保存到素材管理页面，保存成功后即可使用</div>
    <el-form ref="saveFormRef" :model="saveForm" :rules="saveRules" label-width="0">
      <div class="save-input-label">设置素材名称</div>
      <el-form-item prop="name">
        <el-input
          v-model="saveForm.name"
          placeholder="请输入描述信息"
          show-word-limit
          class="save-material-input"
          style="margin-top: 8px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showSaveDialog = false">取消</el-button>
      <el-button type="primary" @click="confirmSave">确认</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import ImagePreview from './ImagePreview.vue';
import AudioPreview from './AudioPreview.vue';
import VideoPreview from './VideoPreview.vue';
import ModelPreview from './ModelPreview.vue';
import { AIModelType } from '@/config';
import { getUserAiTask, getOssAccessPath, deleteAiTask, cancelAiTask, saveAiMaterial } from '@/api';

// 定义 props
interface Props {
  type: number;
}

const props = defineProps<Props>();

// 定义历史记录项的类型
interface HistoryItem {
  id: number;
  status: 'generating' | 'done' | 'failed';
  taskStatus: number;
  progress?: number;
  icon?: string;
  text?: string;
  subText?: string;
  time?: string;
  taskProduct: string;
  coverPicPath?: string; // 添加coverPicPath字段
}

// 历史记录数据
const history = ref<HistoryItem[]>([]);
const loading = ref(false);

// 获取AI任务列表
const fetchAiTasks = async () => {
  try {
    loading.value = true;
    const response = await getUserAiTask({
      pageNo: 1,
      pageSize: 10,
      taskType: props.type,
    });

    if (response.data && response.data.records) {
      history.value = response.data.records.map((item: any) => ({
        id: item.id,
        status: getCardStatus(item.taskStatus),
        taskStatus: item.taskStatus,
        progress: getCardProgress(item.taskStatus, item.rank),
        text: getCardText(item.taskStatus, item.rank),
        subText: getCardSubText(item.taskStatus, item.rank),
        time: formatTime(item.createTime),
        taskProduct: item.taskProduct || '',
        coverPicPath: item.coverPicPath || '',
      }));
    } else {
      history.value = [];
    }
  } catch (error) {
    console.error('获取AI任务列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 判断是否是失败状态
const isFailedStatus = (status: number) =>
  status === 6 || status === 7 || status === 5 || status === 9;

// 卡片状态映射
function getCardStatus(taskStatus: number) {
  // 只有4是完成，其余都算生成中
  return taskStatus === 4 ? 'done' : 'generating';
}

function getCardProgress(taskStatus: number, rank?: number) {
  if (taskStatus === 4) return 100;
  if (rank === undefined || rank === null) return 60;

  // rank为0时，进度为90%
  // rank越大，进度越小，但最小不低于10%
  // 假设最大rank为10，则每增加1，减少8%进度
  return Math.max(10, 90 - rank * 8);
}

function getCardText(taskStatus: number, rank?: number) {
  if (taskStatus === 4) return '生成完成';
  if (isFailedStatus(taskStatus)) return '生成失败';
  if (rank === 0) return '即将完成...';
  return rank ? `排队中，前方还有${rank}个任务` : '正在生成中...';
}

function getCardSubText(taskStatus: number, rank?: number) {
  if (taskStatus === 4) return '';
  if (isFailedStatus(taskStatus)) return '';
  if (rank === 0) return '请稍候';
  return rank ? '请耐心等待' : '请稍候';
}

// 格式化时间
const formatTime = (timestamp: string | number) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 监听类型变化，重新获取数据
watch(
  () => props.type,
  () => {
    fetchAiTasks();
  },
  { immediate: true } // 立即执行一次
);

// 预览相关变量
const showPreview = ref(false);
const previewImageUrl = ref('');
const showAudioPreview = ref(false);
const previewAudioUrl = ref('');
const showVideoPreview = ref(false);
const previewVideoUrl = ref('');
const showModelPreview = ref(false);
const previewModelUrl = ref('');

// 删除相关变量
const showDeleteDialog = ref(false);
const deleteItemId = ref<number | null>(null);
// 提示相关变量
const showTipsDialog = ref(false);

// 保存相关变量
const showSaveDialog = ref(false);
const saveItemId = ref<number | null>(null);
const saveFormRef = ref<FormInstance>();
const saveForm = reactive({ name: '' });
const saveRules = {
  name: [
    { required: true, message: '请输入素材名称', trigger: 'blur' },
    { min: 1, max: 20, message: '*支持1~20个字符的名称', trigger: 'blur' },
  ],
};

// 关闭预览
function closePreview() {
  previewImageUrl.value = '';
  showPreview.value = false;
}

function closeAudioPreview() {
  previewAudioUrl.value = '';
  showAudioPreview.value = false;
}

function closeVideoPreview() {
  previewVideoUrl.value = '';
  showVideoPreview.value = false;
}

function closeModelPreview() {
  previewModelUrl.value = '';
  showModelPreview.value = false;
}

// 预览相关事件处理
const handlePreviewSave = () => {
  if (deleteItemId.value) {
    // 打开保存对话框，让用户输入素材名称
    openSaveDialog(deleteItemId.value);
  }
};

const handlePreviewDelete = () => {
  if (deleteItemId.value) {
    // 不直接删除，而是打开确认弹窗
    openDeleteDialog(deleteItemId.value);
  }
};

// 预览图片
async function previewImage(item: HistoryItem) {
  try {
    deleteItemId.value = item.id; // 设置当前操作的项目ID
    const response = await getOssAccessPath({ key: item.taskProduct });
    if (response.data) {
      if (props.type === AIModelType.AUDIO) {
        previewAudioUrl.value = response.data;
        showAudioPreview.value = true;
      } else if (props.type === AIModelType.VIDEO) {
        previewVideoUrl.value = response.data;
        showVideoPreview.value = true;
      } else if (props.type === AIModelType.MODEL) {
        previewModelUrl.value = response.data;
        showModelPreview.value = true;
      } else {
        previewImageUrl.value = response.data;
        showPreview.value = true;
      }
    } else {
      ElMessage.error('获取预览地址失败');
    }
  } catch (error) {
    console.error('获取预览地址失败:', error);
    ElMessage.error('获取预览地址失败');
  }
}

// 删除任务
const handleDelete = async (id: number) => {
  try {
    const response = await deleteAiTask(id);
    if (response.code === '200' && !response.error) {
      ElMessage.success('删除成功');
      // 刷新列表
      fetchAiTasks();
      // 关闭所有预览窗口
      closePreview();
      closeAudioPreview();
      closeVideoPreview();
      closeModelPreview();
    } else {
      ElMessage.error('删除失败');
    }
  } catch (error) {
    console.error('删除失败:', error);
    ElMessage.error('删除失败');
  }
};

// 取消生成任务
const handleCancel = async (id: number) => {
  try {
    const response: any = await cancelAiTask(id);
    if (response?.code === '200' && !response?.error) {
      ElMessage.success('已取消生成');
      // 刷新列表
      fetchAiTasks();
    } else {
      ElMessage.error(response?.msg || '取消失败');
    }
  } catch (error) {
    console.error('取消生成失败:', error);
    ElMessage.error('取消失败');
  }
};

// 打开删除确认弹窗
const openDeleteDialog = (id: number) => {
  deleteItemId.value = id;
  showDeleteDialog.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (deleteItemId.value) {
    await handleDelete(deleteItemId.value);
    showDeleteDialog.value = false;
    deleteItemId.value = null;
  }
};

// 保存相关函数
function openSaveDialog(id: number) {
  saveItemId.value = id;
  saveForm.name = '';
  showSaveDialog.value = true;
}

async function confirmSave() {
  if (!saveFormRef.value) return;

  try {
    // 先进行表单验证
    const isValid = await saveFormRef.value.validate();
    if (!isValid) {
      // 验证失败，不执行保存操作
      return;
    }

    if (!saveItemId.value) {
      ElMessage.error('保存失败：未找到任务ID');
      return;
    }

    // 调用保存AI素材接口
    const response = await saveAiMaterial({
      id: saveItemId.value,
      name: saveForm.name,
    });

    if (response.code === '200' || response.code === '2000') {
      ElMessage.success('保存成功');
      showSaveDialog.value = false;
      saveForm.name = '';
      saveItemId.value = null;
      // 刷新列表
      fetchAiTasks();
    } else {
      ElMessage.error(response.message || '保存失败');
      // 保存失败弹窗
      showTipsDialog.value = true;
    }
  } catch (error) {
    // 如果是验证错误，不显示错误消息（Element Plus会自动显示验证错误）
    if (error && typeof error === 'object' && 'message' in error) {
      // 这是验证错误，不需要额外处理
      return;
    }
    console.error('保存素材失败:', error);
    ElMessage.error('保存失败');
  }
}

defineExpose({ fetchAiTasks });
</script>

<style scoped lang="less">
.right-panel {
  flex: 1;
  background: #fff;
  border-radius: 10px;
  padding: 24px 20px;
  min-width: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;

  .history-title {
    font-weight: bold;
    font-size: 24px;
    color: #1e1e1e;
    text-align: left;
    font-style: normal;
    margin-bottom: 12px;
  }

  .history-tip {
    font-weight: 400;
    font-size: 14px;
    color: #797979;
    text-align: left;
    margin-left: 8px;
  }

  .history-list {
    position: relative;
    min-height: 300px;
    display: grid;
    grid-template-columns: repeat(auto-fill, 280px);
    gap: 15px;
    justify-content: left;
    overflow-y: auto;
    flex: 1 1 auto;
    min-height: 0;
    align-content: flex-start;

    .loading-state,
    .empty-state {
      position: absolute;
      inset: 0;
      margin: auto;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #797979;
      font-size: 14px;
      gap: 8px;
      z-index: 2;

      .loading-icon {
        font-size: 24px;
        animation: rotate 1s linear infinite;
      }

      .empty-img {
        width: 140px;
        height: 140px;
        margin-bottom: 8px;
      }
    }

    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    .history-card {
      width: 280px;
      height: 280px;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      background: #fff;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      // 生成中状态的卡片样式
      &:has(.card-progress) {
        border-radius: 10px;
        background: linear-gradient(
          44deg,
          rgba(246, 41, 123, 0.1) -0.9%,
          rgba(208, 41, 246, 0.1) 31.63%,
          rgba(123, 19, 251, 0.1) 59.12%,
          rgba(19, 204, 251, 0.1) 99.93%
        );
        padding: 0;
      }
      // 已完成状态的卡片样式
      &:has(.card-image-box) {
        background: #fff;
        padding: 0;
      }
      .card-progress,
      .card-image-box {
        width: 100%;
        height: 100%;
      }
      // 防止内容溢出
      img,
      .card-title,
      .card-content {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .card-progress {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 11px;

        .progress-icon {
          width: 100px;
          height: 100px;
          position: relative;
          left: 10px;
        }

        .progress-bar {
          width: calc(100% - 40px);
          height: 4px;
          background: #ffffff;
          border-radius: 10px;
          margin: 0 20px 4px 20px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4dcd 0%, #2e76ff 100%);
            border-radius: 10px;
            transition: width 0.3s ease;
          }
        }

        .progress-text {
          font-size: 14px;
          color: #696969;
          text-align: center;
          font-weight: 400;
        }

        .progress-subtext {
          font-size: 14px;
          color: #696969;
          text-align: center;
          margin-bottom: 4px;
          font-weight: 400;
        }

        .cancel-btn {
          width: 96px;
          height: 34px;
          background: #c9453e;
          border-radius: 4px;
          color: #fff;
          border: none;
          font-size: 14px;

          &:disabled {
            background: #999;
            cursor: not-allowed;
          }
        }
      }

      .card-image-box {
        width: 100%;
        height: 100%;
        position: relative;
        cursor: pointer;
        border-radius: 10px;
        overflow: hidden;
        &:hover .audio-play-btn {
          display: none;
        }
        .audio-play-btn {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 70px;
          height: 70px;
          z-index: 2;
          pointer-events: none;
        }

        .card-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 10px;
          background: #dadada;
        }

        .card-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 10px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: none;
        }

        .card-actions {
          display: flex;
          gap: 8px;
          flex-direction: column;
          align-items: center;

          .action-btn {
            width: 92px;
            height: 34px;
            border-radius: 4px;
            border: none;
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            transition: opacity 0.2s ease;

            &.view-btn {
              background: #2e76ff;
              color: #ffffff;
            }

            &.save-btn {
              background: #2e76ff;
              color: #ffffff;
            }

            &.delete-btn {
              background: #c9453e;
              color: #ffffff;
            }

            &:hover {
              opacity: 0.8;
            }
          }
        }

        .card-time {
          font-size: 15px;
          font-weight: 400;
          color: #ffffff;
          text-align: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          position: absolute;
          bottom: 15px;
          left: 50%;
          transform: translateX(-50%);
        }

        &:hover {
          .card-overlay {
            opacity: 1;
            pointer-events: auto;
          }

          .card-time {
            opacity: 1;
          }
        }
      }
    }
  }
}

.delete-dialog-title,
.save-dialog-text {
  color: #797979;
  text-align: left;
  font-size: 14px;
  line-height: 1.7;
  padding: 0px 0 0 0;
}
.save-material-input {
  width: 100%;
}

.save-input-label {
  color: #797979;
  font-size: 14px;
  font-weight: 500;
  margin-top: 18px;
  margin-bottom: 2px;
  text-align: left;
}

.input-tip {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 2px;
  margin-bottom: 4px;
  text-align: left;
}
</style>
