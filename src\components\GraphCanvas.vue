<template>
  <div id="source-view" class="canvas-view borderR14"></div>
  <img v-if="showLoading" class="loading-img" src="@/assets/images/icon/loading-b.png" />
</template>

<script lang="ts" setup>
import { onMounted, ref, onUnmounted } from 'vue'
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls"
import { Vector3, Scene, OrthographicCamera, WebGLRenderer, Group, AmbientLight, Box3, MOUSE, TOUCH, TextureLoader, RepeatWrapping, PlaneGeometry, MeshBasicMaterial, Mesh, GridHelper, BufferGeometry, LineBasicMaterial, LineSegments, BufferAttribute } from 'three'
import { initSize } from "@/utils";
import { CPoint, CLine } from '@/core/primitives'

const props = defineProps({
  handleMouseUp: {
    default: null,
    type: Function
  },
  resData: {
    default: null,
    type: Object
  }
})

let camera: OrthographicCamera;
let width: number, height: number;
let scene: Scene;
let renderer: any;
const showLoading = ref(true);
const graphPoints: any = ref([]);
const childNodeArray: any = ref([]); // 线中间的点位集合
const controlsScale = ref(1);
let controls: any = null;
let animationFrameId: any = null;

onMounted(() => {
  scene = new Scene();
  let canvas = document.getElementById('source-view')
  width = canvas?.getBoundingClientRect().width || 0
  height = canvas?.getBoundingClientRect().height || 0
  let k = width / height
  let s = 6


  camera = new OrthographicCamera(-s * k, s * k, s, -s, 0.1, 1000)
  camera.position.set(-3, 0, 60)
  camera.lookAt(new Vector3(0, 0, 0));

  renderer = new WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setClearColor(0xffffff, 0)
  canvas?.appendChild(renderer.domElement)
  renderer.render(scene, camera);

  // 添加控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.mouseButtons = {
    LEFT: MOUSE.ROTATE,
    MIDDLE: MOUSE.DOLLY,
    RIGHT: MOUSE.PAN
  }
  controls.touches = {
    ONE: TOUCH.ROTATE,
    TWO: TOUCH.DOLLY_PAN
  }
  // 使用控制器
  controls.enableDamping = false;

  // 环境光
  const ambient = new AmbientLight(0xffffff, 3);
  scene.add(ambient);

  // 添加网格地面
  addGridBackground();

  animate()
  function animate() {
    // 使用 requestAnimationFrame 执行动画
    animationFrameId = requestAnimationFrame(animate)

    renderer.render(scene, camera)
  }

  // 双击选中
  canvas?.addEventListener('mouseup', () => {
    props.handleMouseUp && props.handleMouseUp()
  }, false);

  controls.addEventListener("change", onChange);

  if (props.resData) {
    graphPoints.value = props.resData.filter((e: any) => e.nodeType == 1 || e.nodeType == 3);
    childNodeArray.value = props.resData.filter((e: any) => e.nodeType == 2);
    initPoint();
  }
})

onUnmounted(() => {
  cancelAnimationFrame(animationFrameId);
})

const initPoint = () => {
  const sceneChild = scene.children.filter((e) => e.userData.nodeId);
  sceneChild.forEach((e) => {
    scene.remove(e)
  })
  const pointObj: any = {}
  graphPoints.value.forEach((e: any) => {
    pointObj[e.nodeId] = e;
  })
  graphPoints.value.forEach((e: any) => {
    // 这里判断是有效点位还是无效点位
    if (e.relatedNodeIdList.length) {
      drawPoint({ vertex: new Vector3(e.x, 0, e.z) }, e.nodeId, e.nodeType, true);
      e.relatedNodeIdList.forEach((k: any) => {
        const oldPath = scene.getObjectByName(pointObj[k].nodeId + e.nodeId);
        if (!oldPath) {
          drawDashLine({ vertexs: [[e.x, 0, e.z], [pointObj[k].x, 0, pointObj[k].z]] }, [e.nodeId, pointObj[k].nodeId]);
        }
      })
    } else {
      drawPoint({ vertex: new Vector3(e.x, 0, e.z) }, e.nodeId, e.nodeType);
    }
  });
}

const onChange = () => {
  controlsScale.value = controls.object.zoom

  const sceneChild: any = scene.children.filter((e) => e.userData.nodeId);
  if (sceneChild.length) {
    sceneChild.forEach((e: any) => {
      e.setSize(e.userData.initSize * controlsScale.value);
    })
  }
}

// 添加网格背景
const addGridBackground = () => {
  // 创建自定义网格以支持更粗的线条
  const gridSize = 300; // 网格总尺寸
  const gridDivisions = 100; // 网格分割数
  const gridStep = gridSize / gridDivisions;

  // 创建网格线的几何体
  const gridGeometry = new BufferGeometry();
  const gridVertices = [];

  // 创建主网格线（每10个单位一条，更粗更白）
  const mainGridMaterial = new LineBasicMaterial({
    color: 0xf8f8f8, // 非常接近白色
    linewidth: 2,
    transparent: true,
    opacity: 0.8
  });

  // 创建次网格线（每1个单位一条，稍细但仍然很白）
  const subGridMaterial = new LineBasicMaterial({
    color: 0xfcfcfc, // 更接近纯白色
    linewidth: 1,
    transparent: true,
    opacity: 0.6
  });

  // 生成网格线顶点
  const halfSize = gridSize / 2;

  // 主网格线（每10个单位）
  const mainGridVertices = [];
  for (let i = -halfSize; i <= halfSize; i += gridStep * 10) {
    // 垂直线
    mainGridVertices.push(i, 0, -halfSize);
    mainGridVertices.push(i, 0, halfSize);
    // 水平线
    mainGridVertices.push(-halfSize, 0, i);
    mainGridVertices.push(halfSize, 0, i);
  }

  // 次网格线（每1个单位）
  const subGridVertices = [];
  for (let i = -halfSize; i <= halfSize; i += gridStep) {
    if (i % (gridStep * 10) !== 0) { // 跳过主网格线位置
      // 垂直线
      subGridVertices.push(i, 0, -halfSize);
      subGridVertices.push(i, 0, halfSize);
      // 水平线
      subGridVertices.push(-halfSize, 0, i);
      subGridVertices.push(halfSize, 0, i);
    }
  }

  // 创建主网格线
  if (mainGridVertices.length > 0) {
    const mainGridGeometry = new BufferGeometry();
    mainGridGeometry.setAttribute('position', new BufferAttribute(new Float32Array(mainGridVertices), 3));
    const mainGridLines = new LineSegments(mainGridGeometry, mainGridMaterial);
    mainGridLines.name = 'main-grid-lines';
    mainGridLines.position.y = -0.1;
    scene.add(mainGridLines);
  }

  // 创建次网格线
  if (subGridVertices.length > 0) {
    const subGridGeometry = new BufferGeometry();
    subGridGeometry.setAttribute('position', new BufferAttribute(new Float32Array(subGridVertices), 3));
    const subGridLines = new LineSegments(subGridGeometry, subGridMaterial);
    subGridLines.name = 'sub-grid-lines';
    subGridLines.position.y = -0.1;
    scene.add(subGridLines);
  }

  // 添加一个半透明的地面平面作为背景
  const planeGeometry = new PlaneGeometry(gridSize, gridSize);
  const planeMaterial = new MeshBasicMaterial({
    color: 0xfdfdfd, // 非常接近白色的背景
    transparent: true,
    opacity: 0.2
  });
  const plane = new Mesh(planeGeometry, planeMaterial);
  plane.rotation.x = -Math.PI / 2; // 旋转90度使其水平
  plane.position.y = -0.2; // 放在网格下方
  plane.name = 'ground-plane';
  scene.add(plane);
}


// 画线
const drawDashLine = (data: any, nodeIds: any) => {
  const currentLine = scene.getObjectByName(nodeIds[1] + nodeIds[0]);
  if (currentLine) return;
  const cline = new CLine({ ...data, color: 0x333333, lineWidth: 4, depthTest: false, transparent: true });
  cline.userData.points = [new Vector3(...data.vertexs[0]), new Vector3(...data.vertexs[1])]
  cline.name = nodeIds.join('');
  scene.add(cline)
  return cline;
}

// 画点
const drawPoint = (data: any, nodeId: string, nodeType: number, isValid?: boolean) => {
  const cpoint = new CPoint({ ...data, size: 46 * controlsScale.value, depthTest: false, transparent: true, url: isValid ? (nodeType == 1 ? '/images/valid-default.png' : '/images/valid-default2.png') : (nodeType == 1 ? '/images/invalid-default.png' : '/images/invalid-default2.png') });
  cpoint.userData.valid = !!isValid;
  cpoint.name = nodeId;
  cpoint.userData.nodeId = nodeId;
  scene.add(cpoint);
}

const addMesh = (obj: any, callback?: any) => {
  const initModel = scene.getObjectByName('init-model');
  if (initModel) {
    scene.remove(initModel);
  }
  scene.add(obj);
  const boxInfo = new Box3().expandByObject(obj);
  obj.position.set(0, -(boxInfo.min.y - 0.1), 0);
  obj.name = 'init-model';
  callback && callback();
  showLoading.value = false;
}

const getCamera = () => {
  return camera;
}

const getRenderer = () => {
  return renderer;
}

const getScene = () => {
  return scene;
}

defineExpose({
  addMesh,
  getCamera,
  getRenderer,
  getScene
})
</script>
<style scoped lang="less">
.canvas-view {
  width: 100%;
  height: 100%;
}

.loading-img {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -16px;
  margin-top: -16px;
  width: 32px;
  height: 32px;
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>