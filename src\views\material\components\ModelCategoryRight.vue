<template>
  <div class="category-management-right">
    <div class="category-model-list" v-loading="loading" @scroll="handleScroll">
      <div v-if="!loading && models.length === 0" class="empty-state">
        <img src="@/assets/images/nolistimg.png" alt="暂无相关文件" class="empty-img" />
        <span>暂无相关文件</span>
      </div>
      <div v-else v-for="(model, index) in models" :key="index" class="category-model-item">
        <div class="category-model-thumb">
          <img :src="model.image" :alt="model.name" />
        </div>
        <div class="category-model-content">
          <span class="category-model-id">{{ model.materialName }}</span>
          <el-popover
            placement="top-start"
            :width="440"
            trigger="click"
            v-model:visible="popoverVisible[index]"
            popper-class="category-edit-popover"
            :show-arrow="false"
            @show="onPopoverShow(index)"
            @hide="onPopoverHide(model)">
            <div class="category-checkbox-list">
              <label
                v-for="item in allCategoryOptions"
                :key="item.value"
                class="category-checkbox-item">
                <input
                  type="checkbox"
                  v-model="model.checkedCategories"
                  :value="item.value"
                  @click.stop />
                <span
                  :class="{
                    checked:
                      model.checkedCategories && model.checkedCategories.includes(item.value),
                  }">
                  {{ item.label }}
                </span>
              </label>
            </div>
            <template #reference>
              <div class="category-model-action" @click.stop="handleAddToCategory(model, index)">
                <img
                  src="@/assets/images/add343.png"
                  style="width: 12px; height: 12px; margin-right: 4px; position: relative; top: 1px"
                  alt="" />
                <span>添加分类</span>
              </div>
            </template>
          </el-popover>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { ElPopover, ElMessage } from 'element-plus';
import 'element-plus/es/components/popover/style/css';
import { getDefaultMaterial, setMaterialModelType, getModeType } from '@/api/modules/material';

// 定义 emits
const emit = defineEmits<{
  updateTotal: [total: number];
  categoryUpdated: [];
}>();

// 定义 props
const props = defineProps<{
  categoryId: number | null;
  categoryUpdateTrigger?: number; // 添加触发更新的prop
}>();

// 定义模型数据接口
interface ModelItem {
  id: string;
  name: string;
  image: string;
  checkedCategories: string[];
  // 根据API返回的实际字段添加更多属性
  [key: string]: any;
}

// 分页相关状态
const models = ref<ModelItem[]>([]);
const currentPage = ref(1);
const pageSize = ref(30);
const hasMore = ref(true);
const isLoadingMore = ref(false);
const totalCount = ref(0); // 总数量
const popoverVisible = ref<boolean[]>([]);
const loading = ref(false);

// 所有分类选项 - 从API获取
const allCategoryOptions = ref<Array<{ label: string; value: string }>>([]);

// 存储原始选择状态，用于比较是否有变化
const originalSelections = ref<Map<string, string[]>>(new Map());

// 加载模型数据
const loadModels = async (isLoadMore = false, modelTypeId?: number | null) => {
  try {
    if (isLoadMore) {
      isLoadingMore.value = true;
    } else {
      loading.value = true;
    }

    const params: any = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      materialType: 4,
    };
    if (modelTypeId) {
      params.modelType = modelTypeId;
    }

    const response = await getDefaultMaterial(params);

    // 根据API返回的实际数据结构调整
    if (
      response &&
      response.data &&
      response.data.records &&
      Array.isArray(response.data.records)
    ) {
      const newModels = response.data.records.map((item: any) => {
        // 从 modelTypeDtoList 中提取已选中的分类ID，添加安全检查
        const selectedCategoryIds = item.modelTypeDtoList
          ? item.modelTypeDtoList
              .filter((type: any) => type && type.id != null) // 过滤掉 null 和 undefined
              .map((type: any) => String(type.id))
          : [];
        return {
          id: item.id,
          name: item.materialName,
          image: item.thumbnailOssAccessUrl,
          checkedCategories: selectedCategoryIds,
          ...item, // 保留原始数据
        };
      });

      if (isLoadMore) {
        models.value.push(...newModels);
        // 为新加载的模型添加弹窗状态
        popoverVisible.value.push(...newModels.map(() => false));
      } else {
        models.value = newModels;
        // 重置弹窗状态数组
        popoverVisible.value = newModels.map(() => false);
      }

      // 检查是否还有更多数据 - 根据API返回的分页信息
      const { current, pages, total } = response.data;
      hasMore.value = current < pages;

      // 更新总数量
      totalCount.value = total;
      // 通知父组件总数变化
      emit('updateTotal', total);

      if (isLoadMore && newModels.length > 0) {
        currentPage.value++;
      }

      // 可以在这里记录总数等信息
      console.log(
        `加载了 ${newModels.length} 条数据，当前第 ${current} 页，共 ${pages} 页，总计 ${total} 条`
      );
    }
  } catch (error) {
    ElMessage.error('获取模型列表失败');
    console.error('获取模型列表失败:', error);
  } finally {
    loading.value = false;
    isLoadingMore.value = false;
  }
};

// 监听categoryId变化，重置分页并按modelType请求
watch(
  () => props.categoryId,
  (newVal) => {
    currentPage.value = 1;
    loadModels(false, newVal);
  },
  { immediate: true }
);

// 监听分类更新触发
watch(
  () => props.categoryUpdateTrigger,
  (newVal, oldVal) => {
    console.log('🔍 categoryUpdateTrigger 变化:', { newVal, oldVal });
    if (newVal && newVal !== oldVal) {
      console.log('🔄 触发分类选项刷新');
      loadCategoryOptions();
    }
  }
);

// 弹窗显示时记录原始选择状态
function onPopoverShow(idx: number) {
  console.log('🔍 onPopoverShow 被调用，索引:', idx);
  popoverVisible.value = popoverVisible.value.map((_, i) => i === idx);

  // 记录当前模型的原始选择状态
  const model = models.value[idx];
  if (model) {
    originalSelections.value.set(model.id, [...(model.checkedCategories || [])]);
  }

  // 每次打开弹窗时刷新分类选项，确保显示最新的分类
  console.log('🔄 打开弹窗时刷新分类选项');
  loadCategoryOptions();
}

// 弹窗隐藏时提交分类设置
const onPopoverHide = async (model: ModelItem) => {
  // 获取当前选中的分类ID
  const currentCategories = model.checkedCategories || [];
  // 获取原始选中的分类ID
  const originalCategories = originalSelections.value.get(model.id) || [];

  // 比较是否有变化
  const hasChanged =
    currentCategories.length !== originalCategories.length ||
    !currentCategories.every((id) => originalCategories.includes(id));

  // 只有在有变化时才提交
  if (hasChanged) {
    try {
      loading.value = true;
      await setMaterialModelType({
        id: model.id,
        categoryIds: currentCategories,
      });
      ElMessage.success('模型分类设置成功');

      // 更新原始选择状态
      originalSelections.value.set(model.id, [...currentCategories]);

      // 通知父组件分类已更新，需要刷新左侧分类列表
      emit('categoryUpdated');
    } catch (error) {
      ElMessage.error('设置模型分类失败');
      console.error('设置模型分类失败:', error);

      // 如果提交失败，恢复原始选择状态
      model.checkedCategories = [...originalCategories];
    } finally {
      loading.value = false;
    }
  }
};

// 获取分类选项数据
const loadCategoryOptions = async () => {
  console.log('🔄 loadCategoryOptions 开始执行');
  try {
    const response = await getModeType();
    console.log('📡 API 返回的分类数据:', response);

    if (response && response.data && Array.isArray(response.data)) {
      const newOptions = response.data.map((item: any) => ({
        label: item.typeName || item.name,
        value: String(item.id),
      }));
      console.log('📝 新的分类选项:', newOptions);
      console.log('📝 旧的分类选项:', allCategoryOptions.value);

      allCategoryOptions.value = newOptions;

      console.log('✅ 分类选项已更新:', allCategoryOptions.value);
    }
  } catch (error) {
    console.error('❌ 获取分类选项失败:', error);
    ElMessage.error('获取分类选项失败');
  }
};

// 滚动加载更多
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  const { scrollTop, scrollHeight, clientHeight } = target;

  // 当滚动到底部附近时加载更多
  if (scrollHeight - scrollTop - clientHeight < 100 && hasMore.value && !isLoadingMore.value) {
    loadModels(true);
  }
};

const handleAddToCategory = (model: any, _index?: number) => {
  console.log('Opening category selection for model:', model);
  // 现在只是打开弹窗，实际的提交逻辑在 onPopoverHide 中处理
};

onMounted(() => {
  // 初始化加载模型数据和分类选项
  loadCategoryOptions();
});
</script>

<style scoped lang="less">
.category-management-right {
  flex: 1;
  height: 100%; // 撑满父容器高度
  margin-left: 20px;
  border-radius: 4px;
  padding: 16px 0;
  display: flex;
  flex-direction: column;

  .category-model-list {
    flex: 1; // 占据剩余空间
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto; // 内容超出时显示滚动条
    position: relative;

    // 隐藏滚动条
    &::-webkit-scrollbar {
      width: 0;
      display: none;
    }

    &::-webkit-scrollbar-track {
      display: none;
    }

    &::-webkit-scrollbar-thumb {
      display: none;
    }

    &::-webkit-scrollbar-thumb:hover {
      display: none;
    }

    .empty-state {
      position: absolute;
      inset: 0;
      margin: auto;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #797979;
      font-size: 14px;
      gap: 8px;
      z-index: 2;

      .empty-img {
        width: 140px;
        height: 140px;
        margin-bottom: 8px;
      }

      span {
        color: #797979;
        font-size: 14px;
        font-weight: 400;
        text-align: center;
      }
    }

    .category-model-item {
      width: 346px;
      height: 106px;
      border-radius: 0px 0px 0px 0px;
      border: 1px solid #e6edf7;
      display: flex;
      align-items: center;
      overflow: hidden;

      .category-model-thumb {
        width: 90px;
        height: 90px;
        min-width: 90px;
        background: rgba(146, 82, 20, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        margin: 8px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .category-model-content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px;
        color: #1e1e1e;

        .category-model-id {
          font-size: 14px;
          color: #1e1e1e;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 200px; // 设置最大宽度
        }

        .category-model-action {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 6px 12px;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          color: #1e1e1e;

          span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 80px; // 设置最大宽度
          }
        }
      }
    }
  }
}

// 弹窗样式
:global(.category-edit-popover) {
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 18px 24px 12px 24px;
  width: auto;
  min-width: unset;
  max-width: unset;
  display: inline-block;
}

.category-checkbox-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 15px;
  width: 100%;
}

.category-checkbox-item {
  display: flex;
  align-items: center;
  font-size: 15px;
  cursor: pointer;
  user-select: none;
  min-width: 120px; // 改为最小宽度
  max-width: 200px; // 设置最大宽度
  flex: 1; // 让每个选项自适应宽度

  input[type='checkbox'] {
    margin-right: 8px;
    accent-color: #2e76ff;
  }

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1; // 让 span 占据剩余空间
    min-width: 0; // 允许 flex 项目收缩到内容宽度以下
  }

  .checked {
    color: #2e76ff;
    font-weight: 500;
  }

  input[type='checkbox']:checked + span {
    color: #2e76ff;
    font-weight: 500;
  }

  input[type='checkbox']:checked {
    accent-color: #2e76ff;
    border-color: #2e76ff;
  }
}

// 加载更多和无更多数据的样式
.loading-more,
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
}

.loading-more {
  color: #2e76ff;
}

.no-more {
  color: #999;
}
</style>
