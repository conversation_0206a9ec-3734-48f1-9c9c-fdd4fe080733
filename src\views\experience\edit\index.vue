<template>
  <div class="page-tools-box" @mousemove="handleIconMove">
    <div class="edit-tips" v-if="showTips">
      <img src="@/assets/images/icon/edit-text-tips.png" />
      <span>将素材吸附到透明模型中的定位点上才有效哦</span>
    </div>
    <experience-canvas
      ref="canvasRef"
      :handle-mouse-move="handleMouseMoveCanvas"></experience-canvas>
    <header-view :show-share="showShare" :scene-data="editSceneData" ref="headerRef"></header-view>
    <side-left
      :source-pool-mourse-down="sourcePoolMourseDown"
      :deleteAssets="deleteAssets"></side-left>
    <edit-tools ref="editToolsRef" :change-operate-type="changeOperateType"></edit-tools>
    <side-right
      ref="sideRightRef"
      :change-scene-value="changeSceneValue"
      :change-active-value="changeActiveValue"></side-right>
  </div>
  <div
    class="show-icon"
    :style="{ backgroundImage: `url(${dragingImg})` }"
    @mousemove="handleIconMove"
    @mouseup="handleIconUp"></div>
  <share-view v-if="showShareMask" :hide-mask="hideMask" :scene-data="editSceneData"></share-view>
</template>
<script lang="ts" setup>
import ExperienceCanvas from '@/components/experience/ExperienceCanvas.vue';
import { ref, onMounted, reactive, onBeforeMount, watch } from 'vue';
import { ElMessage, ElMessageBox, ElCard } from 'element-plus';
import { Vector3, Vector2, Quaternion, Box3, Scene } from 'three';
// import PageHeader from '@/components/experience/PageHeader.vue'
import HeaderView from '@/components/scene_web/HeaderView.vue';

import SideLeft from '@/components/experience/SideLeft.vue';
import EditTools from '@/components/experience/EditTools.vue';
import SideRight from '@/components/experience/SideRight.vue';
import ShareView from '@/components/experience/ShareView.vue';
import { MouseStyle, updateMouseStyle } from '@/core/mouse';
import {
  getOssAccessPath,
  getSceneMetaPageForWeb,
  getWebScene,
  saveScene,
  updateSceneMeta,
  getUserTypeByToken,
  getWxNotifySceneInfo,
} from '@/api';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { loadZipFileForJSZip } from '@/utils';
const dragingImg = ref(''); // 当前拖拽的资源缩略图
let Icondom: any = null;
let initObjectData: any = reactive({});
const canvasRef = ref();
const sideRightRef = ref();
const store = useStore();
const router = useRouter();
const editToolsRef = ref();
const showShareMask = ref(false);
const editSceneData: any = ref({});
const currentMaterialData: any = ref({});
const submitData = {};
let scene: any = new Scene();
const userType = ref(-1);
let cube: any = null;
const headerRef = ref();
let isDrag = false;
let data: any = {};
let sceneType = 0;
const showTips = ref(false);

const exitEdit = () => {
  // 更新场景之前先截图保存
  const pageQuery: any = router.currentRoute.value.query;
  const sceneId = pageQuery.sceneid || '';
  const renderer = (window as any).renderer2;
  const camera = (window as any).camera2;
  const token = window.localStorage.getItem('token');
  if (!token) return;
  renderer.render(scene, camera);
  let imgData = renderer.domElement.toDataURL('image/jpeg');
  saveScene({ sceneId: sceneId, base64Pic: imgData }).then((res: any) => {
    if (res.code == 200) {
      if ([6, 7].includes(userType.value)) {
        router.push('experience_home');
      } else {
        router.push('home');
      }
    }
  });
};

// 修改高亮的数据
const changeActiveValue = (val: number, key: string, type: string) => {
  const newValue = type == 'rotation' ? (val / 180) * Math.PI : val;
  const editSceneData = { ...store.state.editSceneData };
  editSceneData.outerMaterialMetaDtoList = editSceneData.outerMaterialMetaDtoList.map((e: any) => {
    if (e.uuid == store.state.activeMaterial) {
      const obj = scene.getObjectByName(store.state.activeMaterial);
      e[type][key] = newValue;
      if (type == 'location') {
        obj.position.set(e.location.x, e.location.y, e.location.z);
        cube.position.set(e.location.x, e.location.y, e.location.z);
      } else if (type == 'rotation') {
        obj.rotation.set(-e.rotation.x, -e.rotation.y, -e.rotation.z);
        const quaternion = obj.quaternion.clone();
        let metaInfo = `${quaternion.x},${quaternion.y},${quaternion.z},${quaternion.w},${obj.userData.initScale}`;
        e.metaInfo = metaInfo;
      } else if (type == 'scale') {
        const initScale = obj.userData.initScale;
        obj.scale.set(e.scale.x * initScale, e.scale.y * initScale, e.scale.z * initScale);
      }
      e.flag = e.flag == 'add' ? 'add' : 'update';
    }
    return e;
  });
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
};

const changeSceneValue = (val: string, key: string) => {
  store.state.editSceneData[key] = val;
};

// 切换操作类型
const changeOperateType = (name: string) => {
  store.state.operateType = name;
};

const hideMask = () => {
  showShareMask.value = false;
};

const deleteAssets = () => {
  return new Promise(async (resolve) => {
    await headerRef.value.saveEditInfo(false);
    resolve(1);
  });
};

const showShare = async () => {
  await headerRef.value.saveEditInfo(false);
  showShareMask.value = true;
};

// 点击图片时候修改交互图片的位置
const sourcePoolMourseDown = (e: any, url: string, data: any) => {
  updateMouseStyle(MouseStyle.mouseDrag, true);
  dragingImg.value = url;
  handleIconMove(e);
  currentMaterialData.value = data;
};

// 拖动素材时候悬浮交互
const handleIconMove = (e: any) => {
  if (dragingImg.value) {
    Icondom.style.left = e.clientX - 25 + 'px';
    Icondom.style.top = e.clientY - 25 + 'px';
  }
};

// 鼠标拖动停止事件
const handleIconUp = (e: any) => {
  isDrag = true;
  if (dragingImg.value) {
    const canvasLeftWidth = (document.querySelector('.canvas-left') as any).getBoundingClientRect()
      .width;
    updateMouseStyle(MouseStyle.default, true);
    if ((currentMaterialData.value.storageSize || 1) + store.state.totalSize > 100) {
      store.state.isDragLoading = false;
      store.state.showTips = '素材添加失败，当前项目容量上限100MB';
      hideDragImg();
      return;
    }
    if (canvasLeftWidth < e.clientX) {
      // 创建公共区域
      data = {
        materialDto: { ...currentMaterialData.value },
        location: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
        flag: 'add',
        isStatic: 1,
      };
      let ossKey = data.materialDto.modelStorageMap
        ? data.materialDto.modelStorageMap.web?.ossKey
        : data.materialDto.ossKey;
      if (data.materialDto.materialFormat.includes('video') && data.materialDto.thumbnail) {
        ossKey = data.materialDto.thumbnail;
      }
      if (ossKey) {
        getOssAccessPath({ key: ossKey }).then((res1: any) => {
          canvasRef.value.addModel(
            res1.data,
            data.materialDto,
            (obj: any) => {
              obj.userData.boxInfos.scale = new Vector3(1, 1, 1);
              data.uuid = obj.uuid;
              data.metaInfo = '0,0,0,1,' + obj.userData.initScale;
              obj.scale.set(obj.userData.initScale, obj.userData.initScale, obj.userData.initScale);
              const editSceneData = { ...store.state.editSceneData };
              if (editSceneData.outerMaterialMetaDtoList) {
                editSceneData.outerMaterialMetaDtoList.push(data);
              } else {
                editSceneData.outerMaterialMetaDtoList = [data];
              }
              store.state.editSceneData = { ...editSceneData };
            },
            !!data.materialDto.thumbnail,
            sceneType
          );
        });
      }
    } else {
      store.state.isDragLoading = false;
    }
    hideDragImg();
  }
};

watch(
  () => store.state.isFinishModel,
  async (nv) => {
    if (isDrag) {
      console.log(nv, 'trigger');
      store.state.showSaveTips = false;
      await headerRef.value.saveEditInfo(false);
      store.state.activeMaterial = data.uuid;
      isDrag = false;
    }
  }
);

// 清空素材交互
const hideDragImg = () => {
  dragingImg.value = '';
  Icondom.style.left = '-50%';
  Icondom.style.top = '-50%';
};

const handleMouseMoveCanvas = (point2D: Vector2) => {
  if (dragingImg.value) {
    // 素材缩略图拖拽
    handleIconMove({ clientX: point2D.x, clientY: point2D.y });
    return;
  }
};

const createModel = (data: any, callback: any, sceneType?: any) => {
  data.outerMaterialMetaDtoList?.forEach((item: any, index: number) => {
    const materialData = { ...item };
    let ossKey = item.materialDto.modelStorageMap
      ? item.materialDto.modelStorageMap?.web?.ossKey
      : item.materialDto.ossKey;
    if (item.materialDto.materialFormat.includes('video') && item.materialDto.thumbnail) {
      ossKey = item.materialDto.thumbnail;
    }
    getOssAccessPath({ key: ossKey }).then((res1: any) => {
      canvasRef.value.addModel(
        res1.data,
        item.materialDto,
        (obj: any) => {
          materialData.uuid = obj.uuid;
          obj.position.set(
            materialData.location.x,
            materialData.location.y,
            materialData.location.z
          );
          obj.scale.set(
            materialData.scale.x * obj.userData.initScale,
            materialData.scale.y * obj.userData.initScale,
            materialData.scale.z * obj.userData.initScale
          );
          obj.userData.boxInfos.scale = new Vector3(
            materialData.scale.x,
            materialData.scale.y,
            materialData.scale.z
          );
          const metaInfo = item.metaInfo?.split(',');
          if (metaInfo) {
            const quaternion = new Quaternion(
              +metaInfo[0],
              +metaInfo[1],
              +metaInfo[2],
              +metaInfo[3]
            );
            obj.applyQuaternion(quaternion);
          }
          callback(obj, index);
        },
        !!item.materialDto.thumbnail,
        sceneType
      );
    });
  });
};

onBeforeMount(() => {
  store.dispatch('resetEditData');
});

onMounted(() => {
  Icondom = document.querySelector('.show-icon') as any;
  getInitData();
  store.state.isPlanStyle = false;
  scene = (window as any).scene2;
  cube = scene.getObjectByName('cube-init');

  getUserTypeByToken().then((res: any) => {
    userType.value = res.data;
  });
  if (window.history) {
    history.pushState(null, '', document.URL);
    window.addEventListener('popstate', function () {
      history.pushState(null, '', document.URL);
      exitEdit();
    });
  }
});

const getInitData = () => {
  const scene = (window as any).scene2;
  const pageQuery: any = router.currentRoute.value.query;
  const sceneId = +pageQuery.sceneid || 0;
  sceneType = +pageQuery.sceneType || 0;
  console.log(sceneType, 2222);
  const sceneTypeIdentifyMap: any = {
    5: 'bodyIdentifyPoints',
    6: 'faceIdentifyPoints',
    7: 'handIdentifyPoints',
  };

  if ([3, 5, 6, 7, 8].includes(+sceneType)) {
    let identifyPoints: any = [];
    getWxNotifySceneInfo({ sceneId }).then((res: any) => {
      editSceneData.value = { ...res.data, ...res.data.sceneMetaDto };
      store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData.value));
      if (sceneType == 3 || sceneType == 8) {
        getOssAccessPath({
          key: sceneType == 3 ? res.data.identifyPicKey : res.data.identificationKey,
        }).then((res1: any) => {
          canvasRef.value.addModelBase(res1.data, sceneType);
        });
      } else {
        let modelUrl = '/glb/hk_track_shenti.glb';
        if (sceneType == 5) {
          (scene.getObjectByName('ground-init') as any).position.y = -0.15;
        }
        if (sceneType == 6) {
          modelUrl = '/glb/hk_track_tou.glb';
          (scene.getObjectByName('ground-init') as any).position.y = -1.15;
        }
        if (sceneType == 7) {
          modelUrl = '/glb/hk_track_shou.glb';
          (scene.getObjectByName('ground-init') as any).position.y = -1.05;
        }
        showTips.value = true;
        canvasRef.value.addModelBase(modelUrl, sceneType);
        identifyPoints = [...(editSceneData.value[sceneTypeIdentifyMap[sceneType]] || [])];
        editSceneData.value.identifyPoints = {};
      }
      createModel(
        res.data,
        (obj: any, index: number) => {
          store.state.editSceneData.outerMaterialMetaDtoList[index].uuid = obj.uuid;
          identifyPoints.forEach((e: any) => {
            if (store.state.editSceneData.outerMaterialMetaDtoList[index].id == e.materialMetaId) {
              editSceneData.value.identifyPoints[obj.uuid] = {
                materialId: e.materialId,
                materialAffiliation: e.materialAffiliation,
                pointIndex: e.pointIndex,
                materialMetaId: e.materialMetaId,
              };
            }
          });
          if (editSceneData.value.identifyPoints) {
            store.state.editSceneData.identifyPoints = JSON.parse(
              JSON.stringify(editSceneData.value.identifyPoints)
            );
          }
        },
        sceneType
      );
    });
  } else {
    getSceneMetaPageForWeb({ pageNo: 1, pageSize: 999 }).then((res: any) => {
      editSceneData.value = res.data.records.filter((e: any) => e.id == sceneId)[0] || {};
      if (editSceneData.value.spaceDto?.roomStructurePath) {
        // 加载地图
        loadZipFileForJSZip(editSceneData.value.spaceDto.roomStructurePath, (glb: any) => {
          glb.scene.name = 'model-init';
          scene.add(glb.scene.clone());
          const boxInfo = new Box3().expandByObject(glb.scene);
          if (scene.getObjectByName('ground-init')) {
            (scene.getObjectByName('ground-init') as any).position.y = boxInfo.min.y - 0.1;
          }
          getWebScene({ id: sceneId }).then((res: any) => {
            editSceneData.value = { ...editSceneData.value, ...res.data };
            store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData.value));
            createModel(res.data, (obj: any, index: number) => {
              store.state.editSceneData.outerMaterialMetaDtoList[index].uuid = obj.uuid;
            });
          });
        });
      } else {
        getWebScene({ id: sceneId }).then((res: any) => {
          editSceneData.value = { ...editSceneData.value, ...res.data };
          store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData.value));
          createModel(res.data, (obj: any, index: number) => {
            store.state.editSceneData.outerMaterialMetaDtoList[index].uuid = obj.uuid;
          });
        });
      }
    });
  }
};
</script>
<style scoped lang="less">
.page-tools-box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .edit-tips {
    width: 358px;
    height: 40px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 4px 4px 4px 4px;
    position: absolute;
    top: 74px;
    right: 326px;
    z-index: 12;
    line-height: 40px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 24px;
      height: 24px;
      margin-right: 9px;
    }
  }
}

.show-icon {
  width: 50px;
  height: 50px;
  position: fixed;
  left: -50%;
  top: -50%;
  background-size: 100% 100%;
  z-index: 9999;
  border-radius: 5px;
}

.trackball-control {
  position: fixed;
  right: 32px;
  bottom: 32px;
  width: 56px;
  height: 56px;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
  border: 2px solid #2e76ff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10010;
}
</style>
