<template>
  <div>
    <search-form :formKeysData="keysData" :searchDataEvent="searchDataEvent"></search-form>
  </div>
  <div>
    <table-list :data="tableData" :data-total="pageTotal" :columnList="columnList" :changePage="changePage"
      :page-size="searchForm.pageSize" :page-no="searchForm.pageNo"></table-list>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted, reactive } from 'vue'
import TableList from '@/components/TableList.vue'
import SearchForm from '@/components/SearchForm.vue'
import { UserType } from '@/config'
import { querySystemLog } from '@/api'

const pageTotal = ref(0) // 总计数据条数
const tableData: any = ref([]) // 表格数据

const searchForm = reactive({ // 查询对象
  pageSize: 20,
  pageNo: 1,
  optType: ''
})

const keysData = reactive(
  [
    {
      key: 'optType',
      type: 'input',
      label: '操作类型'
    },
  ]
)

const columnList = [
  {
    prop: 'userName',
    label: '用户'
  },
  {
    prop: 'userType',
    label: '用户类型'
  },
  {
    prop: 'optPage',
    label: '操作页面'
  },
  {
    prop: 'optType',
    label: '操作类型'
  },
  {
    prop: 'optDetail',
    label: '操作详情'
  },
  {
    prop: 'loginIp',
    label: '登录IP'
  },
  {
    prop: 'optTimeStr',
    label: '操作时间'
  }
]
const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getDataList()
}

const searchDataEvent = (data: any) => {
  searchForm.optType = data.optType
  getDataList()
}

const getDataList = () => {
  querySystemLog({ ...searchForm }).then((res: any) => {
    pageTotal.value = res.data.total
    tableData.value = [...res.data.records]
  })
}
onMounted(() => {
  getDataList()
})
</script>