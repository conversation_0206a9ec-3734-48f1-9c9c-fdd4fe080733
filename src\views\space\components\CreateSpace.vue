<template>
  <div class="modal" v-if="!store.state.uploadStep || store.state.uploadStep === 1">
    <div class="modal-content" v-if="!store.state.uploadStep">
      <div class="modal-content-title">
        <div>上传空间数据</div>
      </div>
      <div class="modal-form">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="109px"
          class="demo-ruleForm">
          <el-form-item label="空间数据名称" prop="spaceName">
            <el-input
              v-model="ruleForm.spaceName"
              placeholder="请输入空间数据名称"
              maxlength="20"
              clearable />
          </el-form-item>
          <div :style="{ opacity: !spaceNameError ? 1 : 0 }" class="upload-tips-text">
            仅支持大小写英文字母和数字组合，不含特殊字符
          </div>
          <el-form-item label="数据采集方式" prop="collectionMethod">
            <el-select
              v-model="ruleForm.collectionMethod"
              placeholder="请选择数据采集方式"
              class="select-default"
              popper-class="select-option"
              :suffix-icon="DropDown"
              style="width: 432px; height: 36px">
              <el-option
                v-for="(item, index) in acquisition"
                :key="index"
                :label="item.name"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <div>
            <el-form-item
              label=" "
              prop="ossKey"
              style="margin-top: -32px"
              class="upload-box-style">
              <div class="upload-model-box">
                <div
                  class="upload-material-style styleFor2"
                  :class="errorFile[item] ? 'error-style' : ''"
                  v-for="(item, index) in 2"
                  :key="index">
                  <div class="upload-tips">
                    {{ loadMap[item]['tag' + acquisition[ruleForm.collectionMethod].key] }}
                  </div>
                  <div v-if="!ruleForm['file' + item]?.name">
                    .{{ loadMap[item]['rule' + acquisition[ruleForm.collectionMethod].key] }}格式
                  </div>
                  <div v-else-if="ruleForm['file' + item]?.name">
                    {{ ruleForm['file' + item]?.name }}
                  </div>

                  <div class="upload-input">
                    <div @click="changeFile('fileInput' + item)">
                      {{ ruleForm['file' + item]?.name ? '重新选择 ' : '选择' }}
                    </div>
                    <input
                      :id="'fileInput' + item"
                      type="file"
                      @input="handleFileChange($event, item)"
                      class="input-file" />
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
          <el-form-item class="form-submit">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef)">
                <span v-if="!showLoading">确定</span>
                <img v-if="showLoading" src="@/assets/images/icon/loading.png" />
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- 空间上传进度展示 -->
    <SpaceUploadProgress
      v-if="store.state.uploadStep > 0"
      :uploadStep="store.state.uploadStep"
      @close="handleProgressClose" />
  </div>

  <!-- 确认上传弹框 -->
  <div v-if="showConfirmDialog" class="confirm-dialog-overlay">
    <div class="confirm-dialog">
      <div class="confirm-dialog-header">
        <span style="font-weight: bold; font-size: 18px; color: #1e1e1e">确认上传</span>
        <img
          src="@/assets/images/icon/close.png"
          alt=""
          class="close-icon"
          @click="closeConfirmDialog" />
      </div>

      <div class="confirm-dialog-content">
        <p class="warning-text">您上传的文件格式不匹配，请检查后再上传。</p>

        <div class="file-info">
          <div class="file-item">{{ ruleForm.file1?.name || '未选择文件' }}</div>
          <div class="file-item">{{ ruleForm.file2?.name || '未选择文件' }}</div>
        </div>

        <div class="notice-list">
          <div class="notice-item">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" class="info-icon">
              <circle cx="8" cy="8" r="7" stroke="#999" stroke-width="1" />
              <path d="M8 7v4M8 5h.01" stroke="#999" stroke-width="1" stroke-linecap="round" />
            </svg>
            <span>空间文件成功上传后不可撤回</span>
          </div>
          <div class="notice-item">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" class="info-icon">
              <circle cx="8" cy="8" r="7" stroke="#999" stroke-width="1" />
              <path d="M8 7v4M8 5h.01" stroke="#999" stroke-width="1" stroke-linecap="round" />
            </svg>
            <span>成功上传后将会消耗当前账号空间余量</span>
          </div>
          <div class="notice-item">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" class="info-icon">
              <circle cx="8" cy="8" r="7" stroke="#999" stroke-width="1" />
              <path d="M8 7v4M8 5h.01" stroke="#999" stroke-width="1" stroke-linecap="round" />
            </svg>
            <span>请务必确认所传文件大小、格式均正确，并符合实际项目需求</span>
          </div>
        </div>
      </div>

      <div class="confirm-dialog-footer">
        <button class="cancel-btn" @click="closeConfirmDialog">取消</button>
        <button class="confirm-btn" @click="confirmUpload">确认并上传</button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import DropDown from '@/components/DropDown.vue';
import SpaceUploadProgress from './SpaceUploadProgress.vue';
import type { FormInstance, FormRules } from 'element-plus';
import { genPolycam, genB2g, gene57, genQiyu } from '@/api';
import { createUuid, desensitizte } from '@/utils';
import { Vector3, Quaternion } from 'three';
import { useStore } from 'vuex';

const store = useStore();

const loadMap: any = ref({
  1: {
    tag1: '.glb数据文件',
    tag2: '.glb数据文件',
    tag3: '.glb数据文件',
    tag4: '.glb数据文件',
    rule1: 'zip',
    rule2: 'zip',
    rule3: 'zip',
    rule4: 'zip',
  },
  2: {
    tag1: 'Raw data数据文件',
    tag2: '.b2g点云数据文件',
    tag3: '.e57点云数据文件',
    tag4: 'las数据文件',
    rule1: 'zip',
    rule2: 'b2g',
    rule3: 'zip',
    rule4: 'zip',
  },
});

const props = defineProps({
  handleHide: {
    default: null,
    type: Function,
  },
});
const acquisition: any = ref({
  polycam: {
    name: 'Polycam',
    value: 'polycam',
    key: 1,
    tipsText: [
      '.zip格式文件（含有.glb格式文件，生成mesh模型）',
      '.zip格式文件（含有Raw data数据文件，生成点云数据）',
    ],
    eventName: genPolycam,
    file2Name: 'pics',
  },
  blk: {
    name: '徕卡相机',
    value: 'blk',
    key: 2,
    tipsText: ['.zip格式文件（含有.glb格式文件，生成mesh模型）', '.b2g格式文件（点云数据）'],
    eventName: genB2g,
    file2Name: 'b2g',
  },
  realslam: {
    name: 'RealSlam 10/20',
    value: 'realslam',
    key: 3,
    tipsText: ['.zip格式文件（含有.glb格式文件，生成mesh模型）', '.e57格式文件（点云数据）'],
    eventName: gene57,
    file2Name: 'e57',
  },
  qiyu: {
    name: '其域K1/L2Pro',
    value: 'qiyu',
    key: 4,
    tipsText: ['.glb格式文件（生成mesh模型）', '.zip格式文件（含有点云数据）'],
    eventName: genQiyu,
    file2Name: 'data', // 第二个文件字段名，对应点云数据zip包
  },
});
const showLoading = ref(false);
const spaceNameError = ref(false);
const errorFile: any = ref({ 1: false, 2: false });

// 确认弹框相关状态
const showConfirmDialog = ref(false);
let pendingFormData: FormData | null = null;
let pendingUploadParams: any = null;

interface RuleForm {
  spaceName: string;
  collectionMethod: string;
  file1: any;
  file2: any;
}

const ruleFormRef = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  spaceName: '',
  collectionMethod: 'polycam',
  file1: null,
  file2: null,
});

const validatePass = (rule: any, value: any, callback: any) => {
  const reg = /^[a-zA-Z0-9]+$/;
  if (!value) {
    callback(new Error('请输入空间名称'));
    spaceNameError.value = true;
    return;
  }
  if (!reg.test(value)) {
    callback(new Error('仅支持大小写英文字母和数字组合，不含特殊字符'));
    spaceNameError.value = true;
    return;
  }
  spaceNameError.value = false;
  callback();
};

const rules = reactive<FormRules<RuleForm>>({
  spaceName: [{ required: true, validator: validatePass, trigger: 'blur' }],
  collectionMethod: [{ required: true, message: '请选择数据采集方式', trigger: 'change' }],
  file1: [{ required: true, message: '上传文件不能为空', trigger: 'blur' }],
  file2: [{ required: true, message: '上传文件不能为空', trigger: 'blur' }],
});

const changeFile = (id: string) => {
  document.getElementById(id)?.click();
};

const clearCache = () => {
  (document.getElementById('fileInput1') as any).value = null;
  (document.getElementById('fileInput2') as any).value = null;
};

const handleFileChange = (event: any, fileNumber: any) => {
  const reg = /[\u4e00-\u9fa5]+/;
  const file = event.target.files[0];
  const formatStr =
    loadMap.value[fileNumber]['rule' + acquisition.value[ruleForm.collectionMethod].key];
  if (file?.name.split('.')[1] != formatStr) {
    store.state.showTips = '上传资源格式不正确，请检查资源格式';
    clearCache();
    return;
  }
  if (reg.test(file?.name.split('.')[0])) {
    store.state.showTips = '上传资源名不能包含中文';
    clearCache();
    return;
  }
  if (fileNumber === 1) {
    ruleForm.file1 = file;
  } else if (fileNumber === 2) {
    ruleForm.file2 = file;
  }
};

const changeState = () => {
  props.handleHide();
};

// 处理进度弹框关闭
const handleProgressClose = () => {
  // 重置上传状态
  store.state.uploadStep = 0;
  // 重置任务状态
  store.state.spaceUploadTask = {
    isCompleted: false,
    isSuccess: false,
    errorMessage: null,
    taskId: null,
    taskName: null,
    currentUploadMethod: null,
    taskProccessMap: null,
  };
  // 关闭整个创建空间弹框
  props.handleHide();
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl || showLoading.value) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (!ruleForm.file1 || !ruleForm.file2) {
        errorFile.value['1'] = !ruleForm.file1;
        errorFile.value['2'] = !ruleForm.file2;
        return;
      }
      errorFile.value['1'] = false;
      errorFile.value['2'] = false;

      const hasSensitiveWords = await desensitizte(
        ruleForm.spaceName,
        '空间名称不可包含敏感词汇！'
      );
      if (hasSensitiveWords) {
        return;
      }

      // 准备上传参数
      const uuid = createUuid();
      const { eventName, file2Name } = acquisition.value[ruleForm.collectionMethod];
      const formData = new FormData();
      formData.append('spaceModel', ruleForm.file1);
      formData.append(file2Name, ruleForm.file2);

      const uploadParams = {
        formData,
        spaceName: ruleForm.spaceName,
        uuid,
      };

      // 显示确认弹框而不是直接上传
      showConfirmUploadDialog(formData, uploadParams);
    }
  });
};

// 显示确认弹框
const showConfirmUploadDialog = (formData: FormData, uploadParams: any) => {
  pendingFormData = formData;
  pendingUploadParams = uploadParams;
  showConfirmDialog.value = true;
};

// 关闭确认弹框
const closeConfirmDialog = () => {
  showConfirmDialog.value = false;
  pendingFormData = null;
  pendingUploadParams = null;
  showLoading.value = false;
};

// 确认上传
const confirmUpload = async () => {
  if (!pendingFormData || !pendingUploadParams) return;

  showConfirmDialog.value = false;
  showLoading.value = true;
  store.state.uploadStep = 1; // 开始上传

  // 记录当前上传方式到store中，供WebSocket消息处理使用
  store.state.spaceUploadTask.currentUploadMethod = ruleForm.collectionMethod;

  try {
    const { eventName } = acquisition.value[ruleForm.collectionMethod];
    const res = await eventName(pendingUploadParams);
    showLoading.value = false;

    if (res.code === '5001' || res.code === '5002') {
      store.state.showTips = `您已超出数量限制`;
      store.state.uploadStep = 0;
      return;
    }
    if (res.code === '3001') {
      store.state.showTips = `您上传的空间文件格式错误`;
      store.state.uploadStep = 0;
      return;
    }
    // 上传成功后，WebSocket 会自动更新后续步骤
  } catch (error) {
    showLoading.value = false;
    store.state.uploadStep = 0;
    store.state.showTips = '上传失败，请重试';
  }

  // 清理临时数据
  pendingFormData = null;
  pendingUploadParams = null;
};

onMounted(() => {
  const quaternion = new Quaternion();
  quaternion.setFromAxisAngle(new Vector3(0, 1, 0), Math.PI / 2);
});
watch(
  () => ruleForm.collectionMethod,
  (newState) => {
    ruleForm.file1 = null;
    ruleForm.file2 = null;
    clearCache();
  }
);

// 在组件卸载时重置上传状态
onUnmounted(() => {
  store.state.uploadStep = 0;
});
</script>
<style scoped lang="less">
.input-file {
  display: none;
}
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;
  -webkit-user-select: none;
  /* Chrome, Safari, Opera */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;

  .modal-content {
    width: 453px;
    // height: 736px;
    max-height: 90%;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #edeff2;
    overflow: hidden;
    color: #1e1e1e;

    .modal-content-title {
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 18px;
      color: #1e1e1e;
      padding: 16px 0 8px 24px;
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      padding: 16px 24px;
      box-sizing: border-box;
      overflow: hidden;
      overflow-y: auto;

      .upload-input {
        position: relative;
        width: 60px;
        height: 18px;
        font-weight: 400;
        font-size: 12px;
        color: #2e76ff;
        line-height: 14px;
        text-align: center;
        background-color: rgba(46, 118, 255, 0.2);
        border-radius: 4px;
        margin: 8px 0 0;
        cursor: pointer;

        &:hover {
          background-color: rgba(46, 118, 255, 1);
          color: #fff;
        }

        & > div {
          cursor: pointer;
        }

        input {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          cursor: pointer;
          z-index: 1;
        }
      }

      .upload-tips {
        font-size: 14px;
        text-align: left;
        padding-left: 52px;
        margin-top: -5px;
        font-weight: bold;
        color: rgba(61, 86, 108, 0.3);
      }

      .upload-tips-text {
        width: 100%;
        font-size: 12px;
        color: #797979;
        width: 100%;
        text-align: left;
        margin-bottom: 18px;
        margin-top: -19px;
        padding-left: 110px;
      }

      .tips-text {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        line-height: 14px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        margin-left: 12px;

        & > div:last-child {
          margin-top: 8px;
        }
      }

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .loaded-img {
        width: 100%;
        height: 100%;
        display: inline-block;
      }

      .preview {
        position: relative;
        width: 146px;
        height: 146px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #dadada;
      }

      .preview-audio {
        background-color: #d0d0d0;
        background-image: url(~@/assets/images/background/audio.png);
        background-repeat: no-repeat;
        background-position: 50% 50%;
        border-radius: 8px;
      }

      .platform-style {
        margin-right: 24px;

        & > span {
          vertical-align: middle;
        }

        .platform-style-select {
          width: 16px;
          height: 16px;
          border-radius: 2px;
          display: inline-block;
          margin-right: 4px;
          cursor: pointer;
          background-image: url(~@/assets/images/checkbox-icon.png);
          background-size: 100% 100%;

          &.active {
            background-image: url(~@/assets/images/checkbox-iconA.png);
            background-size: 100% 100%;
          }
        }

        .active {
          color: #2e76ff;
        }
      }

      .upload-box-style {
        position: relative;
        display: flex;
        flex-direction: column;

        .upload-material-tips {
          position: absolute;
          left: 78px;
          top: -31px;
          font-weight: 400;
          font-size: 12px;
          color: #797979;
        }
      }

      .upload-material-style {
        position: relative;
        width: 100%;
        height: 124px;
        border-radius: 10px;
        border: 1px dashed #dadada;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        box-sizing: border-box;
        padding-top: 2px;

        &:hover {
          border-color: #2e76ff;

          button {
            background-color: #2e76ff;
            color: #fff;
          }
        }

        & > div {
          line-height: 1.5;
        }

        .upload-btn {
          margin: 8px 0;
        }

        .upload-tips {
          padding-left: 0;
          color: #1e1e1e;
        }

        .uploading-style,
        .uploaded-style {
          position: relative;
          padding-left: 18px;
        }

        .uploading-style::before {
          content: '';
          width: 14px;
          height: 14px;
          background-image: url(~@/assets/images/icon/loading-icon.png);
          background-size: 100% 100%;
          position: absolute;
          left: 0;
          top: 2px;
          animation: rotate 1.5s linear infinite;
        }

        .uploaded-style::before {
          content: '';
          width: 14px;
          height: 14px;
          background-image: url(~@/assets/images/icon/complete-icon.png);
          background-size: 100% 100%;
          position: absolute;
          left: 0;
          top: 2px;
        }
      }

      .thumbnail-show {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        & > div:first-child {
          width: 92px;
          height: 92px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #dadada;

          .thumbnail-icon-upload {
            width: 92px;
            height: 92px;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }

        .thumbnail-text {
          font-weight: 400;
          font-size: 12px;
          color: #797979;
          line-height: 14px;
          margin-left: 12px;
          text-align: left;

          & > div:last-child {
            margin-top: 8px;
          }
        }
      }

      .upload-model-box {
        width: 100%;
        height: 124px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        & > .upload-material-style.styleFor3 {
          width: 124px;
        }

        & > .upload-material-style.styleFor2 {
          width: 196px;
        }

        & > .upload-material-style.styleFor1 {
          width: 100%;
        }

        & > .upload-material-style.error-style {
          position: relative;
          border: 1px solid #dd5a5a !important;
          overflow: visible;

          &::after {
            content: '\8bf7\4e0a\4f20\6587\4ef6';
            position: absolute;
            left: 0;
            top: calc(100% - 5px);
            color: #f56c6c;
          }
        }
      }
    }

    ::v-deep(.modal-form .el-form > div:not(.upload-tips-text)) {
      margin: 0 0 20px !important;

      &.form-submit {
        margin: 24px 0 0 !important;
      }
    }

    ::v-deep(.el-form-item__label) {
      color: #797979;
    }

    ::v-deep(.el-input__wrapper .el-input__inner) {
      color: #1e1e1e;
    }
  }

  .pic-model {
    // position: relative;
    position: fixed;
    left: 0;
    top: 0;
    font-size: 0;
    z-index: -1;
    opacity: 0;

    img {
      display: block;
    }
  }
}

.thumbnail-icon {
  width: 49px;
  height: 49px;
  background-image: url(~@/assets/images/icon/source-bg.png);

  img {
    width: 100%;
    height: 100%;
  }
}

.uploadMask {
  position: absolute;
  left: 0;
  top: 0;
  width: 105px;
  height: 36px;
  background-color: transparent;
  z-index: 10;
  cursor: pointer;
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;

  img {
    width: 30px;
    height: 30px;
    animation: rotate 1.5s linear infinite;
  }
}

.el-size {
  width: 102px;
  height: 36px;
  box-sizing: border-box;
  margin-right: 95px;

  .el-button {
    background: rgba(46, 118, 255, 0.1) !important;
  }
}

.upload-demo {
  position: relative;
  display: inline-block;

  .progress {
    position: absolute;
    left: 0px;
    top: -18px;
    width: 105px;
  }

  .complete-icon {
    width: 16px;
    position: absolute;
    left: 115px;
    height: 16px;
    line-height: 16px;
    top: -2px;
  }
}

.el-upload__text > span {
  margin-left: 3px;
}

/* 旧的步骤式UI样式已移除，使用新的SpaceUploadProgress组件 */

/* 确认弹框样式 */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirm-dialog {
  width: 520px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.confirm-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.close-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  transition: opacity 0.2s;
}

.close-icon:hover {
  opacity: 0.7;
}

.confirm-dialog-content {
  padding: 10px 24px;
}

.warning-text {
  font-size: 14px;
  color: #797979;
  margin: 0 0 20px 0;
  line-height: 1.5;
  text-align: start;
}

.file-info {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.file-item {
  width: 199px;
  height: 45px;
  background: #f7f7f7;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666666;
  word-break: break-all;
  padding: 0 8px;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.info-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.confirm-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
}

.cancel-btn {
  padding: 8px 20px;
  border: 1px solid #d9d9d9;
  background: #ffffff;
  color: #666666;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.cancel-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.confirm-btn {
  padding: 8px 20px;
  border: none;
  background: #1890ff;
  color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.confirm-btn:hover {
  background: #40a9ff;
}
</style>
