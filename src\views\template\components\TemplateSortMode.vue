<template>
  <div class="template-sort-mode">
    <!-- 网页端排序模式 -->
    <template v-if="activeTab === 'web'">
      <!-- 推荐模板部分 -->
      <section class="template-section">
        <div class="template-section__header">
          <h2 class="template-section__title">推荐模板</h2>
          <nav class="template-section__nav template-section__nav--left">
            <button
              class="nav-tab nav-tab--small"
              :class="{ 'nav-tab--active': menu.isActive }"
              v-for="(menu, index) in recommendMenuList"
              :key="`recommend-${index}`"
              @click="handleActiveRecommendTab(menu)"
              type="button">
              {{ menu.name }}
            </button>
          </nav>
        </div>

        <draggable
          v-model="recommendData"
          class="template-grid template-grid--recommend"
          :class="{ 'template-grid--dragging': isRecommendDragging }"
          item-key="id"
          :animation="200"
          :disabled="false"
          ghost-class="template-card--ghost"
          chosen-class="template-card--chosen"
          drag-class="template-card--drag"
          @start="onRecommendDragStart"
          @end="onRecommendDragEnd">
          <template #item="{ element: item, index: i }">
            <div class="template-card-wrapper">
              <TemplateCard
                :key="`recommend-${item.id}`"
                :template-data="item"
                :card-index="i"
                :dom-mask-indexs="domMaskIndexs"
                :show-edit-button="showEditButton"
                :is-sort-mode="true"
                :disable-video-preview="isRecommendDragging"
                :is-recommend-area="true"
                @mouseenter="handleMouseEnter"
                @mouseleave="handleMouseLeave"
                @edit="handleEdit"
                @experience="handleExperience"
                @create-similar="handleCreateSimilar"
                @remove-recommend="handleRemoveRecommend" />
            </div>
          </template>
        </draggable>
      </section>

      <!-- 所有模板部分 -->
      <section class="template-section">
        <div class="template-section__header">
          <h2 class="template-section__title">所有模板</h2>
          <nav class="template-section__nav template-section__nav--left">
            <button
              class="nav-tab nav-tab--small"
              :class="{ 'nav-tab--active': menu.isActive }"
              v-for="(menu, index) in allMenuList"
              :key="`all-${index}`"
              @click="handleActiveAllTab(menu)"
              type="button">
              {{ menu.name }}
            </button>
          </nav>
        </div>

        <div class="template-grid">
          <div
            class="template-card-wrapper"
            v-for="(item, i) in allTemplatesData"
            :key="`all-${item.id}`">
            <TemplateCard
              :template-data="item"
              :card-index="i"
              :dom-mask-indexs="domMaskIndexs"
              :show-edit-button="showEditButton"
              :is-sort-mode="true"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
              @edit="handleEdit"
              @experience="handleExperience"
              @create-similar="handleCreateSimilar"
              @add-to-recommend="handleAddToRecommend" />
          </div>
        </div>
      </section>
    </template>

    <!-- 小程序端排序模式 -->
    <template v-else>
      <!-- Banner模板部分 -->
      <BannerTemplateSection
        v-model="bannerData"
        @edit="handleEditBanner"
        @remove="handleRemoveBanner"
        @drag-start="onBannerDragStart"
        @drag-end="onBannerDragEnd" />

      <!-- 列表模板部分 -->
      <MiniprogramListSection
        v-model="miniprogramListData"
        :dom-mask-indexs="domMaskIndexs"
        :show-edit-button="showEditButton"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
        @edit="handleEdit"
        @experience="handleExperience"
        @create-similar="handleCreateSimilar"
        @remove="handleRemoveMiniprogramList"
        @drag-start="onMiniprogramListDragStart"
        @drag-end="onMiniprogramListDragEnd"
        @refresh="handleRefreshMiniprogramList" />
    </template>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import draggable from 'vuedraggable';
import TemplateCard from '@/components/TemplateCard.vue';
import BannerTemplateSection from './BannerTemplateSection.vue';
import MiniprogramListSection from './MiniprogramListSection.vue';

// Props
const props = defineProps({
  activeTab: {
    type: String,
    default: 'web',
  },
  recommendTemplates: {
    type: Array,
    default: () => [],
  },
  allTemplates: {
    type: Array,
    default: () => [],
  },
  bannerTemplates: {
    type: Array,
    default: () => [],
  },
  miniprogramList: {
    type: Array,
    default: () => [],
  },
  domMaskIndexs: {
    type: Array,
    default: () => [],
  },
  showEditButton: {
    type: Boolean,
    default: true,
  },
  recommendMenuList: {
    type: Array,
    default: () => [],
  },
  allMenuList: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits([
  'update:recommendTemplates',
  'update:allTemplates',
  'update:bannerTemplates',
  'update:miniprogramList',
  'mouseenter',
  'mouseleave',
  'edit',
  'experience',
  'create-similar',
  'add-to-recommend',
  'remove-recommend',
  'edit-banner',
  'remove-banner',
  'remove-miniprogram-list',
  'recommend-drag-end',
  'banner-drag-end',
  'miniprogram-list-drag-end',
  'active-recommend-tab',
  'active-all-tab',
]);

// Computed
const recommendData = computed({
  get: () => props.recommendTemplates,
  set: (value) => emit('update:recommendTemplates', value),
});

const allTemplatesData = computed({
  get: () => props.allTemplates,
  set: (value) => emit('update:allTemplates', value),
});

const bannerData = computed({
  get: () => props.bannerTemplates,
  set: (value) => emit('update:bannerTemplates', value),
});

const miniprogramListData = computed({
  get: () => props.miniprogramList,
  set: (value) => emit('update:miniprogramList', value),
});

// State
const isRecommendDragging = ref(false);

// Methods - 推荐模板拖拽
const onRecommendDragStart = (evt) => {
  console.log('🔄 开始拖拽推荐模板:', evt);
  isRecommendDragging.value = true;

  recommendData.value.forEach((item) => {
    item.isHover = false;
  });
};

const onRecommendDragEnd = async (evt) => {
  console.log('✅ 推荐模板拖拽结束:', evt);
  isRecommendDragging.value = false;

  if (evt.oldIndex !== evt.newIndex) {
    const orderParams = recommendData.value.map((item, index) => ({
      sceneId: item.sceneId,
      sort: index + 1,
    }));

    emit('recommend-drag-end', { evt, orderParams });
  }
};

// Methods - 事件处理
const handleMouseEnter = (templateData, cardIndex) => {
  emit('mouseenter', templateData, cardIndex);
};

const handleMouseLeave = (templateData) => {
  emit('mouseleave', templateData);
};

const handleEdit = (templateData) => {
  emit('edit', templateData);
};

const handleExperience = (templateData) => {
  emit('experience', templateData);
};

const handleCreateSimilar = (templateData) => {
  emit('create-similar', templateData);
};

const handleAddToRecommend = (templateData) => {
  emit('add-to-recommend', templateData);
};

const handleRemoveRecommend = (templateData) => {
  emit('remove-recommend', templateData);
};

const handleEditBanner = (bannerData) => {
  emit('edit-banner', bannerData);
};

const handleRemoveBanner = (bannerData) => {
  emit('remove-banner', bannerData);
};

const handleRemoveMiniprogramList = (templateData) => {
  emit('remove-miniprogram-list', templateData);
};

const onBannerDragStart = (evt) => {
  // Banner拖拽开始处理已在BannerTemplateSection中处理
};

const onBannerDragEnd = (evt) => {
  emit('banner-drag-end', evt);
};

const onMiniprogramListDragStart = (evt) => {
  // 小程序列表拖拽开始处理已在MiniprogramListSection中处理
};

const onMiniprogramListDragEnd = ({ evt, orderParams }) => {
  emit('miniprogram-list-drag-end', { evt, orderParams });
};

// 刷新小程序列表
const handleRefreshMiniprogramList = () => {
  console.log('🔄 刷新小程序列表模板');
  emit('refresh-miniprogram-list');
};

// 导航按钮事件处理
const handleActiveRecommendTab = (menu) => {
  emit('active-recommend-tab', menu);
};

const handleActiveAllTab = (menu) => {
  emit('active-all-tab', menu);
};
</script>

<style lang="less" scoped>
.template-sort-mode {
  width: 100%;
}

// 模板网格布局
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  padding: 4px;
  overflow: visible;

  // 拖拽状态下的网格样式
  &--dragging {
    :deep(.template-card) {
      transition: transform 0.2s ease;
    }
  }
}

// 模板区域样式
.template-section {
  margin-bottom: 40px;

  &__header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 20px;
    gap: 20px;
  }

  &__title {
    font-size: 20px;
    font-weight: 600;
    color: #1e1e1e;
    margin: 0;
    flex-shrink: 0;
    white-space: nowrap;
  }

  &__nav {
    display: flex;
    gap: 4px;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
  }
}

// 导航标签样式
.nav-tab {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 400;
  color: #737373;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(46, 118, 255, 0.1);
    color: #2e76ff;
  }

  &--active {
    background: rgba(46, 118, 255, 0.1);
    color: #2e76ff;
    font-weight: 500;
  }

  // 小尺寸导航标签（用于排序模式的分区导航）
  &--small {
    padding: 4px 8px;
    font-size: 11px;
  }
}

// 模板卡片包装器
.template-card-wrapper {
  width: 100%;
}

// 拖拽状态下禁用视频预览
.template-grid--dragging {
  :deep(.back_video) {
    display: none !important;
  }
}
</style>
