# 🌌 3D 编辑器天空盒功能使用指南

## 📋 功能概述

为 3D 模式编辑器添加了完整的天空盒功能，支持多种天空盒类型和实时控制。

## 🎯 主要特性

### 1. **多种天空盒类型**

- **渐变天空** - 使用双色渐变创建天空效果
- **纹理天空** - 使用图片纹理创建真实天空
- **模型天空** - 使用 3D 模型创建环境背景

### 2. **实时控制面板**

- 位置：右上角浮动面板
- 可展开/收起
- 实时预览效果

### 3. **快速预设**

- 蓝天白云 - 清新的蓝色天空配白云纹理
- 日落黄昏 - 温暖的橙金色渐变
- 星空夜景 - 深蓝夜空配星星点缀
- 清晨薄雾 - 柔和的晨光色调

## 🔧 使用方法

### 界面操作

1. **打开 3D 编辑器**

   - 确保处于 3D 模式（非 2D 平面模式）
   - 天空盒控制面板会自动显示在右上角

2. **基本控制**

   ```
   ✅ 启用天空盒 - 开关天空盒显示
   🎨 天空盒类型 - 选择渐变/纹理/模型
   🌈 颜色控制 - 调整渐变颜色（仅渐变模式）
   🖼️ 纹理路径 - 设置纹理文件路径
   🎮 模型路径 - 设置3D模型文件路径
   ```

3. **快速预设**
   - 点击预设按钮快速应用常用配置
   - 蓝天：清新的蓝色渐变
   - 日落：温暖的橙红色渐变
   - 夜空：深蓝色夜空渐变
   - 环境模型：使用 GLB 模型

### 编程接口

如果需要通过代码控制天空盒：

```javascript
// 获取3D画布引用
const canvas3DRef = sceneEdit3DRef.value?.canvas3dRef;

// 设置天空盒类型
canvas3DRef?.setSkyboxType('gradient'); // 'gradient' | 'texture' | 'model'

// 设置渐变颜色
canvas3DRef?.setSkyboxColors('#87CEEB', '#E0F6FF');

// 设置纹理
canvas3DRef?.setSkyboxTexture('skybox/custom-sky.jpg');

// 开关天空盒
canvas3DRef?.toggleSkybox(true); // true | false

// 重新创建天空盒
canvas3DRef?.createSkybox();
```

## 📁 文件结构

```
src/
├── components/scene_web/
│   ├── CanvasThree.vue          # 3D画布组件（已增强）
│   └── SkyboxControl.vue        # 天空盒控制面板（新增）
├── views/space/edit-v2/
│   └── index.vue                # 编辑器主页面（已集成）
└── docs/
    └── skybox-usage.md          # 使用文档
```

## 🎨 天空盒资源

### 推荐的纹理格式

- **格式**: JPG, PNG
- **尺寸**: 1024x512 或 2048x1024
- **类型**: 全景图或天空纹理

### 推荐的模型格式

- **格式**: GLB
- **要求**: 内表面有纹理
- **尺寸**: 适中，避免过大文件

### 资源路径示例

```
public/
├── skybox/
│   ├── sky.jpg              # 默认天空纹理
│   ├── sunset.jpg           # 日落纹理
│   └── night.jpg            # 夜空纹理
└── glb/
    └── thirdglb.glb         # 环境模型
```

## 🔍 技术实现

### 核心技术

- **Three.js** - 3D 渲染引擎
- **Vue 3** - 响应式 UI 框架
- **WebGL** - 硬件加速渲染

### 渲染原理

1. **立方体天空盒**: 使用 BoxGeometry 创建立方体，为 6 个面分别生成纹理
   - 顶面：径向渐变天空效果，夜空模式添加星星
   - 底面：较暗的地面色调，添加纹理细节
   - 侧面：线性渐变配云朵效果，增强立体感
2. **纹理天空盒**: 加载图片纹理，应用到立方体的所有面
3. **模型天空盒**: 加载 GLB 模型，设置材质为背面渲染

### 性能优化

- 天空盒对象复用
- 纹理缓存机制
- 按需加载资源

## 🐛 故障排除

### 常见问题

1. **天空盒不显示**

   - 检查是否在 3D 模式
   - 确认天空盒已启用
   - 检查控制台错误信息

2. **纹理加载失败**

   - 确认文件路径正确
   - 检查文件格式支持
   - 查看网络请求状态

3. **模型加载失败**
   - 确认 GLB 文件有效
   - 检查文件大小合理
   - 验证模型结构正确

### 调试方法

```javascript
// 在浏览器控制台中检查天空盒状态
console.log('天空盒对象:', scene.getObjectByName('skybox-init'));

// 检查配置
console.log('天空盒配置:', canvas3DRef?.skyboxConfig);
```

## 🚀 扩展功能

### 未来可能的增强

- 立方体贴图天空盒
- 动态天空效果
- 天气系统集成
- HDR 环境贴图支持
- 天空盒动画效果

### 自定义开发

可以基于现有接口扩展更多天空盒类型和效果。

---

**注意**: 天空盒功能仅在 3D 模式下可用，2D 模式会自动隐藏控制面板。
