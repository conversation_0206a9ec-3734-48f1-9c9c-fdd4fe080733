<template>
  <div class="new-mask">
    <div>
      <div class="header">升级企业版，享受更多功能和权益</div>
      <div>当前账号只能创建10个项目</div>
      <div class="submit-btns">
        <div class="btn-default el-size3">
          <el-button @click="closeEvent">暂不升级</el-button>
        </div>
        <div class="btn-primary el-size3">
          <el-button @click="handleMore" color="#2e76ff">立即升级</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>

const props = defineProps({
  hideAddMask: {
    default: null,
    type: Function
  }
})


const closeEvent = () => {
  props.hideAddMask()
}

const handleMore = () => {
  props.hideAddMask(true)
}

</script>
<style scoped lang="less">
.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  &>div {
    position: relative;
    width: 453px;
    height: 166px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 24px;
    box-sizing: border-box;
    font-weight: 400;
    font-size: 14px;
    color: #797979;
    text-align: left;

    .header {
      font-weight: bold;
      font-size: 18px;
      color: #1E1E1E;
      line-height: 27px;
      margin-bottom: 12px;
    }
    
    .submit-btns {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      position: absolute;
      right: 24px;
      bottom: 24px;
    }
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;
}
</style>