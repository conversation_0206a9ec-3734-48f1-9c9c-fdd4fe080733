<template>
  <div class="new-mask">
    <div>
      <div class="header">敬请期待</div>
      <p>当前功能暂未开放，敬请期待</p>
      <div class="btn">
        <el-button class="_btn" style="width: 112px;" size="large" type="primary" @click="closeEvent">知道了</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>

const props = defineProps({
  hideAddMask: {
    default: null,
    type: Function
  }
})


const closeEvent = () => {
  props.hideAddMask()
}

</script>
<style scoped lang="less">
.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  &>div {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 474px;
    height: 262px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 60px 40px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 600;
    font-size: 18px;
    color: #3D566C;

    .header {
      font-size: 22px;
      margin-bottom: 28px;
      transform: translateY(-10px);
    }

    p {
      margin: 0;
      padding: 0;
      font-size: 14px;
      font-weight: 500;
      color: #3D566C;
    }

    .closed {
      width: 17px;
      height: 17px;
      position: absolute;
      right: 31px;
      top: 31px;
      cursor: pointer;
    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 48px;

      ._btn {
        width: 120px;
        font-weight: 700;

        &:first-child {
          margin-right: 20px;
        }

        &:last-child {
          margin-left: 20px;
        }
      }
    }
  }
}
</style>