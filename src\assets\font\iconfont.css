@font-face {
  font-family: "iconfont"; /* Project id 4245904 */
  src: url('iconfont.woff2?t=1730960401970') format('woff2'),
       url('iconfont.woff?t=1730960401970') format('woff'),
       url('iconfont.ttf?t=1730960401970') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-add:before {
  content: "\e60b";
}

.icon-prev:before {
  content: "\e75e";
}

.icon-next:before {
  content: "\e600";
}

.icon-close:before {
  content: "\e602";
}

.icon-a-ziyuan8:before {
  content: "\e611";
}

.icon-quanxianguanli:before {
  content: "\e608";
}

.icon-bianji:before {
  content: "\e840";
}

.icon-wancheng:before {
  content: "\e624";
}

.icon-shanchu:before {
  content: "\e601";
}

.icon-close-circle:before {
  content: "\e77d";
}

