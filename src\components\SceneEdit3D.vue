<template>
  <div class="scene-edit" v-show="!isPlanStyle">
    <canvas-left :source-pool-mourse-down="sourcePoolMourseDown" :render-scene-data="renderSceneData"
      :handle-active-area="handleActiveArea" :public-material-list="materialMetaDtoList" :delete-source="deleteSource"
      :active-source-material="activeSourceMaterial" :scene-info="currentScene" :interaction-id="interactionId"
      :guideRoute-id="guideRouteId" :hover-source-name="hoverSourceName" :active-source-name="activeSourceName"
      :isPlanStyle="isPlanStyle"></canvas-left>
    <experience-canvas ref="canvasRef" :handle-mouse-move="handleMouseMoveCanvas" :set-location="setLocation"
      :set-rotation="setRotation" :set-scale="setScale" :change-object="changeObject" :operate-event="operateEvent"
      :isPlanStyle="isPlanStyle"></experience-canvas>
    <canvas-right v-if="currentScene.id" :scene-info="currentScene" :operate-index="operateIndex"
      :spatialData3D="spatialData3D" :change-scene-value="changeSceneValue" :change-right-value="changeRightValue"
      :exit-edit="exitEdit"></canvas-right>
    <edit-tools-top ref="editToolsRef" :scene-info="currentScene" :change-operate-type="changeOperateType"
      :save-scene-info="saveSceneInfo"></edit-tools-top>
  </div>
  <div class="show-icon2" :style="{ backgroundImage: `url(${dragingImg})` }" @mousemove="handleIconMove"
    @mouseup="handleIconUp">
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, reactive, onUnmounted } from 'vue'
import ExperienceCanvas from '@/components/experience/ExperienceCanvas.vue'
import CanvasLeft from '@/components/CanvasLeft.vue'
import CanvasRight from '@/components/CanvasRight.vue'
import { Vector3, Scene, Vector2, Quaternion, Box3 } from 'three'
import { useRouter } from 'vue-router'
import { MouseStyle, updateMouseStyle } from '@/core/mouse'
import { radiansToAngle, angleToRadians, loadZipFileForJSZip } from "@/utils";
import { getSceneMetaPageForWeb, saveScene, getWebScene, updateSceneMeta, getOssAccessPath } from '@/api/index'
import EditToolsTop from '@/components/EditToolsTop.vue'
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
const store = useStore();
const props = defineProps({
  showRoute: {
    default: 0,
    type: Number
  },
  isPlanStyle: {
    default: false,
    type: Boolean
  },
  add2DModel: {
    default: null,
    type: Function
  },
  activeSource2D: {
    default: null,
    type: Function
  },
  set2DLocation: {
    default: null,
    type: Function
  },
  refresh2D: {
    default: null,
    type: Function
  }
})
const canvasRef = ref();
let Icondom: any = null
const dragingImg = ref('') // 当前拖拽的资源缩略图
let currentScene: any = ref({}) // 当前场景信息
const router = useRouter()
const submitData: any = {} // 要提交的数据
let scene: Scene;
let materialMetaDtoList: any = reactive([])  // 公共素材
const operateIndex = ref(0) // 当前操作的索引，0是互动区域，1是路线
let currentSource: any = null // 当前的资源
let interactionId = ref('') // 互动区域id
let guideRouteId = ref('') // 路线id
const hoverSourceName = ref('') // 当前悬浮高亮的资源名字
const activeSourceName: any = reactive({}) // 当前被选中的素材名
const spatialData3D: any = ref({ location: new Vector3(0, 0, 0), rotation: new Vector3(0, 0, 0), scale: new Vector3(1, 1, 1) }) // 存放移动旋转缩放信息，和右侧菜单交互
const activeAreaIndex = ref(-99) // 当前高亮的互动区域的index
let areaList: any = [] // 所有互动区域列表
const editToolsRef = ref()
const saveloading = ref(false) // 场景保存是否在请求中
const renderSceneData = ref({ reRender: false }) // 重新渲染场景数据

const exitEdit = () => {
  // 更新场景之前先截图保存
  const renderer = (window as any).renderer2;
  const camera = (window as any).camera2;
  renderer.render(scene, camera)
  let imgData = renderer.domElement.toDataURL("image/jpeg")
  saveScene({ sceneId: submitData.sceneId, base64Pic: imgData }).then((res: any) => {
    router.push('home');
  })
}

const handleActiveArea = (data: any, index: number, isCreate?: boolean) => {
  if (index == -99) {
    activeAreaIndex.value = index
    canvasRef.value.hideCube(activeSourceName.text)
    activeSourceName.text = ''
    return
  }
  activeAreaIndex.value = index
  if (isCreate) {
    const currentData: any = { ...data[index] }
    currentData.scale = new Vector3(currentData.scale?.x || 1, currentData.scale?.y || 1, currentData.scale?.z || 1)  // 缩放
    currentData.rotation = new Vector3(currentData.rotation?.x || 0, currentData.rotation?.y || 0, currentData.rotation?.z || 0) // 旋转
    currentData.location = new Vector3(currentData.location?.x || 0, currentData.location?.y || 0, currentData.location?.z || 0)
    if (!currentData.flag) {
      // 从互动区域列表跳进该页面
      if (interactionId.value != currentData.id) {
        activeAreaIndex.value = -99
      }
    }
    areaList[index] = currentData
    currentData.materialDto.forEach((e: any, i: number) => {
      if (e.materialType) {
        let path = e.modelStorageMap ? e.modelStorageMap.web.ossPath : e.ossPath
        if (e.materialFormat.includes('video')) {
          path = e.thumbnailOssAccessUrl;
        }
        canvasRef.value.addModel(path, e, (obj: any) => {
          areaList[index].materialDto[i] = { ...currentData.materialDto[i], flag: 'update', id: e.id, materialId: e.materialId, uuid: obj.uuid }
          obj.position.set(e.point.x, e.point.y, e.point.z)
          obj.scale.set(e.scale.x, e.scale.y, e.scale.z)
          obj.userData.boxInfos.scale = new Vector3(e.scale.x, e.scale.y, e.scale.z)
          const metaInfo = e.metaInfo?.split(',')
          if (metaInfo) {
            const quaternion = new Quaternion(+metaInfo[0], +metaInfo[1], +metaInfo[2], +metaInfo[3]);
            obj.applyQuaternion(quaternion)
          }
        }, true)
      }
    })
  } else {
    initSpatialData()
  }
}

// 保存场景信息
const saveSceneInfo = () => {
  // 保存时候防抖
  if (saveloading.value) return
  submitData.interactionDtoList = areaList.map((e: any) => {
    const d = {
      flag: e.flag,
      interactionName: e.title,
      sceneId: submitData.sceneId,
      location: e.location.clone(),
      scale: e.scale.clone(),
      rotation: new Vector3(radiansToAngle(e.rotation.x), radiansToAngle(e.rotation.x), radiansToAngle(e.rotation.z)),
      id: e.id,
      materialMetaDtoList: e.materialDto.filter((a: any) => !a.groupName).map((f: any) => {
        const s: any = {
          location: new Vector3(f.location.x, f.location.y, f.location.z),
          rotation: new Vector3(f.rotation.x, f.rotation.y, f.rotation.z),
          scale: new Vector3(f.scale.x, f.scale.y, f.scale.z),
          materialId: f.materialId,
          sceneId: submitData.sceneId,
          materialAffiliation: f.materialAffiliation,
          id: f.id,
          metaInfo: f.metaInfo
        }
        if (f.flag) {
          s.flag = f.flag
        }
        if (typeof s.id == 'string') delete s.id
        return s
      }),
      materialGroupDtoList: e.materialDto.filter((a: any) => a.groupName).map((f: any) => {
        const s: any = {
          location: f.point,
          interactionId: e.id,
          flag: 'update',
          id: f.materialId,
          sceneId: submitData.sceneId,
        }
        if (f.flag) {
          s.flag = f.flag
        }
        if (typeof s.interactionId == 'string') delete s.interactionId
        return s
      })
    }
    if (!e.flag) delete d.flag
    if (typeof e.id == 'string') delete d.id
    return d
  })
  submitData.outerMaterialMetaDtoList = materialMetaDtoList.filter((a: any) => !a.groupName).map((f: any) => {
    const s: any = {
      location: new Vector3(f.location.x, f.location.y, f.location.z),
      rotation: new Vector3(f.rotation.x, f.rotation.y, f.rotation.z),
      scale: new Vector3(f.scale.x, f.scale.y, f.scale.z),
      materialId: f.materialId,
      sceneId: submitData.sceneId,
      materialAffiliation: f.materialAffiliation,
      id: f.id,
      metaInfo: f.metaInfo
    }
    if (f.flag) {
      s.flag = f.flag
    }
    if (typeof s.id == 'string') delete s.id
    return s
  })
  submitData.outerMaterialGroupDtoList = materialMetaDtoList.filter((a: any) => a.groupName).map((f: any) => {
    const s: any = {
      location: f.point,
      flag: 'update',
      id: f.materialId,
      sceneId: submitData.sceneId,
    }
    if (f.flag) {
      s.flag = f.flag
    }
    return s
  })

  saveloading.value = true

  // 更新场景之前先截图保存
  const renderer = (window as any).renderer2;
  const camera = (window as any).camera2;
  renderer.render(scene, camera)
  let imgData = renderer.domElement.toDataURL("image/jpeg")
  saveScene({ ...submitData, base64Pic: imgData }).then((res: any) => {
    ElMessage.success('项目保存成功！')
    setTimeout(() => {
      operateIndex.value = 0
      getInitData()
      props.refresh2D()
    }, 1000)
    const { sceneName, stadiumName, spaceDescribe } = currentScene.value;
    updateSceneMeta({ sceneName, stadiumName, spaceDescribe, id: submitData.sceneId }).then((res: any) => {
    })
  })
}

// 设置右侧边框初始显示
const initSpatialData = () => {
  spatialData3D.value.location = new Vector3(0, 0, 0)
  spatialData3D.value.rotation = new Vector3(0, 0, 0)
  spatialData3D.value.scale = new Vector3(1, 1, 1)
}

// 鼠标拖动停止事件
const handleIconUp = (e: any) => {
  if (dragingImg.value) {
    const canvasLeftWidth = (document.querySelectorAll('.canvas-left')[1] as any).getBoundingClientRect().width
    updateMouseStyle(MouseStyle.default, true)
    if (canvasLeftWidth < e.clientX) {
      let ossKey = ''
      if (currentSource.materialType == '4') {
        ossKey = currentSource.modelStorageMap.web.ossKey
      } else {
        ossKey = currentSource.ossKey
      }
      if (currentSource.materialFormat.includes('video') && currentSource.thumbnail) {
        ossKey = currentSource.thumbnail;
      }
      if (ossKey) {
        getOssAccessPath({ key: ossKey }).then((res1: any) => {
          canvasRef.value.addModel(res1.data, currentSource, (obj: any) => {
            // TODO 这里需要做左右互动
            if (activeAreaIndex.value >= 0) {
              areaList[activeAreaIndex.value].materialDto.push({ ...currentSource, flag: 'add', id: obj.uuid, materialId: currentSource.id, uuid: obj.uuid, location: new Vector3(0, 0, 0), rotation: new Vector3(0, 0, 0), scale: new Vector3(1, 1, 1) })
            } else {
              materialMetaDtoList.push({ ...currentSource, flag: 'add', id: obj.uuid, materialId: currentSource.id, uuid: obj.uuid, location: new Vector3(0, 0, 0), rotation: new Vector3(0, 0, 0), scale: new Vector3(1, 1, 1) })
            }
            obj.userData.boxInfos.scale = new Vector3(1, 1, 1)
            props.add2DModel(currentSource, activeAreaIndex.value)
            currentSource = null
          }, !!currentSource.thumbnail)
        })
      }
    }
    hideDragImg()
  }
}

const addModelFrom2D = (point: any, currentSource: any, activeAreaIndex: number) => {
  let ossKey = ''
  if (currentSource.materialType == '4') {
    ossKey = currentSource.modelStorageMap.web.ossKey
  } else {
    ossKey = currentSource.ossKey
  }
  if (currentSource.materialFormat.includes('video')) {
    ossKey = currentSource.thumbnail;
  }
  if (ossKey) {
    getOssAccessPath({ key: ossKey }).then((res1: any) => {
      canvasRef.value.addModel(res1.data, currentSource, (obj: any) => {
        // TODO 这里需要做左右互动
        if (activeAreaIndex >= 0) {
          areaList[activeAreaIndex].materialDto.push({ ...currentSource, flag: 'add', id: obj.uuid, materialId: currentSource.id, uuid: obj.uuid, location: new Vector3(point.x, point.y, point.z), rotation: new Vector3(0, 0, 0), scale: new Vector3(1, 1, 1) })
        } else {
          materialMetaDtoList.push({ ...currentSource, flag: 'add', id: obj.uuid, materialId: currentSource.id, uuid: obj.uuid, location: new Vector3(point.x, point.y, point.z), rotation: new Vector3(0, 0, 0), scale: new Vector3(1, 1, 1) })
        }
        obj.userData.boxInfos.scale = new Vector3(1, 1, 1)
        obj.position.set(point.x, point.y, point.z)
      }, true)
    })
  }
}

const changeOperateType = (name: string) => {
  canvasRef.value.changeOperateType(name)
  if (name == '保存') {
    saveSceneInfo()
  }
}

const changeRightValue = (value: any, key: string, type: string) => {
  if (activeSourceName.text) {
    canvasRef.value.setTransform(type, key, value)
  }
}

// 修改场景元信息
const changeSceneValue = (val: any, key: any) => {
  currentScene.value[key] = val;
}

// 选中素材高亮交互
const activeSourceMaterial = (i: number, index: number, isActive?: boolean, once?: boolean) => { // 这里的isActive是区分是选中还是取消选中状态的
      store.dispatch('updateCurrentData', isActive ? materialMetaDtoList[i] : null)
  if (!once) {
    props.activeSource2D(i, index, isActive)
  }
  const uuid = index == -99 ? materialMetaDtoList[i]?.uuid : areaList[index].materialDto[i]?.uuid
  if (!uuid) return
  if (isActive) {
    canvasRef.value.activeCube(uuid, once)
    activeSourceName.text = uuid
  } else {
    canvasRef.value.hideCube(uuid)
    activeSourceName.text = ''
  }
}

const deleteSource = (i: number, index: number) => {
  canvasRef.value.hideCube(index == -99 ? materialMetaDtoList[i].uuid : areaList[index].materialDto[i].uuid)
  if (index == -99) {
    const removeObj: any = scene.getObjectByName(materialMetaDtoList[i].uuid)
    scene.remove(removeObj)
    changeFlag(materialMetaDtoList, i, 'delete')
  } else {
    const removeObj: any = scene.getObjectByName(areaList[index].materialDto[i].uuid)
    scene.remove(removeObj)
    changeFlag(areaList[index].materialDto, i, 'delete')
  }
  activeSourceName.text = ''
}

const changeFlag = (objectList: any, index: number, type: string) => {
  const flag = objectList[index].flag

  // 更新
  if (type == 'update') {
    if (flag != 'add') {
      objectList[index].flag = 'update'
    }
  }

  // 删除
  if (type == 'delete') {
    if (flag == 'add') {
      objectList.splice(index, 1)
    } else {
      objectList[index].flag = 'delete'
    }
  }
}

// 点击图片时候修改交互图片的位置
const sourcePoolMourseDown = (e: any, url: string, data: any) => {
  updateMouseStyle(MouseStyle.mouseDrag, true)
  dragingImg.value = url
  handleIconMove(e)
  currentSource = { ...data }
}

// 清空素材交互
const hideDragImg = () => {
  dragingImg.value = ''
  Icondom.style.left = '-50%'
  Icondom.style.top = "-50%"
}

const handleMouseMoveCanvas = (point2D: Vector2) => {
  if (dragingImg.value) { // 素材缩略图拖拽
    handleIconMove({ clientX: point2D.x, clientY: point2D.y })
    return
  }
}

const operateEvent = (value: string) => {
  editToolsRef.value.operateEvent(value)
}

const changeObject = (name: string, once?: boolean) => {
  let aIndex = -1;
  let sIndex = -1;
  if (!name) {
    activeSourceName.text = ''
    initSpatialData()
    return
  }
  let activeModelData: any = null
  materialMetaDtoList.forEach((e: any) => {
    if (e.uuid == name) {
      activeModelData = { ...e }
    }
  })
  areaList.forEach((a: any, index: number) => {
    a.materialDto.forEach((e: any, i: number) => {
      if (e.uuid == name) {
        activeModelData = { ...e }
        aIndex = index;
        sIndex = i;
      }
    })
  })
  if (activeModelData) {
    const location = activeModelData.location || new Vector3(0, 0, 0)
    const rotation = activeModelData.rotation || new Vector3(0, 0, 0)
    const scale = activeModelData.scale || new Vector3(1, 1, 1)
    spatialData3D.value.location = new Vector3(location.x, location.y, location.z)
    spatialData3D.value.rotation = new Vector3(rotation.x, rotation.y, rotation.z)
    spatialData3D.value.scale = new Vector3(scale.x, scale.y, scale.z)
  }
  activeSourceName.text = name

  if (once) return
  // 点击3D模型对应触发2D页面
  if (aIndex !== -1 && sIndex !== -1) {
    props.activeSource2D(sIndex, aIndex, true)
  } else {
    const index = materialMetaDtoList.findIndex((e: any) => e.uuid == activeSourceName.text)
    if (index !== -1) {
      props.activeSource2D(index, -99, true)
    }
  }
}

const setLocation = (key: string, value: string) => {
  setTransform(key, value, 'location')
  props.set2DLocation(spatialData3D.value.location.clone())
}

const setRotation = (key: string, value: string, angles?: number) => {
  const angle = angleToRadians(+value)
  setTransform(key, angle + '', 'rotation', angles)
}

const setScale = (key: string, value: string) => {
  setTransform(key, value, 'scale')
}

const setTransform = (key: string, value: string, type: string, angles?: number) => {
  if (type == 'rotation') {
    if (angles == undefined) {
      spatialData3D.value[type][key] += +value
    }
  } else {
    spatialData3D.value[type][key] = +value
  }
  materialMetaDtoList = materialMetaDtoList.map((item: any) => {
    if (item.uuid == activeSourceName.text) {
      if (type == 'rotation') {
        item[type][key] += +value
      } else {
        item[type][key] = +value
      }
      const quaternion = (scene.getObjectByName(activeSourceName.text) as any).quaternion
      item.metaInfo = `${quaternion.x},${quaternion.y},${quaternion.z},${quaternion.w}`
    }
    return item;
  })
  areaList.forEach((a: any, index: number) => {
    a.materialDto.forEach((e: any, i: number) => {
      if (e.uuid == activeSourceName.text) {
        if (type == 'rotation') {
          areaList[index].materialDto[i][type][key] += +value
        } else {
          areaList[index].materialDto[i][type][key] = +value
        }
        const quaternion = (scene.getObjectByName(activeSourceName.text) as any).quaternion
        areaList[index].materialDto[i].metaInfo = `${quaternion.x},${quaternion.y},${quaternion.z},${quaternion.w}`
      }
    })
  })
}

// 拖动素材时候悬浮交互
const handleIconMove = (e: any) => {
  if (dragingImg.value) {
    Icondom.style.left = e.clientX - 25 + 'px'
    Icondom.style.top = e.clientY - 25 + 'px'
  }
}

onMounted(() => {
  getInitData()
})

const getInitData = (noReload?: boolean) => {
  const pageQuery: any = router.currentRoute.value.query
  const sceneId = pageQuery.sceneid || ''
  Icondom = (document.querySelector('.show-icon2') as any)
  scene = (window as any).scene2;
  interactionId.value = pageQuery.interactionid
  guideRouteId.value = pageQuery.guideRouteid
  materialMetaDtoList = reactive([]);
  areaList = [];
  saveloading.value = false
  renderSceneData.value.reRender = false

  const uuid = scene.getObjectByName(activeSourceName.text)?.uuid
  if (uuid) {
    canvasRef.value.hideCube(uuid)
    activeSourceName.text = ''
  }

  for (let i = 0; i < scene.children.length; i++) {
    if (scene.children[i].name.indexOf('-init') === -1) {

      // 删除文字标记
      scene.children[i].traverse((e: any) => {
        if (e.element) {
          e.parent?.remove(e)
        }
      })
      scene.remove(scene.children[i])
      i--
    }
  }
  operateEvent('')
  getSceneMetaPageForWeb({ pageNo: 1, pageSize: 999 }).then((res: any) => {
    currentScene.value = res.data.records.filter((e: any) => e.id == sceneId)[0] || {}
    submitData.sceneId = sceneId

    if (noReload) {
      getSceneData(sceneId)
      return
    }
    // 加载地图
    loadZipFileForJSZip(currentScene.value.spaceDto.roomStructurePath, (glb: any) => {
      glb.scene.name = 'model-init'
      // glb.scene.traverse((e: any) => {
      //   if (e.material?.side == 0) {
      //     e.material.side = 2;
      //   }
      // })
      scene.add(glb.scene);
      const boxInfo = new Box3().expandByObject(glb.scene)
      if (scene.getObjectByName('ground-init')) {
        (scene.getObjectByName('ground-init') as any).position.y = boxInfo.min.y - 0.1
      }
      getSceneData(sceneId)
    })
  })
}

const getSceneData = (sceneId: string) => {
  getWebScene({ id: sceneId }).then((res: any) => {
    currentScene.value = { ...currentScene.value, ...res.data }
    const outerMaterialMetaDtoList = [...(res.data.outerMaterialMetaDtoList || [])].map((e: any) => ({ ...e, ...e.materialDto, point: new Vector3(e.location.x, e.location.y, e.location.z), id: e.id, materialId: e.materialId, materialAffiliation: e.materialAffiliation }))
    materialMetaDtoList.push(...outerMaterialMetaDtoList)

    const outerMaterialGroupDtoList = [...(res.data.outerMaterialGroupDtoList || [])].map((e: any) => ({ ...e, point: new Vector3(e.location.x, e.location.y, e.location.z), id: e.id, materialId: e.id, materialName: e.groupName }))
    materialMetaDtoList.push(...outerMaterialGroupDtoList)

    // 公共素材生成模型  TODO这里还需考虑其他类型素材的展示
    materialMetaDtoList.forEach((e: any, i: number) => {
      if (e.materialType) {
        let path = e.modelStorageMap ? e.modelStorageMap.web.ossPath : e.ossPath
        if (e.materialFormat.includes('video')) {
          path = e.thumbnailOssAccessUrl;
        }
        canvasRef.value.addModel(path, e, (obj: any) => {
          materialMetaDtoList[i] = { ...materialMetaDtoList[i], flag: 'update', id: e.id, materialId: e.materialId, uuid: obj.uuid }
          obj.position.set(e.point.x, e.point.y, e.point.z)
          obj.scale.set(e.scale.x, e.scale.y, e.scale.z)
          obj.userData.boxInfos.scale = new Vector3(e.scale.x, e.scale.y, e.scale.z)
          const metaInfo = e.metaInfo?.split(',')
          if (metaInfo) {
            const quaternion = new Quaternion(+metaInfo[0], +metaInfo[1], +metaInfo[2], +metaInfo[3]);
            obj.applyQuaternion(quaternion)
          }
        }, true)
      }
    })
    renderSceneData.value.reRender = true
  })
}

onUnmounted(() => {
  for (let i = 0; i < scene.children.length; i++) {
    // 删除文字标记
    scene.children[i].traverse((e: any) => {
      if (e.element) {
        e.parent?.remove(e)
      }
    })
    scene.remove(scene.children[i])
    i--
  }
})
defineExpose({
  addModelFrom2D,
  setLocation,
  activeSourceMaterial,
  changeRightValue,
  exitEdit,
  getInitData
})
</script>
<style scoped lang="less">
.show-icon2 {
  width: 50px;
  height: 50px;
  position: fixed;
  left: -50%;
  top: -50%;
  background-size: 100% 100%;
  z-index: 99;
}

.scene-edit {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
</style>