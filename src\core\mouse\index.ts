export enum MouseStyle {
  mouseDrag = "url('/mouse/mouse-drag.png') 20 20,pointer",
  mouseRotate = "url('/mouse/mouse-rotate.png') 8 8,pointer",
  mouseScaleTB = "url('/mouse/mouse-scale-tb.png') 20 20,pointer",
  mouseScaleLR = "url('/mouse/mouse-scale-lr.png') 20 20,pointer",
  mouseAdd = "url('/mouse/add-icon.png') 20 20,pointer",
  default = "",
}

export function updateMouseStyle(style: MouseStyle, session = false) {
  document.body.style.cursor =
    style != undefined ? style : sessionStorage.mouseStyle;
  session && (sessionStorage.mouseStyle = style);
}
