import {
  BufferGeometry,
  Mesh,
  Intersection,
  MeshBasicMaterial,
  Raycaster,
  RepeatWrapping,
  TextureLoader,
  Vector2,
  Vector3,
  Float32BufferAttribute,
  Matrix4,
  Quaternion,
  Plane,
  BufferAttribute,
  Group,
} from "three";
import { CLine } from "./CLine";
import { CCircle } from "./CCircle";
import { CSprite } from "./CSprite";
import {
  screenToWorldVector,
  worldToScreenVector,
  point2DByPoint3D,
  areaOfTriangle,
  pointToLine,
  getAngle,
  pointRotate,
} from "@/utils";
import { canvasIds } from "@/core/config";

export const ADSORPTION_RADIUS = 10; // 点吸附半径
const initSize = new Vector2(1, 1); // 原始的互动区域的二维尺寸

export type CPlanAttrs = {
  normal?: Vector3;
  position?: Vector2;
  width?: number; // 互动区域的空间长度
  height?: number; // 互动区域的空间宽度
  color?: number;
  transparent?: boolean;
  url?: string;
  depthTest?: boolean;
  constraint?: any; // 约束缓存
  marks?: any; // 标注缓存
  type?: string;
  areaIndex?: number;
  sizeScale?: Vector2;
  reversalDepthTest?: boolean;
};
export class CPlan extends Mesh {
  geometry: BufferGeometry;
  material: MeshBasicMaterial;
  name: string;
  userData: { [k: string]: any };
  constructor(attrs?: CPlanAttrs) {
    super();

    this.name = "cplan";
    this.geometry = new BufferGeometry();
    this.material = new MeshBasicMaterial({
      side: 2,
      depthTest: false
    });
    this.userData = {};
    this.initialize(attrs);
  }
  getType() {
    // 获取类型
    return "Point";
  }

  initData(attrs?: CPlanAttrs) {
    // 约束/标注/索引缓存
    this.userData = {
      constraint: {
        links: [],
      },
      marks: [],
      index: -1,
    };
    if (attrs && attrs.constraint !== undefined)
      this.userData.constraint = attrs.constraint; // constraint data cache
    if (attrs && attrs.marks !== undefined) this.userData.marks = attrs.marks; // marks data cache
    const normal =
      attrs && attrs.normal !== undefined
        ? attrs.normal.normalize()
        : new Vector3(0, 1, 0); // 当前planeGeometry传入的法向量
    const matrix = new Matrix4().makeRotationFromQuaternion(
      new Quaternion().setFromUnitVectors(
        new Vector3(0, 0, 1), // 初始向量
        normal // 面所在的法向量
      )
    );
    this.userData.matrix = matrix.clone();
  }

  initUI(attrs?: CPlanAttrs) {
    this.userData.areaIndex =
      attrs && attrs.areaIndex !== undefined ? attrs.areaIndex : -1;
    // 默认坐标原点
    if (attrs?.width && attrs?.height && attrs?.position) {
      this.setVertexs(attrs.width, attrs.height, attrs.position);
    } else if (attrs?.width && attrs?.height){
      this.setVertexs(attrs.width, attrs.height);
    }

    if (attrs && attrs.sizeScale !== undefined) this.userData.sizeScale = attrs.sizeScale.clone(); // 默认纹理
    if (attrs && attrs.url !== undefined) this.loadTexture(attrs.url); // 默认纹理
    if (attrs && attrs.color !== undefined) this.setColor(attrs.color); // 颜色
    if (attrs && attrs.reversalDepthTest) {
      this.material.depthTest = attrs.reversalDepthTest;
    } else {
      this.material.depthTest = false;
    }
    this.renderOrder = 10;

    // - 所有情况下点都是 depthtest 为false
    // this.setDepthTest(false);

    const onChange = () => {
      this.userData.center = worldToScreenVector(this.position.clone()).clone();
    };

    const resizeCanvas = () => {
      const pathname = window.sessionStorage.getItem('path')?.slice(1) || 'home';
      if(canvasIds[pathname]) {
        this.userData.center = worldToScreenVector(this.position.clone()).clone();
      } else {
        window.removeEventListener("resize", resizeCanvas);
      }
    };
    const controls = (window as any).controls;
    controls.addEventListener("change", onChange);
    // 被销毁后 移除事件
    this.geometry.addEventListener("dispose", (e: any) => {
      controls.removeEventListener("change", onChange);
    });

    window.addEventListener("resize", resizeCanvas, false);
  }

  initialize(attrs?: CPlanAttrs) {
    this.initData(attrs);
    this.initUI(attrs);
  }

  // @override 射线拾取点
  raycast(raycaster: Raycaster, intersects: Intersection[]) {
    let pointData = [...this.userData.linePoints];
    const ori = (raycaster as any).ray.origin;
    if (this.userData.rotate) {
      const linePoints = [...this.userData.linePoints].map((e) =>
        e.clone().sub(this.children[0].userData.repeatCenter)
      );
      pointData = linePoints.map((e) => {
        const quaternion = new Quaternion();
        quaternion.setFromAxisAngle(
          new Vector3(0, 1, 0),
          -this.userData.rotate
        );
        const f = e.clone().applyQuaternion(quaternion);
        return f;
      });
      pointData = pointData.map((e) =>
        e.clone().add(this.children[0].userData.repeatCenter)
      );
    }

    const l1 = pointData[2].clone().sub(pointData[0]);
    const l2 = pointData[4].clone().sub(pointData[2]);
    const l3 = pointData[6].clone().sub(pointData[4]);
    const l4 = pointData[8].clone().sub(pointData[6]);

    const ll1 = ori.clone().sub(pointData[0]);
    const ll2 = ori.clone().sub(pointData[2]);
    const ll3 = ori.clone().sub(pointData[4]);
    const ll4 = ori.clone().sub(pointData[6]);

    const c1 = l1.clone().cross(ll1).normalize();
    const c2 = l2.clone().cross(ll2).normalize();
    const c3 = l3.clone().cross(ll3).normalize();
    const c4 = l4.clone().cross(ll4).normalize();

    if (c1.y > 0 && c2.y > 0 && c3.y > 0 && c4.y > 0) {
      intersects.push({
        distance: 0,
        object: this,
      } as any);
    }
  }

  loadTexture(url: string) {
    const t = new TextureLoader().load(url);
    t.wrapS = t.wrapT = RepeatWrapping;
    this.material.map = t;

    // 根据缩放倍数来计算纹理
    if(this.userData.sizeScale) {
      this.setUV(this.userData.sizeScale.x, this.userData.sizeScale.y)
    } else {
      const uvs = new Float32Array([0.25, 0.25, 0.75, 0.25, 0.75, 0.75, 0.25, 0.25, 0.75, 0.75, 0.25, 0.75]);
      this.geometry.attributes.uv = new BufferAttribute(uvs, 2); //2个为一组,表示一个顶点的纹理坐标
    }
    return this;
  }

  //
  setDepthTest(depthTest: boolean) {
    this.material.depthTest = depthTest;
    return this;
  }

  // 顶点位置： 多段数据
  setVertexs(
    width: number,
    height: number,
    positionx?: Vector2,
    hideRotate?: boolean
  ) {
    let position = positionx?.clone()
    if(!position) {
      position = worldToScreenVector(new Vector3(0, 0, 0))
    }
    if (width && height && position) {
      const positions: Array<number> = [];
      const pointForV3: Array<Vector3> = [];
      const position3D = screenToWorldVector(position.x, position.y);

      // 平面的三维顶点
      const p1 = new Vector3(position3D.x - width, position3D.y, position3D.z + height)
      const p2 = new Vector3(position3D.x + width, position3D.y, position3D.z + height)
      const p3 = new Vector3(position3D.x + width, position3D.y, position3D.z - height)
      const p4 = new Vector3(position3D.x - width, position3D.y, position3D.z - height);

      [p1, p2, p3, p1, p3, p4].forEach((v: Vector3) => {
        // 计算以中心点为原点的相对坐标
        const p = v.clone().sub(position3D);
        positions.push(...p.toArray());
        pointForV3.push(p);
      });

      const LinePoints: Array<Vector3> = [];
      const VArray = [p1, p2, p3, p4]
      VArray.forEach((v, i) => {
        if (i < VArray.length - 1) {
          LinePoints.push(
            v,
            new Vector3(
              (v.x + VArray[i + 1].x) / 2,
              (v.y + VArray[i + 1].y) / 2,
              (v.z + VArray[i + 1].z) / 2
            )
          );
        } else {
          LinePoints.push(
            v,
            new Vector3(
              (v.x + VArray[0].x) / 2,
              (v.y + VArray[0].y) / 2,
              (v.z + VArray[0].z) / 2
            )
          );
        }
      });
      if (
        !this.geometry.attributes.position ||
        (this.geometry.attributes.position as any).array.length <
          positions.length
      ) {
        this.geometry.setAttribute(
          "position",
          new Float32BufferAttribute(positions, 3)
        );
      } else {
        (this.geometry.attributes.position as any).array.set(positions);
        (this.geometry.attributes.position as any).needsUpdate = true;
      }
      // 位置改变，需要重新计算包围球属性
      this.geometry.computeBoundingSphere();
      if (this.userData.hasSide) {
        const cline: any = this.children[0];
        cline.userData.repeatCenter = position3D.clone();
        cline.setVertexs([...LinePoints, LinePoints[0]], "update");
        (this.children[0] as any).geometry.center();
        if (hideRotate) {
          cline.setBaseStyle();
        }
      } else {
        const line = new CLine({
          vertexs: [...LinePoints, LinePoints[0]],
          createPoints: true,
          color: 0xf89e01,
          lineWidth: 2,
          areaIndex: this.userData.areaIndex,
          repeatCenter: position3D.clone(), // 设置线上点的显示位置
        });
        line.userData.parent = "cplan";
        this.add(line);
        // 线的中心点归零
        line.geometry.center();
        this.userData.hasSide = true;
      }

      // 设置旋转的元素
      let rotateGroup = this.getObjectByName("rotateGroup");
      if (!rotateGroup) {
        rotateGroup = new Group();
        rotateGroup.name = "rotateGroup";
        // 加入旋转图标
        const point = new CSprite({
          vertex: new Vector3(0, 0, 0),
          url: require("@/assets/images/background/rotate-default.png"),
        });
        rotateGroup.add(point);
        point.userData.type = "rotatePoint";
        point.userData.state = "default";
        point.userData.point = position3D.clone();
        point.userData.rpoint = [
          LinePoints[6].clone(),
          LinePoints[4].clone(),
        ].map((e: any) =>
          pointRotate(e, position3D, this.userData.rotate || 0)
        ); // 用来计算区域的参考点
        this.add(rotateGroup);
      } else {
        const rotateIcon = rotateGroup.children[0];
        rotateIcon.userData.point = position3D.clone();
        rotateIcon.userData.rpoint = [
          LinePoints[6].clone(),
          LinePoints[4].clone(),
        ].map((e: any) =>
          pointRotate(e, position3D, this.userData.rotate || 0)
        ); // 用来计算区域的参考点
        if (hideRotate) {
          rotateIcon.visible = false;
        }
      }

      // 中心点归零
      this.userData.center = position.clone();
      this.userData.linePoints = [...LinePoints, LinePoints[0]];

      // 设置位置
      this.position.set(...position3D.toArray());
    }
    return this;
  }

  // 计算面积
  getArea() {
    // 先拿到点位
    const positionPoint = this.geometry.getAttribute("position").array;
    const points = [];
    for (let i = 0; i < positionPoint?.length; i += 3) {
      points.push(
        new Vector3(
          positionPoint[i],
          positionPoint[i + 1],
          positionPoint[i + 2]
        )
      );
    }
    let areaValue = 0;
    for (let i = 0; i < points?.length; i += 3) {
      areaValue += areaOfTriangle(points[i], points[i + 1], points[i + 2]);
    }
    return areaValue;
  }

  dragPoint = (
    type: string,
    currentPosition: Vector3,
    oldScale: Vector2,
    pointIndex: number
  ) => {
    const width = initSize.x;
    const height = initSize.y;
    const quaternion = new Quaternion().setFromAxisAngle(
      new Vector3(0, 1, 0),
      -(this.userData.rotate || 0)
    );
    const current2DPosition = worldToScreenVector(currentPosition); // 当前移动点
    const position: Vector2 = this.userData.center.clone(); // 中心点
    const controlPoint3D = this.userData.linePoints[pointIndex || 0]
      .clone()
      .sub(this.position.clone())
      .applyQuaternion(quaternion)
      .clone()
      .add(this.position.clone());
    const controlPoint2D = worldToScreenVector(controlPoint3D); // 控制点
    const currentWidth = width * oldScale.x;
    const currentHeight = height * oldScale.y;
    if (type === "vertex") {
      const newDistance = current2DPosition.distanceTo(position);
      const oldDistance = controlPoint2D.distanceTo(position);
      let ratio = 1 + (newDistance - oldDistance) / 2 / oldDistance; // 比率
      let newWidth = ratio * currentWidth;
      let newHeight = ratio * currentHeight;

      // 设置宽高最小为0.6m
      if(newWidth < 0.6) {
        newWidth = 0.6
        ratio = newWidth / currentWidth
        newHeight = ratio * currentHeight
      }
      if(newHeight < 0.6) {
        newHeight = 0.6
        ratio = newHeight / currentHeight
        newWidth = ratio * currentWidth
      }

      this.setVertexs(newWidth, newHeight, position);
      this.setUV(newWidth / width, newHeight / height)
      return ratio;
    } else if (type === "vertexCenter" && pointIndex !== null) {
      let newWidth = width * oldScale.x;
      let newHeight = height * oldScale.y;

      if (pointIndex === 3 || pointIndex === 7) {
        const oldWidth = controlPoint2D.distanceTo(position) * 2;
        const pointToLineLength = pointToLine(
          position.clone(),
          controlPoint2D.clone(),
          current2DPosition.clone()
        ); // 拖拽点到x轴的距离
        const pointToCenter = position.clone().distanceTo(current2DPosition); // 当前移动点到中心点的距离
        const diffLength =
          Math.sqrt(
            Math.pow(pointToCenter, 2) - Math.pow(pointToLineLength, 2)
          ) -
          oldWidth / 2;
        newWidth = currentWidth * (1 + diffLength / oldWidth);
      } else if (pointIndex === 5 || pointIndex === 1) {
        const oldHeight = controlPoint2D.distanceTo(position) * 2;
        const pointToLineLength = pointToLine(
          position.clone(),
          controlPoint2D.clone(),
          current2DPosition.clone()
        ); // 拖拽点在x轴上的投影长度
        const pointToCenter = position.clone().distanceTo(current2DPosition); // 当前移动点到中心点的距离
        const diffLength =
          Math.sqrt(
            Math.pow(pointToCenter, 2) - Math.pow(pointToLineLength, 2)
          ) -
          oldHeight / 2;
        newHeight = currentHeight * (1 + diffLength / oldHeight);
      }

      // 设置宽高最小为0.6m
      newWidth = Math.max(newWidth, 0.6)
      newHeight = Math.max(newHeight, 0.6)

      this.setVertexs(newWidth, newHeight, position);
      const scaleX = (newWidth / currentWidth) * oldScale.x;
      const scaleY = (newHeight / currentHeight) * oldScale.y;

      this.setUV(scaleX, scaleY)
      return new Vector2(scaleX, scaleY);
    }
  };

  setUV(scaleX: number, scaleY: number) {
    // 根据缩放大小来计算纹理位置
    const x1 = Math.max(0.5 - scaleX * 0.25, 0);
    const x2 = Math.min(0.5 + scaleX * 0.25, 1);
    const y1 = Math.max(0.5 - scaleY * 0.25, 0);
    const y2 = Math.min(0.5 + scaleY * 0.25, 1);
    const uvs = new Float32Array([x1, y1, x2, y1, x2, y2, x1, y1, x2, y2, x1, y2]);
    this.geometry.attributes.uv = new BufferAttribute(uvs, 2); //2个为一组,表示一个顶点的纹理坐标
  }

  rotatePoint(
    currentPosition: Vector3,
    oldRotate: number,
    startPoint: any,
    startRotate: number
  ) {
    const current2DPosition = worldToScreenVector(currentPosition); // 当前移动点
    const point2D = worldToScreenVector(startPoint); // 控制点
    const position: Vector2 = this.userData.center.clone(); // 中心点
    const v1 = point2D.clone().sub(position);
    const v2 = current2DPosition.clone().sub(position);
    const angle = getAngle(v1, v2) + startRotate;
    const diffRotate = oldRotate - angle;
    this.rotateY(diffRotate);
    this.userData.rotate = angle;
    return angle;
  }

  // 颜色
  setColor(color: number) {
    this.material.color.set(color);
    return this;
  }

  getColor() {
    return this.material.color;
  }

  // 克隆过滤一些属性后的自身信息
  cloneSelffilter(filters: any) {
    const Json = this.toJSON();
    for (const key in Json) {
      if (filters && filters.indexOf(key) > -1) Json[key] = undefined;
    }
    return new CPlan(Json);
  }

  cloneSelf() {
    return new CPlan(this.toJSON());
  }

  toJSON() {
    const json: { [k: string]: any } = {
      name: this.name,
      color: this.getColor(),
    };

    for (const key in this.userData) {
      json[key] = this.userData[key];
    }
    return json;
  }
}
