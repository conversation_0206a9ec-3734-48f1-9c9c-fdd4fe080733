<template>
  <div class="modal-type">
    <div class="modal-content">
      <div class="modal-content-title">
        <div>格式筛选</div>
        <div class="icon iconfont icon-close" @click="changeState"></div>
      </div>
      <div class="modal-form">
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm">
          <el-form-item label="素材类型" prop="sourceType">
            <el-select v-model="ruleForm.sourceType" placeholder="请选择资源类型" class="select-default"
              popper-class="select-option" :suffix-icon="DropDown" style="width: 432px; height: 36px;">
              <el-option value="fbx" v-if="!hideOther">fbx</el-option>
              <el-option value="obj" v-if="!hideOther">obj</el-option>
              <el-option value="glb">glb</el-option>
              <el-option value="gltf">gltf</el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="form-submit">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef)">确认</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { addEquipment, updateEquipment } from '@/api'
import DropDown from '@/components/DropDown.vue'

const props = defineProps({
  changeFormatState: {
    default: null,
    type: Function
  },
  hideOther: {
    default: false,
    type: Boolean
  }
})

interface RuleForm {
  sourceType: string
}

const ruleFormRef = ref<FormInstance>()
const ruleForm: any = reactive<RuleForm>({
  sourceType: '',
})

const rules = reactive<FormRules<RuleForm>>({
  sourceType: [
    { required: true, message: '请选择素材格式', trigger: 'blur' }
  ],
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      props.changeFormatState(false, ruleForm.sourceType)
    } else {
      console.log('error submit!', fields)
    }
  })
}

const changeState = () => {
  props.changeFormatState(false)
}

</script>
<style scoped lang="less">
.modal-type {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;

  .modal-content {
    width: 816px;
    height: 320px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -408px;
    margin-top: -160px;
    background: #EDEFF2;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    border: 1px solid #EDEFF2;
    overflow: hidden;

    .modal-content-title {
      height: 76px;
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px 0 63px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;

      .icon-close {
        font-size: 26px;
        cursor: pointer;
        font-weight: 400;

        &:hover {
          color: #2E76FF;
        }
      }
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      padding: 50px 95px;
      box-sizing: border-box;

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-submit {
        margin-top: 92px;
      }
    }
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;
}
</style>