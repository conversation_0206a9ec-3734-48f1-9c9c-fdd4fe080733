<template>
  <div class="video-params">
    <TabsBar
      v-model="tab"
      :tabs="[
        { label: '图片生成视频', value: 'img2video' },
        { label: '文本生成视频', value: 'txt2video' },
      ]" />
    <div style="width: 100%" v-if="tab === 'img2video'">
      <div class="upload-box" @click="handleUploadClick">
        <input
          ref="fileInput"
          type="file"
          accept="image/jpeg,image/png"
          style="display: none"
          @change="onFileChange" />
        <div v-if="!imageUrl" class="upload-placeholder">
          <img src="@/assets/images/starimg1.png" class="upload-icon" />
          <div class="upload-tip">点击上传图片到此处（可选）</div>
          <div class="upload-desc">
            仅支持JPG/PNG格式，文件大小不超过10MB
            <br />
            分辨率最低要求128*128
          </div>
        </div>
        <img v-else :src="imageUrl" class="uploaded-image" />
      </div>
      <el-input
        v-model="img2videoDesc"
        type="textarea"
        :maxlength="150"
        show-word-limit
        placeholder="结合图片，输入创意描述（*必填）"
        class="desc-input1" />
    </div>
    <div v-else style="width: 100%">
      <el-input
        v-model="txt2videoDesc"
        type="textarea"
        :maxlength="1500"
        show-word-limit
        placeholder="描述想要生成的视频"
        class="desc-input2" />
    </div>
    <!-- 分辨率选择区域统一放在tab分支外部，所有tab都显示 -->
    <div class="section-label">分辨率</div>
    <div class="resolution-btns">
      <div
        :class="['resolution-btn', { active: resolution === '480p' }]"
        @click="resolution = '480p'">
        普通
      </div>
      <div
        :class="['resolution-btn', { active: resolution === '720p' }]"
        @click="resolution = '720p'">
        高清
      </div>
    </div>
    <!-- 视频比例区域 -->
    <div class="section-label" style="width: 100%">视频比例</div>
    <div class="ratio-btns">
      <div v-if="tab === 'img2video'" class="ratio-btn active auto-match">自动匹配图片</div>
      <template v-else>
        <div
          v-for="item in videoRatios"
          :key="item.value"
          :class="['ratio-btn', { active: videoRatio === item.value }]"
          @click="videoRatio = item.value">
          {{ item.label }}
        </div>
      </template>
    </div>
    <div class="section-label">视频时长</div>
    <div class="duration-btns">
      <div :class="['duration-btn', { active: duration === 5 }]" @click="duration = 5">5s</div>
      <div :class="['duration-btn', { active: duration === 10 }]" @click="duration = 10">10s</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { COMMON_RATIOS } from '@/views/ai/config/ratios';
import TabsBar from '@/components/TabsBar.vue';

const tab = ref<'img2video' | 'txt2video'>('img2video');
const img2videoDesc = ref('');
const txt2videoDesc = ref('');
const resolution = ref<'480p' | '720p'>('480p');
const duration = ref(5);
const imageUrl = ref('');
const fileInput = ref<HTMLInputElement | null>(null);
const imageFile = ref<File | null>(null); // 新增：用于存储文件对象

const videoRatio = ref('1:1');
const videoRatios = COMMON_RATIOS;

console.log(
  'defineExpose 前:',
  'tab:',
  tab.value,
  'img2videoDesc:',
  img2videoDesc.value,
  'txt2videoDesc:',
  txt2videoDesc.value
);
defineExpose({
  // 验证参数（不显示错误消息）
  validateParams: () => {
    // 无论哪种模式，都必须填写描述文本
    const desc = tab.value === 'img2video' ? img2videoDesc.value : txt2videoDesc.value;
    return !!desc?.trim();
  },
  // 获取参数（显示错误消息）
  getParams: () => {
    if (tab.value === 'img2video') {
      // 图片模式下，描述文本必填，图片可选
      if (!img2videoDesc.value?.trim()) {
        ElMessage.warning('请输入视频描述');
        return null;
      }
      return {
        desc: img2videoDesc.value,
        img2videoDesc: img2videoDesc.value,
        txt2videoDesc: txt2videoDesc.value,
        tab: tab.value,
        ratio: 'keep_ratio',
        duration: duration.value,
        resolution: resolution.value,
        pic: imageFile.value || null, // 图片可选，可能为null
        VideoQuality: resolution.value,
      };
    } else {
      // 文本模式下，描述文本必填，不需要图片
      if (!txt2videoDesc.value?.trim()) {
        ElMessage.warning('请输入视频描述');
        return null;
      }
      return {
        desc: txt2videoDesc.value,
        img2videoDesc: img2videoDesc.value,
        txt2videoDesc: txt2videoDesc.value,
        tab: tab.value,
        ratio: videoRatio.value,
        duration: duration.value,
        resolution: resolution.value,
        pic: null, // 文本模式不需要图片
        VideoQuality: resolution.value,
      };
    }
  },
  // 暴露当前状态供验证使用
  tab,
  img2videoDesc,
  txt2videoDesc,
  imageFile,
});

const handleUploadClick = () => {
  fileInput.value?.click();
};
const onFileChange = (e: Event) => {
  const files = (e.target as HTMLInputElement).files;
  if (!files || !files[0]) return;
  const file = files[0];
  if (!['image/jpeg', 'image/png'].includes(file.type)) {
    ElMessage.error('仅支持JPG/PNG格式');
    return;
  }
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过10MB');
    return;
  }

  // 检查图片分辨率
  const reader = new FileReader();
  reader.onload = (ev) => {
    const img = new Image();
    img.onload = () => {
      if (img.width < 128 || img.height < 128) {
        ElMessage.error('分辨率不能小于128*128');
        // 清空文件输入框
        if (fileInput.value) {
          fileInput.value.value = '';
        }
        return;
      }
      // 分辨率验证通过，保存文件和显示预览
      imageFile.value = file;
      imageUrl.value = ev.target?.result as string;
    };
    img.src = ev.target?.result as string;
  };
  reader.readAsDataURL(file);
};
</script>

<style scoped lang="less">
.video-params {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  overflow: visible;
}
.param-title {
  font-weight: bold;
  font-size: 24px;
  color: #1e1e1e;
  margin-bottom: 12px;
}
.video-tabs-bar {
  position: relative;
  width: 100%;
  height: 36px;
  background: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  .video-tabs-slider {
    position: absolute;
    top: 3.5px;
    left: 4px;
    width: calc(50% - 8px);
    height: 29px;
    background: #fff;
    border-radius: 3px;
    z-index: 1;
    transition: left 0.3s, width 0.3s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  }
  .video-tab-btn {
    position: relative;
    z-index: 2;
    flex: 1;
    height: 29px;
    line-height: 29px;
    text-align: center;
    font-weight: 500;
    font-size: 14px;
    color: #797979;
    cursor: pointer;
    user-select: none;
    margin: 0 4px;
    background: transparent;
    transition: color 0.2s;
    &.active {
      font-weight: 500;
      font-size: 14px;
      color: #1e1e1e;
    }
  }
}
.upload-box {
  width: 100% !important;
  min-width: 0;
  box-sizing: border-box;
  height: 220px;
  background: #f5f6f7;
  border-radius: 8px 8px 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 2px;
  position: relative;
  .upload-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #bfc6d1;
    .upload-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 8px;
    }
    .upload-tip {
      margin-bottom: 8px;
      font-weight: 400;
      font-size: 14px;
      color: #1e1e1e;
    }
    .upload-desc {
      font-weight: 400;
      font-size: 14px;
      color: #797979;
      line-height: 24px;
      text-align: center;
    }
  }
  .uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 8px;
  }
}
.desc-input1 {
  width: 100% !important;
  min-width: 0;
  display: block;
  box-sizing: border-box;
  margin-bottom: 18px;
  :deep(.el-textarea),
  :deep(.el-textarea__inner) {
    width: 100% !important;
    min-width: 0;
    box-sizing: border-box;
    background: #f5f6f7 !important;
    border-radius: 0px 0px 8px 8px !important;
    border: none !important;
    min-height: 80px;
    height: 80px;
    resize: none;
    box-shadow: none !important;
    padding-top: 10px !important;
  }
  :deep(.el-input__count) {
    right: 12px !important;
    left: auto !important;
    bottom: 8px !important;
    background: #f5f5f5 !important;
    border-radius: 8px !important;
    color: #797979 !important;
    padding: 2px 8px !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    box-shadow: none !important;
  }
}
.desc-input2 {
  width: 100% !important;
  min-width: 0;
  display: block;
  box-sizing: border-box;
  margin-bottom: 12px;
  :deep(.el-textarea),
  :deep(.el-textarea__inner) {
    width: 100% !important;
    min-width: 0;
    box-sizing: border-box;
    background: #f5f6f7 !important;
    border-radius: 8px !important;
    border: none !important;
    min-height: 300px;
    height: 300px;
    resize: none;
    box-shadow: none !important;
    padding-top: 10px !important;
  }
  :deep(.el-input__count) {
    right: 12px !important;
    left: auto !important;
    bottom: 8px !important;
    background: #f5f5f5 !important;
    border-radius: 8px !important;
    color: #797979 !important;
    padding: 2px 8px !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    box-shadow: none !important;
  }
}
.section-label {
  font-size: 14px;
  color: #797979;
  margin-bottom: 6px;
  margin-top: 8px;
  text-align: left;
}
.resolution-btns {
  display: flex;
  gap: 12px;
  width: 100%;
  margin-bottom: 12px;
  .resolution-btn {
    flex: 1;
    min-width: 0;
    height: 33px;
    background: #f5f5f5;
    border-radius: 4px;
    color: #797979;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    transition: background 0.2s, color 0.2s, font-weight 0.2s;
    font-weight: 400;
    white-space: nowrap;
    &.active {
      background: rgba(46, 118, 255, 0.1);
      color: #2e76ff;
      font-weight: 400;
      border-radius: 4px;
    }
  }
}
.ratio-btns {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 11px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 8px;
  .ratio-btn {
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    padding: 0 12px;
    height: 33px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #797979;
    cursor: pointer;
    border: none;
    transition: background 0.2s, color 0.2s, font-weight 0.2s;
    font-weight: 400;
    white-space: nowrap;
    &.active {
      background: rgba(46, 118, 255, 0.1);
      color: #2e76ff;
      font-weight: 400;
      border-radius: 4px;
    }
  }
  :deep(.auto-match) {
    grid-column: 1 / -1;
  }
}
.duration-btns {
  display: flex;
  width: 100%;
  gap: 12px;
  margin-bottom: 24px;
  .duration-btn {
    flex: 1;
    min-width: 0;
    height: 33px;
    background: #f5f5f5;
    border-radius: 4px;
    color: #797979;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    transition: background 0.2s, color 0.2s, font-weight 0.2s;
    font-weight: 400;
    white-space: nowrap;
    &.active {
      background: rgba(46, 118, 255, 0.1);
      color: #2e76ff;
      font-weight: 400;
      border-radius: 4px;
    }
  }
}
.generate-btn {
  width: 100%;
  height: 48px;
  background: #2e76ff !important;
  color: #fff !important;
  font-size: 18px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  box-shadow: none;
  margin-top: 16px;
}
</style>
