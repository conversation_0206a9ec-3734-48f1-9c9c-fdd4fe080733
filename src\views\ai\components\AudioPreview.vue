<template>
  <div v-if="show" class="preview-overlay" @click="handleClose">
    <div class="preview-content" @click.stop>
      <img
        src="@/assets/images/close-tips.png"
        @click="handleClose"
        alt="关闭"
        class="close-icon"
        style="width: 36px; height: 36px"
      />
      <div class="audio-player">
        <img
          :src="
            playing ? require('@/assets/images/pause22.png') : require('@/assets/images/play11.png')
          "
          @click="togglePlay"
          class="play-icon"
          alt="播放/暂停"
        />
        <div class="progress-bar-container" @click="handleProgressClick">
          <div class="progress-bar-bg"></div>
          <div class="progress-bar-fill" :style="{ width: progressPercentage + '%' }"></div>
          <div class="progress-bar-thumb" :style="{ left: progressPercentage + '%' }"></div>
        </div>
        <span class="audio-time">{{ formatTime(currentTime) }}/{{ formatTime(duration) }}</span>
      </div>
      <div class="preview-actions">
        <button class="preview-btn save-btn" @click="handleSave">保存素材</button>
        <button class="preview-btn delete-btn" @click="handleDelete">删除</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onUnmounted } from 'vue';

const props = defineProps<{
  show: boolean;
  audioUrl: string;
}>();
const emit = defineEmits(['close', 'save', 'delete']);

const audio = ref<HTMLAudioElement | null>(null);
const playing = ref(false);
const currentTime = ref(0);
const duration = ref(0);

// 计算进度条百分比
const progressPercentage = computed(() => {
  if (!duration.value) return 0;
  return (currentTime.value / duration.value) * 100;
});

// 处理进度条点击
const handleProgressClick = (event: MouseEvent) => {
  const container = event.currentTarget as HTMLElement;
  const rect = container.getBoundingClientRect();
  const clickPosition = event.clientX - rect.left;
  const percentage = clickPosition / rect.width;

  if (audio.value && duration.value) {
    const newTime = percentage * duration.value;
    audio.value.currentTime = Math.max(0, Math.min(newTime, duration.value));
    currentTime.value = audio.value.currentTime;
  }
};

watch(
  () => props.audioUrl,
  (url) => {
    if (audio.value) {
      audio.value.pause();
      playing.value = false;
      audio.value = null;
    }
    if (url) {
      audio.value = new Audio(url);
      audio.value.addEventListener('loadedmetadata', () => {
        duration.value = audio.value?.duration || 0;
      });
      audio.value.addEventListener('timeupdate', () => {
        currentTime.value = audio.value?.currentTime || 0;
      });
      audio.value.addEventListener('ended', () => {
        playing.value = false;
        currentTime.value = 0;
      });
      audio.value.addEventListener('error', () => {
        playing.value = false;
        console.error('Audio playback error');
      });
    }
  },
  { immediate: true }
);

const togglePlay = async () => {
  if (!audio.value) return;
  try {
    if (playing.value) {
      await audio.value.pause();
      playing.value = false;
    } else {
      await audio.value.play();
      playing.value = true;
    }
  } catch (error) {
    console.error('Error toggling play state:', error);
    playing.value = false;
  }
};

const handleClose = () => {
  if (audio.value) audio.value.pause();
  emit('close');
};

const handleSave = () => emit('save');
const handleDelete = () => emit('delete');

const formatTime = (s: number) => {
  const m = Math.floor(s / 60);
  const ss = Math.floor(s % 60);
  return `${m}:${ss.toString().padStart(2, '0')}`;
};

onUnmounted(() => {
  if (audio.value) audio.value.pause();
});
</script>

<style scoped lang="less">
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.preview-content {
  max-width: 80%;
  min-width: 420px;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  position: relative;
}

.close-icon {
  position: absolute;
  top: 0px;
  right: -60px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 1001;
  transition: opacity 0.2s ease;
  &:hover {
    opacity: 0.7;
  }
}

.audio-player {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 474px;
  height: 138px;
  background: url('@/assets/images/yinping.png') no-repeat center;
  background-size: 100% 100%;
  border-radius: 10px;
  padding: 24px 24px 16px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}
.play-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  margin-right: 8px;
}
.audio-slider {
  flex: 1;
  margin: 0 12px;
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: rgba(41, 115, 255, 0.2); // 未播放部分的背景色
  border-radius: 33px;
  outline: none;
  cursor: pointer;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: #2973ff;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.2);
    }
  }

  &::-webkit-slider-runnable-track {
    height: 8px;
    background: linear-gradient(
      to right,
      #2973ff 0%,
      #2973ff calc((var(--value) - var(--min)) / (var(--max) - var(--min)) * 100%),
      rgba(41, 115, 255, 0.2) calc((var(--value) - var(--min)) / (var(--max) - var(--min)) * 100%)
    );
    border-radius: 33px;
  }

  // Firefox styles
  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #2973ff;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.2);
    }
  }

  &::-moz-range-track {
    height: 8px;
    background: rgba(41, 115, 255, 0.2);
    border-radius: 33px;
  }

  &::-moz-range-progress {
    height: 8px;
    background: #2973ff;
    border-radius: 33px;
  }
}

.audio-time {
  min-width: 70px;
  text-align: right;
  color: #222;
  font-size: 15px;
}
.preview-actions {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-top: 12px;
  .preview-btn {
    width: 120px;
    height: 40px;
    border-radius: 4px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s ease;
    &.save-btn {
      background: #2e76ff;
      color: #ffffff;
    }
    &.delete-btn {
      background: #c9453e;
      color: #ffffff;
    }
    &:hover {
      opacity: 0.8;
    }
  }
}

.progress-bar-container {
  flex: 1;
  margin: 0 12px;
  height: 8px;
  position: relative;
  cursor: pointer;
  border-radius: 33px;
  overflow: visible; // 修改为visible以显示滑块
  width: 262px;
}

.progress-bar-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  box-shadow: inset 0px 0px 5px 0px rgba(0, 0, 0, 0.25);
  border-radius: 33px;
}

.progress-bar-fill {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background: #2973ff;
  border-radius: 33px;
  transition: width 0.1s linear;
}

.progress-bar-thumb {
  position: absolute;
  width: 6px;
  height: 18px;
  background: #2e76ff;
  border-radius: 27px;
  top: 50%;
  transform: translate(-50%, -50%);
  left: calc(v-bind(progressPercentage) * 1%);
  pointer-events: none; // 防止滑块影响点击事件
  transition: left 0.1s linear;
}

.play-icon {
  width: 64px;
  height: 64px;
  cursor: pointer;
  margin-right: 0px;
  transition: opacity 0.2s;

  &:hover {
    opacity: 0.8;
  }
}
</style>
