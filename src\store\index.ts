import { createStore } from 'vuex';

/**
 * 存储数据接口 - 定义用户存储相关的数据结构
 */
interface StorageData {
  home: {
    userdSceneNum: number; // 用户已使用场景数量
    packageSceneNum: number; // 套餐场景数量
    spaceArSceneUsedNum: number; // 空间AR场景已使用数量
    spaceArScenePackageNum: number; // 空间AR场景套餐数量
    planeArSceneUsedNum: number; // 平面AR场景已使用数量
    planeArScenePackageNum: number; // 平面AR场景套餐数量
    materialUsedStorage?: number; // 材料已使用存储空间
    materialPackageStorage?: number; // 材料套餐存储空间
    spaceUseNum?: number; // 空间使用数量
    packageNum?: number; // 套餐数量
    singleUploadNum?: number; // 单次上传数量
  };
  material: {
    userUsedStorage: number; // 用户已使用存储空间
    userPackageStorage: number; // 用户套餐存储空间
  };
  space: {
    spaceUseNum: number; // 空间使用数量
    packageNum: number; // 套餐数量
    singleUploadNum: number; // 单次上传数量
  };
}

/**
 * 全局状态接口 - 定义Vuex store的完整状态结构
 */
interface State {
  // ==================== 基础UI状态 ====================
  homeListType: number; // 首页列表显示类型 (1: 列表, 2: 卡片)
  stateStore: {
    activeArea: number; // 当前激活的互动区域索引
    activeRoute: number; // 当前激活的路线索引
  };
  activeAreaIndex: number; // 当前激活区域索引
  deleteArea: {
    dataIndex: number; // 删除区域的数据索引
    type: string; // 删除区域类型
  };

  // ==================== 编辑相关状态 ====================
  editSceneData: any; // 编辑场景数据
  isPlanStyle: boolean; // 是否为平面样式
  activeMaterial: string; // 当前激活的材料
  operateType: string; // 操作类型 (移动、旋转、缩放等)
  activeAreaUuid: string; // 当前激活区域的UUID
  deleteData: any; // 删除数据
  currentDeleteData: any; // 当前删除数据
  randerTotal: number; // 渲染总数
  showSaveTips: boolean; // 是否显示保存提示
  showAreaUuidData: any; // 显示区域UUID数据
  totalSize: number; // 总大小
  uploadStep: number; // 上传步骤状态 (0: 未开始, 1: 上传中, 2: 解压中, 3: 生成数据中, 4: 上传到Immersal)

  // ==================== 空间上传任务状态 ====================
  spaceUploadTask: {
    isCompleted: boolean; // 任务是否完成
    isSuccess: boolean; // 任务是否成功
    errorMessage: string | null; // 错误消息
    taskId: string | null; // 任务ID
    taskName: string | null; // 任务名称
    currentUploadMethod: string | null; // 当前上传方式 (polycam/b2g/e57)
    taskProccessMap: any; // 任务进度映射
  };

  // ==================== 功能状态 ====================
  storageData: StorageData; // 存储数据
  isOperation: number; // 操作状态
  addProjectFlag: boolean; // 添加项目标志
  showTips: string; // 显示提示信息
  pageH: number; // 页面高度
  hasSpaceAr: boolean; // 是否有空间AR

  // ==================== 从Redux迁移的状态 ====================
  isWorkAuth: boolean; // 工作权限状态
  currentData: any; // 当前数据
  sceneId: string; // 场景ID
  interactionId: string; // 交互ID
  saveBehavior: boolean; // 保存行为状态
  userType: number; // 用户类型
  userInfo: any; // 用户信息
  experienceScene: number; // 体验场景数量

  // ==================== 用户相关状态 ====================
  profilePic: string; // 用户头像
  profilePicture: string; // 用户图片
  activeSceneTab: any; // 激活的场景标签
  userDto: any; // 用户DTO
  packageInfoDto: any; // 套餐信息DTO
  userBindPackageDto: any; // 用户绑定套餐DTO

  // ==================== 加载状态 ====================
  isDragLoading: boolean; // 拖拽加载状态
  isFinishModel: boolean; // 模型完成状态
  isRequesting: boolean; // 请求状态
}

/**
 * Vuex Store 配置
 * 统一管理应用全局状态
 */
export default createStore<State>({
  state: {
    // ==================== 基础UI状态初始值 ====================
    homeListType: 1, // 默认列表形式
    stateStore: {
      activeArea: 0, // 默认激活区域0
      activeRoute: 0, // 默认激活路线0
    },
    activeAreaIndex: 0, // 默认激活区域索引
    deleteArea: { dataIndex: 0, type: '' }, // 默认删除区域
    editSceneData: null, // 编辑场景数据初始为空
    isPlanStyle: false, // 默认非平面样式
    activeMaterial: '', // 默认无激活材料
    operateType: '移动', // 默认操作类型为移动
    activeAreaUuid: '', // 默认无激活区域UUID
    deleteData: {}, // 删除数据初始为空对象
    currentDeleteData: {}, // 当前删除数据初始为空对象
    randerTotal: 0, // 渲染总数初始为0
    showSaveTips: true, // 默认显示保存提示
    showAreaUuidData: null, // 区域UUID数据初始为空
    totalSize: 0, // 总大小初始为0
    uploadStep: 0, // 上传步骤初始为0

    // ==================== 空间上传任务状态初始值 ====================
    spaceUploadTask: {
      isCompleted: false, // 任务未完成
      isSuccess: false, // 任务未成功
      errorMessage: null, // 无错误消息
      taskId: null, // 无任务ID
      taskName: null, // 无任务名称
      currentUploadMethod: null, // 无当前上传方式
      taskProccessMap: null, // 无任务进度映射
    },

    // ==================== 功能状态初始值 ====================
    storageData: {
      home: {
        userdSceneNum: 0, // 用户已使用场景数量
        packageSceneNum: 0, // 套餐场景数量
        spaceArSceneUsedNum: 0, // 空间AR场景已使用数量
        spaceArScenePackageNum: 0, // 空间AR场景套餐数量
        planeArSceneUsedNum: 0, // 平面AR场景已使用数量
        planeArScenePackageNum: 0, // 平面AR场景套餐数量
      },
      material: {
        userUsedStorage: 0, // 用户已使用存储空间
        userPackageStorage: 0, // 用户套餐存储空间
      },
      space: {
        spaceUseNum: 0, // 空间使用数量
        packageNum: 0, // 套餐数量
        singleUploadNum: 0, // 单次上传数量
      },
    },
    isOperation: 0, // 操作状态初始为0
    addProjectFlag: false, // 添加项目标志初始为false
    showTips: '', // 提示信息初始为空
    pageH: 0, // 页面高度初始为0
    hasSpaceAr: false, // 空间AR初始为false

    isWorkAuth: false, // 工作权限初始为false
    currentData: null, // 当前数据初始为null
    sceneId: '', // 场景ID初始为空
    interactionId: '', // 交互ID初始为空
    saveBehavior: false, // 保存行为初始为false
    userType: 2, // 用户类型默认为2
    userInfo: null, // 用户信息初始为null
    experienceScene: 0, // 体验场景数量初始为0

    // ==================== 用户相关状态初始值 ====================
    profilePic: '', // 用户头像初始为空
    profilePicture: '', // 用户图片初始为空
    activeSceneTab: null, // 激活场景标签初始为null
    userDto: null, // 用户DTO初始为null
    packageInfoDto: null, // 套餐信息DTO初始为null
    userBindPackageDto: null, // 用户绑定套餐DTO初始为null

    // ==================== 加载状态初始值 ====================
    isDragLoading: false, // 拖拽加载初始为false
    isFinishModel: false, // 模型完成初始为false
    isRequesting: false, // 请求状态初始为false
  },

  /**
   * Getters - 计算属性，用于获取状态
   * 提供从Redux迁移的getter方法
   */
  getters: {
    getIsWorkAuth: (state) => state.isWorkAuth, // 获取工作权限状态
    getCurrentData: (state) => state.currentData, // 获取当前数据
    getSceneId: (state) => state.sceneId, // 获取场景ID
    getInteractionId: (state) => state.interactionId, // 获取交互ID
    getSaveBehavior: (state) => state.saveBehavior, // 获取保存行为状态
    getUserType: (state) => state.userType, // 获取用户类型
    getUserInfo: (state) => state.userInfo, // 获取用户信息
    getExperienceScene: (state) => state.experienceScene, // 获取体验场景数量
    getProfilePic: (state) => state.profilePic, // 获取用户头像
    getActiveSceneTab: (state) => state.activeSceneTab, // 获取激活场景标签
    getUserDto: (state) => state.userDto, // 获取用户DTO
    getPackageInfoDto: (state) => state.packageInfoDto, // 获取套餐信息DTO
    getUserBindPackageDto: (state) => state.userBindPackageDto, // 获取用户绑定套餐DTO
  },

  /**
   * Mutations - 同步状态修改方法
   * 直接修改state，只能通过commit调用
   */
  mutations: {
    /**
     * 重置编辑数据 - 清空所有编辑相关状态
     */
    resetEditData(state) {
      state.stateStore = { activeArea: -1, activeRoute: -1 };
      state.activeAreaIndex = -1;
      state.editSceneData = {};
      state.isPlanStyle = true;
      state.activeMaterial = '';
      state.operateType = '移动';
      state.activeAreaUuid = '';
      state.deleteData = {};
      state.currentDeleteData = {};
      state.randerTotal = 0;
      state.showSaveTips = true;
      state.showAreaUuidData = null;
      state.totalSize = 0;
    },

    // ==================== 基础UI状态mutations ====================
    setHomeListType(state, type) {
      state.homeListType = type; // 设置首页列表类型
    },
    setStateStore(state, data) {
      state.stateStore = data; // 设置状态存储
    },
    setActiveAreaIndex(state, index) {
      state.activeAreaIndex = index; // 设置激活区域索引
    },
    setDeleteArea(state, data) {
      state.deleteArea = data; // 设置删除区域
    },
    setEditSceneData(state, data) {
      state.editSceneData = data; // 设置编辑场景数据
    },
    setIsPlanStyle(state, data) {
      state.isPlanStyle = data; // 设置平面样式
    },
    setStorageData(state, data) {
      state.storageData = data; // 设置存储数据
    },
    setIsOperation(state, data) {
      state.isOperation = data; // 设置操作状态
    },
    setAddProjectFlag(state, data) {
      state.addProjectFlag = data; // 设置添加项目标志
    },
    setShowTips(state, data) {
      state.showTips = data; // 设置提示信息
    },
    setPageH(state, data) {
      state.pageH = data; // 设置页面高度
    },

    setIsWorkAuth(state, val: boolean) {
      state.isWorkAuth = val; // 设置工作权限状态
    },
    setCurrentData(state, val: any) {
      state.currentData = val; // 设置当前数据
    },
    setSceneId(state, id: string) {
      state.sceneId = id; // 设置场景ID
    },
    setInteractionId(state, id: string) {
      state.interactionId = id; // 设置交互ID
    },
    setSaveBehavior(state) {
      state.saveBehavior = !state.saveBehavior; // 切换保存行为状态
    },
    setUserType(state, type: number) {
      state.userType = type; // 设置用户类型
    },
    setUserInfo(state, info: any) {
      state.userInfo = info; // 设置用户信息
    },
    setExperienceScene(state, length: number) {
      state.experienceScene = length; // 设置体验场景数量
    },

    // ==================== 用户相关状态mutations ====================
    setProfilePic(state, pic: string) {
      state.profilePic = pic; // 设置用户头像
    },
    setProfilePicture(state, picture: string) {
      state.profilePicture = picture; // 设置用户图片
    },
    setActiveSceneTab(state, tab: any) {
      state.activeSceneTab = tab; // 设置激活场景标签
    },
    setUserDto(state, userDto: any) {
      state.userDto = userDto; // 设置用户DTO
    },
    setPackageInfoDto(state, packageInfo: any) {
      state.packageInfoDto = packageInfo; // 设置套餐信息DTO
    },
    setUserBindPackageDto(state, bindPackage: any) {
      state.userBindPackageDto = bindPackage; // 设置用户绑定套餐DTO
    },

    // ==================== 加载状态mutations ====================
    setIsDragLoading(state, loading: boolean) {
      state.isDragLoading = loading; // 设置拖拽加载状态
    },
    setIsFinishModel(state, finished: boolean) {
      state.isFinishModel = finished; // 设置模型完成状态
    },
    setIsRequesting(state, requesting: boolean) {
      state.isRequesting = requesting; // 设置请求状态
    },
  },

  /**
   * Actions - 异步状态修改方法
   * 可以包含异步操作，通过dispatch调用
   */
  actions: {
    /**
     * 重置编辑数据 - 调用对应的mutation
     */
    resetEditData({ commit }) {
      commit('resetEditData');
    },

    updateIsWorkAuth({ commit }, val: boolean) {
      commit('setIsWorkAuth', val); // 更新工作权限状态
    },
    updateCurrentData({ commit }, val: any) {
      commit('setCurrentData', val); // 更新当前数据
    },
    updateSceneId({ commit }, id: string) {
      commit('setSceneId', id); // 更新场景ID
    },
    updateInteractionId({ commit }, id: string) {
      commit('setInteractionId', id); // 更新交互ID
    },
    toggleSaveBehavior({ commit }) {
      commit('setSaveBehavior'); // 切换保存行为状态
    },
    updateUserType({ commit }, type: number) {
      commit('setUserType', type); // 更新用户类型
    },
    updateUserInfo({ commit }, info: any) {
      commit('setUserInfo', info); // 更新用户信息
    },
    updateExperienceScene({ commit }, length: number) {
      commit('setExperienceScene', length); // 更新体验场景数量
    },

    // ==================== 用户相关状态actions ====================
    updateProfilePic({ commit }, pic: string) {
      commit('setProfilePic', pic); // 更新用户头像
    },
    updateProfilePicture({ commit }, picture: string) {
      commit('setProfilePicture', picture); // 更新用户图片
    },
    updateActiveSceneTab({ commit }, tab: any) {
      commit('setActiveSceneTab', tab); // 更新激活场景标签
    },
    updateUserDto({ commit }, userDto: any) {
      commit('setUserDto', userDto); // 更新用户DTO
    },
    updatePackageInfoDto({ commit }, packageInfo: any) {
      commit('setPackageInfoDto', packageInfo); // 更新套餐信息DTO
    },
    updateUserBindPackageDto({ commit }, bindPackage: any) {
      commit('setUserBindPackageDto', bindPackage); // 更新用户绑定套餐DTO
    },

    // ==================== 加载状态actions ====================
    updateIsDragLoading({ commit }, loading: boolean) {
      commit('setIsDragLoading', loading); // 更新拖拽加载状态
    },
    updateIsFinishModel({ commit }, finished: boolean) {
      commit('setIsFinishModel', finished); // 更新模型完成状态
    },
    updateIsRequesting({ commit }, requesting: boolean) {
      commit('setIsRequesting', requesting); // 更新请求状态
    },

    // ==================== 用户数据管理actions ====================
    /**
     * 清理用户数据（登出时使用）
     */
    clearUserData({ commit }) {
      commit('setUserType', null);
      commit('setUserInfo', null);
      commit('setUserDto', null);
      commit('setProfilePic', '');
      commit('setProfilePicture', '');
      commit('setPackageInfoDto', null);
      commit('setUserBindPackageDto', null);
    },
  },

  modules: {}, // 模块化配置，当前为空

  plugins: [],
});
