<template>
  <div class="template-square">
    <!-- 页面头部组件 -->
    <TemplateHeader
      :is-admin="isAdmin"
      :is-sort-mode="isSortMode"
      v-model:active-tab-value="activeTabValue"
      :platform-tabs="platformTabs"
      @title-click="handleTitleClick"
      @sort-click="handleSortClick" />

    <!-- 普通模式布局 -->
    <TemplateNormalMode
      v-if="!isSortMode"
      :template-list="data"
      :ar-top-menu-list="arTopMenuList"
      :ar-all-menu-list="arMenuList"
      :active-top-menu-tab="activeTopMenuTab"
      :active-all-menu-tab="activeMenuTab"
      :dom-mask-indexs="domMaskIndexs"
      :show-edit-button="userType === 1"
      @active-top-tab="activeTopTab"
      @active-all-tab="activeTab"
      @mouseenter="mouseenterTemp"
      @mouseleave="mouseoverTemp"
      @edit="editTemplate"
      @experience="startLearn"
      @create-similar="openCreateScene" />

    <!-- 排序模式布局 -->
    <TemplateSortMode
      v-else
      :active-tab="activeTabValue"
      v-model:recommend-templates="recommendedData"
      v-model:all-templates="allData"
      v-model:banner-templates="bannerTemplateData"
      v-model:miniprogram-list="miniprogramListData"
      :recommend-menu-list="recommendMenuList"
      :all-menu-list="allMenuList"
      :dom-mask-indexs="domMaskIndexs"
      :show-edit-button="userType === 1"
      @mouseenter="mouseenterTemp"
      @mouseleave="mouseoverTemp"
      @edit="editTemplate"
      @experience="startLearn"
      @create-similar="openCreateScene"
      @add-to-recommend="handleAddToRecommend"
      @remove-recommend="handleRemoveRecommend"
      @edit-banner="handleEditBanner"
      @remove-banner="handleRemoveBanner"
      @remove-miniprogram-list="handleRemoveMiniprogramList"
      @recommend-drag-end="handleRecommendDragEnd"
      @banner-drag-end="handleBannerDragEnd"
      @miniprogram-list-drag-end="handleMiniprogramListDragEnd"
      @active-recommend-tab="activeRecommendTab"
      @active-all-tab="activeAllTab"
      @refresh-miniprogram-list="handleRefreshMiniprogramList" />
  </div>
  <create-scene
    v-if="modalShow"
    :modal-show="modalShow"
    :handle-hide="handleHide"
    :requestCopyWxScene="requestCopyWxScene"
    :sceneType="sceneType"
    :isCopyTemp="isCopyTemp"
    :hasRoomScene="hasRoomScene"
    :hasPlaneScene="hasPlaneScene"></create-scene>
  <QrCode
    v-if="isOpenQR"
    :hide-add-mask="hideQrCode"
    :codeUrl="codeUrl"
    :name="qrName"
    :hoverCurrentTemplate="hoverCurrentTemplate"
    :sceneDataPlatform="sceneDataPlatform"
    :activeTempSceneId="activeTempSceneId"
    :sceneType="sceneType"></QrCode>
  <SetTemplate
    v-if="isOpenSetTemp"
    ref="setTemplateRef"
    :hide-add-mask="hideSetTemp"
    @confirm="confirm"
    @update="update"
    :currentScene="hoverCurrentScene"></SetTemplate>
</template>

<script lang="ts" setup>
// @ts-nocheck
import '@/assets/media/style.css';
import CreateScene from '@/views/scene/create/index.vue';
import SetTemplate from '@/components/experience/create/SetTemplate.vue';
import QrCode from '@/components/experience/create/QrCode.vue';
import TemplateCard from '@/components/TemplateCard.vue';
import TabsBar from '@/components/TabsBar.vue';
import { onMounted, ref, watch, computed, nextTick } from 'vue';
import { arMenus, scenePlatformTabs } from '@/config';
import { useRouter } from 'vue-router';
import draggable from 'vuedraggable';

import {
  queryTemplateScene,
  updateTemplateInfo,
  getWxAppQrCode,
  setSceneAsTemplate,
  copyWxScene,
  getOrgnizationPackage,
  setWebRecommend,
  orderWebTemplate,
  removeRecommend,
  removeWxRecommend,
  removeBannerRecommend,
  orderBannerTemplate,
} from '@/api';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import TemplateHeader from '@/views/template/components/TemplateHeader.vue';
import TemplateNormalMode from '@/views/template/components/TemplateNormalMode.vue';
import TemplateSortMode from '@/views/template/components/TemplateSortMode.vue';
import { getOssAccessPath } from '@/api/modules/storage';

const showDelBtn = ref(false);
const deleteContent = ref({ title: '', content: '' });
const firstTopTab = { name: '所有平台', sceneType: -1, scenePlatform: -1 };
const firstTab = { name: '所有类型', sceneType: -1, scenePlatform: -1 };
const arMenuList = ref([firstTab, ...arMenus]);
const arTopMenuList = ref([firstTopTab, ...scenePlatformTabs]);
const activeMenuTab = ref(firstTab);
const activeTopMenuTab = ref(firstTopTab);
const platformIndexValue = ref(-1); // 初始化为-1，表示"所有平台"
const data = ref([]);
// 排序模式专用数据
const recommendedData = ref([]);
const allData = ref([]);
const activeTempSceneId = ref(0);
const sceneDataPlatform = ref(3);
const domMaskIndexs = ref([]);
const modalShow = ref(false);
const sceneType = ref(1);
const qrName = ref('');
// 从store中获取用户类型，而不是硬编码
const userType = computed(() => store.state.userDto?.userType || 5); // 默认为普通用户

// 排序模式下的菜单列表（排除"所有类型"选项）
const sortModeMenuList = computed(() => {
  return arMenuList.value.filter((menu) => menu.name !== '所有类型');
});

// 推荐模板独立的菜单状态
const recommendMenuList = ref([]);
// 所有模板独立的菜单状态
const allMenuList = ref([]);

// 初始化独立的菜单状态
const initSortModeMenus = () => {
  const baseMenus = arMenus.map((menu) => ({ ...menu, isActive: false }));

  // 推荐模板菜单：默认激活第一个（空间AR）
  recommendMenuList.value = baseMenus.map((menu, index) => ({
    ...menu,
    isActive: index === 0,
  }));

  // 所有模板菜单：默认激活第一个（空间AR）
  allMenuList.value = baseMenus.map((menu, index) => ({
    ...menu,
    isActive: index === 0,
  }));

  console.log('🔧 初始化排序模式菜单:', {
    arMenus,
    recommendMenuList: recommendMenuList.value,
    allMenuList: allMenuList.value,
    defaultActiveMenu: arMenus[0], // 应该是"空间AR"，sceneType: 1
  });
};
const hoverCurrentTemplate = ref();
const isOpenQR = ref(false);
const isOpenSetTemp = ref(false);
const hoverCurrentScene = ref();
const setTemplateRef = ref();
const codeUrl = ref('');
const isCopyTemp = ref(false);
let tempCopyScene = {};
const router = useRouter();
const store = useStore();
const hasRoomScene = ref(true);
const hasPlaneScene = ref(true);

// TabsBar相关数据
const activeTabValue = ref('web');
const platformTabs = [
  { label: '网页端', value: 'web' },
  { label: '小程序端', value: 'miniprogram' },
];

// 排序状态管理
const isSortMode = ref(false);

// 拖拽状态管理
const isDragging = ref(false);

// 小程序端相关数据
const bannerTemplateData = ref([
  { id: 1, isHover: false },
  { id: 2, isHover: false },
  { id: 3, isHover: false },
]);
const miniprogramListData = ref([]);

// 删除确认相关数据
const currentDeleteTemplate = ref(null);
const currentDeleteType = ref('');

// 小程序端拖拽状态
const isBannerDragging = ref(false);
const isMiniprogramListDragging = ref(false);

// 超管权限检查 - 只有超级管理员(userType=1)可以使用排序功能
const isAdmin = computed(() => {
  const userDto = store.state.userDto;
  const userType = userDto?.userType;
  return userType === 1; // 只有超级管理员可以使用排序功能
});

// 设置模板排序按钮点击事件
const handleSortClick = () => {
  isSortMode.value = !isSortMode.value;
  console.log('设置模板排序', isSortMode.value ? '进入排序模式' : '退出排序模式');

  if (isSortMode.value) {
    // 进入排序模式，初始化独立菜单状态并获取模板数据
    initSortModeMenus();

    // 根据当前平台获取对应的模板数据
    if (activeTabValue.value === 'miniprogram') {
      requestGetMiniprogramTemplates();
    } else {
      requestGetSortModeTemplates();
    }
  }
};

// 获取排序模式下的模板数据
const requestGetSortModeTemplates = async () => {
  try {
    // 获取当前激活的推荐模板菜单项（默认为"空间AR"）
    const activeRecommendMenu =
      recommendMenuList.value.find((menu) => menu.isActive) || recommendMenuList.value[0];

    // 获取推荐模板
    const recommendParams = {
      sceneType: activeRecommendMenu.sceneType,
      scenePlatform: activeTopMenuTab.value.scenePlatform,
    };

    console.log('推荐模板请求参数:', {
      activeRecommendMenu,
      recommendParams,
    });

    // 根据当前平台选择推荐参数
    if (activeTabValue.value === 'miniprogram') {
      recommendParams.wxRecommend = 1;
    } else {
      recommendParams.recommend = 1;
    }

    const recommendRes = await queryTemplateScene(recommendParams);
    console.log('推荐模板API响应:', {
      code: recommendRes.code,
      codeType: typeof recommendRes.code,
      dataLength: recommendRes.data ? recommendRes.data.length : 0,
    });
    if (
      recommendRes.code == '200' ||
      recommendRes.code == 200 ||
      recommendRes.code === '200' ||
      recommendRes.code === 200
    ) {
      recommendedData.value = recommendRes.data || [];
      console.log('✅ 推荐模板数据设置成功:', recommendedData.value);
    } else {
      console.error('❌ 推荐模板API响应码不匹配:', recommendRes.code);
    }

    // 获取当前激活的所有模板菜单项（默认为"空间AR"）
    const activeAllMenu = allMenuList.value.find((menu) => menu.isActive) || allMenuList.value[0];

    // 获取所有模板
    const allParams = {
      sceneType: activeAllMenu.sceneType,
      scenePlatform: activeTopMenuTab.value.scenePlatform,
    };

    console.log('所有模板请求参数:', {
      activeAllMenu,
      allParams,
    });

    const allRes = await queryTemplateScene(allParams);
    console.log('所有模板API响应:', {
      code: allRes.code,
      codeType: typeof allRes.code,
      dataLength: allRes.data ? allRes.data.length : 0,
    });
    if (
      allRes.code == '200' ||
      allRes.code == 200 ||
      allRes.code === '200' ||
      allRes.code === 200
    ) {
      allData.value = allRes.data || [];
      console.log('✅ 所有模板数据设置成功:', allData.value);
    } else {
      console.error('❌ 所有模板API响应码不匹配:', allRes.code);
    }
  } catch (error) {
    console.error('获取排序模式模板数据失败:', error);
    ElMessage.error('获取模板数据失败');
  }
};

// 获取小程序端模板数据
const requestGetMiniprogramTemplates = async () => {
  try {
    // 获取小程序端列表模板（使用 wxRecommend=1）
    const listParams = {
      wxRecommend: 1,
    };

    const listRes = await queryTemplateScene(listParams);
    if (
      listRes.code == '200' ||
      listRes.code == 200 ||
      listRes.code === '200' ||
      listRes.code === 200
    ) {
      miniprogramListData.value = listRes.data || [];
    } else {
      console.error('❌ 小程序端列表模板API响应码不匹配:', listRes.code);
    }
  } catch (error) {
    ElMessage.error('获取小程序端模板数据失败');
  }
};

// 推荐模板部分的菜单点击事件
const activeRecommendTab = (menu) => {
  // 重置推荐模板菜单的激活状态
  recommendMenuList.value.forEach((item) => {
    item.isActive = false;
  });

  // 设置当前菜单为激活状态
  menu.isActive = true;

  // 获取推荐模板数据
  const params = {
    sceneType: menu.sceneType,
    scenePlatform: activeTopMenuTab.value.scenePlatform,
  };

  // 根据当前平台选择推荐参数
  if (activeTabValue.value === 'miniprogram') {
    params.wxRecommend = 1;
  } else {
    params.recommend = 1;
  }

  queryTemplateScene(params)
    .then((res) => {
      if (res.code == '200' || res.code == 200 || res.code === '200' || res.code === 200) {
        recommendedData.value = res.data || [];
      } else {
        console.error('❌ 推荐模板菜单点击API响应码不匹配:', res.code);
      }
    })
    .catch((error) => {
      console.error('获取推荐模板失败:', error);
    });
};

// 所有模板部分的菜单点击事件
const activeAllTab = (menu) => {
  // 重置所有模板菜单的激活状态
  allMenuList.value.forEach((item) => {
    item.isActive = false;
  });

  // 设置当前菜单为激活状态
  menu.isActive = true;

  // 获取所有模板数据
  const params = {
    sceneType: menu.sceneType,
    scenePlatform: activeTopMenuTab.value.scenePlatform,
  };

  queryTemplateScene(params)
    .then((res) => {
      if (res.code == '200' || res.code == 200 || res.code === '200' || res.code === 200) {
        allData.value = res.data || [];
      } else {
        console.error('❌ 所有模板菜单点击API响应码不匹配:', res.code);
      }
    })
    .catch((error) => {
      console.error('获取所有模板失败:', error);
    });
};

// 模板管理标题点击事件 - 返回初始状态
const handleTitleClick = () => {
  if (isSortMode.value) {
    isSortMode.value = false;
  }
};

// 添加至推荐模版事件处理
const handleAddToRecommend = async (templateData) => {
  try {
    // 调用设置网页端推荐模板API
    const response = await setWebRecommend(templateData.id);

    if (response.code === 200 || response.code === '200') {
      ElMessage.success(`推荐模版添加完成`);

      // 可选：刷新推荐模板数据
      if (isSortMode.value) {
        requestGetSortModeTemplates();
      }
    } else {
      console.error('设置推荐模板失败:', response);
      ElMessage.error('添加至推荐模版失败，请重试');
    }
  } catch (error) {
    console.error('设置推荐模板API调用失败:', error);
    ElMessage.error('添加至推荐模版失败，请重试');
  }
};

// 删除推荐模版事件处理
const handleRemoveRecommend = async (templateData) => {
  console.log('🔍 handleRemoveRecommend 被调用，templateData:', templateData);
  // 设置确认弹窗内容
  store.state.showTips = '确认将当前模版从推荐列表中移除？';
  showDelBtn.value = true;
  deleteContent.value = {
    title: '确认移除',
    content: '确认将当前模版从推荐列表中移除？',
  };
  // 保存当前要删除的模板数据
  currentDeleteTemplate.value = templateData;
  currentDeleteType.value = 'web';
  console.log('✅ 设置完成，currentDeleteTemplate.value:', currentDeleteTemplate.value);
};

// 拖拽开始事件
const onDragStart = (evt) => {
  isDragging.value = true;

  // 清除所有推荐模板的 hover 状态，防止视频预览显示
  recommendedData.value.forEach((item) => {
    item.isHover = false;
  });
};

// 拖拽结束事件
const onDragEnd = async (evt) => {
  isDragging.value = false;

  // 如果位置没有改变，不需要调用API
  if (evt.oldIndex === evt.newIndex) {
    return;
  }

  try {
    // 生成排序数据
    const orderData = recommendedData.value.map((item, index) => ({
      firstData: item.id,
      secondData: index,
    }));

    // 调用排序API
    const response = await orderWebTemplate(orderData);

    if (response.code === 200 || response.code === '200') {
      ElMessage.success('模板排序已更新');
    } else {
      ElMessage.error('排序更新失败，请重试');

      // 恢复原始顺序
      await requestGetSortModeTemplates();
    }
  } catch (error) {
    console.error('❌ 排序API调用异常:', error);
    ElMessage.error('排序更新失败，请重试');

    // 恢复原始顺序
    await requestGetSortModeTemplates();
  }
};

// Banner模板拖拽事件
const onBannerDragStart = (evt) => {
  isBannerDragging.value = true;

  // 清除所有Banner模板的 hover 状态
  bannerTemplateData.value.forEach((item) => {
    item.isHover = false;
  });
};

const onBannerDragEnd = async (evt) => {
  if (evt.oldIndex !== evt.newIndex) {
    try {
      // 生成正确格式的排序数据
      const orderData = bannerTemplateData.value.map((item, index) => ({
        firstData: item.id,
        secondData: index,
      }));

      const response = await orderBannerTemplate(orderData);

      if (response.code === 200 || response.code === '200') {
        ElMessage.success('Banner模板排序已更新');
      } else {
        ElMessage.error('排序更新失败，请重试');

        // 恢复原始顺序
        await requestGetBannerTemplates();
      }
    } catch (error) {
      console.error('Banner排序失败:', error);
      ElMessage.error('排序更新失败，请重试');

      // 恢复原始顺序
      await requestGetBannerTemplates();
    }
  }
};

// 小程序端列表模板拖拽事件
const onMiniprogramListDragStart = (evt) => {
  isMiniprogramListDragging.value = true;
  // 清除所有列表模板的 hover 状态
  miniprogramListData.value.forEach((item) => {
    item.isHover = false;
  });
};

const onMiniprogramListDragEnd = async (evt) => {
  isMiniprogramListDragging.value = false;

  // 如果位置没有改变，不需要调用API
  if (evt.oldIndex === evt.newIndex) {
    return;
  }

  try {
    // 生成排序数据
    const orderData = miniprogramListData.value.map((item, index) => ({
      firstData: item.id,
      secondData: index,
    }));

    // TODO: 这里需要调用小程序端的排序API，暂时使用通用的排序API
    const response = await orderWebTemplate(orderData);

    if (response.code === 200 || response.code === '200') {
      ElMessage.success('小程序端列表模板排序已更新');
    } else {
      console.error('❌ 小程序端列表模板排序API调用失败:', response);
      ElMessage.error('排序更新失败，请重试');

      // 恢复原始顺序
      await requestGetMiniprogramTemplates();
    }
  } catch (error) {
    console.error('❌ 小程序端列表模板排序API调用异常:', error);
    ElMessage.error('排序更新失败，请重试');

    // 恢复原始顺序
    await requestGetMiniprogramTemplates();
  }
};

// Banner模板修改事件处理
const handleEditBanner = (bannerData) => {
  // TODO: 这里可以添加修改Banner模板的逻辑
  ElMessage.success('Banner模板修改功能待实现');
};

// Banner模板删除事件处理
const handleRemoveBanner = async (bannerData) => {
  console.log('🔍 handleRemoveBanner 被调用，bannerData:', bannerData);
  store.state.showTips = '确认将当前模版从Banner推荐中移除？';
  showDelBtn.value = true;
  deleteContent.value = {
    title: '确认移除',
    content: '确认将当前模版从Banner推荐中移除？',
  };
  currentDeleteTemplate.value = bannerData;
  currentDeleteType.value = 'banner';
  console.log('✅ 设置完成，currentDeleteTemplate.value:', currentDeleteTemplate.value);
};

// 小程序端列表模板删除事件处理
const handleRemoveMiniprogramList = async (templateData) => {
  console.log('🔍 handleRemoveMiniprogramList 被调用，templateData:', templateData);
  // 设置确认弹窗内容
  store.state.showTips = '确认将当前模版从小程序端模版中移除？';
  showDelBtn.value = true;
  deleteContent.value = {
    title: '确认移除',
    content: '确认将当前模版从小程序端模版中移除？',
  };
  // 保存当前要删除的模板数据
  currentDeleteTemplate.value = templateData;
  currentDeleteType.value = 'miniprogram';
  console.log('✅ 设置完成，currentDeleteTemplate.value:', currentDeleteTemplate.value);
};

// 新的拖拽事件处理函数
const handleRecommendDragEnd = async ({ evt, orderParams }) => {
  if (evt.oldIndex !== evt.newIndex) {
    try {
      // 生成正确格式的排序数据
      const orderData = recommendedData.value.map((item, index) => ({
        firstData: item.id,
        secondData: index,
      }));

      // 调用排序API
      const response = await orderWebTemplate(orderData);

      if (response.code === 200 || response.code === '200') {
        ElMessage.success('推荐模板排序已更新');
      } else {
        console.error('❌ 排序API调用失败:', response);
        ElMessage.error('排序更新失败，请重试');

        // 恢复原始顺序
        await requestGetSortModeTemplates();
      }
    } catch (error) {
      console.error('❌ 排序API调用异常:', error);
      ElMessage.error('排序更新失败，请重试');

      // 恢复原始顺序
      await requestGetSortModeTemplates();
    }
  }
};

const handleBannerDragEnd = async (evt) => {
  if (evt.oldIndex !== evt.newIndex) {
    try {
      // 生成正确格式的排序数据
      const orderData = bannerTemplateData.value.map((item, index) => ({
        firstData: item.id,
        secondData: index,
      }));

      const response = await orderBannerTemplate(orderData);

      if (response.code === 200 || response.code === '200') {
        ElMessage.success('Banner模板排序已更新');
      } else {
        ElMessage.error('排序更新失败，请重试');

        // 恢复原始顺序
        await requestGetBannerTemplates();
      }
    } catch (error) {
      console.error('Banner排序失败:', error);
      ElMessage.error('排序更新失败，请重试');

      // 恢复原始顺序
      await requestGetBannerTemplates();
    }
  }
};

const handleMiniprogramListDragEnd = async ({ evt, orderParams }) => {
  if (evt.oldIndex !== evt.newIndex) {
    try {
      // 生成正确格式的排序数据
      const orderData = miniprogramListData.value.map((item, index) => ({
        firstData: item.id,
        secondData: index,
      }));

      // TODO: 这里需要调用小程序端的排序API，暂时使用通用的排序API
      const response = await orderWebTemplate(orderData);

      if (response.code === 200 || response.code === '200') {
        ElMessage.success('小程序端列表模板排序已更新');
      } else {
        ElMessage.error('排序更新失败，请重试');

        // 恢复原始顺序
        await requestGetMiniprogramTemplates();
      }
    } catch (error) {
      ElMessage.error('排序更新失败，请重试');

      // 恢复原始顺序
      await requestGetMiniprogramTemplates();
    }
  }
};

// 刷新小程序列表
const handleRefreshMiniprogramList = async () => {
  console.log('🔄 刷新小程序列表模板');
  await requestGetMiniprogramTemplates();
};

// 获取Banner模板数据
const requestGetBannerTemplates = async () => {
  try {
    console.log('🔄 开始获取Banner模板数据...');
    const res = await queryTemplateScene({ bannerRecommend: 1 });
    console.log('📡 Banner模板API响应:', res);

    if (res.code == '200' || res.code == 200 || res.code === '200' || res.code === 200) {
      console.log('✅ Banner模板API调用成功，原始数据:', res.data);

      // 处理图片字段
      const banners = await Promise.all(
        (res.data || []).map(async (item, index) => {
          console.log(`🔄 处理第${index + 1}个Banner模板:`, item);

          let bannerBackgroundPicUrl = '';
          let bannerCoverPicUrl = '';

          // 处理背景图
          if (item.bannerBackgroundPic) {
            console.log(`🔄 处理背景图，key: ${item.bannerBackgroundPic}`);
            try {
              const ossRes = await getOssAccessPath({ key: item.bannerBackgroundPic });
              console.log(`📡 背景图OSS响应:`, ossRes);
              if (ossRes.code === 200 || ossRes.code === '200') {
                bannerBackgroundPicUrl = ossRes.data;
                console.log('✅ Banner背景图处理成功:', bannerBackgroundPicUrl);
              } else {
                console.error('❌ Banner背景图OSS响应码错误:', ossRes.code);
              }
            } catch (error) {
              console.error('❌ Banner背景图处理失败:', error);
            }
          } else {
            console.log('⚠️ 该Banner模板没有背景图');
          }

          // 处理封面图
          if (item.bannerCoverPic) {
            console.log(`🔄 处理封面图，key: ${item.bannerCoverPic}`);
            try {
              const ossRes = await getOssAccessPath({ key: item.bannerCoverPic });
              console.log(`📡 封面图OSS响应:`, ossRes);
              if (ossRes.code === 200 || ossRes.code === '200') {
                bannerCoverPicUrl = ossRes.data;
                console.log('✅ Banner封面图处理成功:', bannerCoverPicUrl);
              } else {
                console.error('❌ Banner封面图OSS响应码错误:', ossRes.code);
              }
            } catch (error) {
              console.error('❌ Banner封面图处理失败:', error);
            }
          } else {
            console.log('⚠️ 该Banner模板没有封面图');
          }

          const processedItem = {
            ...item,
            bannerBackgroundPicUrl,
            bannerCoverPicUrl,
          };
          console.log(`✅ 第${index + 1}个Banner模板处理完成:`, processedItem);

          return processedItem;
        })
      );

      console.log('✅ Banner模板数据处理完成:', banners);
      bannerTemplateData.value = banners;
    } else {
      console.error('❌ Banner模板API响应码不匹配:', res.code);
    }
  } catch (error) {
    console.error('❌ 获取Banner模板数据失败:', error);
    ElMessage.error('获取Banner模板数据失败');
  }
};

// 确认删除模板
const confirmDeleteTemplate = async () => {
  console.log('🔍 confirmDeleteTemplate 被调用');
  console.log('🔍 currentDeleteTemplate.value:', currentDeleteTemplate.value);
  console.log('🔍 currentDeleteType.value:', currentDeleteType.value);

  if (!currentDeleteTemplate.value) {
    console.log('❌ currentDeleteTemplate.value 为空');
    return;
  }

  try {
    let response;

    if (currentDeleteType.value === 'miniprogram') {
      // 调用小程序端的移除推荐模板API
      response = await removeWxRecommend(currentDeleteTemplate.value.id);
    } else if (currentDeleteType.value === 'web') {
      // 调用网页端的移除推荐模板API
      response = await removeRecommend(currentDeleteTemplate.value.id);
    } else if (currentDeleteType.value === 'banner') {
      // 调用Banner的移除推荐模板API
      response = await removeBannerRecommend(currentDeleteTemplate.value.id);
    }

    if (response.code === 200 || response.code === '200') {
      const successMessage =
        currentDeleteType.value === 'miniprogram'
          ? '已移除小程序端列表模板'
          : currentDeleteType.value === 'banner'
          ? '已移除Banner推荐模板'
          : '已移除网页端推荐模板';
      ElMessage.success(successMessage);

      // 刷新对应的模板数据
      if (currentDeleteType.value === 'miniprogram') {
        await requestGetMiniprogramTemplates();
      } else if (currentDeleteType.value === 'web') {
        await requestGetSortModeTemplates();
      } else if (currentDeleteType.value === 'banner') {
        await requestGetBannerTemplates(); // 只刷新 Banner 区域
      }
    } else {
      ElMessage.error('移除失败，请重试');
    }
  } catch (error) {
    console.error('移除模板API调用失败:', error);
    ElMessage.error('移除失败，请重试');
  } finally {
    // 清理状态
    currentDeleteTemplate.value = null;
    currentDeleteType.value = '';
    store.state.showTips = '';
  }
};

// 监听 store 中的确认事件
watch(
  () => store.state.showTips,
  (newTips) => {
    console.log('🔍 TipsView 监听器触发，newTips:', newTips);
    if (
      newTips &&
      (newTips.includes('确认将当前模版从推荐列表中移除') ||
        newTips.includes('确认将当前模版从小程序端模版中移除') ||
        newTips.includes('确认将当前模版从Banner推荐中移除'))
    ) {
      console.log('✅ 设置确认删除事件到 store');
      // 设置确认事件到 store
      store.state.confirmDeleteTemplate = confirmDeleteTemplate;
    }
  }
);

const itemLeftWechat = computed(() => {
  return (item) => {
    return item.isLeft && item.scenePlatform == 3;
  };
});

const itemTopWechat = computed(() => {
  return (item) => {
    return item.isTop && item.scenePlatform == 3;
  };
});

const itemLeftEye = computed(() => {
  return (item) => {
    return item.isLeft && item.sceneType == 1 && item.scenePlatform != 3;
  };
});

const itemTopEye = computed(() => {
  return (item) => {
    return item.isTop && item.sceneType == 1 && item.scenePlatform != 3;
  };
});

const _sceneType = computed(() => {
  return (item) => {
    switch (item.sceneType) {
      case 1:
        return '空间AR';
      case 2:
        return '平面AR';
      case 3:
        return '图像AR';
      case 5:
        return '身体AR';
      case 6:
        return '人脸AR';
      case 7:
        return '手势AR';
      case 8:
        return '单场景AR';
    }
  };
});

const requestCopyWxScene = (sceneName) => {
  const params = {
    sourceSceneId: tempCopyScene.sceneId,
    sceneName,
  };
  copyWxScene(params)
    .then((res) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '创建成功！' });
        isCopyTemp.value = false;
        router.push('/experience_edit?sceneid=' + res.data + '&sceneType=' + sceneType.value);
      } else {
        console.error('创建同款失败:', res);
      }
    })
    .catch((error) => {
      console.error('copyWxScene API 错误:', error);
    });
};

const mouseenterTemp = (item, i) => {
  const thresholdRight = item.sceneType == 1 && item.scenePlatform != 3 ? 544 : 280;
  const thresholdTop = item.sceneType == 1 && item.scenePlatform != 3 ? 300 : 375;
  item.isHover = true;
  sceneDataPlatform.value = item.scenePlatform;
  sceneType.value = item.sceneType;
  activeTempSceneId.value = item.sceneId;
  hoverCurrentTemplate.value = item;

  const node = document.getElementById('template-card-' + i);
  const rect = node.getBoundingClientRect();
  const distanceFromTop = rect.top + window.scrollY;
  const distanceFromRight = document.documentElement.clientWidth - rect.right;

  if (distanceFromRight < thresholdRight) {
    item.isLeft = true;
  } else {
    item.isLeft = false;
  }

  if (distanceFromTop < thresholdTop) {
    item.isTop = true;
  } else {
    item.isTop = false;
  }
};

const mouseoverTemp = (item) => {
  item.isHover = false;
};

const handleHide = (renew?: boolean) => {
  modalShow.value = false;
  if (isCopyTemp.value) {
    // 如果是正在 创建模板同款
    isCopyTemp.value = false;
  } else {
    if (renew) {
      // 判断是否需要重新渲染
      requestGetTemplateSquares();
    }
  }
};

const update = (params) => {
  return new Promise((resolve) => {
    updateTemplateInfo(params).then((res) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '修改成功！' });
        isOpenSetTemp.value = false;
        requestGetTemplateSquares();
      }
    });
  });
};

const confirm = (params) => {
  return new Promise((resolve) => {
    setSceneAsTemplate(params).then((res) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '操作成功！' });
        isOpenSetTemp.value = false;
        requestGetTemplateSquares();
        hoverCurrentScene.value.sceneStatus = 3;
      }
    });
  });
};

const hideSetTemp = () => {
  isOpenSetTemp.value = false;
};

const hideQrCode = () => {
  isOpenQR.value = false;
};

const editTemplate = (item) => {
  hoverCurrentScene.value = item;
  isOpenSetTemp.value = true;
  nextTick(() => {
    setTemplateRef.value.setParmasEdit(JSON.parse(JSON.stringify(item)), true);
  });
};

const openCreateScene = (item) => {
  const userDto = store.state.userDto;
  const packageInfoDto = store.state.packageInfoDto;
  const userBindPackageDto = store.state.userBindPackageDto;
  const storageData = store.state.storageData?.home;

  // 检查必要的数据是否存在
  if (!userDto) {
    console.error('用户数据不存在，无法创建同款');
    store.state.showTips = '用户信息获取失败，请刷新页面后重试';
    return;
  }

  if (!storageData) {
    console.error('存储数据不存在，无法创建同款');
    store.state.showTips = '数据加载失败，请刷新页面后重试';
    return;
  }
  if ([6, 7].includes(userType.value)) {
    if (storageData.planeArSceneUsedNum >= 10) {
      store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
      return;
    }
  } else {
    if (userDto.packageVersion == 'V2') {
      if (!userBindPackageDto) {
        console.error('用户套餐绑定数据不存在');
        store.state.showTips = '套餐信息获取失败，请刷新页面后重试';
        return;
      }
      if (userBindPackageDto.singleIdentify == 1) {
        // 单点服务
        if (storageData.planeArSceneUsedNum >= userBindPackageDto.sceneNum) {
          store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
          return;
        }
      } else {
        // 空间服务
        if ([2, 3, 6].includes(item.sceneType)) {
          if (storageData.planeArSceneUsedNum >= userBindPackageDto.sceneNum) {
            store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
            return;
          }
        } else {
          store.state.showTips = '创建同款失败，当前账号暂无创建该类型的项目权限';
          return;
        }
      }
    } else {
      if (!packageInfoDto) {
        console.error('套餐信息数据不存在');
        store.state.showTips = '套餐信息获取失败，请刷新页面后重试';
        return;
      }
      if (storageData.planeArSceneUsedNum >= packageInfoDto.planeSceneNum) {
        store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
        return;
      }
    }
  }

  tempCopyScene = item;
  modalShow.value = true;
  isCopyTemp.value = true;
};

const startLearn = (item) => {
  isOpenQR.value = true;
  getWXCode(item);
};

const getWXCode = async (item) => {
  qrName.value = !activeTempSceneId.value ? item.sceneName : item.templateName;
  const { data } = await getWxAppQrCode({ sceneId: item.sceneId, path: 'pages/njyjxr/scene' });
  if (data) {
    codeUrl.value = 'data:image/jpeg;base64,' + data;
  }
};

const requestGetTemplateSquares = () => {
  const params = {
    sceneType: activeMenuTab.value.sceneType,
    scenePlatform: platformIndexValue.value,
  };

  queryTemplateScene(params).then((res) => {
    if (res.code == '200') {
      data.value = res.data;
    }
  });
};

const activeTab = (menu) => {
  for (let i = 0; i < arMenuList.value.length; i++) {
    const item = arMenuList.value[i];
    item.isActive = false;
  }
  activeMenuTab.value = menu;
  requestGetTemplateSquares();
};

const activeTopTab = (menu) => {
  activeTopMenuTab.value = menu;
  // 更新platformIndexValue以匹配选中的平台
  if (menu.value !== undefined) {
    // 来自scenePlatformTabs的平台选项，使用value属性
    platformIndexValue.value = menu.value;
  } else if (menu.scenePlatform !== undefined && menu.scenePlatform !== -1) {
    // 其他情况，使用scenePlatform属性
    platformIndexValue.value = menu.scenePlatform;
  } else {
    // "所有平台"的情况，设置为-1，表示不传递scenePlatform参数
    platformIndexValue.value = -1;
  }

  requestGetTemplateSquares();
};

onMounted(() => {
  // 初始化排序模式的独立菜单状态
  initSortModeMenus();

  // 获取模板数据
  requestGetTemplateSquares();

  // 更新当前数据
  store.dispatch('updateCurrentData', {
    sceneId: router.currentRoute.value.query.sceneId,
    experienceId: router.currentRoute.value.query.experienceId,
    templateId: router.currentRoute.value.query.templateId,
    pageType: router.currentRoute.value.query.pageType,
  });

  // 获取用户数据 - 确保创建同款功能正常工作
  getOrgnizationPackage({})
    .then((res: any) => {
      // 更新store中的用户数据
      if (res.data.userDto) {
        store.dispatch('updateUserDto', res.data.userDto);
      }

      if (res.data.packageInfoDto) {
        store.dispatch('updatePackageInfoDto', res.data.packageInfoDto);
      }

      if (res.data.userBindPackageDto) {
        store.dispatch('updateUserBindPackageDto', res.data.userBindPackageDto);
      }

      // 更新存储数据
      if (res.data.storageData) {
        store.state.storageData = res.data.storageData;
      }
    })
    .catch((error) => {
      console.error('获取用户数据失败:', error);
      store.state.showTips = '用户数据加载失败，请刷新页面重试';
    });
});

// 监听 activeTabValue 的变化（网页端/小程序端切换）
watch(
  () => activeTabValue.value,
  (newValue, oldValue) => {
    // 如果在排序模式下，根据平台切换获取对应数据
    if (isSortMode.value) {
      if (newValue === 'miniprogram') {
        requestGetMiniprogramTemplates();
      } else {
        requestGetSortModeTemplates();
      }
    }
  }
);

watch(
  () => store.state.activeSceneTab,
  (nv, oldValue) => {
    // 避免初始化时的重复请求
    if (oldValue === undefined) {
      return;
    }

    // 只有当当前不是"所有平台"时才更新platformIndexValue
    if (activeTopMenuTab.value.scenePlatform !== -1) {
      platformIndexValue.value = nv.value;
    }

    requestGetTemplateSquares();
  }
);
</script>

<style lang="less">
.template-square {
  // 模板区域（排序模式专用）
  .template-section {
    &__header {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 0px;
      padding-bottom: 12px;
      gap: 20px;
    }

    &__title {
      font-size: 16px;
      font-weight: 600;
      color: #1e1e1e;
      margin: 0;
      flex-shrink: 0;
    }

    &__nav {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-wrap: wrap;

      // 靠左对齐的导航
      &--left {
        justify-content: flex-start;
      }
    }
  }

  // 模板网格布局
  .template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    padding: 4px;
    overflow: visible; // 允许视频预览超出网格边界

    // 拖拽状态下的网格样式
    &--dragging {
      .template-card {
        transition: transform 0.2s ease;
      }
    }
  }

  // 拖拽相关样式
  .template-card {
    // 拖拽时的幽灵样式
    &--ghost {
      opacity: 0.5;
      background: #f0f0f0;
      border: 2px dashed #ccc;
      transform: rotate(5deg);
    }

    // 选中时的样式
    &--chosen {
      transform: scale(1.05);
      box-shadow: 0 8px 24px rgba(46, 118, 255, 0.3);
      z-index: 1000;
    }

    // 拖拽中的样式
    &--drag {
      transform: rotate(5deg);
      opacity: 0.8;
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
    }
  }

  // 拖拽状态下禁用视频预览
  .template-grid--dragging {
    .back_video {
      display: none !important;
    }
  }

  // 响应式设计
  @media (max-width: 1400px) {
    .template-grid {
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      gap: 20px;
    }
  }

  @media (max-width: 1200px) {
    .template-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 18px;
    }
  }

  @media (max-width: 992px) {
    .template-grid {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: 16px;
    }
  }

  @media (max-width: 768px) {
    .template-grid {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 14px;
      padding: 2px;
    }
  }

  @media (max-width: 576px) {
    .template-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 12px;
    }
  }
}
</style>
