<template>
  <div v-if="visible" class="category-edit-dialog-mask">
    <div class="category-edit-dialog">
      <div class="dialog-title">选择分类</div>
      <div class="category-checkbox-list">
        <div class="category-checkbox-column" v-for="(col, colIdx) in columns" :key="colIdx">
          <label v-for="item in col" :key="item.value" class="category-checkbox-item">
            <input type="checkbox" v-model="checkedValues" :value="item.value" />
            <span :class="{ checked: checkedValues.includes(item.value) }">{{ item.label }}</span>
          </label>
        </div>
      </div>
      <div class="dialog-footer">
        <button class="btn-cancel" @click="onCancel">取消</button>
        <button class="btn-confirm" @click="onConfirm">确定</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';

const props = defineProps<{
  visible: boolean;
  allCategories: { label: string; value: string }[];
  checked: string[];
}>();
const emit = defineEmits(['update:visible', 'confirm']);

const checkedValues = ref<string[]>([]);

watch(
  () => props.checked,
  (val) => {
    checkedValues.value = [...val];
  },
  { immediate: true }
);

const columns = computed(() => {
  // 4列分组
  const colNum = 4;
  const result: (typeof props.allCategories)[][] = Array.from({ length: colNum }, () => []);
  props.allCategories.forEach((item, idx) => {
    result[idx % colNum].push(item);
  });
  return result;
});

function onCancel() {
  emit('update:visible', false);
}
function onConfirm() {
  emit('confirm', checkedValues.value);
  emit('update:visible', false);
}
</script>

<style scoped>
.category-edit-dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.category-edit-dialog {
  background: #fff;
  border-radius: 12px;
  min-width: 480px;
  max-width: 700px;
  padding: 32px 32px 24px 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}
.dialog-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 24px;
}
.category-checkbox-list {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
}
.category-checkbox-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.category-checkbox-item {
  display: flex;
  align-items: center;
  font-size: 15px;
  cursor: pointer;
  user-select: none;
}
.category-checkbox-item input[type='checkbox'] {
  margin-right: 8px;
  accent-color: #2e76ff;
}
.category-checkbox-item .checked {
  color: #2e76ff;
  font-weight: 500;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
.btn-cancel,
.btn-confirm {
  min-width: 72px;
  padding: 6px 18px;
  border-radius: 4px;
  border: none;
  font-size: 15px;
  cursor: pointer;
}
.btn-cancel {
  background: #f5f7fa;
  color: #333;
}
.btn-confirm {
  background: #2e76ff;
  color: #fff;
}
</style>
