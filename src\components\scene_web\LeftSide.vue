<template>
  <div class="canvas-left" :class="hide_side ? 'hide-left-side' : ''">
    <div class="interaction-title">
      <div @click="handleCancel" style="cursor: pointer;">公共区域</div>
      <div>
        <div class="tab-bar">
          <div :class="!isPlanStyle ? 'active' : ''" @click="isPlanStyle = false">3D</div>
          <div :class="isPlanStyle ? 'active' : ''" @click="isPlanStyle = true">2D</div>
        </div>
        <div v-if="isPlanStyle" class="add-interaction" @click="createInteraction"></div>
      </div>
    </div>
    <div class="left-tabs-content">
      <div class="area-list-box" @scroll="tabsContentScroll">
        <div v-show="item.flag != 'delete'" v-for="(item, index) in editSceneData.interactionDtoList" :key="index"
          @click.stop="handleClick(item.uuid)" :class="interactionStatus[item.uuid]?.active ? ' active' : ''">
          <div class="area-name"
            :class="(item.materialMetaDtoList.length ? 'has-draw-down' : '') + (interactionStatus[item.uuid]?.closed ? ' hide ' : '')">
            <div>
              <div @click.stop="handleClickOpen(item.uuid)"></div>
              <img src="@/assets/images/icon/area-list.png" />
              <span v-if="store.state.stateStore.activeArea != index">{{
                item.interactionName }}</span>
            </div>
          </div>
          <div v-show="!interactionStatus[item.uuid]?.closed || item.uuid == store.state.activeAreaUuid"
            class="source-list" :class="store.state.activeMaterial == e?.uuid ? 'active' : ''"
            v-for="(e, i) in item.materialMetaDtoList" :key="i" @click.stop="handleSelected(e, item)">
            <div>{{ e.elementName || e.materialDto.materialName }}</div>
            <img v-if="store.state.activeMaterial == e?.uuid" src="@/assets/images/icon/delete.png"
              @click.stop="deleteSourse($event, 1, e, index, i)" />
          </div>
          <div class="more-list" :class="(showMoreTop - 119) >= -26 && deleteInteractionIndex == index ? 'active' : ''"
            @mousemove="hoverMoreList($event, index)" @mouseleave="leaveMoreList" @click.stop="null"></div>
        </div>
        <div class="source-list" v-for="(e, i) in editSceneData.outerMaterialMetaDtoList" :key="i"
          :class="store.state.activeMaterial == e?.uuid ? 'active' : ''" @click.stop="handleSelected(e)">
          <div>{{ e.elementName || e.materialDto.materialName }}</div>
          <img v-if="store.state.activeMaterial == e?.uuid" src="@/assets/images/icon/delete.png"
            @click.stop="deleteSourse($event, 2, e, i)" />
        </div>
      </div>
      <div v-if="(showMoreTop - 119) >= -26" class="more-list-box" :style="{ top: (showMoreTop - 119) + 'px' }"
        @mousemove="hoverDelete" @mouseleave="leaveDelete">
        <div>
          <div @click="dialogVisible = true">删除</div>
        </div>
      </div>
    </div>
    <div class="left-source-box">
      <div class="add-source">
        <span>素材库</span>
        <div v-if="searchForm.activeLibrary == 2" @click.stop="modalShow = true" class="add"></div>
      </div>
      <div class="material-library">
        <div class="material-library-tab">
          <div @click="activeLibraryEvent(1)" :class="searchForm.activeLibrary == 1 ? 'active' : ''">公共库</div>
          <div @click="activeLibraryEvent(2)" :class="searchForm.activeLibrary == 2 ? 'active' : ''">个人库</div>
        </div>
        <div></div>
      </div>
      <div class="material-type-tab">
        <div v-for="item in materialTypeList" @click="changeMateriaType(item.value)"
          :class="item.value == searchForm.materialType ? 'active' : ''">{{
            item.title }}</div>
      </div>
    </div>
    <div class="source-pool-box">
      <div :class="showLoading ? 'show-loading' : ''">
        <div class="source-pool" v-for="(item, index) in sourceList"
          :style="{ marginRight: (index + 1) % 4 == 0 ? '0' : '7px' }" :key="index">
          <div :class="hoverImage == index + '' ? 'hoverSource' : ''"
            @mousedown="handleMouseDown($event, materialUrls[item.materialType].url_a, item)"
            @mousemove="handleMouseMove(index)" @mouseleave="handleMouseLeave">
            <img
              :src="item.thumbnailOssAccessUrl || materialUrls[item.materialType][hoverImage == index + '' ? 'url_a' : 'url']"
              draggable="false" />
            <div class="preview" @mousedown.stop="handlePreview(item)"></div>
          </div>
          <div class="source-name">{{ item.materialName }}</div>
        </div>
      </div>
    </div>
  </div>
  <create-source-material v-if="modalShow" :handle-hide="handleHide"></create-source-material>
  <material-preview :source-type="showSourceType" :handle-hide="closedPreview" :model-type="modelType" v-if="sourceUrl"
    :source-url="sourceUrl"></material-preview>
  <div v-if="!hide_side" class="hide-side" @click="hide_side = true">
    <img src="@/assets/images/icon/hide-side.png" />
  </div>
  <div v-if="hide_side" class="show-side" @click="hide_side = false">
    <img src="@/assets/images/icon/show-side.png" />
  </div>
  <div class="dialog-box" v-show="dialogVisible">
    <div>
      <div class="title">{{ deleteSourceIndex ? '确认要删除当前素材？' : '确认要删除当前互动区域？' }}</div>
      <div>{{ deleteSourceIndex ? '删除后将不可恢复' : '删除后，区域中的素材都将删掉' }}</div>
      <div class="sure-btn" @click="deleteSourceIndex ? deleteSourceEvent() : deleteInteraction()">确定</div>
      <div class="close-icon" @click="dialogVisible = false"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, reactive } from 'vue'
import DropDown from '@/components/DropDown.vue'
import { materialUrls, materialType } from '@/config'
import { getMaterialPageByUser, getOssAccessPath, getDefaultMaterial, selectMaterialGroup, deleteMaterialGroup } from '@/api'
import CreateSourceMaterial from '@/views/material/create/CreateSourceMaterial.vue'
import MaterialCombination from '@/views/create/MaterialCombination.vue'
import MaterialPreview from '@/components/MaterialPreview.vue'
import { cropNumber } from '@/utils/index'
import { useStore } from "vuex";
import { ElMessage } from 'element-plus'

const store = useStore();

const props = defineProps({
  sourcePoolMourseDown: {
    default: null,
    type: Function
  },
  deleteAssets: {
    default: null,
    type: Function
  },
  headerRef: {
    default: null,
    type: Object
  }
})

let timer: any = null
let pageTotal = 0;
let isPending = false
const showLoading = ref(false);
const hide_side = ref(false)
const modalShow = ref(false) // 显示新建
const modalShow2: any = ref(null) // 显示素材组合
const sourceType = ref('') // 资源类型
const sourceList: any = ref([])
const hoverImage: any = ref('') // 悬浮在照片上
const groupMaterial: any = ref([])
const showgroupListIndex = ref(-1)
const sourceUrl = ref('')
const showSourceType = ref('')
const modelType = ref('')
const editSceneData: any = ref({})
const interactionStatus: any = ref({})
const isPlanStyle = ref(true);
const showMoreTop = ref(0);
const activeMoreTarget: any = ref(null);
const deleteInteractionIndex = ref(-1);
const dialogVisible = ref(false);
const deleteSourceIndex = ref('')
let isAddPlaying = false
const searchForm: any = reactive({ // 查询对象
  pageSize: 50,
  materialType: '4',
  activeLibrary: 1
})
const currentPageNo = ref(1) // 设置一个加载步长
const materialTypeList = ref([
  {
    title: '模型',
    value: '4'
  },
  {
    title: '视频',
    value: '1'
  },
  {
    title: '音频',
    value: '2'
  },
  {
    title: '图片',
    value: '3'
  },
  {
    title: '文本',
    value: '5'
  }
])

const scenePlatformMap: any = {
  1: '1,2',
  2: '1,2,3',
  3: '2'
}
const platformV2 = ref('')
let tipsText = '';

const changeMateriaType = (value: string) => {
  if (searchForm.materialType == value) return;
  searchForm.materialType = value;
  currentPageNo.value = 1;
  sourceList.value = [];
  getMaterialTotal(searchForm.activeLibrary)
}

const activeLibraryEvent = (value: number) => {
  if (searchForm.activeLibrary == value) return;
  searchForm.activeLibrary = value;
  currentPageNo.value = 1;
  sourceList.value = [];
  getMaterialTotal(searchForm.activeLibrary)
}

const createInteraction = async () => {
  if (isAddPlaying) return ElMessage({ type: 'warning', message: '上一个互动区域正在生成中，请稍等...' })
  isAddPlaying = true
  let interactionName = ''
  if (!editSceneData.value.interactionDtoList || !editSceneData.value.interactionDtoList.slice(-1)[0]) {
    interactionName = '互动区域01'
  } else {
    const nameNumber = +cropNumber(editSceneData.value.interactionDtoList.slice(-1)[0]?.interactionName)
    if (nameNumber) {
      interactionName = editSceneData.value.interactionDtoList.slice(-1)[0].interactionName.slice(0, -((nameNumber + '').length)) + (nameNumber + 1)
    } else {
      interactionName = editSceneData.value.interactionDtoList.slice(-1)[0].interactionName + '01'
    }
  }
  await props.headerRef.saveEditInfo()
  const lastInteraction = {
    location: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    scale: { x: 1, y: 1, z: 1 },
    materialMetaDtoList: [],
    materialGroupDtoList: [],
    interactionName,
    flag: 'add'
  }
  const interactionDtoList = [...store.state.editSceneData.interactionDtoList || []]
  interactionDtoList.push(lastInteraction);
  editSceneData.value.interactionDtoList = [...interactionDtoList]
  editSceneData.value.changeTime = new Date().getTime()
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData.value))
  isAddPlaying = false
}

const hoverMoreList = (event: any, index: number) => {
  showMoreTop.value = event.target.getBoundingClientRect().top;
  activeMoreTarget.value = event.target;
  deleteInteractionIndex.value = index
}

const leaveMoreList = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    showMoreTop.value = 0;
    activeMoreTarget.value = null;
  }, 500)
}

const hoverDelete = () => {
  clearTimeout(timer)
}

const leaveDelete = () => {
  showMoreTop.value = 0;
  activeMoreTarget.value = null;
}

const tabsContentScroll = () => {
  if (activeMoreTarget.value) {
    showMoreTop.value = activeMoreTarget.value.getBoundingClientRect().top;
  }
}

const deleteInteraction = async () => {
  dialogVisible.value = false;
  const curEditSceneData = { ...store.state.editSceneData, changeTime: new Date().getTime(), changeType: 'deleteInteraction' }
  const deleteData: any = { ...store.state.deleteData }
  const deleteInteractionData: any = curEditSceneData.interactionDtoList[deleteInteractionIndex.value];
  // 新增的互动区域直接删除，原有的互动区域删除并保存删除的数据
  if (deleteInteractionData.flag != 'add') {
    deleteData[deleteInteractionData.uuid] = { ...deleteInteractionData };
  }
  const currentDeleteData = curEditSceneData.interactionDtoList.splice(deleteInteractionIndex.value, 1)[0];
  store.state.editSceneData = JSON.parse(JSON.stringify(curEditSceneData));
  store.state.deleteData = { ...deleteData }
  store.state.currentDeleteData = { ...currentDeleteData, deleteIndex: deleteInteractionIndex.value }
  deleteInteractionIndex.value = -1
  store.state.randerTotal = 0;
  await props.deleteAssets()
}

// 删除素材二次确认
const deleteSourse = (event: any, type: number, data: any, index: number, i?: number) => { // type1=互动区域素材，type2=公共区域素材
  dialogVisible.value = true;
  if (type == 1) { //删除互动区域里的素材
    deleteSourceIndex.value = `${index},${i}`

  } else if (type == 2) {
    deleteSourceIndex.value = index + ''
  }

}

// 删除素材
const deleteSourceEvent = async () => {
  dialogVisible.value = false;
  let curEditSceneData = { ...store.state.editSceneData, changeTime: new Date().getTime(), changeType: 'deleteSource' }
  const deleteData: any = { ...store.state.deleteData }
  let deleteSourceData = null;
  const deleteIndexs = deleteSourceIndex.value.split(',')
  let currentDeleteData = null;
  if (deleteIndexs.length == 2) { // 互动区域里的素材
    deleteSourceData = curEditSceneData.interactionDtoList[deleteIndexs[0]].materialMetaDtoList[deleteIndexs[1]]
    currentDeleteData = curEditSceneData.interactionDtoList[deleteIndexs[0]].materialMetaDtoList.splice(deleteIndexs[1], 1)[0]
  } else if (deleteIndexs.length == 1) { // 公共区域里的素材
    deleteSourceData = curEditSceneData.outerMaterialMetaDtoList[deleteIndexs[0]];
    if (deleteSourceData.materialDto.materialType == '2' && deleteSourceData.materialDto.id == curEditSceneData.backgroundMusic) { // 音乐删除后，背景音乐需要更新
      curEditSceneData = { ...curEditSceneData, backgroundMusic: null }
    }
    currentDeleteData = curEditSceneData.outerMaterialMetaDtoList.splice(deleteIndexs[0], 1)[0]
  }
  if (deleteSourceData.flag != 'add') {
    deleteData[deleteSourceData.uuid] = { ...deleteSourceData, interactionId: deleteIndexs[1] ? curEditSceneData.interactionDtoList[deleteIndexs[0]].id : 'public' };
  }
  store.state.deleteData = { ...deleteData }


  store.state.editSceneData = JSON.parse(JSON.stringify(curEditSceneData));
  store.state.currentDeleteData = { ...currentDeleteData, deleteIndex: deleteSourceIndex.value }
  deleteSourceIndex.value = ''
  store.state.randerTotal = 0;
  await props.deleteAssets()
}

const handleCancel = () => {
  store.state.activeMaterial = ''
  if (store.state.activeAreaUuid) {
    handleClick(store.state.activeAreaUuid)
  }
}

const handleSelected = (data: any, inter?: any) => {
  if (store.state.isDragLoading || store.state.isRequesting) return ElMessage({ type: 'warning', message: '数据加载中，请勿频繁操作' })
  if (store.state.activeMaterial == data.uuid) {
    store.state.activeMaterial = ''
    return
  }
  if (store.state.activeAreaUuid) {
    handleClick(store.state.activeAreaUuid)
  }
  if (inter) {
    handleClick(inter.uuid)
  }
  store.state.activeMaterial = data.uuid || ''
  store.state.operateType = '移动'
      if (inter) {
      store.dispatch('updateCurrentData', { interactionId: inter.id })
  }
}

// 互动区域高亮和展开的逻辑
const handleClick = (interactionId: string) => {
  if (store.state.activeMaterial) {
    store.state.activeMaterial = ''
  }
  Object.keys(interactionStatus.value).forEach((key: string) => {
    if (key != interactionId) {
      interactionStatus.value[key].active = false
    } else {
      interactionStatus.value[key].active = !interactionStatus.value[key].active
      store.state.activeAreaUuid = interactionStatus.value[key].active ? interactionId : ''
    }
  })
  if (!interactionStatus.value[interactionId]) {
    interactionStatus.value[interactionId] = {
      active: true,
      closed: false
    }
    store.state.activeAreaUuid = interactionId
  }
}

const handleClickOpen = (interactionId: string) => {
  Object.keys(interactionStatus.value).forEach((key: string) => {
    if (key == interactionId) {
      interactionStatus.value[key].closed = !interactionStatus.value[key].closed
    }
  })
  if (!interactionStatus.value[interactionId]) {
    interactionStatus.value[interactionId] = {
      closed: true
    }
  }
}

const handleHide = (renew?: boolean, materialType?: string) => {
  modalShow.value = false;
  if (renew) {// 判断是否需要重新渲染
    searchForm.activeLibrary = 2;
    searchForm.materialType = materialType;
    currentPageNo.value = 1;
    sourceList.value = [];
    getMaterialTotal(searchForm.activeLibrary);
    sourceType.value = ''
  }
}

const getMaterialTotal = (type: number) => {
  const scenePlatform = store.state.editSceneData.scenePlatform;
  showLoading.value = true;
  if (type == 1) {
    getDefaultMaterial({ ...searchForm, pageNo: currentPageNo.value, platformV2: platformV2.value }).then((res: any) => {
      let data = res.data.records.map((d: any) => {
        d.materialAffiliation = 2;
        return d;
      })
      if (scenePlatform == '3' && searchForm.materialType == '4') {
        data = data.filter((e: any) => (e.materialFormat == 'glb' || e.materialFormat == 'gltf'))
      }
      sourceList.value.push(...data);
      pageTotal = res.data.total;
      showLoading.value = false;
    })
  } else if (type == 2) {
    getMaterialPageByUser({ ...searchForm, pageNo: currentPageNo.value, platformV2: platformV2.value }).then((res: any) => {
      let data = res.data.records.map((d: any) => {
        d.materialAffiliation = 1;
        return d;
      })
      if (scenePlatform == '3' && searchForm.materialType == '4') {
        data = data.filter((e: any) => (e.materialFormat == 'glb' || e.materialFormat == 'gltf'))
      }
      sourceList.value.push(...data);
      pageTotal = res.data.total;
      showLoading.value = false;
    })
  }
}

const handlePreview = (data: any) => {
  showSourceType.value = data.materialFormat ? data.materialFormat.split('/')[0] : 'text'
  if (['fbx', 'glb', 'gltf', 'obj'].includes(showSourceType.value)) {
    showSourceType.value = 'model'
    modelType.value = data.materialFormat
  }
  let ossKey = ''
  if (showSourceType.value == 'model') {
    ossKey = data.modelStorageMap?.web?.ossKey || ''
  } else {
    ossKey = data.ossKey
  }
  showSourceType.value = showSourceType.value.toUpperCase().toLowerCase();
  if (showSourceType.value == 'png' || showSourceType.value == 'jpg') {
    showSourceType.value = 'image';
  }
  if (showSourceType.value == 'mp4') {
    showSourceType.value = 'video';
  }
  if (showSourceType.value == 'mp3') {
    showSourceType.value = 'audio';
  }
  if (ossKey) {
    getOssAccessPath({ key: ossKey }).then((res1: any) => {
      sourceUrl.value = res1.data
    })
  }
}

const handleMouseMove = (index: number, index2?: number, type?: string) => {
  if (!hoverImage.value) {
    hoverImage.value = index + ''
  }
  if (type) {
    hoverImage.value = `${index}${index2}${type}`
  }
}

const handleMouseLeave = () => {
  hoverImage.value = ''
}

const handleMouseDown = (e: any, url: string, data: any) => {
  if (store.state.isDragLoading || store.state.isRequesting) {
    return ElMessage({ message: '数据加载中，请勿频繁操作', type: 'warning' })
  }
  store.state.isDragLoading = true
  if (data.thumbnail) {
    getOssAccessPath({ key: data.thumbnail }).then((res: any) => {
      props.sourcePoolMourseDown(e, res.data, data)
    })
  } else {
    props.sourcePoolMourseDown(e, url, data)
  }
}


const closedPreview = () => {
  sourceUrl.value = ''
}

const scrollEvent = (e: any) => {
  const scrollHeight = e.target.scrollHeight;
  const height = e.target.getBoundingClientRect().height;
  const scrollTop = e.target.scrollTop;
  if (scrollHeight - height - scrollTop < 10 && showLoading.value == false && (currentPageNo.value * searchForm.pageSize) < pageTotal) {
    currentPageNo.value += 1;
    getMaterialTotal(searchForm.activeLibrary);
  }
}

onMounted(() => {
  const sourcePoolBox: any = document.querySelector('.source-pool-box');
  sourcePoolBox.addEventListener('scroll', scrollEvent, false)
})

watch(store.state, (newState: any) => {
  editSceneData.value = JSON.parse(JSON.stringify(newState.editSceneData))
  if (newState.isPlanStyle != isPlanStyle.value) {
    isPlanStyle.value = store.state.isPlanStyle
  }
  if (!platformV2.value) {
    platformV2.value = scenePlatformMap[newState.editSceneData.scenePlatform]
    if (platformV2.value) {
      getMaterialTotal(searchForm.activeLibrary);
    }
  }
})

watch(() => store.state.activeMaterial, (newState: any) => {
  setTimeout(() => {
    const targetObj: any = document.querySelector('.left-tabs-content .area-list-box .source-list.active')?.getBoundingClientRect();
    const boxObj: any = document.querySelector('.area-list-box')?.getBoundingClientRect();
    const disDiff: any = targetObj?.top - (boxObj.top + boxObj.height);
    const st = document.querySelector('.area-list-box')?.scrollTop || 0;
    if (disDiff > -36) {
      document.querySelector('.area-list-box')?.scrollTo(0, st + disDiff + 136)
    } else if (targetObj?.top - boxObj.top < 60) {
      document.querySelector('.area-list-box')?.scrollTo(0, st + (targetObj?.top - boxObj.top) - 60)
    }
  })
})

watch(isPlanStyle, (newState) => {
  store.state.isPlanStyle = newState;
})

watch(() => store.state.activeAreaUuid, (newState) => {
  Object.keys(interactionStatus.value).forEach((key: string) => {
    interactionStatus.value[key].active = false
  })
  if (!interactionStatus.value[newState]) {
    interactionStatus.value[newState] = {
      active: true
    }
  } else if (newState) {
    interactionStatus.value[newState].active = true
  }
})

watch(() => store.state.randerTotal, (newState) => {
  if (newState == 2) {
    store.state.editSceneData.changeType = ''
  }
})

watch(() => store.state.showAreaUuidData, (newState: any) => {
  if (newState.id) {
    const data = newState.materialMetaDtoList.filter((e: any) => e.uuid == newState.activeChild)[0]
    handleSelected(data, newState)
  } else {
    const data = store.state.editSceneData.outerMaterialMetaDtoList.filter((e: any) => e.uuid == newState.activeChild)[0]
    handleSelected(data, null)
  }
})

watch(() => store.state.showTips, (newState) => {
  if (newState) {
    tipsText = newState;
  } else if (newState == '' && tipsText == '已上传的素材取消保存') {
    modalShow.value = false;
    tipsText = '';
  }
})
</script>
<style scoped lang="less">
.dialog-box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: space-around;
  align-items: center;

  &>div {
    position: relative;
    width: 524px;
    height: 186px;
    background: #FFFFFF;
    box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.27);
    border-radius: 6px;
    padding: 32px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 400;
    font-size: 16px;
    color: #6F6F6F;

    &>.title {
      font-weight: 500;
      font-size: 20px;
      color: #0F0F0F;
      margin-bottom: 14px;
    }

    .sure-btn {
      width: 96px;
      height: 32px;
      line-height: 32px;
      background: linear-gradient(225deg, #0375FF 0%, #3C96FF 100%);
      box-shadow: inset -1px -1px 0px 0px rgba(255, 255, 255, 0.2), inset 1px 1px 0px 0px rgba(11, 91, 225, 0.4);
      border-radius: 4px;
      font-weight: 400;
      font-size: 17px;
      color: #FFFFFF;
      text-align: center;
      position: absolute;
      right: 17px;
      bottom: 21px;
      cursor: pointer;
    }

    .close-icon {
      width: 20px;
      height: 20px;
      background: url(~@/assets/images/icon/close.png);
      background-size: 100% 100%;
      position: absolute;
      right: 17px;
      top: 15px;
      cursor: pointer;
    }
  }
}

.interaction-title {
  margin: 16px 16px 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  font-size: 17px;
  color: #414141;
}

.interaction-title>div:last-child {
  width: 168px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.tab-bar {
  font-weight: bold;
  font-size: 16px;
  color: #414040;
  width: 136px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  overflow: hidden;

  &>div {
    position: relative;
    width: 50%;
    height: 26px;
    line-height: 26px;
    padding-left: 34px;
    box-sizing: border-box;
    text-align: left;
    background: #D1D4D6;
    cursor: pointer;
  }

  &>div.active {
    color: #3671FE;
    background-color: #F3F4F5;
  }

  &>div:first-child::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 5px;
    width: 16px;
    height: 16px;
    background: url(~@/assets/images/icon/room-icon.png);
    background-size: 100% 100%;
  }

  &>div:last-child::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 5px;
    width: 16px;
    height: 16px;
    background: url(~@/assets/images/icon/plan-icon.png);
    background-size: 100% 100%;
  }

  &>div:first-child.active::before {
    background: url(~@/assets/images/icon/room-iconA.png);
    background-size: 100% 100%;
  }

  &>div:last-child.active::before {
    background: url(~@/assets/images/icon/plan-iconA.png);
    background-size: 100% 100%;
  }
}

.add-interaction {
  width: 24px;
  height: 24px;
  margin-left: 8px;
  background: url(~@/assets/images/icon/add-interaction.png);
  background-size: 100% 100%;
  cursor: pointer;
}

.edit-area-name,
.edit-route-name {
  width: 160px;
  height: 100%;
  box-sizing: border-box;
  display: inline-block;

  ::v-deep(.el-input__inner) {
    color: #fff !important;
    height: 18px;
    line-height: 18px;
    font-weight: 400;

    &::placeholder {
      color: #ddd !important;
    }
  }

  ::v-deep(.el-input__wrapper) {
    background: rgba(0, 0, 0, 0) !important;
    border: none !important;
  }
}

#app .source-type {
  ::v-deep(.el-input__wrapper) {
    height: 34px;
  }

  ::v-deep(.el-input__wrapper .el-input__inner) {
    font-size: 14px;
  }
}


.canvas-left {
  position: fixed;
  left: 0;
  top: 63px;
  width: 317px;
  height: calc(100% - 59px);
  z-index: 10;
  background: #DFE0E3;
  // backdrop-filter: blur(20px);

  .material-library {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .material-library-tab {
    width: 184px;
    border-radius: 6px;
    overflow: hidden;
    background: #D1D4D6;
    height: 34px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &>div {
      position: relative;
      width: 50%;
      height: 34px;
      line-height: 34px;
      color: #414141;
      font-weight: 500;
      font-size: 14px;
      color: #414141;
      padding-right: 12px;
      box-sizing: border-box;
      text-align: right;
      cursor: pointer;
    }

    &>div:first-child:before {
      content: '';
      position: absolute;
      left: 12px;
      top: 9px;
      width: 18px;
      height: 16px;
      background: url(~@/assets/images/experience-icon/common-library-icon.png);
      background-size: 100% 100%;
    }

    &>div:last-child:before {
      content: '';
      position: absolute;
      left: 12px;
      top: 9px;
      width: 18px;
      height: 16px;
      background: url(~@/assets/images/experience-icon/personal-library.png);
      background-size: 100% 100%;
    }

    &>div.active {
      background-color: rgba(255, 255, 255, 0.56);
      color: #2E76FF;
    }

    &>div:first-child.active:before {
      background: url(~@/assets/images/experience-icon/common-library-iconA.png);
      background-size: 100% 100%;

    }

    &>div:last-child.active:before {
      background: url(~@/assets/images/experience-icon/personal-libraryA.png);
      background-size: 100% 100%;
    }
  }

  .material-type-tab {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &>div {
      padding: 10px 7px 3px;
      font-weight: 500;
      font-size: 14px;
      color: #414141;
      cursor: pointer;
    }

    &>div.active {
      border-bottom: 1px solid #000;
    }
  }
}

.canvas-left.hide-left-side {
  left: -320px;
}

.hide-side,
.show-side {
  position: absolute;
  top: 50%;
  margin-top: -65px;
  left: 317px;
  width: 16px;
  height: 42px;
  cursor: pointer;
  z-index: 1;

  img {
    width: 100%;
    height: 100%;
  }
}

.show-side {
  left: 0;
}

.left-tabs-content {
  position: relative;
  height: calc(55% - 120px);
  min-height: 232px;

  .add {
    height: 39px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, #FFFFFF 100%);
    padding-left: 47px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    color: #4A4A4A;
    cursor: pointer;

    span {
      margin-left: 12px;
    }
  }

  .more-list-box {
    padding: 8px 0;
    width: 120px;
    background: #FFFFFF;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    position: absolute;
    right: -126px;
    top: -10px;
    z-index: 12;
    font-weight: 400;
    font-size: 14px;
    color: #414141;

    &>div {
      height: 32px;
      line-height: 32px;
      text-align: center;
      cursor: pointer;
    }

    &>div:hover {
      background: rgba(54, 113, 254, 0.1);
    }
  }

  .area-list-box {
    padding: 16px 16px 0 16px;
    box-sizing: border-box;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;

    &>div:not(.source-list) {
      position: relative;
      background: rgba(255, 255, 255, 0.234);
      border-radius: 6px;
      padding: 5px 0 1px;
      margin-bottom: 8px;

      .more-list {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 10px;
        top: 10px;
        background: url(~@/assets/images/icon/more-icon.png);
        background-size: 100% 100%;
        cursor: pointer;
      }

      .more-list.active {
        background: url(~@/assets/images/icon/more-iconA.png);
        background-size: 100% 100%;
      }

      .more-list.active {
        background: url(~@/assets/images/icon/more-iconA.png);
        background-size: 100% 100%;
      }

      &>.source-list {
        margin-bottom: 8px;
      }

      &>.source-list:last-child {
        margin-bottom: 0
      }
    }

    &>div:not(.source-list).active {
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    }

    &>div.source-list {
      margin-bottom: 8px;
    }

    .area-name {
      position: relative;
      width: 263px;
      height: 34px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      padding-left: 36px;
      padding-right: 12px;
      color: #414040;
      box-sizing: border-box;
      cursor: pointer;


      &>div {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        img {
          vertical-align: middle;
        }

        span {
          vertical-align: middle;
          display: inline-block;
          height: 18px;
          line-height: 18px;
          margin-left: 8px;
        }

        &>div:first-child {
          width: 36px;
          height: 34px;
          position: absolute;
          left: 0;
          top: 0;
          background-color: transparent
        }
      }

      .tool-box {
        width: 38px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .delete {
        width: 20px;
        height: 20px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        cursor: pointer;
      }
    }

    .route-list-style {
      margin-bottom: 8px;
    }

    .has-draw-down::before {
      content: '';
      width: 8px;
      height: 8px;
      position: absolute;
      left: 15px;
      top: 12px;
      background: url(~@/assets/images/icon/draw-down.png);
      transform: rotate(0deg);
    }

    .hide.has-draw-down::before {
      transform: rotate(-90deg);
    }

    .source-list {
      height: 14px;
      font-size: 14px;
      font-weight: 400;
      color: #414040;
      padding: 10px 15px 10px 35px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      cursor: pointer;
      margin: 0 15px;

      &>div {
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: keep-all;
        text-align: left;
      }

      &:hover {
        background-color: rgba(60, 150, 255, 0.1);
      }

      &.hoveActive {
        background-color: rgba(60, 150, 255, 0.1);
      }

      &.active {
        background: linear-gradient(225deg, #3C96FF 0%, #0375FF 100%);
        color: #FFFFFF;
      }

      img {
        width: 14px;
        height: 14px;
        cursor: pointer;
      }
    }
  }
}

.left-source-box {
  padding: 18px 20px 12px;
  border-top: 1px solid rgba(200, 200, 200, 0.6);

  .add-source,
  .add-source-combination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    margin-bottom: 12px;
    height: 32px;

    .add {
      width: 32px;
      height: 32px;
      background: url(~@/assets/images/icon/add.png);
      background-size: 100% 100%;
      position: relative;
      z-index: 1;
      cursor: pointer;

      &:hover {
        &::before {
          content: '\65b0\589e\7d20\6750';
          position: absolute;
          left: -17px;
          top: 39px;
          font-size: 12px;
          width: 66px;
          text-align: center;
          height: 28px;
          line-height: 28px;
          background-color: #333537;
          letter-spacing: 1px;
          border-radius: 4px;
          color: #fff;
        }

        &::after {
          content: '';
          position: absolute;
          left: 10px;
          top: 27px;
          width: 0;
          height: 0;
          border: 6px solid transparent;
          border-bottom-color: #333537;
        }
      }
    }
  }

  .add-source-combination .add:hover::before {
    content: '\65b0\589e\7d20\6750\7ec4\5408';
    width: 90px;
    left: -29px;
  }
}

.source-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-top: 13px;

  .select-default {
    height: 34px;
  }

  &>span {
    font-size: 12px;
    font-weight: 400;
    color: #000000;
  }
}

.source-pool-box {

  height: calc(45% - 65px);
  min-height: 208px;
  padding-left: 20px;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;

  &>div {
    position: relative;
    height: auto;
    overflow: hidden;
    padding-bottom: 52px;
    box-sizing: border-box;

    &.show-loading::after {
      content: '';
      width: 32px;
      height: 32px;
      background-image: url(~@/assets/images/icon/loading-b.png);
      background-size: 100% 100%;
      display: inline-block;
      position: absolute;
      bottom: 10px;
      left: 50%;
      margin-left: -16px;
      animation: rotate 1.5s linear infinite;
    }
  }
}

.source-pool {
  float: left;
  width: 64px;
  height: 84px;
  margin-right: 7px;
  margin-bottom: 20px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  &>div:first-child {
    position: relative;
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 5px;
    margin-bottom: 5px;
    overflow: hidden;

    .default-source {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 14px;
      line-height: 14px;
      background-image: url(~@/assets/images/icon/default-source-icon.png);
      background-size: 100% 100%;
      font-size: 10px;
      font-weight: 600;
      color: #2166EA;
      text-align: center;
    }

    &.hoverSource {
      position: relative;
      background: #D8D8D8 linear-gradient(180deg, #FFFFFF 0%, #A8DDFF 67%, #2C8CFF 100%, #2C8CFF 100%);

      .preview {
        width: 12px;
        height: 12px;
        position: absolute;
        right: 3px;
        top: 3px;
        background-image: url(~@/assets/images/preview.png);
        background-size: 100% 100%;
        cursor: pointer;
      }

      .default-source {
        background-image: url(~@/assets/images/icon/default-source-iconA.png);
        color: #fff;
      }
    }

    img {
      width: 100%;
      height: 100%;
    }
  }

  &>div:last-child {
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    color: #414040;
  }

  .source-name {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    word-break: break-all;
  }
}

.delete-box {
  margin-top: 22px;
  margin-bottom: 5px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .el-size {
    width: 43px;
    height: 20px;
    font-weight: 400;
    text-shadow: 0px 5px 10px rgba(200, 200, 200, 0.5);
    padding: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;

    span {
      font-size: 10px;
      transform: scale(0.83333);
      transform-origin: 0 0;
    }

    &.btn-sure {
      background: #E84A4B;
      box-shadow: 0px 5px 10px 0px rgba(200, 200, 200, 0.5), 0px 5px 9px 0px rgba(235, 159, 159, 0.3);
      border-radius: 3px;
      color: #FFFFFF;
      border: none;

      &:hover {
        background: #e84a4b;
        box-shadow: 0px 0px 4px 4px rgba(255, 35, 35, 0.3);
      }

      &:active {
        background: #cf4243;
        box-shadow: 0px 5px 10px 0px rgba(255, 35, 35, 0.5);
      }
    }

    &.btn-cancle {
      background: #F7F8FA;
      box-shadow: 0px 5px 10px 0px rgba(200, 200, 200, 0.5), 0px 5px 10px 0px rgba(185, 203, 225, 0.5);
      border-radius: 3px;
      color: #333333;
      border: none;

      &:hover {
        background: #f7f8fa;
        box-shadow: 0px 0px 4px 4px rgba(185, 203, 225, 0.3);
      }

      &:active {
        background: #edeff2;
        box-shadow: 0px 5px 10px 0px rgba(185, 203, 225, 0.5);
      }
    }
  }
}

.popover-btn {
  color: #F23333 !important;
}

/* 媒体查询，适配ipad端和小电脑页面 */
@media screen and (max-width: 1200px) {
  .canvas-left {
    width: 267px;

    .left-tabs-content .area-list-box .area-name {
      width: 213px;
    }

    .source-pool {
      width: 52px;
      height: 80px;

      &>div:first-child {
        width: 52px;
        height: 52px;
      }
    }

    .edit-area-name {
      width: 120px;
    }
  }

  .canvas-left.hide-left-side {
    left: -270px;
  }

  .hide-side {
    left: 267px;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>