// @ts-nocheck
import axios from 'axios';
import { ElMessage } from 'element-plus';
import store from '@/store';
import { loginErrorText } from '@/config';
const customTipCodes = ['2001', '2002', '2003', '2004', '2005', '2006', '2007', '2008', '2009'];
const specialCodeList = ['2003', '2008']; // 需要特殊弹框处理的错误码

const specialTips = [
  {
    requestMessage: '用户已经注册',
    title: '当前账号已注册',
    message: '您可以通过手机验证码直接登录',
    icon: 'https://bpic.51yuansu.com/pic3/cover/03/61/89/5bd6c24a745b8_610.jpg?x-oss-process=image/resize,h_360,m_lfit/sharpen,100',
  },
  {
    requestMessage: '当前账号在另一个浏览器登录，账号被迫退出',
    title: '安全验证提示',
    message: '当前账号在另一个浏览器登录，账号被迫退出',
    icon: 'https://img0.baidu.com/it/u=1474687380,2650276256&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
  },
];

// 特殊错误码对应的弹框配置
const specialCodeTips: Record<string, any> = {
  '2003': {
    title: '提示',
    message: '当前账号已过期，请联系相关客服。',
    icon: 'https://img0.baidu.com/it/u=1474687380,2650276256&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
  },
  '2008': {
    title: '提示',
    message: '当前账号已过期，请联系相关客服。',
    icon: 'https://img0.baidu.com/it/u=1474687380,2650276256&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
  },
};

// 控制多个请求报错时错误提示框始终只显示一个
export const uniqueMessageBox = (function () {
  const arr: any = [];
  return function (msg: any, code = '2000') {
    if (specialCodeList.includes(code)) {
      // 特殊错误码使用特殊弹框
      const specialTip = specialCodeTips[code];
      if (specialTip) {
        store.state.loginShowInfo = {
          ...specialTip,
          requestMessage: msg, // 保留原始错误信息
        };
      } else {
        store.state.showTips = msg;
      }
    } else if (customTipCodes.includes(code)) {
      store.state.showTips = msg;
    } else {
      if (!arr.length) {
        if (!msg) return;
        arr.push(msg);
        ElMessage({
          showClose: true,
          message: msg,
          type: 'error',
          duration: 2000,
          onClose: () => {
            arr.shift();
          },
        });
      }
    }
  };
})();

const baseURL = 'api';

const axiosInstance = axios.create({
  baseURL,
  withCredentials: true,
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  function (config) {
    // 空间相关接口使用特殊代理
    if (
      config.url == '/space/getSpaceStorage' ||
      config.url?.includes('/space/getTaskInfo') ||
      config.url?.includes('/space/genPolycam') ||
      config.url?.includes('/space/genB2g') ||
      config.url?.includes('/space/gene57') ||
      config.url?.includes('/space/genQiyu') ||
      config.url?.includes('/space/getSpaceById')
    ) {
      config.baseURL =
        process.env.NODE_ENV === 'production'
          ? window.location.host == 'mixedspace.njyjxr.com'
            ? 'https://vps.njyjxr.com:18443/vps'
            : 'http://*************/vps'
          : '/api2'; // 发170
    }

    const token = window.localStorage.getItem('token');
    if (token) {
      config.headers.token = token;
    }
    const njyj_version = window.localStorage.getItem('njyj-version');
    if (njyj_version) {
      config.headers['njyj-version'] = njyj_version;
    }

    // 添加clientid头，特别是对空间相关接口
    const clientid = window.localStorage.getItem('clientid');
    if (
      clientid &&
      (config.url?.includes('/space/genPolycam') ||
        config.url?.includes('/space/genB2g') ||
        config.url?.includes('/space/gene57') ||
        config.url?.includes('/space/genQiyu'))
    ) {
      config.headers.clientid = clientid;
    }

    return config;
  },
  function (error) {
    // 对请求错误做些什么
    uniqueMessageBox('网络错误');
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    if (response.data.code === '1001') {
      window.location.href = window.location.origin + '/#/login';
    } else if (response.data.code === '1002') {
      const currentLoginTypeId = response.data.data?.currentLoginTypeId;
      if (currentLoginTypeId) {
        const tipTarget = specialTips.find(
          (tip) => tip.requestMessage == loginErrorText[currentLoginTypeId]
        );
        if (tipTarget) {
          store.state.loginShowInfo = tipTarget;
        } else {
          store.state.showTips = loginErrorText[currentLoginTypeId];
        }
      } else {
        store.state.showTips = response.data.message;
        const token = window.localStorage.getItem('token');
        if (!token) {
          if (window.location.href != 'http://localhost:8080/#/login') {
            window.location.href = window.location.origin + '/#/login';
          }
          return Promise.resolve({});
        }
      }
      window.location.href = window.location.origin + '/#/login';
      return Promise.resolve({});
    } else if (response.data.code !== '2000' && response.data.code != '200') {
      if (['2002'].includes(response.data.code)) {
        store.state.showTips = response.data.message;
      } else {
        uniqueMessageBox(response.data.message, response.data.code);
      }
    }
    return Promise.resolve(response.data);
  },
  function (error) {
    const message = error.response.data?.message;
    const response = error.response;
    if (response.data.code === '1001') {
      window.location.href = window.location.origin + '/#/login';
    } else if (response.data.code === '1002') {
      const currentLoginTypeId = response.data.data?.currentLoginTypeId;
      if (currentLoginTypeId) {
        const tipTarget = specialTips.find(
          (tip) => tip.requestMessage == loginErrorText[currentLoginTypeId]
        );
        if (tipTarget) {
          store.state.loginShowInfo = tipTarget;
        } else {
          store.state.showTips = loginErrorText[currentLoginTypeId];
        }
      } else {
        store.state.showTips = response.data.message;
        const token = window.localStorage.getItem('token');
        if (!token) {
          if (window.location.href != 'http://localhost:8080/#/login') {
            window.location.href = window.location.origin + '/#/login';
          }
          return Promise.resolve({});
        }
      }
      window.location.href = window.location.origin + '/#/login';
      return Promise.resolve({});
    } else if (message) {
      // 检查是否是特殊错误码
      const errorCode = response.data.code;
      if (specialCodeList.includes(errorCode)) {
        const specialTip = specialCodeTips[errorCode];
        if (specialTip) {
          store.state.loginShowInfo = {
            ...specialTip,
            requestMessage: message,
          };
        } else {
          store.state.showTips = message;
        }
      } else {
        const tipTarget = specialTips.find((tip) => tip.requestMessage == message);
        if (tipTarget) {
          store.state.loginShowInfo = tipTarget;
        } else {
          store.state.loginShowInfo = null;
          store.state.showTips = error.response.data.message;
        }
      }
      return Promise.reject(error.response.data);
    } else {
      uniqueMessageBox('网络错误');
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
