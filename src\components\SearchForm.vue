<template>
  <div class="search-form-container">
    <div class="search-form">
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item
          v-for="(e, j) in formKeysData"
          :key="j"
          :class="{
            'picker-style': e.type === 'date-picker',
            'select-platform': e.type === 'select',
            'input-style': e.type === 'input',
          }">
          <el-input
            v-if="e.type === 'input' && !isModelCategory"
            :prefix-icon="Search"
            v-model="formInline[e.key]"
            style="width: 225px"
            :placeholder="'请输入' + e.label"
            @keyup.enter="onSubmit"
            @input="debouncedSearch" />
          <el-select
            v-if="e.type === 'select' && !isModelCategory"
            v-model="formInline[e.key]"
            :placeholder="e.placeholder || '请选择' + e.label"
            clearable
            class="select-default"
            popper-class="select-option"
            :style="e.width ? `width: ${e.width}px` : ''"
            @change="handleSelectChange">
            <el-option
              v-for="op in e.dataList"
              :label="op.name"
              :value="op.value"
              :key="op.value" />
          </el-select>
          <el-date-picker
            v-if="e.type === 'date-picker'"
            v-model="formInline[e.key]"
            type="daterange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-value="[
              new Date(
                currentDate.getFullYear(),
                currentDate.getMonth() - 1,
                currentDate.getDate()
              ),
              new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate()),
            ]" />
        </el-form-item>
        <el-form-item class="form-btn"></el-form-item>

        <!-- 素材管理 -->
        <div
          v-if="routeNameMap[currentRoute] == '素材管理'"
          style="position: absolute; left: 0px; top: 6px; display: flex; align-items: center">
          <span
            class="progress-title"
            :class="{
              clickable: isModelCategory,
              'public-material': currentRoute === '/default_material',
            }"
            @click="isModelCategory && toggleBack()">
            {{ currentRoute === '/default_material' ? '公共素材' : '素材管理' }}
          </span>
          <self-progress
            v-if="currentRoute !== '/default_material'"
            :sceneStorage="[materialStorage.userUsedStorage, materialStorage.userPackageStorage]"
            color="#158E64"
            titleText="素材总容量"
            unitText="MB"></self-progress>
          <div v-if="currentRoute === '/default_material'" class="model-category-wrapper">
            <div v-if="!isModelCategory" class="model-category-title" @click="toggleModelCategory">
              模型分类管理
            </div>
            <div v-else style="display: flex; align-items: center; position: relative; left: -12px">
              <img
                src="@/assets/images/jiantou233.png"
                alt=""
                class="model-category-icon"
                style="width: 24px; height: 24px; margin-right: 8px" />
              <span class="model-category-tag">模型分类管理</span>
            </div>
          </div>
        </div>

        <!-- 空间管理 -->
        <div
          v-if="routeNameMap[currentRoute] == '空间管理'"
          style="position: absolute; left: 0px; top: 6px; display: flex; align-items: center">
          <span class="progress-title">空间管理</span>
          <self-progress
            :sceneStorage="[spaceStorage.spaceUseNum, spaceStorage.packageNum]"
            color="#2E76FF"
            titleText="空间总容量(图片数量)"
            unitText="张"></self-progress>
        </div>

        <!-- 路径导航 -->
        <div
          v-if="routeNameMap[currentRoute] == '路径导航'"
          style="position: absolute; left: 0px; top: 6px; display: flex; align-items: center">
          <span class="progress-title">路径导航</span>
          <!-- <self-progress
            :sceneStorage="[materialStorage.userUsedStorage, materialStorage.userPackageStorage]"
            color="#158E64"
            titleText="素材总容量"
            unitText="MB"></self-progress> -->
        </div>

        <!-- 设备管理 -->
        <div
          v-if="routeNameMap[currentRoute] == '设备管理'"
          style="position: absolute; left: 0px; top: 6px; display: flex; align-items: center">
          <span class="progress-title">设备管理</span>
        </div>

        <!-- 客户管理 -->
        <div
          v-if="routeNameMap[currentRoute] == '客户管理'"
          style="position: absolute; left: 0px; top: 6px; display: flex; align-items: center">
          <span class="progress-title">客户管理</span>
        </div>
      </el-form>
    </div>
    <div class="search-form-bottom" v-if="uploadButtons.length > 0 && showSearchFormBottom">
      <div class="upload-buttons">
        <div
          v-for="(button, index) in uploadButtons"
          :key="index"
          class="upload-btn"
          :class="{ 'ai-generate': button.isAI }"
          @click="handleButtonClick(button)">
          <div class="btn-content">
            <img class="upload-icon" :src="button.icon" alt="" />
            <span v-if="!button.isAI" class="upload-text">{{ button.prefix }}</span>
            <span :class="button.isAI ? '' : 'type-text'">{{ button.label }}</span>
          </div>
          <div class="hover-icon">
            <img v-if="button.isAI" src="@/assets/images/ai55.png" class="hover-icon-img" alt="" />
            <img
              v-if="button.isAI"
              class="upload-icon"
              style="width: 24px; height: 24px; top: 2px; left: -4px"
              src="@/assets/images/ai44.png"
              alt="" />
            <img
              v-else
              src="@/assets/images/circlerightup.png"
              class="upload-icon"
              style="width: 24px; height: 24px; top: 2px; left: -4px"
              alt="" />
          </div>
        </div>

        <!-- 设备数据展示区域 -->
        <div v-if="Object.keys(deviceData).length > 0" class="device-data-section">
          <div v-for="(item, key) in deviceData" :key="key" class="device-data-item">
            <div class="device-data-title">{{ item.title }}</div>
            <div class="device-data-value">{{ item.value }}</div>
            <div class="device-data-line"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, reactive, watch } from 'vue';
import { routeNameMap, scenePlatformTabs } from '@/config';
import SelfProgress from '@/components/SelfProgress.vue';
import { Search } from '@element-plus/icons-vue';
import { useStore } from 'vuex';

interface UploadButton {
  icon: string;
  prefix?: string;
  label: string;
  isAI?: boolean;
  action?: string;
  materialType?: string;
  type?: string;
}

interface DeviceDataItem {
  title: string;
  value: number;
}

const props = defineProps({
  formKeysData: {
    default: null,
    type: Object,
  },
  searchDataEvent: {
    default: null,
    type: Function,
  },
  currentRoute: {
    type: String,
    required: true,
  },
  uploadButtons: {
    type: Array as () => UploadButton[],
    default: () => [],
  },
  deviceData: {
    type: Object as () => Record<string, DeviceDataItem>,
    default: () => ({}),
  },
  showSearchFormBottom: {
    type: Boolean,
    default: true,
  },
});

let formInline: any = reactive({});
const currentDate = new Date();
const materialStorage: any = ref({}); // 素材容量
const spaceStorage: any = ref({}); // 空间容量

const store = useStore();

const isModelCategory = ref(false);

onMounted(() => {
  props.formKeysData.forEach((v: any) => {
    formInline[v.key] = '';
  });
});

const onSubmit = () => {
  Object.keys(formInline).forEach((k) => {
    if (formInline[k] && formInline[k].trim) {
      formInline[k] = formInline[k].trim();
    }
  });
  formInline['pageNo'] = 1;
  props.searchDataEvent && props.searchDataEvent(formInline);
};

// 防抖搜索函数
let searchTimer: any = null;
const debouncedSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer);
  }
  searchTimer = setTimeout(() => {
    onSubmit();
  }, 500); // 500ms 防抖延迟
};

const resetForm = () => {
  props.formKeysData.forEach((v: any) => {
    if (v.key == 'date') {
      formInline[v.keys[0]] = '';
      formInline[v.keys[1]] = '';
    }
    formInline[v.key] = '';
  });
  formInline['pageNo'] = 1;
  props.searchDataEvent && props.searchDataEvent(formInline, 'reset');
};

// 处理下拉框变化事件
const handleSelectChange = () => {
  // 当下拉框值改变时，自动触发搜索
  Object.keys(formInline).forEach((k) => {
    if (formInline[k] && formInline[k].trim) {
      formInline[k] = formInline[k].trim();
    }
  });
  formInline['pageNo'] = 1;
  props.searchDataEvent && props.searchDataEvent(formInline);
};

const handleUpload = (type: string) => {
  // 根据类型跳转到对应的上传页面
  const materialTypeMap: { [key: string]: string } = {
    model: '4',
    image: '3',
    video: '1',
    audio: '2',
  };

  // 可以通过emit触发父组件事件，或者直接跳转到上传页面
  console.log(`上传${type}素材，类型：${materialTypeMap[type]}`);

  // 如果父组件有上传事件处理函数，可以调用
  if (props.searchDataEvent) {
    props.searchDataEvent({ action: 'upload', materialType: materialTypeMap[type] });
  }
};

const handleAIGenerate = () => {
  // 处理AI生成素材
  console.log('AI生成素材');

  // 如果父组件有AI生成事件处理函数，可以调用
  if (props.searchDataEvent) {
    props.searchDataEvent({ action: 'ai-generate' });
  }
};

const handleButtonClick = (button: UploadButton) => {
  // 处理按钮点击事件
  console.log('按钮点击:', button);

  if (props.searchDataEvent) {
    props.searchDataEvent({
      action: button.action || 'upload',
      materialType: button.materialType,
      type: button.type,
    });
  }
};

watch(
  () => store.state.storageData,
  (newState) => {
    materialStorage.value = newState.material;
    spaceStorage.value = newState.space;
  },
  { deep: true, immediate: true }
);

const emit = defineEmits(['toggleModelCategory']);

const toggleModelCategory = () => {
  console.log('SearchForm toggle clicked');
  isModelCategory.value = true;
  emit('toggleModelCategory', true);
};

const toggleBack = () => {
  isModelCategory.value = false;
  emit('toggleModelCategory', false);
};
</script>
<style scoped lang="less">
.model-category-wrapper {
  display: flex;
  align-items: center;
  height: 32px;
}

.model-category-tag {
  font-weight: bold;
  font-size: 20px;
  color: #1e1e1e;
  margin-right: 16px;
  display: flex;
  align-items: center;
  height: 32px;
}
.search-form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.search-form {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  top: 34px;
  margin-top: -34px;

  .model-category-title {
    width: 114px;
    height: 32px;
    background: #2e76ff;
    border-radius: 4px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    cursor: pointer;
  }

  .block {
    position: absolute;
    top: -13px;
    left: 9px;
  }

  .progress {
    position: absolute;
    right: 116px;
    top: 10px;
    display: block;
    transform: translateY(7px);
    margin-left: 24px;
    font-size: 12px;
    color: #1e1e1e;
    line-height: 18px;
  }

  // :deep(.el-form-item__content) {
  //   min-width: 200px;
  // }

  :deep(.el-select__wrapper) {
    min-height: 34px;
  }

  :deep(.el-select__wrapper) {
    border: 1px solid rgba(54, 113, 254, 0.3);
  }

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.picker-style .el-form-item__content) {
    width: 202px !important;

    .el-input__wrapper {
      padding-right: 0 !important;
      padding-left: 10px !important;
    }
  }

  // 素材类型下拉选择器样式，参考 ItemListView
  :deep(.select-platform) {
    .el-select {
      width: 122px;

      .el-select__wrapper {
        box-shadow: none !important;
        border: none !important;
        background: #ffffff;
        border-radius: 4px;
      }
    }
  }

  .el-size1 {
    width: 68px;
    height: 34px;
    box-sizing: border-box;
    margin-left: 8px;
  }

  .el-col {
    text-align: left;
  }

  .el-col.btn {
    text-align: right;
  }

  .search-form .el-form-item__label {
    font-size: 14px;
    font-weight: 400;
    color: #6f6f6f;
  }

  .form-btn {
    margin-left: -8px;
    margin-right: 0;
    min-height: 0;
  }
}

#app .search-form .demo-form-inline {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;

  ::v-deep(.el-input__wrapper) {
    height: 34px;
    box-shadow: none !important;
    border: none !important;
    background: #ffffff;
    border-radius: 4px;
  }

  ::v-deep(.el-input__wrapper .el-input__inner) {
    font-size: 14px;
  }

  ::v-deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 0;
  }
}

.search-form-bottom {
  margin-top: 16px;

  .upload-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 20px 0;

    .upload-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      width: 155px;
      height: 50px;
      padding: 10px 24px;
      background: linear-gradient(360deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
      border-radius: 10px;
      border: 1px solid rgba(0, 0, 0, 0.06);
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      font-weight: 500;
      color: #333333;

      .btn-content {
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;

        .upload-icon {
          width: 24px;
          height: 24px;
          transition: all 0.3s ease;
        }
      }

      .hover-icon {
        opacity: 0;
        transform: translateX(10px);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        .upload-icon {
          width: 24px;
          height: 24px;
        }

        .icon {
          font-size: 16px;
          color: #3671fe;
        }
      }

      &:hover {
        background: linear-gradient(
            182deg,
            rgba(46, 118, 255, 0.18) 0%,
            rgba(255, 255, 255, 0) 100%
          ),
          #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.04);
        color: #3671fe;

        .btn-content {
          transform: translateX(-30px);

          .upload-icon {
            opacity: 0;
          }
        }

        .hover-icon {
          opacity: 1;
          transform: translateX(0);
        }
      }

      &.ai-generate {
        background: var(
          --ai,
          linear-gradient(90deg, #f6297b -20.7%, #d029f6 29.48%, #7b13fb 71.9%, #13ccfb 134.86%)
        );
        color: #ffffff;
        border: none;

        .hover-icon .icon {
          color: #ffffff;
        }

        &:hover {
          background: var(
            --ai,
            linear-gradient(90deg, #f6297b -20.7%, #d029f6 29.48%, #7b13fb 71.9%, #13ccfb 134.86%)
          );
          color: #ffffff;
          opacity: 0.9;

          .btn-content {
            transform: translateX(-30px);

            .upload-icon {
              opacity: 0;
            }
          }

          .hover-icon {
            opacity: 1;
            transform: translateX(0);
          }
        }
      }
    }
  }
}

.upload-text {
  transition: opacity 0.2s;
}
.upload-btn:hover .upload-text {
  display: none;
}
.type-text {
  margin-left: 2px;
}
.progress-title {
  width: 80px;
  height: 32px;
  font-weight: bold;
  font-size: 20px;
  color: #1e1e1e;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-right: 16px;
  display: flex;
  align-items: center;

  &.clickable {
    cursor: pointer;
    &:hover {
      color: #2e76ff;
    }
  }

  &.public-material {
    font-weight: bold;
    font-size: 20px;
    color: #797979;
  }
}

.device-data-section {
  display: flex;
  margin-left: 20px;
  border-radius: 10px;
  background: #ffffff;
  overflow: hidden;

  .device-data-item {
    height: 50px;
    min-width: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 10px 20px;
    gap: 2px;
    flex-direction: row;
    background: #f0f5ff;
    align-items: center;
    justify-content: space-between;
    // 最后一个没有line
    &:last-child {
      .device-data-line {
        display: none;
      }
    }
    .device-data-line {
      width: 1px;
      height: 36px;
      background: rgba(0, 0, 0, 0.06);
      border-radius: 0px 0px 0px 0px;
    }
    .device-data-title {
      font-weight: 500;
      font-size: 12px;
      color: #1e1e1e;
      line-height: 14px;
    }

    .device-data-value {
      font-weight: bold;
      font-size: 22px;
      color: #000000;
      line-height: 20px;
    }

    &:last-child {
      border-right: none;
    }
  }
}

.hover-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .hover-icon-img {
    position: absolute;
    width: 66px;
    height: 66px;
    z-index: 1;
  }

  .upload-icon {
    position: relative;
    width: 24px;
    height: 24px;
    z-index: 2;
  }
}
</style>

<!--
使用示例：

// 素材管理页面
const materialButtons = [
  {
    icon: '@/assets/images/upload222.png',
    prefix: '上传',
    label: '模型素材',
    materialType: '4',
    action: 'upload',
    type: 'model'
  },
  {
    icon: '@/assets/images/upload222.png',
    prefix: '上传',
    label: '图片素材',
    materialType: '3',
    action: 'upload',
    type: 'image'
  },
  {
    icon: '@/assets/images/upload222.png',
    prefix: '上传',
    label: '视频素材',
    materialType: '1',
    action: 'upload',
    type: 'video'
  },
  {
    icon: '@/assets/images/upload222.png',
    prefix: '上传',
    label: '音频素材',
    materialType: '2',
    action: 'upload',
    type: 'audio'
  },
  {
    icon: '@/assets/images/upload222.png',
    label: 'AI生成 素材',
    isAI: true,
    action: 'ai-generate'
  }
];

// 空间管理页面
const spaceButtons = [
  {
    icon: '@/assets/images/upload222.png',
    prefix: '上传',
    label: '空间数据',
    action: 'upload',
    type: 'space'
  }
];

// 路径导航页面
const navigationButtons = [
  {
    icon: '@/assets/images/upload222.png',
    prefix: '上传',
    label: '导航数据',
    action: 'upload',
    type: 'navigation'
  }
];

// 在模板中使用
<search-form
  :form-keys-data="keysData"
  :search-data-event="searchDataEvent"
  :current-route="currentRoute"
  :upload-buttons="materialButtons"
/>
-->
