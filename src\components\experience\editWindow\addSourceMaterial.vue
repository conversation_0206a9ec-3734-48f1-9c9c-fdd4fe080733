<template>
  <div class="new-mask">
    <div>
      <img
        class="closed"
        src="@/assets/images/experience-icon/mask-close.png"
        alt=""
        @click="closeEvent" />
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" status-icon>
        <div class="title">添加素材</div>
        <div class="list-title">
          素材名称
          <span class="required">*</span>
        </div>
        <el-form-item label="" prop="materialName">
          <el-input
            style="width: 100%; margin-top: 20px"
            v-model="ruleForm.materialName"
            placeholder="请输入素材名称" />
        </el-form-item>
        <div class="list-title">
          素材类型
          <span class="required">*</span>
        </div>
        <el-form-item label="" prop="materialType">
          <el-select
            v-model="ruleForm.materialType"
            placeholder="请选择素材类型"
            class="select-default"
            popper-class="select-option"
            :suffix-icon="DropDown"
            style="width: 100%; margin-top: 20px"
            @change="changeMaterialType">
            <el-option
              v-for="(item, index) in materialType"
              :key="index"
              :label="item.name"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <div class="list-title">备注</div>
        <el-form-item label="" prop="materialDescribe">
          <el-input
            style="width: 100%; margin-top: 20px; height: 100px"
            v-model="ruleForm.materialDescribe"
            type="textarea"
            resize="none"
            placeholder="请输入2-20字描述信息" />
        </el-form-item>
        <el-form-item label="" prop="ossKey">
          <el-upload
            :before-upload="beforeUpload"
            class="upload-demo"
            :action="uploadURL"
            :on-change="handleChange"
            :headers="headerObj"
            :show-file-list="false"
            :on-success="uploaded"
            :on-error="uploadFailed">
            <div class="upload-materials">{{ sourceName || '+上传素材' }}</div>
          </el-upload>
        </el-form-item>
        <div class="upload-tips">仅支持glb、gltf格式</div>
        <div class="sure-btn" @click="submitForm(ruleFormRef)">完成</div>
      </el-form>
    </div>
    <tips-view></tips-view>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import DropDown from '@/components/DropDown.vue';
import type { FormInstance, FormRules } from 'element-plus';
import { addPersonMaterialMeta } from '@/api';
import { materialType } from '@/config';
import { convertToLowerCase } from '@/utils/index';
import { useStore } from 'vuex';
import TipsView from '@/components/TipsView.vue';

const props = defineProps({
  hideMask: {
    default: null,
    type: Function,
  },
});

const sceneTypes: any = ref([]);
const token = window.localStorage.getItem('token');
const uploadURL = ref(''); // 上传素材接口地址
const uploadThumbnail = ref(''); // 上传缩略图接口地址
const baseURL = process.env.NODE_ENV === 'production' ? '/api' : '/api1'; // 基础url
const loadedURL = ref(''); // 预览素材照片地址
const njyj_version = window.localStorage.getItem('njyj-version');
const headerObj: any = ref({
  token: token || '',
});
if (njyj_version) {
  headerObj.value['njyj-version'] = njyj_version;
}

const sourceName = ref('');
let rawFileName = '';

interface RuleForm {
  materialName: string;
  materialType: string;
  materialDescribe: string;
  ossKey: string;
}

const ruleFormRef = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  materialName: '',
  materialType: '',
  materialDescribe: '',
  ossKey: '',
});

const rules = reactive<FormRules<RuleForm>>({
  materialName: [
    { required: true, message: '请输入素材名称', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value && value.trim() === '') {
          callback(new Error('素材名称不能全部为空格'));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
  materialType: [{ required: true, message: '请选择素材类型', trigger: 'blur' }],
  ossKey: [{ required: true, message: '请上传素材', trigger: 'blur' }],
});

const store = useStore();

const uploadFailed = () => {
  store.state.showTips = '您的资源上传失败，请检查网络';
};

const handleChange = (file: any) => {
  loadedURL.value = URL.createObjectURL(file.raw);
};

const uploaded = (res: any) => {
  ruleForm.ossKey = res.data.materialOssKey;
  if (ruleForm.materialType === '4') {
    ruleForm.wxModelStorageOssKey = res.data.materialOssKey;
  }
  sourceName.value = rawFileName;
};

const beforeUpload = (rawFile: any) => {
  rawFileName = rawFile.name;
  if (ruleForm.materialType === '4') {
    const modelFormat = convertToLowerCase(rawFile.name.split('.').slice(-1)[0]);
    if (!['gltf', 'glb'].includes(modelFormat)) {
      store.state.showTips = '上传失败，请选择glb或gltf格式文件';
      return;
    }
    uploadURL.value = `${baseURL}/material/uploadMaterial?fileName=${rawFile.name}&materialType=${ruleForm.materialType}&platform=4&modelFormat=${modelFormat}`;
    ruleForm.materialFormat = modelFormat;
  } else {
    uploadURL.value = `${baseURL}/material/uploadMaterial?fileName=${rawFile.name}&materialType=${ruleForm.materialType}`;
    ruleForm.materialFormat = rawFile.type;
  }
  ruleForm.storageSize = rawFile.size;

  loadedURL.value = '';
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (sourceName.value != '') {
        addPersonMaterialMeta({ ...ruleForm }).then((res: any) => {
          props.hideMask(true);
        });
        sourceName.value = '';
      }
    } else {
    }
  });
};

const changeMaterialType = (v: string) => {
  ruleForm.wxModelStorageOssKey = '';
};

const closeEvent = () => {
  props.hideMask();
};

onMounted(() => {
  //
});
</script>
<style scoped lang="less">
.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  & > div {
    position: relative;
    width: 520px;
    height: 738px;
    background: #ffffff;
    border-radius: 8px;
    padding: 60px 40px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 600;
    font-size: 18px;
    color: #3d566c;

    .closed {
      width: 17px;
      height: 17px;
      position: absolute;
      right: 31px;
      top: 31px;
      cursor: pointer;
    }

    .title {
      font-weight: 600;
      font-size: 22px;
      color: #3d566c;
      text-align: center;
    }

    .upload-materials {
      width: 440px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      border-radius: 6px;
      border: 1px dashed #3671fe;
      background: rgba(54, 113, 254, 0.06);
      font-size: 18px;
      color: #3671fe;
      margin-top: 30px;
      cursor: pointer;
    }

    .upload-tips {
      font-weight: bold;
      font-size: 12px;
      color: rgba(61, 86, 108, 0.3);
      margin-top: 10px;
    }

    .list-title {
      font-weight: 600;
      font-size: 18px;
      color: #3d566c;
      text-align: left;
      margin-top: 30px;

      .required {
        color: #ff3838;
        margin-left: 5px;
        position: relative;
        top: -2px;
      }
    }

    .sure-btn {
      width: 340px;
      height: 42px;
      line-height: 42px;
      background: #3671fe;
      border-radius: 6px;
      margin: 30px 0 0 50px;
      font-weight: bold;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      cursor: pointer;
    }

    ::v-deep(.el-textarea__inner) {
      padding: 10px 20px;
      border-radius: 4px;
      border: 1px solid rgba(54, 113, 254, 0.3);
      box-shadow: none;
    }
  }
}
</style>
