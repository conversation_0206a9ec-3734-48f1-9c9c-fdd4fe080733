<template>
  <div class="canvas-tool">
    <img src="@/assets/images/icon/scenex.png" />
    <span class="scene-name">{{ sceneInfo.sceneName }}</span>
    <div class="canvas-tool-icon-box">
      <div class="canvas-tool-icon" v-for="(item, index) in toolsList" :key="index"
        :class="operateType == item.name ? `l${index + 1} active` : `l${index + 1}`" @click="operateEvent(item.name)">
        <img :src="operateType == item.name ? item.url_a : item.url" />
      </div>
      <div @click="handleFullScreen" class="canvas-tool-icon" :class="has_full ? 'full-no-screen' : 'full-screen'">
      </div>
      <div class="canvas-tool-icon save-scene" @click="saveScene"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
const props = defineProps({
  sceneInfo: {
    default: null,
    type: Object
  },
  saveSceneInfo: {
    default: null,
    type: Function
  },
  changeOperateType: {
    default: null,
    type: Function
  }
})

const toolsList = [
  {
    name: '移动',
    url: require('@/assets/images/icon/move-3d-icon.png'),
    url_a: require('@/assets/images/icon/move-3d-iconA.png'),
  },
  {
    name: '旋转',
    url: require('@/assets/images/icon/rotate-3d-icon.png'),
    url_a: require('@/assets/images/icon/rotate-3d-iconA.png')
  },
  {
    name: '缩放',
    url: require('@/assets/images/icon/scale-3d-icon.png'),
    url_a: require('@/assets/images/icon/scale-3d-iconA.png')
  }
]
const operateType = ref('')
const has_full = ref(false)

const handleFullScreen = () => {
  let element = document.documentElement;     // 返回 html dom 中的root 节点 <html>
  // 判断浏览器设备类型
  if (!has_full.value) {
    element.requestFullscreen && element.requestFullscreen();
  } else {
    document.exitFullscreen && document.exitFullscreen();
  }
}

const saveScene = () => {
  props.saveSceneInfo()
}

const operateEvent = (name: string) => {
  operateType.value = name
  props.changeOperateType(name)
}

onMounted(() => {
  document.addEventListener('fullscreenchange', (e: any) => {
    if (!has_full.value) {
      has_full.value = true
    } else {
      has_full.value = false
    }
  })
})

defineExpose({
  operateEvent
})

</script>
<style scoped lang="less">
.canvas-tool {
  width: 396px;
  height: 48px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 13px;
  position: fixed;
  top: 118px;
  left: 50%;
  margin-left: -210px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 20px;

  span {
    margin-right: 10px;
    width: calc(100% - 190px);
    max-width: 165px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;
    text-align: left;
  }

  .on-off {
    width: 65px;
    cursor: pointer;
  }

  .canvas-tool-icon-box {
    width: 228px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .canvas-tool-icon {
    width: 32px;
    height: 32px;
    cursor: pointer;
    border-radius: 8px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .canvas-tool-icon {
    position: relative;

    &:hover {
      &::before {
        content: '';
        position: absolute;
        left: -3px;
        top: 39px;
        font-size: 12px;
        width: 38px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        background-color: #333537;
        letter-spacing: 1px;
        border-radius: 4px;
        color: #fff;
      }

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 27px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #333537;
      }
    }
  }

  .canvas-tool-icon.l1 {
    &:hover {
      &::before {
        content: '\79fb\52a8';
      }
    }
  }

  .canvas-tool-icon.l2 {
    &:hover {
      &::before {
        content: '\65cb\8f6c';
      }
    }
  }

  .canvas-tool-icon.l3 {
    &:hover {
      &::before {
        content: '\7f29\653e';
      }
    }
  }

  .save-scene {
    background: url(~@/assets/images/icon/save-3d-icon.png);
    background-size: 100% 100%;
    position: relative;

    &:hover {
      background: url(~@/assets/images/icon/save-3d-iconA.png);
      background-size: 100% 100%;

      &::before {
        content: '\4fdd\5b58';
        position: absolute;
        left: -3px;
        top: 39px;
        font-size: 12px;
        width: 38px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        background-color: #333537;
        letter-spacing: 1px;
        border-radius: 4px;
        color: #fff;
      }

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 27px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #333537;
      }
    }
  }

  .full-screen,
  .full-no-screen {
    background: url(~@/assets/images/icon/full-screen.png);
    background-size: 100% 100%;
    position: relative;
  }

  .full-screen {
    &:hover {
      background: url(~@/assets/images/icon/full-screenA.png);

      &::before {
        content: '\5168\5c4f';
        position: absolute;
        left: -3px;
        top: 39px;
        font-size: 12px;
        width: 38px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        background-color: #333537;
        letter-spacing: 1px;
        border-radius: 4px;
        color: #fff;
      }

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 27px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #333537;
      }
    }
  }

  .full-no-screen {
    &:hover {
      background: url(~@/assets/images/icon/full-screenA.png);

      &::before {
        content: '\53d6\6d88\5168\5c4f';
        position: absolute;
        left: -17px;
        top: 39px;
        font-size: 12px;
        width: 66px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        background-color: #333537;
        letter-spacing: 1px;
        border-radius: 4px;
        color: #fff;
      }

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 27px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #333537;
      }
    }
  }
}
</style>