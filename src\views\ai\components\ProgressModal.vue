<template>
  <div class="progress-modal-mask">
    <div class="progress-modal">
      <div class="modal-bg"></div>
      <div class="modal-gradient"></div>
      <div class="close-icon-wrap">
        <img
          src="@/assets/images/close-tips.png"
          @click="$emit('close')"
          alt="关闭"
          class="close-icon" />
      </div>
      <img :src="icon" class="progress-icon" />
      <div class="progress-bar-wrap">
        <div class="progress-bar-bg"></div>
        <div class="progress-bar" :style="{ width: currentProgress + '%' }"></div>
      </div>
      <div class="progress-text">{{ computedText }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';

interface Props {
  icon: string;
  progress?: number;
  text?: string;
  rank: number;
  status: number;
}

const props = defineProps<Props>();
const currentProgress = ref(0);
const baseProgress = ref(0); // 基础进度，随时间增长
let progressInterval: ReturnType<typeof setInterval> | null = null;

// 计算目标进度
const targetProgress = computed(() => {
  // status为4时表示完成，进度条100%
  if (props.status === 4) {
    return 100;
  }

  // status为2时表示失败，进度条保持当前进度
  if (props.status === 2) {
    return currentProgress.value;
  }

  // 基础进度上限
  const maxProgress = props.rank === 0 ? 95 : 80;

  // 如果有rank，降低进度上限
  if (props.rank > 0) {
    // rank越大，进度越小，但保持最小10%的进度
    const rankFactor = Math.max(0.1, 1 - props.rank * 0.1);
    return Math.max(10, maxProgress * rankFactor);
  }

  return maxProgress;
});

// 更新进度的函数
const updateProgress = () => {
  // 首先更新基础进度
  if (props.status !== 4 && props.status !== 2) {
    // 基础进度每次增加0.1%，但不超过60%
    baseProgress.value = Math.min(60, baseProgress.value + 0.1);
  }

  const target = targetProgress.value;
  const current = currentProgress.value;

  // 计算实际目标进度（基础进度 + 状态进度的加权和）
  let actualTarget = Math.max(baseProgress.value, target * 0.7 + baseProgress.value * 0.3);

  if (current < actualTarget) {
    // 根据与目标的距离调整增长速度
    const distance = actualTarget - current;
    // 确保即使距离很小也有最小增长速度
    const increment = Math.max(0.05, Math.min(0.2, distance * 0.03));
    currentProgress.value = Math.min(actualTarget, current + increment);
  } else if (current > actualTarget && props.status !== 4) {
    // 如果目标进度比当前小（比如排队数增加），缓慢降低进度
    currentProgress.value = Math.max(actualTarget, current - 0.2);
  }
};

// 监听状态变化
watch(
  () => props.status,
  (newStatus) => {
    if (newStatus === 4) {
      // 完成时快速到达100%
      currentProgress.value = 100;
      baseProgress.value = 100;
    }
  },
  { immediate: true }
);

// 启动进度更新定时器
onMounted(() => {
  // 初始化进度
  currentProgress.value = 0;
  baseProgress.value = 0;
  // 每50ms更新一次进度
  progressInterval = setInterval(updateProgress, 50);
});

// 清理定时器
onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval);
    progressInterval = null;
  }
});

const computedText = computed(() => {
  if (props.status === 4) {
    return '处理完成';
  }
  if (props.status === 2) {
    return '处理失败';
  }
  if (props.rank > 0) {
    return `排队中，前方还有${props.rank}个任务`;
  }
  if (props.rank === 0) {
    return '处理中...';
  }
  return props.text || '处理中...';
});
</script>

<style scoped>
.progress-modal-mask {
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.35);
  display: flex;
  align-items: center;
  justify-content: center;
}
.progress-modal {
  position: relative;
  min-width: 420px;
  min-height: 300px;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px 32px 32px 32px;
  overflow: hidden;
  background: transparent;
}
.modal-bg {
  position: absolute;
  inset: 0;
  background: #fff;
  border-radius: 10px;
  z-index: 0;
}
.modal-gradient {
  position: absolute;
  inset: 0;
  border-radius: 10px;
  background: linear-gradient(
    44deg,
    rgba(246, 41, 123, 0.1) -0.9%,
    rgba(208, 41, 246, 0.1) 31.63%,
    rgba(123, 19, 251, 0.1) 59.12%,
    rgba(19, 204, 251, 0.1) 99.93%
  );
  z-index: 1;
  pointer-events: none;
}
.progress-modal > *:not(.modal-bg):not(.modal-gradient) {
  position: relative;
  z-index: 2;
}
.close-icon-wrap {
  width: 100%;
  height: 48px;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-top: 10px;
  box-sizing: border-box;
}
.close-icon {
  width: 48px;
  height: 48px;
  cursor: pointer;
  position: relative;
  left: 24px;
}

.progress-icon {
  width: 140px;
  height: 140px;
  margin-bottom: 24px;
  margin-top: 8px;
}
.progress-bar-wrap {
  width: 80%;
  height: 4px;
  background: rgba(198, 211, 235, 0.2);
  border-radius: 28px 28px 28px 28px;
  background: none;
  margin-bottom: 36px;
  position: relative;
}
.progress-bar-bg {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-radius: 4px;
  width: 100%;
  height: 100%;
  z-index: 0;
}
.progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(90deg, #ff4dcd 0%, #2e76ff 100%);
  border-radius: 4px;
  height: 100%;
  z-index: 1;
  transition: width 0.05s linear;
}
.progress-text {
  margin-bottom: 8px;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;
}
</style>
