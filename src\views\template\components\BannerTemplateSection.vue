<template>
  <section class="template-section">
    <div class="template-section__header">
      <h2 class="template-section__title">Banner模板</h2>
      <button class="add-banner-btn" @click="handleAdd" type="button">添加Banner模板</button>
    </div>

    <draggable
      v-model="bannerData"
      class="template-grid template-grid--banner"
      :class="{ 'template-grid--dragging': isDragging }"
      item-key="id"
      :animation="200"
      :disabled="false"
      ghost-class="banner-card--ghost"
      chosen-class="banner-card--chosen"
      drag-class="banner-card--drag"
      @start="onDragStart"
      @end="onDragEnd">
      <template #item="{ element: item, index: i }">
        <div
          :key="`banner-${item.id}`"
          class="banner-card"
          :class="{ 'banner-card--hover': item.isHover }"
          @mouseenter="item.isHover = true"
          @mouseleave="item.isHover = false">
          <!-- Banner卡片图片容器 -->
          <div class="banner-card__image-wrapper">
            <!-- 占位图片 -->
            <div class="banner-card__placeholder">
              <div class="banner-card__content">
                <img
                  :src="item.bannerBackgroundPicUrl"
                  alt="Banner模板"
                  class="banner-background-image" />
                <img
                  :src="item.bannerCoverPicUrl || item.bannerCoverPic"
                  alt="Banner模板"
                  class="banner-cover-image" />
              </div>
            </div>

            <!-- 悬停遮罩层和操作按钮 -->
            <div class="banner-card__overlay" v-if="item.isHover">
              <div class="banner-card__actions">
                <button
                  class="banner-card__action-btn banner-card__action-btn--primary"
                  @click="handleEdit(item)"
                  type="button">
                  修改
                </button>
                <button
                  class="banner-card__action-btn banner-card__action-btn--danger"
                  @click="handleRemove(item)"
                  type="button">
                  移除
                </button>
              </div>
            </div>
          </div>
        </div>
      </template>
    </draggable>

    <!-- 添加Banner模板弹窗 -->
    <el-dialog
      v-model="showAddBannerDialog"
      :title="dialogTitle"
      width="453px"
      :close-on-click-modal="false"
      @close="handleCancel"
      class="add-banner-dialog">
      <div class="banner-form">
        <!-- 模板名称选择 -->
        <div class="form-item">
          <el-select
            v-model="selectedTemplate"
            class="template-select"
            :loading="isLoadingTemplates"
            clearable>
            <el-option
              v-for="template in availableTemplates"
              :key="template.id"
              :label="template.name"
              :value="template.value" />
          </el-select>
        </div>

        <!-- 图片上传区域 -->
        <div class="upload-section">
          <div class="upload-item">
            <div class="upload-label">上传封面图</div>
            <el-upload
              class="upload-area"
              :show-file-list="false"
              :before-upload="beforeCoverUpload"
              :http-request="handleCoverUpload"
              accept="image/png,image/jpg,image/jpeg">
              <div class="upload-placeholder">
                <img v-if="coverImageUrl" :src="coverImageUrl" class="uploaded-image" />
                <div v-else class="upload-content">
                  <img src="@/assets/images/starimg1.png" style="width: 28px" />
                </div>
              </div>
            </el-upload>
            <div class="upload-tips">
              点击图片可更换封面
              <br />
              支持512KB的png或jpg图片
            </div>
          </div>

          <div class="upload-item">
            <div class="upload-label">上传背景图</div>
            <el-upload
              class="upload-area"
              :show-file-list="false"
              :before-upload="beforeBackgroundUpload"
              :http-request="handleBackgroundUpload"
              accept="image/png,image/jpg,image/jpeg">
              <div class="upload-placeholder">
                <img v-if="backgroundImageUrl" :src="backgroundImageUrl" class="uploaded-image" />
                <div v-else class="upload-content">
                  <img src="@/assets/images/starimg1.png" style="width: 28px" />
                </div>
              </div>
            </el-upload>
            <div class="upload-tips">
              点击图片可更换封面
              <br />
              支持512KB的png或jpg图片
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirmAdd">{{ confirmButtonText }}</el-button>
        </div>
      </template>
    </el-dialog>
  </section>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import draggable from 'vuedraggable';
import { ElMessage, ElDialog, ElSelect, ElOption, ElButton, ElUpload } from 'element-plus';
import {
  queryTemplateScene,
  uploadBannerPic,
  uploadBannerBackGroundPic,
  setBannerTemplate,
  updateBannerTemplate,
} from '@/api';
import { getOssAccessPath } from '@/api/modules/storage';

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits([
  'update:modelValue',
  'edit',
  'remove',
  'drag-start',
  'drag-end',
  'refresh',
]);

// Computed
const bannerData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// State
const isDragging = ref(false);
const showAddBannerDialog = ref(false);
const selectedTemplate = ref('');
const coverImageUrl = ref('');
const backgroundImageUrl = ref('');

// 编辑状态管理
const isEditMode = ref(false);
const editingBanner = ref(null);

// 添加响应式监听，调试图片URL变化
watch(coverImageUrl, (newUrl) => {
  console.log('🖼️ 封面图URL变化:', newUrl);
});

watch(backgroundImageUrl, (newUrl) => {
  console.log('🖼️ 背景图URL变化:', newUrl);
});
// 存储上传后的图片URL（用于提交API）
const uploadedCoverPicUrl = ref('');
const uploadedBackgroundPicUrl = ref('');

// 可选择的模板列表（从API获取）
const availableTemplates = ref([]);
const isLoadingTemplates = ref(false);

// 计算弹窗标题
const dialogTitle = computed(() => {
  return isEditMode.value ? '修改Banner模板' : '添加Banner模板';
});

// 计算确认按钮文本
const confirmButtonText = computed(() => {
  return isEditMode.value ? '确定修改' : '确定添加';
});

// Methods
const onDragStart = (evt) => {
  console.log('🔄 开始拖拽Banner模板:', evt);
  isDragging.value = true;

  // 清除所有Banner模板的 hover 状态
  bannerData.value.forEach((item) => {
    item.isHover = false;
  });

  emit('drag-start', evt);
};

const onDragEnd = async (evt) => {
  console.log('✅ Banner模板拖拽结束:', evt);
  isDragging.value = false;

  if (evt.oldIndex !== evt.newIndex) {
    console.log(`Banner模板从位置 ${evt.oldIndex} 移动到 ${evt.newIndex}`);
  }

  emit('drag-end', evt);
};

const handleEdit = async (item) => {
  console.log('修改Banner模板:', item);

  // 设置编辑模式
  isEditMode.value = true;
  editingBanner.value = item;

  // 回显数据
  selectedTemplate.value = item.templateId || item.id;
  coverImageUrl.value = item.bannerCoverPicUrl || '';
  backgroundImageUrl.value = item.bannerBackgroundPicUrl || '';
  uploadedCoverPicUrl.value = item.bannerCoverPic || '';
  uploadedBackgroundPicUrl.value = item.bannerBackgroundPic || '';

  // 确保模板列表已加载
  if (availableTemplates.value.length === 0) {
    await fetchAvailableTemplates();
  }

  // 显示弹窗
  showAddBannerDialog.value = true;
};

const handleRemove = (item) => {
  console.log('删除Banner模板:', item);
  emit('remove', item);
};

// 添加操作
const handleAdd = async () => {
  // 设置添加模式
  isEditMode.value = false;
  editingBanner.value = null;

  // 重置表单
  resetForm();

  // 确保模板列表已加载
  if (availableTemplates.value.length === 0) {
    await fetchAvailableTemplates();
  }

  // 显示弹窗
  showAddBannerDialog.value = true;
};

// 图片上传相关方法
const beforeCoverUpload = (file) => {
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type);
  const isValidSize = file.size / 1024 <= 512; // 512KB

  if (!isValidType) {
    ElMessage.error('只支持 JPG/PNG 格式的图片!');
    return false;
  }
  if (!isValidSize) {
    ElMessage.error('图片大小不能超过 512KB!');
    return false;
  }
  return true;
};

const beforeBackgroundUpload = (file) => {
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type);
  const isValidSize = file.size / 1024 <= 512; // 512KB

  if (!isValidType) {
    ElMessage.error('只支持 JPG/PNG 格式的图片!');
    return false;
  }
  if (!isValidSize) {
    ElMessage.error('图片大小不能超过 512KB!');
    return false;
  }
  return true;
};

// 封面图上传处理
const handleCoverUpload = async (options) => {
  const { file, onSuccess, onError } = options;

  // 立即使用本地文件进行预览回显
  coverImageUrl.value = URL.createObjectURL(file);
  console.log('🖼️ 封面图立即回显:', coverImageUrl.value);

  try {
    console.log('🔄 开始上传Banner封面图:', file.name);

    // 调用封面图上传API
    const response = await uploadBannerPic(file);

    console.log('📡 封面图上传API响应:', response);

    if (response.code === 200 || response.code === '200') {
      // 保存API返回的图片URL（用于提交表单）
      let serverImageUrl = '';

      if (response.data && response.data.url) {
        serverImageUrl = response.data.url;
      } else if (response.data && typeof response.data === 'string') {
        serverImageUrl = response.data;
      } else if (typeof response.data === 'object' && response.data) {
        serverImageUrl = response.data.path || response.data.src || response.data.imageUrl || '';
      }

      // 保存服务器返回的URL用于提交
      if (serverImageUrl) {
        uploadedCoverPicUrl.value = serverImageUrl;
        console.log('✅ 封面图服务器URL保存成功:', serverImageUrl);
      }

      ElMessage.success('封面图上传成功');
      console.log('✅ 封面图上传成功:', response.data);

      // 调用成功回调
      onSuccess(response.data);
    } else {
      console.error('❌ 封面图上传失败:', response);
      ElMessage.error(response.message || '封面图上传失败');

      // 上传失败，清除预览图片
      coverImageUrl.value = '';
      onError(new Error(response.message || '上传失败'));
    }
  } catch (error) {
    console.error('❌ 封面图上传异常:', error);
    ElMessage.error('封面图上传失败，请重试');

    // 上传失败，清除预览图片
    coverImageUrl.value = '';
    onError(error);
  }
};

// 背景图上传处理
const handleBackgroundUpload = async (options) => {
  const { file, onSuccess, onError } = options;

  // 立即使用本地文件进行预览回显
  backgroundImageUrl.value = URL.createObjectURL(file);
  console.log('🖼️ 背景图立即回显:', backgroundImageUrl.value);

  try {
    console.log('🔄 开始上传Banner背景图:', file.name);

    // 调用背景图上传API
    const response = await uploadBannerBackGroundPic(file);

    console.log('📡 背景图上传API响应:', response);

    if (response.code === 200 || response.code === '200') {
      // 保存API返回的图片URL（用于提交表单）
      let serverImageUrl = '';

      if (response.data && response.data.url) {
        serverImageUrl = response.data.url;
      } else if (response.data && typeof response.data === 'string') {
        serverImageUrl = response.data;
      } else if (typeof response.data === 'object' && response.data) {
        serverImageUrl = response.data.path || response.data.src || response.data.imageUrl || '';
      }

      // 保存服务器返回的URL用于提交
      if (serverImageUrl) {
        uploadedBackgroundPicUrl.value = serverImageUrl;
        console.log('✅ 背景图服务器URL保存成功:', serverImageUrl);
      }

      ElMessage.success('背景图上传成功');
      console.log('✅ 背景图上传成功:', response.data);

      // 调用成功回调
      onSuccess(response.data);
    } else {
      console.error('❌ 背景图上传失败:', response);
      ElMessage.error(response.message || '背景图上传失败');

      // 上传失败，清除预览图片
      backgroundImageUrl.value = '';
      onError(new Error(response.message || '上传失败'));
    }
  } catch (error) {
    console.error('❌ 背景图上传异常:', error);
    ElMessage.error('背景图上传失败，请重试');

    // 上传失败，清除预览图片
    backgroundImageUrl.value = '';
    onError(error);
  }
};

// 获取可选择的模板列表
const fetchAvailableTemplates = async () => {
  try {
    isLoadingTemplates.value = true;
    console.log('🔄 开始获取小程序端模板列表...');

    // 请求小程序端的模板数据
    const response = await queryTemplateScene({
      scenePlatform: 3, // 小程序端
    });

    console.log('📡 模板列表API响应:', response);

    if (response.code === 200 || response.code === '200') {
      // 将API返回的数据转换为下拉选项格式
      availableTemplates.value = response.data.map((template) => ({
        id: template.id || template.sceneId,
        name: template.sceneName || template.name || `模板${template.id}`,
        value: template.id || template.sceneId,
      }));

      console.log('✅ 模板列表获取成功:', availableTemplates.value);
    } else {
      console.error('❌ 模板列表API响应码不匹配:', response.code);
      ElMessage.error('获取模板列表失败');
    }
  } catch (error) {
    console.error('❌ 获取模板列表失败:', error);
    ElMessage.error('获取模板列表失败，请重试');
  } finally {
    isLoadingTemplates.value = false;
  }
};

// 弹窗相关方法
const handleConfirmAdd = async () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请选择模板内容');
    return;
  }

  // 检查是否上传了图片
  if (!uploadedCoverPicUrl.value && !uploadedBackgroundPicUrl.value) {
    ElMessage.warning('请至少上传一张图片（封面图或背景图）');
    return;
  }

  try {
    if (isEditMode.value) {
      // 修改模式
      console.log('🔄 开始修改Banner模板:', {
        currentTemplateId: editingBanner.value.id,
        changeTemplateId: selectedTemplate.value,
        bannerCoverPic: uploadedCoverPicUrl.value,
        bannerBackGroundPic: uploadedBackgroundPicUrl.value,
      });

      const response = await updateBannerTemplate({
        currentTemplateId: editingBanner.value.id,
        changeTemplateId: selectedTemplate.value,
        bannerCoverPic: uploadedCoverPicUrl.value,
        bannerBackGroundPic: uploadedBackgroundPicUrl.value,
      });

      console.log('📡 修改Banner模板API响应:', response);

      if (response.code === 200 || response.code === '200') {
        ElMessage.success('Banner模板修改成功');
        console.log('✅ Banner模板修改成功:', response.data);
      } else {
        console.error('❌ Banner模板修改失败:', response);
        ElMessage.error(response.message || 'Banner模板修改失败');
        return;
      }
    } else {
      // 添加模式
      console.log('🔄 开始添加Banner模板:', {
        templateId: selectedTemplate.value,
        bannerCoverPic: uploadedCoverPicUrl.value,
        bannerBackGroundPic: uploadedBackgroundPicUrl.value,
      });

      const response = await setBannerTemplate({
        templateId: selectedTemplate.value,
        bannerCoverPic: uploadedCoverPicUrl.value,
        bannerBackGroundPic: uploadedBackgroundPicUrl.value,
      });

      console.log('📡 设置Banner模板API响应:', response);

      if (response.code === 200 || response.code === '200') {
        ElMessage.success('Banner模板添加成功');
        console.log('✅ Banner模板添加成功:', response.data);
      } else {
        console.error('❌ Banner模板添加失败:', response);
        ElMessage.error(response.message || 'Banner模板添加失败');
        return;
      }
    }

    // 关闭弹窗并重置表单
    showAddBannerDialog.value = false;
    resetForm();

    // 刷新Banner列表
    await fetchBannerList();
  } catch (error) {
    console.error('❌ Banner模板操作异常:', error);
    ElMessage.error(isEditMode.value ? 'Banner模板修改失败，请重试' : 'Banner模板添加失败，请重试');
  }
};

// 重置表单
const resetForm = () => {
  selectedTemplate.value = '';
  coverImageUrl.value = '';
  backgroundImageUrl.value = '';
  uploadedCoverPicUrl.value = '';
  uploadedBackgroundPicUrl.value = '';
  isEditMode.value = false;
  editingBanner.value = null;
};

// 取消操作
const handleCancel = () => {
  showAddBannerDialog.value = false;
  resetForm();
};

// 获取Banner推荐列表
const fetchBannerList = async () => {
  try {
    console.log('🔄 开始获取Banner推荐列表...');

    // 请求Banner推荐数据
    const response = await queryTemplateScene({
      bannerRecommend: 1,
    });

    console.log('📡 Banner列表API响应:', response);

    if (response.code === 200 || response.code === '200') {
      // 更新Banner数据
      const bannerList = response.data || [];
      console.log('✅ Banner列表获取成功:', bannerList);

      // 处理bannerBackgroundPic和bannerCoverPic字段，使用getOssAccessPath接口
      if (bannerList && bannerList.length > 0) {
        for (const banner of bannerList) {
          // 处理背景图
          if (banner.bannerBackgroundPic) {
            try {
              const ossResponse = await getOssAccessPath({ key: banner.bannerBackgroundPic });
              if (ossResponse.code === 200 || ossResponse.code === '200') {
                banner.bannerBackgroundPicUrl = ossResponse.data;
                console.log('✅ Banner背景图处理成功:', banner.bannerBackgroundPicUrl);
              }
            } catch (ossError) {
              console.error('❌ Banner背景图处理失败:', ossError);
            }
          }

          // 处理封面图
          if (banner.bannerCoverPic) {
            try {
              const ossResponse = await getOssAccessPath({ key: banner.bannerCoverPic });
              if (ossResponse.code === 200 || ossResponse.code === '200') {
                banner.bannerCoverPicUrl = ossResponse.data;
                console.log('✅ Banner封面图处理成功:', banner.bannerCoverPicUrl);
              }
            } catch (ossError) {
              console.error('❌ Banner封面图处理失败:', ossError);
            }
          }
        }
      }

      // 触发父组件更新Banner数据
      emit('update:modelValue', bannerList);

      return bannerList;
    } else {
      console.error('❌ Banner列表API响应码不匹配:', response.code);
      ElMessage.error('获取Banner列表失败');
      return [];
    }
  } catch (error) {
    console.error('❌ 获取Banner列表失败:', error);
    ElMessage.error('获取Banner列表失败，请重试');
    return [];
  }
};

// 监听弹窗打开，获取模板列表
const handleDialogOpen = () => {
  if (availableTemplates.value.length === 0) {
    fetchAvailableTemplates();
  }
};

// 组件挂载时获取模板列表和Banner列表
onMounted(() => {
  fetchAvailableTemplates();
  fetchBannerList();
});
</script>

<style lang="less" scoped>
// Banner模板卡片样式 - 参考TemplateCard结构
.banner-card {
  position: relative;
  background: #fff;
  border-radius: 10px;
  overflow: visible;
  cursor: move;
  transition: all 0.3s ease;
  // 预留边框空间，避免hover时尺寸变化
  border: 2px solid transparent;
  // 添加内边距，让内容与边框之间有间距
  padding: 2px;
  z-index: 0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &--hover {
    border-color: #2e76ff;
    box-shadow: 0 4px 16px rgba(46, 118, 255, 0.2);
    z-index: 10;
  }

  // 拖拽时的幽灵样式
  &--ghost {
    opacity: 0.5;
    background: #f0f0f0;
    border: 2px dashed #ccc;
    transform: rotate(5deg);
  }

  // 选中时的样式
  &--chosen {
    transform: scale(1.05);
    box-shadow: 0 8px 24px rgba(46, 118, 255, 0.3);
    z-index: 1000;
  }

  // 拖拽中的样式
  &--drag {
    transform: rotate(5deg);
    opacity: 0.8;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
  }
}

// Banner卡片图片容器
.banner-card__image-wrapper {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
  border-radius: 8px;
}

// Banner卡片占位内容
.banner-card__placeholder {
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.banner-card__content {
  text-align: center;
  padding: 0;
  height: auto;
  min-height: 400px;
  position: relative;
}

.banner-background-image {
  width: 100%;
  height: auto;
  max-height: none;
  object-fit: cover;
  border-radius: 8px;
}

.banner-cover-image {
  position: absolute;
  top: 57%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  width: 77%;
  object-fit: fill;
  border-radius: 8px;
  z-index: 1;
}

.banner-card__title {
  font-size: 16px;
  font-weight: 600;
  color: #1e1e1e;
  margin-bottom: 8px;
}

.banner-card__description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

// 悬浮遮罩层 - 完全参考TemplateCard
.banner-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1; // 默认显示，通过v-if控制显示/隐藏
  transition: opacity 0.3s ease;
  border-radius: 8px;
  pointer-events: auto; // 允许点击事件
  z-index: 20; // 确保遮罩层在封面图之上
}

// 操作按钮组 - 完全参考TemplateCard
.banner-card__actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

// 操作按钮 - 完全参考TemplateCard样式
.banner-card__action-btn {
  padding: 8px 24px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-width: 92px;

  &--primary {
    background: #2e76ff;
    color: #ffffff;

    &:hover {
      background: #1251cb;
    }
  }

  &--danger {
    background: #f56565;
    color: #ffffff;

    &:hover {
      background: #e53e3e;
    }
  }
}

// 模板网格布局
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  padding: 4px;
  overflow: visible;

  // 拖拽状态下的网格样式
  &--dragging {
    .banner-card {
      transition: transform 0.2s ease;
    }
  }
}

// 模板区域样式
.template-section {
  margin-bottom: 40px;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  &__title {
    font-size: 20px;
    font-weight: 600;
    color: #1e1e1e;
    margin: 0;
  }
}

// 添加Banner按钮样式
.add-banner-btn {
  width: 120px;
  height: 32px;
  background: #2e76ff;
  border-radius: 4px;
  border: none;
  font-weight: bold;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 12px;
  color: #ffffff;

  &:hover {
    opacity: 0.8;
  }
}

// 添加Banner弹窗样式
.add-banner-dialog {
  :deep(.el-dialog) {
    width: 453px;
    height: 386px;
    background: #ffffff;
    border-radius: 8px;
  }

  .banner-form {
    padding: 0px 10px;
  }

  .form-item {
    margin-bottom: 24px;
  }

  .template-select {
    width: 100%;
  }

  .upload-section {
    display: flex;
    gap: 20px;
  }

  .upload-item {
    flex: 1;
  }

  .upload-label {
    font-size: 14px;
    font-weight: 500;
    color: #797979;
    margin-bottom: 12px;
  }

  .upload-area {
    :deep(.el-upload) {
      width: 100%;
    }
  }

  .upload-placeholder {
    width: 100%;
    height: 120px;
    border: 1px dashed #d1d5db;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #2e76ff;
      background: #f0f7ff;
    }
  }

  .upload-content {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .upload-icon {
    font-size: 24px;
    color: #9ca3af;
    margin-bottom: 8px;
  }

  .upload-tips {
    font-size: 12px;
    color: #6b7280;
    line-height: 1.4;
    margin-top: 16px;
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
