<template>
  <div id="edit_3d" style="position: relative">
    <!-- 3D方向控制器 -->
    <OrientationWidget
      ref="orientationWidgetRef"
      :main-camera="camera"
      :main-controls="controls"
      :size="110"
      :animation-duration="1000"
      :enable-animation="true"
      :show-sphere="false" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, onBeforeUnmount, onUnmounted } from 'vue';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import {
  Vector3,
  Scene,
  PerspectiveCamera,
  WebGLRenderer,
  BoxGeometry,
  AmbientLight,
  MeshStandardMaterial,
  Mesh,
  Vector2,
  Raycaster,
  PointLight,
  Matrix4,
  Quaternion,
  Group,
  Plane,
  PlaneGeometry,
  MeshBasicMaterial,
  Box3,
  Box3Helper,
  TextureLoader,
  VideoTexture,
  Euler,
  AnimationMixer,
  MOUSE,
  TOUCH,
  BackSide,
  SphereGeometry,
  CubeTextureLoader,
  CanvasTexture,
} from 'three';
import { CLine, CPoint } from '@/core/primitives';
import { axisPlan, loader } from '@/config/threeJs';
import { useStore } from 'vuex';
import OrientationWidget from './OrientationWidget.vue';
import {
  searchMaterialFromUuid,
  createBox,
  initSize,
  createGroundLine,
  changeActiveWithArea,
} from '@/utils';
import { getNjyjNodes } from '@/api/index';
import { ElMessage } from 'element-plus';

// 天空盒配置
const skyboxConfig = ref({
  enabled: true,
  type: 'gradient', // 'gradient', 'texture', 'model'
  color1: '#87CEEB', // 天空蓝
  color2: '#E0F6FF', // 浅蓝
  textureUrl: 'skybox/sky.jpg',
  modelUrl: 'glb/thirdglb.glb',
});

const props = defineProps({
  isPlanStyle: {
    default: false,
    type: Boolean,
  },
  setLocation: {
    default: null,
    type: Function,
  },
  setRotation: {
    default: null,
    type: Function,
  },
  setScale: {
    default: null,
    type: Function,
  },
  editSceneData: {
    default: null,
    type: Object,
  },
});

const store = useStore();

const operateType = ref('移动');
const rotatePlan = ref(''); // 记录当前旋转的面
let dragObjectName = ''; // 记录当前拖拽的箭头
const activeModelName = ref(''); // 当前高亮的模型名字
const objectUuid: any = ref([]);
const modelGroup = new Group();
let activeObject: any = ref([]);
const scene = new Scene();
let cube: any = null;
const searchInfo: any = ref({}); // 查询到的当前被操作元素的位置
let countLoaderNum = 0; // 计数cube里的箭头加载了几个
let canvas: any = null;
let camera: any = null;
let controls: any = null;
let renderer: any = null;
const graphPoints: any = ref([]);
const childNodeArray: any = ref([]); // 线中间的点位集合
const controlsScale = ref(1);
const currentGraphId = ref(0);
let animationFrameId: any = null;

onMounted(() => {
  canvas = document.getElementById('edit_3d');
  let aspect = window.innerWidth / window.innerHeight;
  // 改为透视摄像机，彻底解决视野裁切问题
  camera = new PerspectiveCamera(75, aspect, 0.1, 1000);
  // 设置合适的初始位置，适合透视摄像机的观察距离
  camera.position.set(10, 10, 10);
  camera.lookAt(new Vector3(0, 0, 0));
  renderer = new WebGLRenderer({ antialias: true });
  renderer.setSize(window.innerWidth, window.innerHeight);
  // 设置透明背景，让天空盒对象控制背景
  renderer.setClearColor(0x000000, 0); // 完全透明背景
  canvas?.appendChild(renderer.domElement);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.render(scene, camera);
  (window as any).renderer2 = renderer;

  controls = new OrbitControls(camera, renderer.domElement);
  // 设置控制器目标点为场景中心
  controls.target.set(0, 0, 0);
  controls.mouseButtons = {
    LEFT: MOUSE.ROTATE,
    MIDDLE: MOUSE.DOLLY,
    RIGHT: MOUSE.PAN,
  };
  controls.touches = {
    ONE: TOUCH.ROTATE,
    TWO: TOUCH.DOLLY_PAN,
  };
  controls.maxZoom = 10;
  controls.minZoom = 0.05;
  // controls.maxPolarAngle = 0.45 * Math.PI;

  // 启用阻尼以获得更顺畅的控制体验
  controls.enableDamping = true;
  controls.dampingFactor = 0.05; // 阻尼系数，值越小越平滑

  // 优化旋转和缩放的灵敏度
  controls.rotateSpeed = 1.0; // 旋转速度
  controls.zoomSpeed = 1.2; // 缩放速度
  controls.panSpeed = 0.8; // 平移速度

  // 启用自动旋转的平滑停止
  controls.enableRotate = true;
  controls.enableZoom = true;
  controls.enablePan = true;

  (window as any).controls2 = controls;

  // 环境光
  const ambient = new AmbientLight(0xffffff, 1);
  ambient.name = 'ambient-init';
  scene.add(ambient);
  (window as any).scene2 = scene;
  (window as any).camera2 = camera;
  (window as any).debugQuarterCircles = debugQuarterCircles;

  // 点光源
  const light = new PointLight(0xffffff, 1, 100);
  light.position.set(10, 10, 10);
  light.name = 'point-init';
  light.castShadow = true; // default false
  scene.add(light);
  modelGroup.name = 'init-group';
  scene.add(modelGroup);

  const geometry = new BoxGeometry(0.5, 0.5, 0.5);
  const material = new MeshStandardMaterial({
    color: 0x00ff00,
    transparent: true,
    opacity: 0,
    depthTest: false,
    depthWrite: false,
  });
  cube = new Mesh(geometry, material);
  cube.name = 'cube-init';

  // xy面，黄色
  const xyPlan = createPlan(50, 50, 0xffff00, 'xy-plan');
  cube.add(xyPlan);

  // yz面，蓝绿色
  const yzPlan = createPlan(50, 50, 0x00ffff, 'yz-plan');
  const matrix2 = new Matrix4().makeRotationFromQuaternion(
    new Quaternion().setFromUnitVectors(
      new Vector3(0, 0, 1), // 初始向量
      new Vector3(1, 0, 0) // 面所在的法向量
    )
  );
  // 应用矩阵到平面
  yzPlan.applyMatrix4(matrix2);
  cube.add(yzPlan);

  // xz面，紫色
  const xzPlan = createPlan(50, 50, 0xff00ff, 'xz-plan');
  const matrix3 = new Matrix4().makeRotationFromQuaternion(
    new Quaternion().setFromUnitVectors(
      new Vector3(0, 0, 1), // 初始向量
      new Vector3(0, 1, 0) // 面所在的法向量
    )
  );
  // 应用矩阵到平面
  xzPlan.applyMatrix4(matrix3);
  cube.add(xzPlan);
  cube.material.depthTest = false;
  cube.visible = false;
  scene.add(cube);

  loadedCircle('glb/quarter-circle.glb', 'yz-plan', (glb: any) => {
    glb.scene.renderOrder = 10;
    glb.scene.rotateY(-Math.PI / 2);
    // 初始化时隐藏旋转环，只有在旋转模式下才显示
    glb.scene.children[0].visible = false;
    cube.add(glb.scene);
  });

  loadedCircle('glb/quarter-circle.glb', 'xy-plan', (glb: any) => {
    glb.scene.renderOrder = 10;
    // 初始化时隐藏旋转环，只有在旋转模式下才显示
    glb.scene.children[0].visible = false;
    cube.add(glb.scene);
  });

  loadedCircle('glb/quarter-circle.glb', 'xz-plan', (glb: any) => {
    glb.scene.renderOrder = 10;
    glb.scene.rotateX(Math.PI / 2);
    // 初始化时隐藏旋转环，只有在旋转模式下才显示
    glb.scene.children[0].visible = false;
    cube.add(glb.scene);
  });

  const geometryx = new PlaneGeometry(200, 200);
  const materialx = new MeshBasicMaterial({
    side: 1,
    transparent: false, // 完全不透明
    color: 0xf0f0f0, // 浅灰色背景，提供清晰的视觉基础
    depthWrite: false, // 防止与网格线产生深度冲突
  });

  // Load ground texture with error handling
  const textureLoader = new TextureLoader();
  textureLoader.load(
    'sourceType/ground.png',
    (texture) => {
      // Success callback - texture loaded
      materialx.map = texture;
      materialx.needsUpdate = true;
      console.log('✅ Ground texture loaded successfully');
    },
    undefined,
    (error) => {
      // Error callback - texture failed to load
      console.warn('⚠️ Ground texture failed to load, using solid color fallback');
      // Keep the solid color background as fallback
    }
  );

  const ground = new Mesh(geometryx, materialx);
  ground.name = 'ground-init';
  ground.lookAt(0, 1, 0);
  ground.position.y = -0.01; // Slightly below grid lines to prevent z-fighting
  ground.renderOrder = -1; // Ensure ground renders before other objects
  createGroundLine(ground, 100);
  scene.add(ground);

  // 添加天空盒
  createSkybox();

  animate();
  function animate() {
    if (!props.isPlanStyle) {
      // 更新控制器以支持阻尼效果
      controls.update();

      renderer.render(scene, camera);
      if (renderer.domElement.style.width == '0px') {
        resizeCanvas();
      }
    }
    // 使用 requestAnimationFrame 执行动画
    animationFrameId = requestAnimationFrame(animate);
  }

  canvas?.addEventListener('mousedown', (e: any) => {
    if (!props.isPlanStyle) {
      documentMouseDown(e, controls);
    }
  });

  document?.addEventListener('mouseup', (e: any) => {
    const activeModel: any = scene.getObjectByName(activeModelName.value);
    dragObjectName = '';
    if (rotatePlan.value) {
      activeModel.userData.oldRotate = new Vector3(0, 0, 0);
      rotatePlan.value = '';
    }
    controls.enabled = true; // 取消页面禁止转动
    // props.handleMouseUp && props.handleMouseUp(activeModelName.value);
  });

  document?.addEventListener('mousemove', (e: any) => {
    documentMove(e, cube);
  });

  // canvas?.addEventListener('mousemove', mousemoveCanvas, false);
  controls.addEventListener('change', onChange);
  window.addEventListener('resize', resizeCanvas, false);
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas);
  cancelAnimationFrame(animationFrameId);
});

const onChange = () => {
  controlsScale.value = controls.object.zoom;

  const sceneChild: any = scene.children.filter((e) => e.userData.nodeId);
  if (sceneChild.length) {
    sceneChild.forEach((e: any) => {
      e.setSize(e.userData.initSize * controlsScale.value);
    });
  }

  const reverseScale = 1 / (window as any).controls2.object.zoom;
  cube.scale.set(reverseScale, reverseScale, reverseScale);

  const angleDot = (window as any).controls2.object.position
    .clone()
    .sub(new Vector3(0, 0, 0))
    .normalize()
    .clone()
    .dot(new Vector3(0, 1, 0));
  const groundObj: any = scene.getObjectByName('ground-init');
  // console.log(angleDot)
  if (Math.abs(angleDot) < 0.5) {
    groundObj.children.forEach((childLine: any) => {
      childLine.setOpacity(1 - (0.5 - Math.abs(angleDot)) * 2);
    });
  }
};

const resizeCanvas = () => {
  if (renderer) {
    renderer.setSize(window.innerWidth, window.innerHeight);
    // 重置相机投影的相关参数
    const k = window.innerWidth / window.innerHeight; //窗口宽高比
    const s = 5;
    camera.left = -s * k;
    camera.right = s * k;
    camera.top = s;
    camera.bottom = -s;
    camera.updateProjectionMatrix();
  }
};

// 创建天空盒
const createSkybox = () => {
  console.log('🌌 开始创建天空盒，配置:', skyboxConfig.value);

  if (!skyboxConfig.value.enabled) {
    console.log('❌ 天空盒已禁用');
    return;
  }

  // 移除现有天空盒
  const existingSkybox = scene.getObjectByName('skybox-init');
  if (existingSkybox) {
    scene.remove(existingSkybox);
    console.log('🗑️ 移除现有天空盒');
  }

  console.log('🎨 创建天空盒类型:', skyboxConfig.value.type);

  switch (skyboxConfig.value.type) {
    case 'gradient':
      createGradientSkybox();
      break;
    case 'texture':
      createTextureSkybox();
      break;
    case 'model':
      createModelSkybox();
      break;
    default:
      createGradientSkybox();
  }
};

// 为天空盒的每个面创建纹理
const createSkyboxFaceTexture = (
  context: CanvasRenderingContext2D,
  faceIndex: number,
  color1: string,
  color2: string
) => {
  const size = 512;

  // 清除画布
  context.clearRect(0, 0, size, size);

  // 根据面的索引创建不同的纹理效果
  switch (faceIndex) {
    case 0: // 右面 (+X)
    case 1: // 左面 (-X)
      createCloudTexture(context, size, color1, color2, 'horizontal');
      break;
    case 2: // 上面 (+Y) - 天空
      createSkyTexture(context, size, color1, color2);
      break;
    case 3: // 下面 (-Y) - 地面色调
      createGroundTexture(context, size, color2);
      break;
    case 4: // 前面 (+Z)
    case 5: // 后面 (-Z)
      createCloudTexture(context, size, color1, color2, 'vertical');
      break;
  }
};

// 创建云朵纹理
const createCloudTexture = (
  context: CanvasRenderingContext2D,
  size: number,
  color1: string,
  color2: string,
  direction: string
) => {
  // 基础渐变
  const gradient =
    direction === 'horizontal'
      ? context.createLinearGradient(0, 0, size, 0)
      : context.createLinearGradient(0, 0, 0, size);

  gradient.addColorStop(0, color1);
  gradient.addColorStop(0.7, color2);
  gradient.addColorStop(1, color1);

  context.fillStyle = gradient;
  context.fillRect(0, 0, size, size);

  // 添加云朵效果
  context.globalCompositeOperation = 'overlay';
  for (let i = 0; i < 8; i++) {
    const x = Math.random() * size;
    const y = Math.random() * size;
    const radius = 30 + Math.random() * 50;

    const cloudGradient = context.createRadialGradient(x, y, 0, x, y, radius);
    cloudGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
    cloudGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

    context.fillStyle = cloudGradient;
    context.beginPath();
    context.arc(x, y, radius, 0, Math.PI * 2);
    context.fill();
  }
  context.globalCompositeOperation = 'source-over';
};

// 创建天空纹理（顶部）
const createSkyTexture = (
  context: CanvasRenderingContext2D,
  size: number,
  color1: string,
  color2: string
) => {
  // 径向渐变从中心向外
  const gradient = context.createRadialGradient(
    size / 2,
    size / 2,
    0,
    size / 2,
    size / 2,
    size / 2
  );
  gradient.addColorStop(0, color1);
  gradient.addColorStop(1, color2);

  context.fillStyle = gradient;
  context.fillRect(0, 0, size, size);

  // 添加星星效果（如果是夜空）
  if (color1.includes('#191970') || color2.includes('#000080')) {
    context.fillStyle = 'rgba(255, 255, 255, 0.8)';
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * size;
      const y = Math.random() * size;
      context.beginPath();
      context.arc(x, y, 1, 0, Math.PI * 2);
      context.fill();
    }
  }
};

// 创建地面纹理（底部）
const createGroundTexture = (context: CanvasRenderingContext2D, size: number, color2: string) => {
  // 使用较暗的颜色作为地面
  const darkColor = adjustColorBrightness(color2, -0.3);
  context.fillStyle = darkColor;
  context.fillRect(0, 0, size, size);

  // 添加一些纹理细节
  context.fillStyle = adjustColorBrightness(color2, -0.1);
  for (let i = 0; i < 10; i++) {
    const x = Math.random() * size;
    const y = Math.random() * size;
    const width = 20 + Math.random() * 40;
    const height = 5 + Math.random() * 10;
    context.fillRect(x, y, width, height);
  }
};

// 创建球体天空渐变纹理
const createSkyGradientTexture = (
  context: CanvasRenderingContext2D,
  color1: string,
  color2: string
) => {
  const width = 1024;
  const height = 512;

  // 清除画布
  context.clearRect(0, 0, width, height);

  // 创建从底部（地平线）到顶部的垂直渐变
  const gradient = context.createLinearGradient(0, height, 0, 0);

  // 根据颜色类型创建不同的渐变效果，地平线在中间位置
  if (isNightSky(color1, color2)) {
    // 夜空效果：底部深色，地平线（中间）适中，顶部深色
    gradient.addColorStop(0, darkenColor(color2, 0.4)); // 底部很深
    gradient.addColorStop(0.3, darkenColor(color2, 0.2)); // 下方深色
    gradient.addColorStop(0.5, color2); // 地平线（中间）正常色
    gradient.addColorStop(0.7, blendColors(color1, color2, 0.6)); // 中上空过渡
    gradient.addColorStop(0.9, color1); // 高空
    gradient.addColorStop(1, darkenColor(color1, 0.3)); // 顶部深色
  } else if (isSunsetSky(color1, color2)) {
    // 日落效果：底部深橙，地平线（中间）橙红，顶部金黄
    gradient.addColorStop(0, darkenColor(color1, 0.5)); // 底部深橙红
    gradient.addColorStop(0.3, darkenColor(color1, 0.2)); // 下方深橙
    gradient.addColorStop(0.5, color1); // 地平线（中间）橙红
    gradient.addColorStop(0.7, blendColors(color1, color2, 0.5)); // 橙黄过渡
    gradient.addColorStop(0.9, color2); // 高空金黄
    gradient.addColorStop(1, lightenColor(color2, 0.1)); // 顶部亮金黄
  } else {
    // 普通天空效果：底部深色，地平线（中间）正常，顶部天空色
    gradient.addColorStop(0, darkenColor(color2, 0.6)); // 底部很深
    gradient.addColorStop(0.3, darkenColor(color2, 0.3)); // 下方深色
    gradient.addColorStop(0.5, color2); // 地平线（中间）正常色
    gradient.addColorStop(0.7, blendColors(color1, color2, 0.6)); // 中上空过渡
    gradient.addColorStop(0.9, blendColors(color1, color2, 0.2)); // 高空过渡
    gradient.addColorStop(1, color1); // 顶部天空色
  }

  context.fillStyle = gradient;
  context.fillRect(0, 0, width, height);

  // 添加额外的对比度增强层
  addContrastLayer(context, width, height);

  // 添加明显的地平线效果
  addHorizonLine(context, width, height, color1, color2);

  // 添加云朵效果
  addCloudsToSky(context, width, height, color1, color2);

  // 如果是夜空，添加星星
  if (isNightSky(color1, color2)) {
    addStarsToSky(context, width, height);
  }

  // 如果是日落，添加太阳光晕
  if (isSunsetSky(color1, color2)) {
    addSunGlow(context, width, height, color1);
  }
};

// 添加对比度增强层
const addContrastLayer = (context: CanvasRenderingContext2D, width: number, height: number) => {
  context.globalCompositeOperation = 'overlay';

  // 创建径向渐变来增强中心到边缘的对比
  const contrastGradient = context.createRadialGradient(
    width / 2,
    height / 2,
    0,
    width / 2,
    height / 2,
    Math.max(width, height) / 2
  );

  contrastGradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)'); // 中心稍亮
  contrastGradient.addColorStop(0.7, 'rgba(0, 0, 0, 0)'); // 透明过渡
  contrastGradient.addColorStop(1, 'rgba(0, 0, 0, 0.15)'); // 边缘稍暗

  context.fillStyle = contrastGradient;
  context.fillRect(0, 0, width, height);

  // 添加水平渐变来增强地平线效果
  const horizonGradient = context.createLinearGradient(0, height * 0.6, 0, height);
  horizonGradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
  horizonGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.08)'); // 地平线附近稍亮
  horizonGradient.addColorStop(1, 'rgba(255, 255, 255, 0.12)'); // 底部更亮

  context.fillStyle = horizonGradient;
  context.fillRect(0, height * 0.6, width, height * 0.4);

  context.globalCompositeOperation = 'source-over';
};

// 添加明显的地平线效果
const addHorizonLine = (
  context: CanvasRenderingContext2D,
  width: number,
  height: number,
  color1: string,
  color2: string
) => {
  const horizonY = height * 0.5; // 地平线位置（中间位置，与网格平面对齐）

  // 添加地平线光带效果
  context.globalCompositeOperation = 'screen';

  // 创建地平线主光带
  const horizonGradient = context.createLinearGradient(0, horizonY - 30, 0, horizonY + 30);

  if (isSunsetSky(color1, color2)) {
    // 日落地平线：微妙的橙红光带
    horizonGradient.addColorStop(0, 'rgba(255, 69, 0, 0)'); // 透明
    horizonGradient.addColorStop(0.3, 'rgba(255, 140, 0, 0.08)'); // 微妙橙色光
    horizonGradient.addColorStop(0.5, 'rgba(255, 215, 0, 0.12)'); // 微妙金色光带
    horizonGradient.addColorStop(0.7, 'rgba(255, 140, 0, 0.08)'); // 微妙橙色光
    horizonGradient.addColorStop(1, 'rgba(255, 69, 0, 0)'); // 透明
  } else if (isNightSky(color1, color2)) {
    // 夜空地平线：极其柔和的蓝紫光带
    horizonGradient.addColorStop(0, 'rgba(65, 105, 225, 0)'); // 透明
    horizonGradient.addColorStop(0.4, 'rgba(135, 206, 235, 0.06)'); // 极柔和天空蓝光
    horizonGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.04)'); // 极柔和白色光带
    horizonGradient.addColorStop(0.6, 'rgba(135, 206, 235, 0.06)'); // 极柔和天空蓝光
    horizonGradient.addColorStop(1, 'rgba(65, 105, 225, 0)'); // 透明
  } else {
    // 普通天空地平线：极其柔和的白色光带
    horizonGradient.addColorStop(0, 'rgba(255, 255, 255, 0)'); // 透明
    horizonGradient.addColorStop(0.3, 'rgba(255, 255, 255, 0.06)'); // 极柔和白光
    horizonGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)'); // 微妙白光带
    horizonGradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.06)'); // 极柔和白光
    horizonGradient.addColorStop(1, 'rgba(255, 255, 255, 0)'); // 透明
  }

  context.fillStyle = horizonGradient;
  context.fillRect(0, horizonY - 30, width, 60);

  // 添加地平线下方的深色效果（让下方更深）
  const darkenGradient = context.createLinearGradient(0, horizonY, 0, height);
  darkenGradient.addColorStop(0, 'rgba(0, 0, 0, 0)'); // 地平线处透明
  darkenGradient.addColorStop(0.4, 'rgba(0, 0, 0, 0.15)'); // 逐渐变深
  darkenGradient.addColorStop(1, 'rgba(0, 0, 0, 0.35)'); // 底部更深

  context.globalCompositeOperation = 'multiply'; // 使用乘法混合让颜色变深
  context.fillStyle = darkenGradient;
  context.fillRect(0, horizonY, width, height - horizonY);
  context.globalCompositeOperation = 'source-over'; // 重置混合模式

  // 添加地平线细线（微妙的分界线）
  context.globalCompositeOperation = 'overlay';
  context.strokeStyle = 'rgba(255, 255, 255, 0.15)'; // 降低透明度
  context.lineWidth = 0.5; // 更细的线条
  context.beginPath();
  context.moveTo(0, horizonY);
  context.lineTo(width, horizonY);
  context.stroke();

  // 添加地平线上方的大气散射效果（极其微妙）
  const atmosphereGradient = context.createLinearGradient(0, horizonY - 30, 0, horizonY);
  atmosphereGradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
  atmosphereGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.02)'); // 极微妙
  atmosphereGradient.addColorStop(1, 'rgba(255, 255, 255, 0.04)'); // 很微妙

  context.fillStyle = atmosphereGradient;
  context.fillRect(0, horizonY - 30, width, 30);

  context.globalCompositeOperation = 'source-over';
};

// 添加云朵到天空
const addCloudsToSky = (
  context: CanvasRenderingContext2D,
  width: number,
  height: number,
  color1: string,
  color2: string
) => {
  // 只在非夜空时添加云朵
  if (isNightSky(color1, color2)) {
    return; // 夜空不添加云朵
  }

  context.globalCompositeOperation = 'soft-light'; // 使用更自然的混合模式

  // 创建更自然的云朵形状
  for (let i = 0; i < 6; i++) {
    const baseX = Math.random() * width;
    const baseY = height * 0.15 + Math.random() * height * 0.35; // 主要在上半部分

    // 创建不规则云朵形状
    createNaturalCloud(context, baseX, baseY, width, height, i);
  }

  // 添加一些小的云朵碎片
  context.globalCompositeOperation = 'overlay';
  for (let i = 0; i < 8; i++) {
    const x = Math.random() * width;
    const y = height * 0.1 + Math.random() * height * 0.4;
    const size = 12 + Math.random() * 20;

    const fragmentGradient = context.createRadialGradient(x, y, 0, x, y, size);
    fragmentGradient.addColorStop(0, 'rgba(255, 255, 255, 0.06)');
    fragmentGradient.addColorStop(0.6, 'rgba(255, 255, 255, 0.03)');
    fragmentGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

    context.fillStyle = fragmentGradient;
    context.beginPath();
    context.arc(x, y, size, 0, Math.PI * 2);
    context.fill();
  }

  context.globalCompositeOperation = 'source-over';
};

// 创建自然的云朵形状
const createNaturalCloud = (
  context: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  width: number,
  height: number,
  seed: number
) => {
  // 使用种子创建可重复的随机数
  const random = (n: number) => {
    const x = Math.sin(seed * 12.9898 + n * 78.233) * 43758.5453;
    return x - Math.floor(x);
  };

  // 创建云朵的多个"泡泡"组成不规则形状
  const bubbleCount = 4 + Math.floor(random(0) * 3);

  for (let j = 0; j < bubbleCount; j++) {
    const offsetX = (random(j * 2) - 0.5) * 80;
    const offsetY = (random(j * 2 + 1) - 0.5) * 30;
    const bubbleX = centerX + offsetX;
    const bubbleY = centerY + offsetY;

    // 确保云朵在画布范围内
    if (bubbleX < -40 || bubbleX > width + 40) continue;

    const bubbleSize = 25 + random(j * 3) * 45;
    const opacity = 0.08 - j * 0.01; // 每个泡泡透明度递减

    // 创建更柔和的云朵渐变
    const cloudGradient = context.createRadialGradient(
      bubbleX,
      bubbleY,
      0,
      bubbleX,
      bubbleY,
      bubbleSize
    );

    cloudGradient.addColorStop(0, `rgba(255, 255, 255, ${opacity})`);
    cloudGradient.addColorStop(0.3, `rgba(255, 255, 255, ${opacity * 0.8})`);
    cloudGradient.addColorStop(0.7, `rgba(255, 255, 255, ${opacity * 0.4})`);
    cloudGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

    context.fillStyle = cloudGradient;
    context.beginPath();
    context.arc(bubbleX, bubbleY, bubbleSize, 0, Math.PI * 2);
    context.fill();
  }
};

// 添加星星到夜空
const addStarsToSky = (context: CanvasRenderingContext2D, width: number, height: number) => {
  context.fillStyle = 'rgba(255, 255, 255, 0.8)';

  // 添加不同大小的星星
  for (let i = 0; i < 50; i++) {
    const x = Math.random() * width;
    const y = Math.random() * height * 0.7; // 主要在上半部分
    const size = Math.random() * 2 + 0.5;

    context.beginPath();
    context.arc(x, y, size, 0, Math.PI * 2);
    context.fill();
  }

  // 添加一些闪烁的亮星
  context.fillStyle = 'rgba(255, 255, 255, 1)';
  for (let i = 0; i < 10; i++) {
    const x = Math.random() * width;
    const y = Math.random() * height * 0.5;

    // 绘制十字形亮星
    context.fillRect(x - 3, y - 0.5, 6, 1);
    context.fillRect(x - 0.5, y - 3, 1, 6);
  }
};

// 添加太阳光晕到日落天空
const addSunGlow = (
  context: CanvasRenderingContext2D,
  width: number,
  height: number,
  color1: string
) => {
  const sunX = width * 0.8; // 太阳位置
  const sunY = height * 0.7;

  context.globalCompositeOperation = 'screen';

  // 创建太阳光晕
  const glowGradient = context.createRadialGradient(sunX, sunY, 0, sunX, sunY, 150);
  glowGradient.addColorStop(0, `${color1}80`); // 半透明的主色
  glowGradient.addColorStop(0.5, `${color1}40`);
  glowGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

  context.fillStyle = glowGradient;
  context.beginPath();
  context.arc(sunX, sunY, 150, 0, Math.PI * 2);
  context.fill();

  context.globalCompositeOperation = 'source-over';
};

// 辅助函数：判断是否为夜空
const isNightSky = (color1: string, color2: string): boolean => {
  return (
    color1.includes('#191970') ||
    color2.includes('#000080') ||
    color1.includes('#000080') ||
    color2.includes('#191970')
  );
};

// 辅助函数：判断是否为日落天空
const isSunsetSky = (color1: string, color2: string): boolean => {
  return (
    color1.includes('#FF6B35') ||
    color1.includes('#FFA07A') ||
    color2.includes('#FFD700') ||
    color2.includes('#F0E68C')
  );
};

// 辅助函数：调整颜色亮度
const lightenColor = (color: string, amount: number): string => {
  return adjustColorBrightness(color, amount);
};

const darkenColor = (color: string, amount: number): string => {
  return adjustColorBrightness(color, -amount);
};

// 辅助函数：混合两个颜色
const blendColors = (color1: string, color2: string, ratio: number): string => {
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');

  const r1 = parseInt(hex1.substr(0, 2), 16);
  const g1 = parseInt(hex1.substr(2, 2), 16);
  const b1 = parseInt(hex1.substr(4, 2), 16);

  const r2 = parseInt(hex2.substr(0, 2), 16);
  const g2 = parseInt(hex2.substr(2, 2), 16);
  const b2 = parseInt(hex2.substr(4, 2), 16);

  const r = Math.round(r1 * ratio + r2 * (1 - ratio));
  const g = Math.round(g1 * ratio + g2 * (1 - ratio));
  const b = Math.round(b1 * ratio + b2 * (1 - ratio));

  return `rgb(${r}, ${g}, ${b})`;
};

// 调整颜色亮度的辅助函数，增强效果
const adjustColorBrightness = (color: string, amount: number): string => {
  const hex = color.replace('#', '');

  // 增强亮度调整效果
  const enhancedAmount = amount * 1.5; // 放大调整幅度

  const r = Math.max(0, Math.min(255, parseInt(hex.substring(0, 2), 16) + enhancedAmount * 255));
  const g = Math.max(0, Math.min(255, parseInt(hex.substring(2, 4), 16) + enhancedAmount * 255));
  const b = Math.max(0, Math.min(255, parseInt(hex.substring(4, 6), 16) + enhancedAmount * 255));
  return `rgb(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)})`;
};

// 创建球体天空盒
const createGradientSkybox = () => {
  console.log('🎨 开始创建球体天空盒');
  console.log('颜色1:', skyboxConfig.value.color1);
  console.log('颜色2:', skyboxConfig.value.color2);

  // 使用球体几何体，更自然的天空效果
  const geometry = new SphereGeometry(150, 64, 64);

  // 创建天空纹理
  const canvas = document.createElement('canvas');
  canvas.width = 1024;
  canvas.height = 512; // 2:1 比例，适合球体UV映射
  const context = canvas.getContext('2d')!;

  // 创建从地平线到顶部的天空渐变
  createSkyGradientTexture(context, skyboxConfig.value.color1, skyboxConfig.value.color2);

  const texture = new TextureLoader().load(canvas.toDataURL());
  texture.needsUpdate = true;

  const material = new MeshBasicMaterial({
    map: texture,
    side: BackSide,
    transparent: false,
    depthWrite: false,
    depthTest: false,
  });

  const skybox = new Mesh(geometry, material);
  skybox.name = 'skybox-init';
  skybox.renderOrder = -999;
  skybox.position.set(0, 0, 0);
  scene.add(skybox);

  console.log('✅ 球体天空盒创建成功');
  console.log('场景中的对象数量:', scene.children.length);
  console.log('天空盒位置:', skybox.position);
  console.log('相机位置:', camera.position);
};

// 创建纹理天空盒
const createTextureSkybox = () => {
  console.log('🖼️ 开始创建纹理天空盒');

  const geometry = new SphereGeometry(150, 64, 64);
  const texture = new TextureLoader().load(skyboxConfig.value.textureUrl);

  const material = new MeshBasicMaterial({
    map: texture,
    side: BackSide,
    depthWrite: false,
    depthTest: false,
  });

  const skybox = new Mesh(geometry, material);
  skybox.name = 'skybox-init';
  skybox.renderOrder = -999;
  skybox.position.set(0, 0, 0);
  scene.add(skybox);
  console.log('✅ 纹理天空盒创建成功');
};

// 创建模型天空盒
const createModelSkybox = () => {
  loader.glb.load(
    skyboxConfig.value.modelUrl,
    function (glb: any) {
      const obj = glb.scene;
      obj.name = 'skybox-init';
      obj.traverse((e: any) => {
        if (e.material) {
          e.material.side = BackSide;
        }
      });
      scene.add(obj);
      console.log('✅ GLB模型天空盒加载成功');
    },
    undefined,
    (error: any) => {
      console.warn('⚠️ GLB模型天空盒加载失败，使用渐变天空盒:', error);
      createGradientSkybox();
    }
  );
};

// 加载旋转面片
const loadedCircle = (url: string, playName: string, callback: any) => {
  loader.glb.load(url, function (glb: any) {
    glb.scene.children[0].material.depthTest = false;
    glb.scene.children[0].userData.planName = playName;
    // 设置旋转环的名称，确保可以被射线检测识别
    glb.scene.children[0].name = 'System_QuarterCircle';
    console.log(`🔄 加载旋转环: ${playName}, 名称: ${glb.scene.children[0].name}`);
    callback(glb);
  });
};

// 创建操作平面
const createPlan = (width: number, height: number, color: number, planName: string) => {
  const geometry = new PlaneGeometry(width, height);
  const material = new MeshBasicMaterial({
    color: color,
    side: 2,
    transparent: true,
    opacity: 0,
    depthTest: false,
    depthWrite: false,
  });
  const plane = new Mesh(geometry, material);
  plane.name = planName;
  return plane;
};

const loaderVideo = (url: any, data: any) => {
  const materialSize = {
    width: data.materialMediaInfoDto?.width || 1000,
    height: data.materialMediaInfoDto?.height || 1000,
  };
  const width = materialSize.width / 1000;
  const height = materialSize.height / 1000;
  // 创建视频元素
  const video = document.createElement('video');
  video.src = url; // 视频文件路径
  video.load();
  video.crossOrigin = 'anonymous';
  video.loop = true;

  // 创建视频纹理
  const videoTexture = new VideoTexture(video);

  // 创建材质并将视频纹理应用上去
  const videoMaterial = new MeshBasicMaterial({ map: videoTexture, side: 2 });

  // 创建平面几何体
  const geometry = new PlaneGeometry(width, height);

  // 创建网格并添加到场景
  const mesh = new Mesh(geometry, videoMaterial);
  return mesh;
};

const loaderImage = (url: any, data: any) => {
  const materialType = data.materialType;
  let materialParam: any = { side: 2 };
  if (url.toLowerCase().includes('.png') || materialType == '5') {
    materialParam.transparent = true;
  }
  const materialSize = {
    width: data.materialMediaInfoDto?.width || 1000,
    height: data.materialMediaInfoDto?.height || 1000,
  };
  const width = materialSize.width / 1000;
  const height = materialSize.height / 1000;
  const geometry = new PlaneGeometry(width, height);
  const material = new MeshBasicMaterial(materialParam);
  const loader = new TextureLoader();
  loader.crossOrigin = 'Anonymous';
  const t = loader.load(url);
  material.map = t;
  const mesh = new Mesh(geometry, material);
  return mesh;
};

const addModel = (url: string, data: any, callback: any, noplay?: boolean) => {
  const materialFormat = data.materialDto.materialFormat;
  const materialType = data.materialDto.materialType;
  // 生成图元
  if (materialType == '1') {
    if (noplay) {
      const obj = loaderImage(url, data.materialDto);
      setModel(obj, data, callback);
    } else {
      const obj = loaderVideo(url, data.materialDto);
      setModel(obj, data, callback);
    }
  } else if (materialType == '3' || materialType == '5') {
    const obj = loaderImage(url, data.materialDto);
    setModel(obj, data, callback);
  } else if (materialType == '4' && loader[materialFormat]) {
    loader[materialFormat].load(url, function (object: any) {
      const obj: any = object.scene || object;
      setModel(obj, data, callback);
    });
  } else if (materialType == '2') {
    loader.glb.load('glb/sound-model.glb', function (glb: any) {
      const obj: any = glb.scene || glb;
      setModel(obj, data, callback);
    });
  }
};

const setModel = (obj: any, data: any, callback: any) => {
  if (objectUuid.value.includes(data.uuid)) return;
  obj.name = data.uuid;
  objectUuid.value.push(data.uuid);
  modelGroup.add(obj);
  obj.userData.oldRotate = new Vector3(0, 0, 0);
  const boxInfo = new Box3().expandByObject(obj);
  initSize(boxInfo, obj);
  const helper = createBox(boxInfo, 0xffca57, 2);
  helper.name = 'box-helper';
  obj.add(helper);
  obj.userData.boxInfos = { ...boxInfo, initScale: new Vector3(1, 1, 1) };
  obj.getObjectByName('box-helper').visible = false;
  store.state.isFinishModel = !store.state.isFinishModel;
  callback(obj);
};

// const mousemoveCanvas = (e: any) => {
//   props.handleMouseMove(new Vector2(e.clientX, e.clientY))
// }

const loaderArrow = (points: any, modelName: string, obj: any, object3D: any, resolve: any) => {
  loader.glb.load(`glb/${modelName}.glb`, function (glb: any) {
    object3D.name = 'TransformArrow';
    object3D.position.set(points[1].x, points[1].y, points[1].z);
    object3D.add(glb.scene);
    glb.scene.scale.set(3.9, 3.9, 3.9);
    const matrix = new Matrix4().makeRotationFromQuaternion(
      new Quaternion().setFromUnitVectors(
        new Vector3(0, 1, 0), // 初始向量
        new Vector3(
          modelName.includes('red') ? 1 : 0,
          modelName.includes('green') ? 1 : 0,
          modelName.includes('blue') ? 1 : 0
        ) // 面所在的法向量
      )
    );
    // 应用矩阵到立方体
    glb.scene.applyMatrix4(matrix);
    glb.scene.children[0].userData.glbType = modelName.split('-')[0] + '-arrow';
    glb.scene.children[0].material.depthTest = false;
    glb.scene.renderOrder = 10;
    glb.scene.visible =
      (operateType.value == '缩放' && modelName.includes('cube')) ||
      ((operateType.value == '移动' ||
        operateType.value == '旋转' ||
        operateType.value == '移动旋转') &&
        modelName.includes('arrow'));
    if (object3D.children.length != 2) {
      loaderArrow(points, modelName.split('-')[0] + '-cube', obj, object3D, resolve);
    } else {
      obj.add(object3D);
      countLoaderNum += 1;
      if (countLoaderNum == 3) {
        resolve();
      }
    }
  });
};

const addEdit = (values: any) => {
  return new Promise((resolve) => {
    values.forEach((data: any) => {
      const cline = new CLine({
        vertexs: [new Vector3(0, 0, 0), data[0]],
        color: data[1],
        lineWidth: 3,
        reversalDepthTest: true,
        transparent: true,
      });
      cline.name = 'TransformArrow';
      cline.userData.glbType = data[2];
      const object3D = new Group();
      cube.add(cline);
      loaderArrow([new Vector3(0, 0, 0), data[0]], data[2], cube, object3D, resolve);
    });
  });
};

const documentMouseDown = async (e: any, controls: any) => {
  const pointer = new Vector2();
  pointer.x = (e.clientX / window.innerWidth) * 2 - 1;
  pointer.y = -(e.clientY / window.innerHeight) * 2 + 1;

  const raycaster = new Raycaster();
  // 通过摄像机和鼠标位置更新射线
  raycaster.setFromCamera(pointer, camera);
  // 计算物体和射线的焦点 - 递归检测所有子对象
  const intersects = raycaster.intersectObjects(scene.children, true);

  // 旋转 - 支持"旋转"和"移动旋转"模式
  const quarterCircle = intersects.filter((e: any) => e.object.name == 'System_QuarterCircle')[0];

  // 调试：检查射线检测结果
  if (operateType.value == '旋转' || operateType.value == '移动旋转') {
    const allQuarterCircles = intersects.filter(
      (e: any) => e.object.name == 'System_QuarterCircle'
    );
    console.log(`🎯 射线检测结果: 找到 ${allQuarterCircles.length} 个旋转环`);
    allQuarterCircles.forEach((circle, index) => {
      console.log(
        `  旋转环 ${index}: 名称=${circle.object.name}, 平面=${circle.object.userData.planName}`
      );
    });

    if (allQuarterCircles.length === 0) {
      console.log('❌ 未检测到任何旋转环，可能的原因:');
      console.log('  1. 旋转环不可见');
      console.log('  2. 旋转环名称不正确');
      console.log('  3. 旋转环被其他对象遮挡');

      // 检查cube中的旋转环状态
      let visibleCircles = 0;
      cube.traverse((child: any) => {
        if (child.name === 'System_QuarterCircle') {
          console.log(`  cube中的旋转环: ${child.userData.planName}, 可见: ${child.visible}`);
          if (child.visible) visibleCircles++;
        }
      });
      console.log(`  cube中可见的旋转环数量: ${visibleCircles}`);
    }
  }

  if (quarterCircle && (operateType.value == '旋转' || operateType.value == '移动旋转')) {
    controls.enabled = false; // 页面禁止转动
    rotatePlan.value = quarterCircle.object.userData.planName;
    const targetPlan = intersects.filter((p: any) => p.object.name == rotatePlan.value)[0];
    if (targetPlan) {
      // 记录旋转的初始位置
      targetPlan.object.userData.rotateStartPoint = targetPlan.point;
    }
    console.log(`🔄 开始旋转操作，平面: ${rotatePlan.value}`);
    return;
  }
  // 移动和缩放
  if (intersects.length) {
    const arrowArrays = intersects.filter((e: any) =>
      ['RedCube', 'GreenCube', 'BlueCube'].includes(e.object.name)
    )[0];
    if (arrowArrays) {
      dragObjectName = arrowArrays.object.name || '';

      // 缩放需要重置初始值
      if (operateType.value == '缩放') {
        const activeModel: any = scene.getObjectByName(activeModelName.value);
        const boxInfos = activeModel.userData.boxInfos;
        boxInfos.initScale = new Vector3(boxInfos.scale.x, boxInfos.scale.y, boxInfos.scale.z);
      }
      const planName = axisPlan[dragObjectName];
      if (planName) {
        controls.enabled = false; // 页面禁止转动
      }
      return;
    }
    const intersectObject = intersects
      .map((item: any, index: number) => {
        const obj = searchModel(item.object);
        return obj;
      })
      .filter((obj: any) => objectUuid.value.includes(obj.name))[0];

    // 没选中物体情况下
    if (!intersectObject) {
      // store.state.activeMaterial = ''
      // hideCube(activeModelName.value)
      return;
    }
    if (store.state.isDragLoading || store.state.isRequesting) {
      // 射线拾取到了模型再判断是否保存好
      return ElMessage({ message: '数据加载中，请勿频繁操作', type: 'warning' });
    }

    // 选中物体情况下
    if (activeModelName.value !== intersectObject.name) {
      const activeModel: any = scene.getObjectByName(intersectObject.name);
      resetCube(activeModel.position);

      changeActiveWithArea(intersectObject.name);
    }
    activeModelName.value = intersectObject.name;
    if (activeModelName.value) {
      if (!store.state.operateType) {
        store.state.operateType = '移动';
      }
      const activeModel: any = scene.getObjectByName(activeModelName.value);
      if (activeModel) {
        if (cube && cube.children.length <= 6) {
          await addEdit([
            [new Vector3(0, 0, 1), 0x0000ff, 'blue-arrow'],
            [new Vector3(0, 1, 0), 0x00ff00, 'green-arrow'],
            [new Vector3(1, 0, 0), 0xff0000, 'red-arrow'],
          ]);
        }
      }
    }
  }
};

const resetCube = (position?: any) => {
  cube?.position.set(position?.x || 0, position?.y || 0, position?.z || 0);
  const activeModel: any = scene.getObjectByName(activeModelName.value);
  if (activeModel && activeModel.getObjectByName('box-helper')) {
    activeModel.getObjectByName('box-helper').visible = false;
  }
};

// 点击左侧高亮模型
const activeCube = async (name: string) => {
  const activeModel: any = scene.getObjectByName(name);
  resetCube(activeModel.position);
  activeModelName.value = name;
  cube.visible = true;
  activeModel.getObjectByName('box-helper').visible = true;
  if (cube && cube.children.length <= 6) {
    await addEdit([
      [new Vector3(0, 0, 1), 0x0000ff, 'blue-arrow'],
      [new Vector3(0, 1, 0), 0x00ff00, 'green-arrow'],
      [new Vector3(1, 0, 0), 0xff0000, 'red-arrow'],
    ]);
    modelDepthAdaptive(name);
    return;
  }
  modelDepthAdaptive(name);
};

// 自适应图片和模型的深度问题
const modelDepthAdaptive = (name: string) => {
  const materialData = searchMaterialFromUuid({ ...store.state.editSceneData }, name);
  const curMaterialType = materialData.ele.materialDto.materialType;
  if (cube.children) {
    cube.children.forEach((e: any) => {
      if (e.material) {
        if (e.name != 'TransformArrow') {
          e.material.depthTest = curMaterialType != '4' && curMaterialType != '2';
        } else {
          e.material.depthTest = false;
        }
      } else {
        e.traverse((c: any) => {
          if (
            c.material &&
            !['System_QuarterCircle', 'BlueArrow', 'GreenArrow', 'RedArrow'].includes(c.name)
          ) {
            c.material.depthTest = curMaterialType != '4' && curMaterialType != '2';
          }
        });
      }
    });
  }
};

const hideCube = (name: string) => {
  const activeModel: any = scene.getObjectByName(name);
  cube.visible = false;
  activeModelName.value = '';
  if (activeModel && activeModel.getObjectByName('box-helper')) {
    activeModel.getObjectByName('box-helper').visible = false;
  }
};

// 递归查询所有模型
const searchModel: any = (object: any) => {
  if (object?.parent?.name == 'init-group' || !object?.parent || object.name == 'box-helper') {
    return object;
  } else {
    return searchModel(object?.parent);
  }
};
// 用于计算箭头的拖拽
const documentMove = (e: any, cube: any) => {
  const pointer = new Vector2();
  pointer.x = (e.clientX / window.innerWidth) * 2 - 1;
  pointer.y = -(e.clientY / window.innerHeight) * 2 + 1;

  const raycaster = new Raycaster();

  // 通过摄像机和鼠标位置更新射线
  raycaster.setFromCamera(pointer, camera);

  // 计算物体和射线的焦点 - 递归检测所有子对象
  const intersects = raycaster.intersectObjects(scene.children, true);

  // 旋转 - 支持"旋转"和"移动旋转"模式
  if (rotatePlan.value && (operateType.value == '旋转' || operateType.value == '移动旋转')) {
    const targetPlan = intersects.filter((p: any) => p.object.name == rotatePlan.value)[0];
    const activeModel: any = scene.getObjectByName(activeModelName.value);
    if (targetPlan) {
      const v1 = targetPlan.object.userData.rotateStartPoint.clone().sub(activeModel.position);
      const v2 = targetPlan.point.clone().sub(activeModel.position);
      const angles = v1.clone().angleTo(v2);
      const directionVector = activeModel.position
        .clone()
        .sub(targetPlan.object.userData.rotateStartPoint || new Vector3(0, 0, 0))
        .clone()
        .cross(v2);
      if (rotatePlan.value == 'yz-plan') {
        // X
        rotateAxis('x', activeModel, angles, directionVector, new Vector3(1, 0, 0));
      } else if (rotatePlan.value == 'xz-plan') {
        // Y
        rotateAxis('y', activeModel, angles, directionVector, new Vector3(0, 1, 0));
      } else if (rotatePlan.value == 'xy-plan') {
        // Z
        rotateAxis('z', activeModel, angles, directionVector, new Vector3(0, 0, 1));
      }
    }
    return;
  } else if (operateType.value == '旋转' || operateType.value == '移动旋转') {
    // 修改移入部分高亮 - 支持"旋转"和"移动旋转"模式
    const quarterCircle: any = intersects.filter(
      (e: any) => e.object.name == 'System_QuarterCircle'
    )[0];
    if (quarterCircle) {
      if (!activeObject.value.length) {
        activeObject.value = [
          {
            id: quarterCircle.object.id,
            oldColor: JSON.stringify(quarterCircle.object.material.color),
          },
        ];
        quarterCircle.object.material.color.setRGB(1, 1, 0);
      }
    } else {
      if (activeObject.value.length) {
        const obj: any = scene.getObjectById(activeObject.value[0].id);
        obj.material.color.set(JSON.parse(activeObject.value[0].oldColor));
        activeObject.value = [];
      }
    }
  }
  if (dragObjectName) {
    // 移动和缩放
    if (intersects.length) {
      let planInfo: any = null;
      planInfo = axisPlan[dragObjectName].p1;
      let targetPlan = intersects.filter((p: any) => p.object.name == planInfo.name);
      let activeModel: any = scene.getObjectByName(activeModelName.value);
      if (!targetPlan.length) {
        planInfo = axisPlan[dragObjectName].p2;
        targetPlan = intersects.filter((p: any) => p.object.name == planInfo.name);
        activeModel = scene.getObjectByName(activeModelName.value);
      }
      if (!targetPlan.length) return;
      var normal = planInfo.normal; // 表示平面的法向量
      normal.clone().applyQuaternion(activeModel.quaternion);
      var pointOnPlane = cube.position.clone(); // 平面上的一点
      // 创建平面对象
      var plane = new Plane();
      plane.setFromNormalAndCoplanarPoint(normal, pointOnPlane);

      var distance = plane.distanceToPoint(targetPlan[0].point);
      let ll = targetPlan[0].point.clone().distanceTo(cube.position);
      const l = Math.sqrt(Math.pow(ll, 2) - Math.pow(distance, 2));
      if (dragObjectName == 'RedCube') {
        const flag =
          targetPlan[0].point.clone().sub(pointOnPlane).clone().dot(new Vector3(1, 0, 0)) > 0;
        transform3D('x', flag, l);
      } else if (dragObjectName == 'GreenCube') {
        const flag =
          targetPlan[0].point.clone().sub(pointOnPlane).clone().dot(new Vector3(0, 1, 0)) > 0;
        transform3D('y', flag, l);
      } else if (dragObjectName == 'BlueCube') {
        const flag =
          targetPlan[0].point.clone().sub(pointOnPlane).clone().dot(new Vector3(0, 0, 1)) > 0;
        transform3D('z', flag, l);
      }
    }
  } else {
    if (
      operateType.value == '移动' ||
      operateType.value == '缩放' ||
      operateType.value == '移动旋转'
    ) {
      let targetPlan: any = intersects.filter((p: any) => axisPlan[p.object.name])[0];
      targetPlan = targetPlan?.object;
      if (targetPlan) {
        if (!activeObject.value.length) {
          scene.traverse((e: any) => {
            if (e.userData.glbType == targetPlan.userData.glbType) {
              activeObject.value.push({
                id: e.id,
                oldColor: JSON.stringify(e.material.color),
              });
              e.material.color.setRGB(1, 1, 0);
            }
          });
        }
      } else {
        if (activeObject.value.length) {
          activeObject.value.forEach((e: any) => {
            const obj: any = scene.getObjectById(e.id);
            obj.material.color.set(JSON.parse(e.oldColor));
          });
          activeObject.value = [];
        }
      }
    }
  }
};

const transform3D = (type: string, flag: boolean, l: number) => {
  if (operateType.value == '移动' || operateType.value == '移动旋转') {
    setPosition(type, l, flag);
  } else if (operateType.value == '缩放') {
    setScale(type, l);
  }
};

const rotateAxis = (
  type: string,
  activeModel: any,
  radians: number,
  directionVector: any,
  normal: Vector3,
  angle?: number
) => {
  const newAngle = (activeModel.userData.oldRotate[type] || 0) - radians;
  const sign = directionVector[type] / Math.abs(directionVector[type]) || 0;
  activeModel.rotateOnWorldAxis(normal, newAngle * sign);
  activeModel.userData.oldRotate[type] = radians;
  // let metaInfo = `${quaternion.x},${quaternion.y},${quaternion.z},${quaternion.w},${activeModel.userData.initScale}`;
  searchInfo.value = searchMaterialFromUuid(props.editSceneData, activeModelName.value);
  if (searchInfo.value.key) {
    const { key, index, i } = searchInfo.value;
    const editSceneData = { ...store.state.editSceneData, changeTime: new Date().getTime() };
    const { x, y, z } = activeModel.rotation;
    if (i == undefined) {
      editSceneData[key][index].metaInfo = '';
      editSceneData[key][index].rotation = { x: -x, y: -y, z: -z };
      if (editSceneData[key][index].flag != 'add') {
        editSceneData[key][index].flag = 'update';
      }
    } else {
      editSceneData[key][index].materialMetaDtoList[i].metaInfo = '';
      editSceneData[key][index].materialMetaDtoList[i].rotation = { x: -x, y: -y, z: -z };
      if (editSceneData[key][index].materialMetaDtoList[i].flag != 'add') {
        editSceneData[key][index].materialMetaDtoList[i].flag = 'update';
      }
    }
    store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
  }
};

const setScale = (type: string, diff: number) => {
  const zoom = (window as any).controls2.object.zoom;
  const activeModel: any = scene.getObjectByName(activeModelName.value);
  const boxInfos = activeModel.userData.boxInfos;
  let newScale = diff * zoom * boxInfos.initScale[type];
  newScale = Math.max(newScale, 0.01); // 最小缩小系数限制为0.01
  if (type == 'x') {
    activeModel.scale.setX(newScale * activeModel.userData.initScale);
  } else if (type == 'y') {
    activeModel.scale.setY(newScale * activeModel.userData.initScale);
  } else if (type == 'z') {
    activeModel.scale.setZ(newScale * activeModel.userData.initScale);
  }
  activeModel.userData.boxInfos.scale[type] = newScale;
  searchInfo.value = searchMaterialFromUuid(props.editSceneData, activeModelName.value);
  if (searchInfo.value.key) {
    const { key, index, i } = searchInfo.value;
    const editSceneData = { ...store.state.editSceneData, changeTime: new Date().getTime() };
    if (i == undefined) {
      editSceneData[key][index].scale[type] = newScale;
      if (editSceneData[key][index].flag != 'add') {
        editSceneData[key][index].flag = 'update';
      }
    } else {
      editSceneData[key][index].materialMetaDtoList[i].scale[type] = newScale;
      if (editSceneData[key][index].materialMetaDtoList[i].flag != 'add') {
        editSceneData[key][index].materialMetaDtoList[i].flag = 'update';
      }
    }
    store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
  }
};

const setPosition = (type: string, diff: number, flag: boolean) => {
  const zoom = (window as any).controls2.object.zoom;
  const activeModel: any = scene.getObjectByName(activeModelName.value);
  const tag = flag ? 1 : -1;
  const point = cube.position.clone();
  point[type] += diff * tag - 1 / zoom;
  cube.position.set(point.x, point.y, point.z);
  activeModel.position.set(point.x, point.y, point.z);
  searchInfo.value = searchMaterialFromUuid(props.editSceneData, activeModelName.value);
  if (searchInfo.value.key) {
    const { key, index, i } = searchInfo.value;
    const editSceneData = { ...store.state.editSceneData, changeTime: new Date().getTime() };
    if (i == undefined) {
      editSceneData[key][index].location[type] = point[type];
      if (editSceneData[key][index].flag != 'add') {
        editSceneData[key][index].flag = 'update';
      }
    } else {
      editSceneData[key][index].materialMetaDtoList[i].location[type] = point[type];
      if (editSceneData[key][index].materialMetaDtoList[i].flag != 'add') {
        editSceneData[key][index].materialMetaDtoList[i].flag = 'update';
      }
    }
    store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
  }
};

const changeOperateType = (value: string) => {
  operateType.value = value;

  // 控制移动箭头的显示
  const arrowGroups = cube.children.filter(
    (item: any) => item.name == 'TransformArrow' && item.type == 'Group'
  );
  arrowGroups.forEach((group: any) => {
    // 移动箭头：在"移动"、"旋转"或"移动旋转"模式下显示
    group.children[0].visible = value == '移动' || value == '旋转' || value == '移动旋转';
    // 缩放箭头：只在"缩放"模式下显示
    group.children[1].visible = value == '缩放';
  });

  // 控制旋转环的显示
  cube.traverse((child: any) => {
    if (child.name === 'System_QuarterCircle') {
      // 旋转环：在"旋转"或"移动旋转"模式下显示
      child.visible = value == '旋转' || value == '移动旋转';
    }
  });

  console.log(`🎮 操作模式切换为: ${value}`);
  console.log(`📊 移动箭头显示: ${value == '移动' || value == '旋转' || value == '移动旋转'}`);
  console.log(`🔄 旋转环显示: ${value == '旋转' || value == '移动旋转'}`);

  // 统计控制器状态
  let arrowCount = 0;
  let circleCount = 0;

  arrowGroups.forEach((group: any) => {
    if (group.children[0].visible) arrowCount++;
  });

  cube.traverse((child: any) => {
    if (child.name === 'System_QuarterCircle' && child.visible) {
      circleCount++;
    }
  });

  console.log(`📈 控制器统计: ${arrowCount} 个移动箭头, ${circleCount} 个旋转环`);

  if (value === '移动旋转') {
    console.log('✅ 移动旋转模式已激活 - 用户可以同时使用移动和旋转功能');

    // 调试：检查旋转环的详细状态
    console.log('🔍 旋转环详细状态检查:');
    cube.traverse((child: any) => {
      if (child.name === 'System_QuarterCircle') {
        console.log(
          `  - 旋转环: ${child.userData.planName}, 可见: ${child.visible}, ID: ${child.id}`
        );
        console.log(`    材质: ${child.material ? '存在' : '不存在'}`);
        console.log(`    几何体: ${child.geometry ? '存在' : '不存在'}`);
        console.log(
          `    位置: (${child.position.x.toFixed(2)}, ${child.position.y.toFixed(
            2
          )}, ${child.position.z.toFixed(2)})`
        );
      }
    });
  }
};

// 画线
const drawDashLine = (graphGroup: any, groundY: number, data: any, nodeIds: any) => {
  const currentLine = scene.getObjectByName(nodeIds[1] + nodeIds[0]);
  if (currentLine) return;
  const cline = new CLine({
    ...data,
    color: 0x333333,
    lineWidth: 4,
    depthTest: false,
    transparent: true,
  });
  cline.userData.points = [new Vector3(...data.vertexs[0]), new Vector3(...data.vertexs[1])];
  cline.name = nodeIds.join('');
  cline.position.y = groundY;
  graphGroup.add(cline);
};

// 画点
const drawPoint = (
  graphGroup: any,
  groundY: number,
  data: any,
  nodeId: string,
  nodeType: number,
  isValid?: boolean
) => {
  const cpoint = new CPoint({
    ...data,
    size: 46 * controlsScale.value,
    depthTest: false,
    transparent: true,
    url: isValid
      ? nodeType == 1
        ? '/images/valid-default.png'
        : '/images/valid-default2.png'
      : nodeType == 1
      ? '/images/invalid-default.png'
      : '/images/invalid-default2.png',
  });
  cpoint.userData.valid = !!isValid;
  cpoint.name = nodeId;
  cpoint.userData.nodeId = nodeId;
  cpoint.position.y = groundY;
  graphGroup.add(cpoint);
};

watch(
  () => store.state.editSceneData.graphId,
  (newState) => {
    const groundObj: any = scene.getObjectByName('ground-init');
    const groundY = groundObj.position.y;
    const graphLine: any = scene.getObjectByName('graph-group');
    graphLine && scene.remove(graphLine);

    if (currentGraphId.value != newState && groundY && newState) {
      const graphGroup = new Group();
      graphGroup.name = 'graph-group';
      scene.add(graphGroup);
      getNjyjNodes({ graphId: newState }).then((res) => {
        graphPoints.value = res.data.filter((e: any) => e.nodeType == 1 || e.nodeType == 3);
        childNodeArray.value = res.data.filter((e: any) => e.nodeType == 2);
        initPoint(groundY, graphGroup);
      });
    }
    currentGraphId.value = newState;
  }
);

const initPoint = (groundY: number, graphGroup: any) => {
  const sceneChild = scene.children.filter((e) => e.userData.nodeId);
  sceneChild.forEach((e) => {
    scene.remove(e);
  });
  const pointObj: any = {};
  graphPoints.value.forEach((e: any) => {
    pointObj[e.nodeId] = e;
  });
  graphPoints.value.forEach((e: any) => {
    // 这里判断是有效点位还是无效点位
    if (e.relatedNodeIdList.length) {
      drawPoint(
        graphGroup,
        groundY,
        { vertex: new Vector3(e.x, 0, e.z) },
        e.nodeId,
        e.nodeType,
        true
      );
      e.relatedNodeIdList.forEach((k: any) => {
        const oldPath = scene.getObjectByName(pointObj[k].nodeId + e.nodeId);
        if (!oldPath) {
          drawDashLine(
            graphGroup,
            groundY,
            {
              vertexs: [
                [e.x, 0, e.z],
                [pointObj[k].x, 0, pointObj[k].z],
              ],
            },
            [e.nodeId, pointObj[k].nodeId]
          );
        }
      });
    } else {
      drawPoint(graphGroup, groundY, { vertex: new Vector3(e.x, 0, e.z) }, e.nodeId, e.nodeType);
    }
  });
};

watch(
  () => store.state.activeMaterial,
  (newState) => {
    if (newState) {
      activeCube(newState);
      if (!operateType.value) {
        store.state.operateType = '移动';
      }
    } else {
      if (store.state.operateType) {
        hideCube(activeModelName.value);
        store.state.operateType = '';
      }
    }
  }
);

watch(
  () => store.state.operateType,
  (newState) => {
    if (newState != operateType.value) {
      changeOperateType(newState);
    }
  }
);

onBeforeUnmount(() => {
  for (let i = 0; i < scene.children.length; i++) {
    // 删除文字标记
    scene.children[i].traverse((e: any) => {
      if (e.element) {
        e.parent?.remove(e);
      }
    });
    scene.remove(scene.children[i]);
    i--;
  }
});

// 设置地面平面可见性（现在控制整体显示/隐藏）
const setGroundVisibility = (visible: boolean) => {
  const ground = scene.getObjectByName('ground-init') as Mesh;
  if (ground) {
    ground.visible = visible;
    console.log(`🏗️ Ground plane visibility set to: ${visible ? 'visible' : 'hidden'}`);
  }
};

// 切换地面平面显示/隐藏
const toggleGroundVisibility = () => {
  const ground = scene.getObjectByName('ground-init');
  if (ground) {
    ground.visible = !ground.visible;
    console.log(`🏗️ Ground plane visibility: ${ground.visible ? 'visible' : 'hidden'}`);
  }
};

// 调试函数：检查旋转环状态
const debugQuarterCircles = () => {
  console.log('🔍 调试旋转环状态:');
  console.log(`当前操作模式: ${operateType.value}`);

  let totalCircles = 0;
  let visibleCircles = 0;

  cube.traverse((child: any) => {
    if (child.name === 'System_QuarterCircle') {
      totalCircles++;
      if (child.visible) visibleCircles++;

      console.log(`旋转环 ${totalCircles}:`);
      console.log(`  - 名称: ${child.name}`);
      console.log(`  - 平面: ${child.userData.planName}`);
      console.log(`  - 可见: ${child.visible}`);
      console.log(
        `  - 位置: (${child.position.x.toFixed(2)}, ${child.position.y.toFixed(
          2
        )}, ${child.position.z.toFixed(2)})`
      );
      console.log(`  - 材质存在: ${!!child.material}`);
      console.log(`  - 几何体存在: ${!!child.geometry}`);

      if (child.material) {
        console.log(`  - 材质透明: ${child.material.transparent}`);
        console.log(`  - 材质不透明度: ${child.material.opacity}`);
      }
    }
  });

  console.log(`总计: ${totalCircles} 个旋转环, ${visibleCircles} 个可见`);

  // 测试射线检测
  const testRaycaster = new Raycaster();
  testRaycaster.setFromCamera(new Vector2(0, 0), camera); // 屏幕中心
  const testIntersects = testRaycaster.intersectObjects(scene.children, true);
  const circleIntersects = testIntersects.filter(
    (e: any) => e.object.name === 'System_QuarterCircle'
  );
  console.log(`射线检测测试: 在屏幕中心检测到 ${circleIntersects.length} 个旋转环`);
};

// 获取方向控制器的引用
const orientationWidgetRef = ref();

defineExpose({
  addModel,
  // 地面平面控制
  setGroundVisibility,
  toggleGroundVisibility,
  // 调试函数
  debugQuarterCircles,
  // 3D场景对象
  scene,
  camera,
  controls,
  // 方向控制器
  orientationWidget: orientationWidgetRef,
});
</script>
<style scoped lang="less">
#edit_3d {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
}

.rr {
  position: fixed;
  width: 60px;
  height: 60px;
  line-height: 60px;
  left: 30%;
  top: 30%;
  background-color: red;
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
}
</style>
