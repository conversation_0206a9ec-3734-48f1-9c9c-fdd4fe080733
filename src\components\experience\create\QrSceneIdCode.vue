<template>
    <div class="new-mask">
        <el-card class="card">
            <header>
                <span>项目预览</span>
                <img class="closed" src="http://njyjxr.oss-cn-shanghai.aliyuncs.com/mask-close.png" alt=""
                    @click="closeEvent">
            </header>
            <main>
                <div id="capTexture">
                    <header>{{ scene.sceneName }}</header>
                    <qrcode-vue :value="qrContent" :size="210" foreground="#000000" class="qrcode" />
                </div>
                <el-button style="width: 130px;margin-top: 12px;" type="primary" @click="downloadImg">下载二维码</el-button>
            </main>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import html2canvas from 'html2canvas' // dom转canvas
import qrcodeVue from 'qrcode.vue' // https://gitcode.com/gh_mirrors/qr/qrcode.vue/overview?utm_source=artical_gitcode&index=top&type=card&webUrl&isLogin=1
import { onMounted, ref } from 'vue';
let data = 0
const props = defineProps({
    scene: {
        require: true,
        type: Object
    },
    hideAddMask: {
        default: null,
        type: Function
    }
})
const qrContent = ref<any>('')

const downloadImg = () => {
    const element = document.getElementById('capTexture') // 获取到要转为图片的dom
    html2canvas(element).then(canvas => {
        const dataURL = canvas.toDataURL('image/png');
        const a = document.createElement('a');
        a.href = dataURL;
        a.download = props.scene.sceneName + '.png';
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    })
}

function encryptString(message, key) {
    // 确保 key 长度为 16 字节
    const keyUtf8 = CryptoJS.enc.Utf8.parse(key.substring(0, 16));
    const iv = CryptoJS.enc.Utf8.parse("1234567890123456"); // 固定 16 字节 IV

    // 使用 AES 加密
    const encrypted = CryptoJS.AES.encrypt(message, keyUtf8, { iv: iv, padding: CryptoJS.pad.Pkcs7 });
    // 将 IV 和密文一起返回（Base64 编码）
    return iv.toString(CryptoJS.enc.Base64) + ":" + encrypted.toString();
}



onMounted(async () => {
    data = props.scene.id + ''
    // 示例
    const key = "h7K3s9jW5n2D1qXo";  // 必须 16 字节密钥
    const encryptedMessage = encryptString(data, key);
    console.log("加密后的字符串:", encryptedMessage);
    qrContent.value = encryptedMessage
})

const closeEvent = () => {
    props.hideAddMask()
}
</script>

<style scoped lang="less">
.new-mask {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .card {
        width: 430px;
        height: 430px;
        box-sizing: border-box;
        padding: 0 4px;

        header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;

            span {
                font-weight: bold;
                font-size: 18px;
                color: #1E1E1E;
            }

            .closed {
                width: 16px;
                height: 16px;
                cursor: pointer;
            }
        }

        main {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            #capTexture {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 20px;
            }

            header {
                font-weight: bold;
                font-size: 20px;
                color: #000000;
                margin-bottom: 16px;
                transform: translateY(-4px);
            }
        }
    }

}
</style>