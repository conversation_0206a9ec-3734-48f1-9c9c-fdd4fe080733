# OrientationWidget 外壳球体隐藏功能指南

## 功能概述

OrientationWidget 组件现在支持隐藏轨迹球的外壳球体，只保留方向指示器（坐标轴箭头和小圆球）可见，创造更简洁的视觉效果。

## 实现的功能

### 1. 外壳球体控制
- **默认隐藏**：外壳球体默认不可见，获得更简洁的视觉效果
- **可选显示**：通过 `showSphere` 属性控制外壳球体的显示/隐藏
- **动态切换**：支持运行时动态切换外壳球体的可见性

### 2. 功能保持完整
- **射线检测正常**：隐藏外壳后，方向指示器的点击检测仍然正常工作
- **拖拽功能保持**：轨迹球的拖拽旋转功能不受影响
- **视角切换正常**：点击方向指示器的视角切换动画功能正常

### 3. 视觉效果优化
- **简洁外观**：只显示坐标轴和端点小球，视觉更加清晰
- **保持交互**：所有交互功能保持不变
- **性能优化**：隐藏的球体不参与渲染，提升性能

## 使用方法

### 基本配置

```vue
<template>
  <!-- 隐藏外壳球体（默认） -->
  <OrientationWidget
    :main-camera="camera"
    :main-controls="controls"
    :show-sphere="false"
  />
  
  <!-- 显示外壳球体 -->
  <OrientationWidget
    :main-camera="camera"
    :main-controls="controls"
    :show-sphere="true"
  />
</template>
```

### 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `showSphere` | boolean | false | 是否显示外壳球体 |
| `mainCamera` | Camera | - | 主场景相机对象 |
| `mainControls` | Controls | - | 主场景控制器对象 |
| `size` | number | 120 | 控件大小 |
| `animationDuration` | number | 1000 | 视角切换动画时长 |
| `enableAnimation` | boolean | true | 是否启用平滑动画 |

### 程序化控制

```javascript
// 获取组件引用
const orientationWidget = ref();

// 动态显示外壳球体
orientationWidget.value.setSphereVisibility(true);

// 动态隐藏外壳球体
orientationWidget.value.setSphereVisibility(false);
```

## 技术实现

### 1. 球体创建与控制

```javascript
// 创建外壳球体
const sphereGeometry = new SphereGeometry(1.8, 32, 32);
const sphereMaterial = new MeshBasicMaterial({
  color: 0x444444,
  transparent: true,
  opacity: 0.1,
  wireframe: true,
});
const sphere = new Mesh(sphereGeometry, sphereMaterial);
sphere.visible = props.showSphere; // 根据props控制可见性
sphere.name = 'orientation-sphere'; // 添加名称便于识别
```

### 2. 动态可见性控制

```javascript
// 控制外壳球体的可见性
const setSphereVisibility = (visible: boolean) => {
  if (orientationGroup) {
    const sphere = orientationGroup.getObjectByName('orientation-sphere');
    if (sphere) {
      sphere.visible = visible;
      console.log(`🔮 外壳球体可见性设置为: ${visible}`);
    }
  }
};
```

### 3. 响应式更新

```javascript
// 监听球体可见性变化
watch(
  () => props.showSphere,
  (newVisibility) => {
    setSphereVisibility(newVisibility);
  }
);
```

## 射线检测优化

### 优先级处理
代码已经优化了射线检测逻辑，优先处理方向指示器的小球：

```javascript
// 优先查找圆球指示器（SphereGeometry），即使被背景球体遮挡
for (const intersect of intersects) {
  const object = intersect.object;
  
  // 检查是否是圆球指示器
  if (object.type === 'Mesh') {
    const geometry = (object as any).geometry;
    if (geometry && geometry.type === 'SphereGeometry') {
      // 确认这个圆球属于方向指示器组
      const parent = object.parent;
      if (parent && parent.name && Object.keys(directionObjects).includes(parent.name)) {
        return intersect;
      }
    }
  }
}
```

### 交互保证
- **点击检测**：方向指示器的小球优先被检测到
- **拖拽功能**：背景区域的拖拽功能正常工作
- **视角切换**：点击小球触发视角切换动画

## 视觉效果对比

### 隐藏外壳前
- 显示半透明的线框球体外壳
- 方向指示器在球体内部
- 视觉上有明确的边界感

### 隐藏外壳后
- 只显示坐标轴和端点小球
- 视觉更加简洁清晰
- 突出方向指示器的重要性

## 性能优化

### 渲染优化
- **隐藏对象**：不可见的球体不参与渲染计算
- **内存保持**：球体对象仍在内存中，便于快速切换
- **交互保持**：所有交互逻辑保持不变

### 兼容性
- **向后兼容**：现有代码无需修改
- **可选功能**：通过属性控制，不影响现有功能
- **平滑切换**：支持运行时动态切换

## 调试功能

### 控制台输出
```
🔮 外壳球体可见性设置为: false
🎯 检测到圆球指示器: x, 距离: 1.23
```

### 调试方法
```javascript
// 检查球体状态
const sphere = orientationGroup.getObjectByName('orientation-sphere');
console.log('球体可见性:', sphere?.visible);

// 动态切换测试
orientationWidget.value.setSphereVisibility(!sphere?.visible);
```

## 最佳实践

1. **默认隐藏**：建议默认隐藏外壳球体，获得更简洁的视觉效果
2. **用户选择**：可以提供用户设置选项，让用户选择是否显示外壳
3. **场景适配**：根据具体的应用场景选择合适的显示模式
4. **性能考虑**：在性能敏感的场景中，隐藏外壳可以提升渲染性能

## 示例代码

完整的使用示例请参考 `src/components/scene_web/CanvasThree.vue` 中的实现：

```vue
<OrientationWidget
  ref="orientationWidgetRef"
  :main-camera="camera"
  :main-controls="controls"
  :size="110"
  :animation-duration="1000"
  :enable-animation="true"
  :show-sphere="false"
/>
```

这个实现完全满足了您的要求，提供了更简洁的视觉效果，同时保持了所有功能的完整性。
