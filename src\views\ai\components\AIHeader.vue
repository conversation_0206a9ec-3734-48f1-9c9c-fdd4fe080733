<template>
  <div class="ai-header">
    <div class="ai-header-left">
      <el-button link class="back-btn" @click="$router.back()">
        <el-icon><ArrowLeft /></el-icon>

        <span style="margin-left: 4px; margin-right: 6px">返回</span>
      </el-button>
      <span class="ai-title">混空AI大模型</span>
    </div>
    <div class="ai-header-tabs">
      <div
        :class="['ai-tab', { active: activeTab === AIModelType.IMAGE }]"
        @click="handleTabClick(AIModelType.IMAGE)">
        <img
          :src="
            activeTab === AIModelType.IMAGE
              ? require('@/assets/images/starimg5.png')
              : require('@/assets/images/starimg1.png')
          "
          style="width: 16px; height: 16px; margin-right: 0px" />
        <span>生成图片</span>
      </div>
      <div
        :class="['ai-tab', { active: activeTab === AIModelType.AUDIO }]"
        @click="handleTabClick(AIModelType.AUDIO)">
        <img
          :src="
            activeTab === AIModelType.AUDIO
              ? require('@/assets/images/starimg6.png')
              : require('@/assets/images/starimg2.png')
          "
          style="width: 16px; height: 16px; margin-right: 0px" />
        <span>生成语音</span>
      </div>
      <div
        :class="['ai-tab', { active: activeTab === AIModelType.VIDEO }]"
        @click="handleTabClick(AIModelType.VIDEO)">
        <img
          :src="
            activeTab === AIModelType.VIDEO
              ? require('@/assets/images/starimg7.png')
              : require('@/assets/images/starimg3.png')
          "
          style="width: 16px; height: 16px; margin-right: 0px" />
        <span>生成视频</span>
      </div>
      <div
        :class="['ai-tab', { active: activeTab === AIModelType.MODEL }]"
        @click="handleTabClick(AIModelType.MODEL)">
        <img
          :src="
            activeTab === AIModelType.MODEL
              ? require('@/assets/images/starimg8.png')
              : require('@/assets/images/starimg4.png')
          "
          style="width: 16px; height: 16px; margin-right: 0px" />
        <span>生成3D模型</span>
      </div>
    </div>
    <div class="ai-header-right">
      <div class="ai-energy">
        <img
          src="@/assets/images/star11.png"
          style="width: 16px; height: 16px; margin-right: 8px"
          alt="AI能量值" />
        <span class="energy-label">AI能量值：</span>
        <span class="energy-value">
          {{ formatNumber(aiEnergyValue) }}
        </span>
        <div class="energy-divider">|</div>
        <el-button link class="buy-btn" @click="handleBuyClick">购买</el-button>
      </div>
      <div class="ai-energy">
        <img
          src="@/assets/images/dingdan.png"
          style="width: 24px; height: 24px; margin-right: 8px"
          alt="AI能量值" />
        <el-popover
          placement="bottom"
          width="50"
          trigger="hover"
          popper-class="order-invoice-popover">
          <template #default>
            <div class="popover-content">
              <div class="popover-item" @click="handleOrderClick">我的订单</div>
              <div class="popover-item" @click="handleInvoiceClick">发票管理</div>
            </div>
          </template>
          <template #reference>
            <span class="energy-label">订单发票</span>
          </template>
        </el-popover>
      </div>
    </div>

    <!-- 购买能量弹窗 -->
    <BuyEnergyDialog
      v-model:show="showBuyDialog"
      :current-energy="currentEnergy"
      :user-email="userEmail"
      @pay="handlePay" />

    <!-- 订单管理弹窗 -->
    <OrderManagementDialog v-model:show="showOrderDialog" />

    <!-- 发票管理弹窗 -->
    <InvoiceManagementDialog v-model:show="showInvoiceDialog" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ArrowLeft } from '@element-plus/icons-vue';
import { useStore } from 'vuex';
import { getOrgnizationPackage } from '@/api';
import { AIModelType } from '@/config';
import BuyEnergyDialog from './BuyEnergyDialog.vue';
import OrderManagementDialog from './OrderManagementDialog.vue';
import InvoiceManagementDialog from './InvoiceManagementDialog.vue';
import { ElMessage } from 'element-plus';

const store = useStore();
const activeTab = ref<number>(AIModelType.IMAGE);
const showBuyDialog = ref(false);
const showOrderDialog = ref(false);
const showInvoiceDialog = ref(false);
const currentEnergy = ref(123000);
const userEmail = ref(window.localStorage.getItem('userName') || '');

// 计算属性：获取AI能量值
const aiEnergyValue = computed(() => {
  const energy = store.state.userBindPackageDto?.aiEnergy;
  console.log('AI Energy Debug:', {
    userBindPackageDto: store.state.userBindPackageDto,
    aiEnergy: energy,
    type: typeof energy,
  });
  return energy;
});

// 格式化数字
const formatNumber = (num: number | null | undefined) => {
  if (num === null || num === undefined || isNaN(Number(num))) {
    return '0';
  }
  return new Intl.NumberFormat().format(Number(num));
};

// 处理购买按钮点击
const handleBuyClick = () => {
  showBuyDialog.value = true;
};

// 处理支付
const handlePay = (amount: number) => {
  console.log('支付金额：', amount);
  ElMessage.success(`正在支付：${amount} 能量值`);
  showBuyDialog.value = false;
};

// 处理订单点击
const handleOrderClick = () => {
  showOrderDialog.value = true;
};

// 处理发票管理点击
const handleInvoiceClick = () => {
  showInvoiceDialog.value = true;
};

// 定义emit
const emit = defineEmits<{
  tabChange: [tab: number];
}>();

// 切换标签页
const handleTabClick = (tab: number) => {
  activeTab.value = tab;
  emit('tabChange', tab);
};

onMounted(() => {
  // 获取用户信息
  getOrgnizationPackage({})
    .then((res: any) => {
      // 更新store中的用户数据
      if (res.data.userBindPackageDto) {
        store.dispatch('updateUserBindPackageDto', res.data.userBindPackageDto);
      }

      if (res.data.userDto) {
        store.dispatch('updateUserDto', res.data.userDto);
      }
    })
    .catch((error) => {
      console.error('获取用户信息失败:', error);
    });
});
</script>

<style scoped lang="less">
.ai-header {
  width: 100%;
  height: 64px;
  background: #fff;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 0 16px;
  position: sticky;
  top: 0;
  z-index: 10;
  box-sizing: border-box;

  &-left {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    width: 200px;

    .back-btn {
      font-size: 16px;
      color: #797979;
      margin-right: 16px;
      padding: 0;
    }

    .ai-title {
      font-weight: 500;
      font-size: 20px;
      color: #000000;
      text-align: left;
    }
  }

  &-tabs {
    display: flex;
    align-items: center;
    gap: 24px;
    flex: 1;
    justify-content: center;
    margin: 0 20px;

    .ai-tab {
      font-size: 14px;
      color: #797979;
      padding: 8px 0;
      cursor: pointer;
      transition: color 0.2s;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      font-weight: 400;

      &.active {
        color: #2e76ff;
        font-weight: 500;
      }

      .tab-icon {
        font-size: 18px;
      }
    }
  }

  &-right {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;

    .ai-energy {
      display: flex;
      align-items: center;
      height: 32px;
      background: linear-gradient(
        44deg,
        rgba(246, 41, 123, 0.1) -0.9%,
        rgba(208, 41, 246, 0.1) 31.63%,
        rgba(123, 19, 251, 0.1) 59.12%,
        rgba(19, 204, 251, 0.1) 99.93%
      );
      border-radius: 4px;
      padding: 0 16px;
      font-size: 14px;
      margin-right: 8px;

      .energy-label {
        color: #1e1e1e;
        margin-right: 4px;
        font-weight: 500;
        font-size: 14px;
        line-height: 18px;
      }

      .energy-value {
        color: #1e1e1e;
        font-weight: 500;
        font-size: 14px;
        line-height: 18px;
        margin-right: 8px;
      }

      .energy-divider {
        width: 14px;
        height: 18px;
        font-size: 14px;
        color: #797979;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 8px;
        position: relative;
        top: -1px;
      }
    }

    .buy-btn {
      color: #2e76ff !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      padding: 0 4px !important;
    }
  }
}

/* 弹出框样式 */
:deep(.order-invoice-popover) {
  padding: 0 !important;
  border-radius: 0px !important;
  background-color: #e8f1fd !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.popover-content {
  .popover-item {
    width: 100%;
    height: 26px;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
    font-size: 12px;
    color: #333;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(46, 118, 255, 0.1);
    }

    &:not(:last-child) {
      border-bottom: 1px solid rgba(255, 255, 255, 0.5);
    }
  }
}
</style>
