<template>
  <div class="login">
    <img src="@/assets/images/fonttext.png" alt="" class="logo-text">
    <div class="back-blue">
      <img src="@/assets/images/backbg.png" alt="">
    </div>
    <main>
      <div class="main">
        <!-- 登录 -->
        <div class="login-box" v-if="!loginStep" :class="{ registerBox: isRegister }">
          <div class="tab-title" v-if="!isRegister">
            <div @click="handleTabs(0)" :class="activeTabIndex == 0 ? 'active' : ''">企业客户
            </div>
            <div class="vertical-line">|</div>
            <div @click="handleTabs(1)" :class="activeTabIndex == 1 ? 'active' : ''">体验客户</div>
          </div>
          <div class="new-header" v-else>
            体验用户手机注册
          </div>
          <div v-if="!activeTabIndex">
            <el-form ref="ruleFormRef" :model="form" :rules="rules">
              <el-form-item prop="email">
                <span class="title_tip">账号</span>
                <el-input v-model="form.email" placeholder="请输入邮箱账号" />
              </el-form-item>
              <el-form-item style="margin-bottom: 40px;">
                <span class="title_tip">密码</span>
                <el-input v-model="form.password" type="password" placeholder="请输入密码" show-password />
              </el-form-item>
              <el-checkbox v-model="remember">记住密码</el-checkbox>
              <div class="agree">
                <el-checkbox v-model="agree"></el-checkbox>
                <span>我已经阅读并同意</span>
                <span @click.stop="showAgree = 1">《服务协议》</span>
                <span>和</span>
                <span @click.stop="showAgree = 2">《隐私政策》</span>
              </div>
              <el-button class="submit-btn" type="primary" @click="onSubmit(ruleFormRef)">登录</el-button>
            </el-form>
            <div class="forgot-password" @click="forgotPassword">忘记密码</div>
          </div>
          <div v-if="activeTabIndex">
            <div class="login2-tips" v-if="!isLoginForPassword">验证即登录，未注册将自动创建体验账号</div>
            <el-form ref="ruleFormRef" :model="form" :rules="rules">
              <el-form-item :class="{ formVw: isRegister }" prop="phoneNo">
                <span class="title_tip">账号</span>
                <el-input v-model="form.phoneNo" placeholder="请输入手机号" clearable :readonly="readonly" />
              </el-form-item>
              <el-form-item :class="{ formVw: isRegister }" class="code-box" prop="verifyCode"
                v-if="!isLoginForPassword || isRegister">
                <span class="title_tip">验证码</span>
                <el-input v-model="form.verifyCode" placeholder="请输入验证码" :readonly="readonly" />
                <div v-if="!sendVerificationing" class="code-text" @click="getCode(ruleFormRef)">{{ codeText }}</div>
                <div v-if="sendVerificationing" class="code-text">{{ seconds }} s</div>
              </el-form-item>
              <el-form-item :class="{ formVw: isRegister }" prop="peoplePassword" v-if="isLoginForPassword"
                style="margin-bottom: 40px;">
                <span class="title_tip">密码</span>
                <el-input v-model="form.peoplePassword" type="passward" placeholder="请输入密码" :readonly="readonly"
                  show-password />
              </el-form-item>
              <el-form-item :class="{ formVw: isRegister }" prop="peoplePassword2" v-if="isRegister">
                <span class="title_tip">确认密码</span>
                <el-input v-model="form.peoplePassword2" placeholder="请再次输入新密码" :readonly="readonly" show-password />
              </el-form-item>
              <template v-if="!isRegister">
                <el-checkbox v-model="peopleRemeber" v-if="isLoginForPassword">记住密码</el-checkbox>
                <div class="agree" :class="{ new_agree: !isLoginForPassword }">
                  <el-checkbox v-model="agree"></el-checkbox>
                  <span>我已经阅读并同意</span>
                  <span @click.stop="showAgree = 1">《服务协议》</span>
                  <span>和</span>
                  <span @click.stop="showAgree = 2">《隐私政策》</span>
                </div>
              </template>
              <el-button class="submit-btn" type="primary" @click="onSubmit(ruleFormRef)"
                :class="{ submitVw: isRegister }">{{
                  isRegister
                    ? '注册' : '登录' }}</el-button>
            </el-form>
            <div class="register" @click="register" v-if="!isRegister && isLoginForPassword">注册</div>
            <div class="login-switch" @click.stop="switchMethod" :class="{ loginVw: isRegister }">{{ isRegister ? '返回登录'
              :
              isLoginForPassword ?
                '验证码登录' :
                '密码登录' }}</div>
            <div class="forgot-password" @click="forgotPassword" v-if="!isRegister && isLoginForPassword">忘记密码</div>
          </div>
        </div>

        <!-- 忘记密码 step1 -->
        <div class="login-box" v-if="loginStep == 1">
          <h3 @click="loginStep = 0">找回密码</h3>
          <div>
            <el-form :model="form" ref="firstRef" :rules="rules">
              <el-form-item prop="email" v-if="!activeTabIndex">
                <span class="title_tip">邮箱</span>
                <el-input v-model="form.email" placeholder="请输入邮箱" />
              </el-form-item>
              <el-form-item prop="phoneNo" v-else>
                <span class="title_tip">手机号</span>
                <el-input v-model="form.phoneNo" placeholder="请输入手机号！" />
              </el-form-item>
              <el-button class="submit-btn" type="primary" @click="onSubmit(firstRef)">下一步</el-button>
            </el-form>
          </div>
        </div>

        <!-- 忘记密码 step2 -->
        <div class="login-box" v-if="loginStep == 2">
          <h3 @click="loginStep = 1">找回密码</h3>
          <div>
            <el-form :model="form" ref="twoRef">
              <el-form-item>
                <span class="title_tip">验证码</span>
                <el-input v-model="form.verifyCode" placeholder="请输入验证码" />
              </el-form-item>
              <div class="login-tips2">{{ findTip }}</div>
              <el-button class="submit-btn" type="primary" @click="onSubmit(twoRef)">下一步</el-button>
            </el-form>
          </div>
        </div>

        <!-- 忘记密码 step3 -->
        <div class="login-box" v-if="loginStep == 3">
          <h3 @click="loginStep = 2">设置新密码</h3>
          <div>
            <el-form :model="form" ref="threeRef" :rules="rules">
              <el-form-item prop="newPassword">
                <span class="title_tip">新密码</span>
                <el-input v-model="form.newPassword" placeholder="请输入新密码" autocomplete="new-password" show-password
                  :prefix-icon="LockIcon" />
              </el-form-item>
              <el-form-item prop="newPassword2">
                <span class="title_tip">确认新密码</span>
                <el-input v-model="form.newPassword2" placeholder="请再次输入新密码" show-password :prefix-icon="LockIcon" />
              </el-form-item>
              <el-button class="submit-btn" type="primary" @click="onSubmit(threeRef)">确认</el-button>
            </el-form>
          </div>
          <div class="login-tips">您正在找回的账号是：{{ activeTabIndex ? `****${lastPhoneNo}` : form.email }}</div>
        </div>
      </div>
    </main>
  </div>

  <!-- 重置密码成功 -->
  <el-dialog v-model="centerDialogVisible" title="" width="460" align-center>
    <span class="login-success">恭喜，账号{{ activeTabIndex ? `****${lastPhoneNo}` : form.email }}重置密码成功</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="centerDialogVisible = false">
          重新登录
        </el-button>
      </span>
    </template>
  </el-dialog>
  <consent-agreement v-if="showAgree" :show-type="showAgree" :handle-hide="() => showAgree = 0"></consent-agreement>
  <tips-view></tips-view>
  <!-- <div class="human-machine-verification" :style="{ left: verificationModel ? 0 : '-100vw' }">
    <div id="captcha-box"></div>
  </div> -->
</template>

<script lang="ts" setup>
import { throttle } from 'lodash';
import { reactive, ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import { login, verifyResetMailCode, getUserByMail, postResetMail, modiftUserWithPasswordAndVerify, verifyPhoneResetPasswordCode, modifyPasswordByMailCode, postVerifyCodeForLogin, normalogin, postVerifyCodeForRegister, registerPasswordAndVerify, postPhoneRestVerifyCode } from '@/api'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from "element-plus";
import ConsentAgreement from './components/ConsentAgreement.vue'
import { useRouter } from 'vue-router'
import UserIcon from './components/UserIcon.vue'
import LockIcon from './components/LockIcon.vue'
import VerificationCode from './components/VerificationCode.vue'
import TipsView from './components/TipsView.vue'
import { useStore } from 'vuex';
const router = useRouter()
const localTabIndex = localStorage.getItem('activeTabIndex')
const activeTabIndex = ref(localTabIndex ? Number(localTabIndex) : 1)

const isLoginForPassword = ref(true)
const lastPhoneNo = ref('')
const readonly = ref(true)
let lastTime = 0;

// do not use same name with ref
const form = reactive({
  password: '',
  email: '',
  code: '',
  newPassword: '',
  newPassword2: '',
  phoneNo: '',
  verifyCode: '',
  peoplePassword: '',
  peoplePassword2: ''
})

const initForm = () => {
  form.password = ''
  form.email = ''
  form.newPassword = ''
  form.newPassword2 = ''
  form.phoneNo = ''
  form.verifyCode = ''
  form.peoplePassword = ''
  form.peoplePassword2 = ''
}
const ruleFormRef = ref<FormInstance>()
const firstRef = ref<FormInstance>()
const twoRef = ref<FormInstance>()
const threeRef = ref<FormInstance>()
interface RuleForm {
  email: '',
  newPassword: '',
  newPassword2: '',
  phoneNo: '',
  verifyCode: ''
}

const findTip = computed(() => {
  return activeTabIndex.value ? `我们已向您的手机****${lastPhoneNo.value}发送了验证码` : `我们已向您的绑定邮箱${form.email}发送了验证码`
})

const validatePass = (rule: any, value: any, callback: any) => {
  const reg = /^(?=.*\d)(?=.*[a-zA-Z])[a-zA-Z\d]{8,20}$/
  if (!reg.test(value)) {
    callback(new Error('密码为8~20位数字与字母组合，不含特殊字符'))
    return
  }
  if (rule.field === 'newPassword2' && value !== form.newPassword) {
    callback(new Error('两次输入密码不一致'))
    return
  }
  if (rule.field === 'peoplePassword2' && value !== form.peoplePassword) {
    callback(new Error('两次输入密码不一致'))
    return
  }
  callback()
}


const verifyPhoneNumber = (rule: any, value: any, callback: any) => {
  let reg = /^1[3-9]\d{9}$/
  if (!reg.test(value.replace(/\s+/g, ""))) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules<RuleForm>>({
  newPassword: [{ validator: validatePass, trigger: 'blur' }],
  newPassword2: [{ validator: validatePass, trigger: 'blur' }],
  peoplePassword: [{ validator: validatePass, trigger: 'blur' }],
  peoplePassword2: [{ validator: validatePass, trigger: 'blur' }],
  email: [
    {
      type: 'email',
      required: true,
      message: '请输入正确的邮箱格式',
      trigger: 'change',
    },
  ],
  phoneNo: [{ required: true, message: '请输入手机号', trigger: 'blur' }, { validator: verifyPhoneNumber, trigger: 'blur' }],
  verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
})

const isRegister = ref(false)
const remember = ref(false)
const peopleRemeber = ref(false)
const agree = ref(false)
const loginStep = ref(0)
const centerDialogVisible = ref(false)
const userId = ''
const showAgree = ref(0)
const sendVerificationing = ref(false)
const seconds = ref(60)
let timer: any = null
const codeText = ref('发送验证码')
const verificationModel = ref(false)
let lastPhoneNumber = ''
let tipThrottle = false

const store = useStore();

const switchMethod = () => {
  const peoplePassword = localStorage.getItem('peoplePassword')
  readonly.value = true
  isLoginForPassword.value = !isLoginForPassword.value
  if (isRegister.value) {
    form.verifyCode = '' // 注册切回验证码登录，清除验证码
    isLoginForPassword.value = true
    document.querySelector('.el-input__password')?.click()
  }
  isRegister.value = false
  setTimeout(() => {
    readonly.value = false
  }, 100);
  codeText.value = '发送验证码'

  if (isLoginForPassword.value && peopleRemeber.value) {
    if (peoplePassword) {
      form.peoplePassword = peoplePassword
    }
  }
  sendVerificationing.value = false
  seconds.value = 60

}

const register = () => {
  form.peoplePassword = ''
  form.verifyCode = ''
  readonly.value = true
  isRegister.value = true
  setTimeout(() => {
    readonly.value = false
  }, 1000);
  codeText.value = '发送验证码'
  sendVerificationing.value = false
  seconds.value = 60
  ruleFormRef.value?.clearValidate()
}



const handleTipTime = () => {
  return ElMessage({ message: '输入手机号！', type: 'warning' })
}


const getCode = async (formEl?: FormInstance | undefined) => {
  let reg = /^1[3-9]\d{9}$/
  if (!form.phoneNo) {
    const now = new Date().getTime();
    if (now - lastTime >= 3000) {
      lastTime = now;
      return ElMessage({ message: '输入手机号！', type: 'warning' })
    } else {
      return
    }
  }
  if (!reg.test(form.phoneNo.replace(/\s+/g, ""))) return
  form.verifyCode = ''
  sendVerificationing.value = true
  const postVerifyCode = isRegister.value ? postVerifyCodeForRegister : postVerifyCodeForLogin
  postVerifyCode({ phoneNo: form.phoneNo.replace(/\s+/g, "") }).then((res) => {
    // verificationModel.value = false
    clearInterval(timer)
    timer = setInterval(() => {
      seconds.value -= 1
      if (seconds.value == 0) {
        codeText.value = '重新获取'
        sendVerificationing.value = false
        seconds.value = 60
        clearInterval(timer)
      }
    }, 1000)
  })
}

const onSubmit = async (formEl?: FormInstance | undefined) => {
  form.email = form.email.trim()
  if (isRegister.value) {
    requestRegister(formEl)
    return
  }
  if (loginStep.value) {
    if (!activeTabIndex.value) {
      if (loginStep.value == 1) {
        postResetMail({ mail: form.email }).then((res: any) => {

        })
        loginStep.value = 2
      } else if (loginStep.value == 2) {
        if (!form.verifyCode) return ElMessage({ message: '验证码不为空！', type: 'warning' })
        if (form.verifyCode) {
          verifyResetMailCode({ code: form.verifyCode, mail: form.email }).then((res) => {
            if (res.data) {
              loginStep.value = 3;
            } else {
              store.state.showTips = '验证码校验失败'
            }
          });
        }
      } else if (loginStep.value == 3) {
        threeEditLogin(formEl)
      }
    } else {
      otherLoginStep(formEl)
    }

    return
  }
  if (!formEl) return
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      if (!loginStep.value) {
        if (!agree.value) {
          store.state.showTips = '请阅读并勾选《服务协议》和《隐私政策》'
          return
        }
        if (!activeTabIndex.value) {
          const parmas = {
            mail: form.email,
            verifyCode: '',
            loginTypeId: 3,
            password: form.password,
            phoneNo: ''
          }
          normalogin(parmas).then((res) => {
            if (res.code === '2000' || res.code === '200') {
              window.localStorage.setItem('token', res.data)
              window.localStorage.setItem('activeTabIndex', activeTabIndex.value)
              window.localStorage.setItem('userName', form.email)
              window.localStorage.setItem('infoPassword', form.password)
              if (remember.value) {
                window.localStorage.setItem('password', form.password)
                window.localStorage.setItem('isRemember', '1')
              } else {
                window.localStorage.setItem('password', '')
                window.localStorage.setItem('isRemember', '')
              }
              window.localStorage.setItem('codePassword', form.password)
              if (agree.value) {
                window.localStorage.setItem('agreeValue', '1')
              } else {
                window.localStorage.setItem('agreeValue', '')
              }
              localStorage.setItem('identity', 'enterprise')
              router.push('/home')
              initForm()
            }
          })
        } else {
          const parmas = {
            phoneNo: form.phoneNo.replace(/\s+/g, ""),
            verifyCode: isLoginForPassword.value ? '' : form.verifyCode,
            loginTypeId: isLoginForPassword.value ? 5 : 4,
            password: isLoginForPassword.value ? form.peoplePassword : '',
            mail: ''
          }
          normalogin(parmas).then((res) => {
            if (res.code == '2000' || res.code == '200') {
              window.localStorage.setItem('token', res.data)
              window.localStorage.setItem('activeTabIndex', activeTabIndex.value)
              window.localStorage.setItem('isLoginForPassword', isLoginForPassword.value + '')
              window.localStorage.setItem('phoneNo', form.phoneNo.replace(/\s+/g, ""))
              window.localStorage.setItem('infoPassword', form.peoplePassword)
              if (peopleRemeber.value) {
                window.localStorage.setItem('peoplePassword', form.peoplePassword)
                window.localStorage.setItem('isPeopleRemeber', '1')
              } else {
                window.localStorage.setItem('peoplePassword', '')
                window.localStorage.setItem('isPeopleRemeber', '')
              }
              if (agree.value) {
                window.localStorage.setItem('agreeValue', '1')
              } else {
                window.localStorage.setItem('agreeValue', '')
              }
              localStorage.setItem('identity', 'experience')
              router.push('/experience_home')
              initForm()
            }
          })
        }
      }
    } else {
    }
  })
}

const threeEditLogin = async (formEl) => {
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      modifyPasswordByMailCode({ code: form.verifyCode, mail: form.email, password: form.newPassword }).then((res) => {
        if (res.code == '200') {
          centerDialogVisible.value = true
          loginStep.value = 0
        }
      })
    } else {
    }
  })
}

const requestRegister = async (formEl) => {
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      const parmas = {
        phoneNo: form.phoneNo.replace(/\s+/g, ""),
        verifyCode: form.verifyCode,
        password: isLoginForPassword.value ? form.peoplePassword : ''
      }
      registerPasswordAndVerify(parmas).then(res => {
        if (res.code == 200) {
          ElMessage({ message: '注册成功！', type: 'success' })
          isRegister.value = false
          isLoginForPassword.value = true
        }
      })
    } else {

    }
  })
}

const otherLoginStep = async (formEl) => {
  if (loginStep.value == 1) {
    let reg = /^1[3-9]\d{9}$/
    if (!form.phoneNo) return ElMessage({ message: '输入手机号！', type: 'warning' })
    if (!reg.test(form.phoneNo.replace(/\s+/g, ""))) return ElMessage({ message: '输入正确的手机号！', type: 'warning' })
    lastPhoneNo.value = form.phoneNo.slice(-4)
    postPhoneRestVerifyCode({ phoneNo: form.phoneNo.replace(/\s+/g, "") }).then(res => {
    })
    loginStep.value = 2
  } else if (loginStep.value == 2) {
    if (!form.verifyCode) return ElMessage({ type: 'warning', message: '验证码不为空！' })
    if (form.verifyCode) {
      verifyPhoneResetPasswordCode({ code: form.verifyCode, phoneNo: form.phoneNo.replace(/\s+/g, "") }).then((res) => {
        if (res.data) {
          form.newPassword = ''
          loginStep.value = 3;
        } else {
          store.state.showTips = '验证码校验失败'
        }
      });
    }
  } else if (loginStep.value == 3) {
    if (!formEl) return
    await formEl.validate((valid: any, fields: any) => {
      if (valid) {
        modiftUserWithPasswordAndVerify({ verifyCode: form.verifyCode, phoneNo: form.phoneNo.replace(/\s+/g, ""), password: form.newPassword }).then((res) => {
          if (res.code == '200') {
            centerDialogVisible.value = true
            loginStep.value = 0
          }
        })
      } else {
      }
    })
  }
}


const forgotPassword = () => {
  form.verifyCode = ''
  loginStep.value = 1
}

const handleTabs = (tabIndex) => {
  if (tabIndex) {
    readonly.value = true
    activeTabIndex.value = 1
    setTimeout(() => {
      readonly.value = false
    }, 1000);
  } else {
    activeTabIndex.value = 0
    isRegister.value = false
  }

}

onUnmounted(() => {
  clearInterval(timer)
})

onMounted(() => {
  window.localStorage.setItem('token', '')

  const userName = window.localStorage.getItem('userName')
  const password = window.localStorage.getItem('password')
  const phoneNo = window.localStorage.getItem('phoneNo')
  const peoplePassword = window.localStorage.getItem('peoplePassword')
  const isRemember = window.localStorage.getItem('isRemember')
  const isPeopleRemeber = window.localStorage.getItem('isPeopleRemeber')
  agree.value = !!window.localStorage.getItem('agreeValue') || false

  if (password && userName && isRemember) {
    form.password = password
    form.email = userName
    remember.value = true
  }
  if (phoneNo && peoplePassword && isPeopleRemeber) {
    form.peoplePassword = peoplePassword
    form.phoneNo = phoneNo
    peopleRemeber.value = true
  }
  const baseURL = process.env.NODE_ENV === 'production' ? '/api' : '/api1'
  setTimeout(() => {
    readonly.value = false
  }, 1000)
})

watch(form, (newState) => {
  if (activeTabIndex.value) return
  if (lastPhoneNumber !== newState.phoneNo) {
    const phoneNo = newState.phoneNo.replace(/\s+/g, "")
    lastPhoneNumber = phoneNo.slice(0, 3) + (phoneNo.length > 3 ? ' ' : '') + (phoneNo.length > 3 ? phoneNo.slice(3, 7) : '') + (phoneNo.length > 7 ? ' ' : '') + (phoneNo.length > 7 ? phoneNo.slice(7, 11) : '');
    form.phoneNo = lastPhoneNumber
  }
})

</script>


<style>
input {
  color: #1E1E1E !important;
}

input::placeholder {
  font-size: 14px;
  color: #8D8D8D !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  /* 背景色 */
  transition: background-color 5000s ease-in-out 0s;
  /* 防止背景色闪烁 */
}
</style>
<style scoped lang="less">
.new-header {
  font-weight: bold;
  font-size: 24px;
  color: #2E76FF;
  text-align: center;
  transform: translateY(-2px);
}

:deep(.el-form-item__error) {
  padding-top: 12px;
  font-size: 12px;
}


:deep(.el-form-item) {
  margin-top: 35px !important;
}

.title_tip {
  font-weight: 500;
  font-size: 16px;
  color: #000000;
}


.human-machine-verification {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.4);
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.formVw {
  margin: 10px 0 !important;
  margin-bottom: 26px !important;
}

.submitVw {
  bottom: 64px !important;
}

.loginVw {
  bottom: 25px !important;
}

.logo-text {
  position: absolute;
  top: 47px;
  left: 47px;
  width: 169px;
  height: 32px;
  z-index: 999;
}


.login {
  background-color: #efefef;
  min-width: 100%;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  background-size: 100% 100%;

  ::v-deep(.el-input__wrapper) {
    font-size: 15px;
    padding-left: 30px;
    height: 54px;
    margin-top: 8px;

    input {
      padding-left: 16px;
      font-size: 14px;
    }
  }

  .code-box {
    position: relative;

    .code-text {
      position: absolute;
      right: 16px;
      top: 55%;
      height: 32px;
      font-weight: 600;
      font-size: 14px;
      color: #2E76FF;
      cursor: pointer;
      text-align: center;

      &::before {
        content: '';
        width: 2px;
        height: 20px;
        background: rgba(216, 216, 216, 0.5);
        position: absolute;
        left: -18px;
        top: 6px;
      }
    }
  }

  main {
    width: 70%;
    display: flex;
    justify-content: center;
    align-items: center;

    .main {
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      overflow: hidden;
      border-radius: 20px;
    }
  }

  .back-blue {
    position: relative;
    height: 100%;
    display: flex;
    width: 540px;
    max-width: 540px;
    min-width: 540px;

    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }


  .login-box {
    position: relative;
    width: 568px;
    height: 630px;
    border-radius: 10px;
    background: #fff;
    box-shadow: 0px 5px 30px 0px rgba(89, 132, 219, 0.08);
    padding: 45px;
    box-sizing: border-box;
    text-align: left;
    backdrop-filter: blur(3px);

    h3 {
      position: relative;
      font-weight: 600;
      font-size: 22px;
      color: rgba(0, 0, 0, 0.5);
      padding-left: 36px;
      cursor: pointer;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 2px;
        width: 26px;
        height: 26px;
        background: url(~@/assets/images/icon/login-back.png);
        background-size: 100% 100%;
      }
    }

    .tab-title {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 600;
      font-size: 24px;
      color: rgba(0, 0, 0, 0.5);
      line-height: 30px;

      .vertical-line {
        color: rgba(0, 96, 255, 0.5);
        margin: 0 2.8vw;
      }

      &>div:not(.vertical-line) {
        position: relative;
        cursor: pointer;

        &.active {
          color: #0060FF;
        }

        &.active::after {
          height: 4px;
          width: 100%;
          position: absolute;
          left: 0;
          bottom: 0;
          background: #3671FE;
          border-radius: 4px;
        }
      }
    }

    .login2-tips {
      text-align: center;
      margin-top: 1.1vw;
      font-size: 14px;
      color: #8d8d8d;
    }

    .register {
      font-size: 14px;
      position: absolute;
      left: 58px;
      bottom: 46px;
      color: #3671FE;
      cursor: pointer;
    }

    .login-switch {
      text-align: center;
      font-size: 14px;
      color: #3671FE;
      cursor: pointer;
      text-decoration: underline;
      position: absolute;
      left: 0;
      bottom: 46px;
      transform: translateX(-50%);
      margin-left: 50%;
    }
  }

  .registerBox {
    height: 700px;
  }

  .submit-btn {
    display: block;
    width: 476px;
    height: 50px;
    position: absolute;
    left: 47px;
    bottom: 100px;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 6px;
    background: #3671FE;
    border-radius: 10px;
  }


  .forgot-password {
    position: relative;
    position: absolute;
    right: 58px;
    bottom: 46px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #236CFF;
    cursor: pointer;
    padding-left: 18px;

    &::after {
      content: '';
      width: 14px;
      height: 14px;
      position: absolute;
      left: 0;
      top: 3.5px;
      background: url(~@/assets/images/icon/forget-password.png);
      background-size: 100% 100%;
    }
  }

  .login-tips {
    font-size: 12px;
    color: #000;
    font-weight: 600;
    margin-top: 35px;
  }

  .login-tips2 {
    font-size: 12px;
    color: #000;
    font-weight: 600;
  }

  .agree {
    color: #A3A3A3;
    cursor: default;
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    margin-top: 14px;

    span:nth-child(2n+1) {
      color: #236CFF;
      cursor: pointer;
    }

    .el-checkbox:last-of-type {
      margin-right: 8px;
      vertical-align: middle;
    }

    &>span {
      display: inline-block;
      position: relative;
      font-size: 14px;
      vertical-align: middle;
    }
  }

  ::v-deep(.el-checkbox) {
    color: #A3A3A3;
    font-weight: 500;
    height: auto;

    .el-checkbox__label {
      font-size: 14px;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #236CFF;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #236CFF;
      border-color: #236CFF;
    }
  }
}

.dialog-footer {
  display: block;
  text-align: center;
}

.login-success {
  font-size: 12px;
  color: #000;
  font-weight: 500;
}

.new_agree {
  margin-top: 32px !important;
}
</style>