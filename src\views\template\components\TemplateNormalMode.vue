<template>
  <div class="template-normal-mode">
    <!-- 导航头部 -->
    <header class="template-normal-mode__header">
      <nav class="nav-tabs">
        <button
          class="nav-tab"
          :class="{ 'nav-tab--active': activeTopMenuTab && activeTopMenuTab.name == menu.name }"
          v-for="(menu, index) in arTopMenuList"
          :key="index"
          @click="handleActiveTopTab(menu)"
          type="button">
          {{ menu.name }}
        </button>
      </nav>
      <nav class="nav-tabs">
        <button
          class="nav-tab"
          :class="{ 'nav-tab--active': activeAllMenuTab && activeAllMenuTab.name == menu.name }"
          v-for="(menu, index) in arAllMenuList"
          :key="index"
          @click="handleActiveAllTab(menu)"
          type="button">
          {{ menu.name }}
        </button>
      </nav>
    </header>

    <!-- 模板网格 -->
    <div class="template-grid" v-if="templateList && templateList.length > 0">
      <div
        class="template-card-wrapper"
        v-for="(item, i) in templateList"
        :key="`template-${item.id || item.sceneId}`">
        <TemplateCard
          :template-data="item"
          :card-index="i"
          :dom-mask-indexs="domMaskIndexs"
          :show-edit-button="showEditButton"
          @mouseenter="handleMouseEnter"
          @mouseleave="handleMouseLeave"
          @edit="handleEdit"
          @experience="handleExperience"
          @create-similar="handleCreateSimilar" />
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="!templateList || templateList.length === 0">
      <img src="@/assets/images/nolistimg.png" alt="暂无相关文件" class="empty-img" />
      <span>暂无相关文件</span>
    </div>

    <!-- 加载更多 -->
    <div class="load-more" v-if="showLoadMore">
      <button class="load-more__button" @click="handleLoadMore" :disabled="isLoading">
        {{ isLoading ? '加载中...' : '加载更多' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import TemplateCard from '@/components/TemplateCard.vue';

// Props
const props = defineProps({
  templateList: {
    type: Array,
    default: () => [],
  },
  arTopMenuList: {
    type: Array,
    default: () => [],
  },
  arAllMenuList: {
    type: Array,
    default: () => [],
  },
  activeTopMenuTab: {
    type: Object,
    default: null,
  },
  activeAllMenuTab: {
    type: Object,
    default: null,
  },
  domMaskIndexs: {
    type: Array,
    default: () => [],
  },
  showEditButton: {
    type: Boolean,
    default: true,
  },
  showLoadMore: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits([
  'active-top-tab',
  'active-all-tab',
  'mouseenter',
  'mouseleave',
  'edit',
  'experience',
  'create-similar',
  'load-more',
]);

// Methods
const handleActiveTopTab = (menu) => {
  emit('active-top-tab', menu);
};

const handleActiveAllTab = (menu) => {
  emit('active-all-tab', menu);
};

const handleMouseEnter = (templateData, cardIndex) => {
  emit('mouseenter', templateData, cardIndex);
};

const handleMouseLeave = (templateData) => {
  emit('mouseleave', templateData);
};

const handleEdit = (templateData) => {
  emit('edit', templateData);
};

const handleExperience = (templateData) => {
  emit('experience', templateData);
};

const handleCreateSimilar = (templateData) => {
  emit('create-similar', templateData);
};

const handleLoadMore = () => {
  emit('load-more');
};
</script>

<style lang="less" scoped>
.template-normal-mode {
  width: 100%;

  &__header {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
    margin-bottom: 20px;
    padding-right: 20px;
  }
}

// 导航标签样式
.nav-tabs {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.nav-tab {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 400;
  color: #1e1e1e;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(46, 118, 255, 0.1);
    color: #2e76ff;
  }

  &--active {
    background: rgba(46, 118, 255, 0.1);
    color: #2e76ff;
    font-weight: 500;
  }
}

// 模板网格布局
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  padding: 4px;
  overflow: visible;
  margin-top: 10px;
}

// 模板卡片包装器
.template-card-wrapper {
  width: 100%;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  min-height: 300px;

  .empty-img {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  span {
    font-size: 14px;
    color: #999999;
    font-weight: 400;
  }
}

// 加载更多按钮
.load-more {
  display: flex;
  justify-content: center;
  margin-top: 40px;

  &__button {
    padding: 12px 32px;
    background: #2e76ff;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: #1d5ce8;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(46, 118, 255, 0.3);
    }

    &:disabled {
      background: #94a3b8;
      cursor: not-allowed;
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 18px;
  }
}

@media (max-width: 992px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }

  .template-normal-mode__header {
    padding-right: 10px;
  }
}

@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 14px;
    padding: 2px;
  }

  .nav-tab {
    padding: 5px 8px;
    font-size: 11px;
  }
}

@media (max-width: 576px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 12px;
  }
}
</style>
