<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div>空间删除</div>
        <div class="icon iconfont icon-close" @click="changeState"></div>
      </div>
      <div class="modal-form">
        <div class="sure-title">{{ spaceBindScene.length ? '当前空间已经在以下项目中使用，不可删除。' : '确认要删除这个空间吗？删除此空间后不可恢复。' }} </div>
        <div class="scene-list">
          <div v-for="(item, index) in spaceBindScene" :key="index">
            项目名称：{{ item.sceneName }}
          </div>
        </div>
        <div class="btn-box" v-if="!spaceBindScene.length">
          <div class="btn-default el-size3">
            <el-button @click="changeState">取消</el-button>
          </div>
          <div class="btn-primary el-size3">
            <el-button @click="submitForm()">确认</el-button>
          </div>
        </div>
        <div class="btn-box" v-else>
          <div class="btn-primary el-size3">
            <el-button @click="changeState">知道了</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { getSpaceBindScene, deleteSpace } from "@/api";

const props = defineProps({
  handleHide: {
    default: null,
    type: Function,
  },
  defaultSpaceData: {
    default: null,
    type: Object,
  },
});

const spaceBindScene: any = ref([]);

const submitForm = async () => {
  deleteSpace({ spaceId: props.defaultSpaceData.id }).then((res: any) => {
    if (res.code == 200) {
      props.handleHide(true);
    }
  });
};

const changeState = () => {
  props.handleHide();
};

onMounted(() => {
  getSpaceBindScene({ spaceId: props.defaultSpaceData.id }).then((res: any) => {
    spaceBindScene.value = [...res.data];
  });
});
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;

  .modal-content {
    width: 816px;
    height: 380px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -408px;
    margin-top: -240px;
    background: #edeff2;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    border: 1px solid #edeff2;
    overflow: hidden;

    .modal-content-title {
      height: 76px;
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px 0 30px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;

      .icon-close {
        font-size: 26px;
        cursor: pointer;
        font-weight: 400;

        &:hover {
          color: #2E76FF;
        }
      }
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      padding: 20px 30px;
      box-sizing: border-box;

      .sure-title {
        text-align: left;
        font-size: 16px;
        font-weight: 600;
      }

      .sure-tips {
        text-align: left;
        margin-top: 15px;
      }

      .scene-list {
        width: 300px;
        height: 200px;
        margin-top: 20px;
        overflow: hidden;
        overflow-y: auto;

        &>div {
          margin: 10px 0;
          text-align: left;
        }
      }

      .btn-box {
        position: absolute;
        bottom: 50px;
        right: 35px;
        display: flex;
        justify-content: right;
        align-items: center;
      }
    }
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;
}
</style>