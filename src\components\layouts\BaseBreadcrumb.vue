<template>
  <el-breadcrumb class="breadcrumb-box" :separator-icon="ArrowRight">
    <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :to="{ path: item.path }" :key="index">{{ item.name }}</el-breadcrumb-item>
  </el-breadcrumb>
</template>
<script lang="ts" setup>
const props = defineProps({
  breadcrumbList: {
    default: null,
    type: Object
  }
})

import { ArrowRight } from '@element-plus/icons-vue'
</script>
<style scoped lang="less">
.breadcrumb-box {
  margin: 20px;
  margin-bottom: 50px;
}
</style>