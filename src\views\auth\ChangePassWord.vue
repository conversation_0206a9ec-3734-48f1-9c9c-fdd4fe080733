<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div style="margin-bottom: 8px;">更新密码</div>
        <!-- <div class="icon iconfont icon-close" @click="changeState"></div> -->
      </div>
      <div class="modal-form">
        <el-form ref="ruleFormRef" :model="ruleForm" label-width="85px" class="demo-ruleForm" :rules="rules">
          <el-form-item label="账号">
            <el-input style="width: 332px;" disabled v-model="ruleForm.email" placeholder="请输入用户名"
              autocomplete="new-password" />
          </el-form-item>
          <el-form-item label="验证码" class="code-box" prop="verifyCode">
            <el-input v-model="ruleForm.verifyCode" placeholder="请输入验证码" autocomplete="new-password" />
            <div v-if="!sendVerificationing" class="code-text" @click="getCode(ruleFormRef)">{{ codeText }}</div>
            <div v-if="sendVerificationing" class="code-text">{{ seconds }} s</div>
          </el-form-item>
          <el-form-item label="设置新密码" prop="password">
            <el-input v-model="ruleForm.password" placeholder="请输入密码" show-password autocomplete="new-password" />
          </el-form-item>
          <el-form-item label="确认新密码" prop="password2">
            <el-input v-model="ruleForm.password2" placeholder="请再次输入新密码" show-password autocomplete="new-password" />
          </el-form-item>
          <el-form-item class="form-submit">
            <div class="btn-default" style="margin-right: 14px;">
              <el-button @click="changeState" class="my-btn">取消</el-button>
            </div>
            <div class="btn-primary">
              <el-button @click="submitForm(ruleFormRef)" class="my-btn">确认</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <tips-view></tips-view>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { modifyPasswordByOrgnizationAdmin, postResetMail, modifyPasswordByMailCode, verifyResetMailCode, modiftUserWithPasswordAndVerify, postPhoneRestVerifyCode, verifyPhoneResetPasswordCode } from '@/api'
import UserIcon from './components/UserIcon.vue'
import LockIcon from './components/LockIcon.vue'
import VerificationCode from './components/VerificationCode.vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from "element-plus";
import { useRouter } from 'vue-router'
import TipsView from './components/TipsView.vue'
import { useStore } from 'vuex'
const router = useRouter()
const codeText = ref('发送验证码')
const sendVerificationing = ref(false)
let timer: any = null
const seconds = ref(60)

const props = defineProps({
  changeEvent: {
    default: null,
    type: Function
  },
  userType: {
    default: 6,
    type: Number
  }
})

const store = useStore()

const ruleFormRef = ref<FormInstance>()
const ruleForm: any = reactive({
  email: '',
  password: '',
  verifyCode: '',
  password2: ''
})

const validatePass = (rule: any, value: any, callback: any) => {
  const reg = /^(?=.*\d)(?=.*[a-zA-Z])[a-zA-Z\d]{8,20}$/
  if (!reg.test(value)) {
    callback(new Error('密码为8~20位数字与字母组合，不含特殊字符'))
    return
  }
  if (rule.field === 'password2' && value !== ruleForm.password) {
    callback(new Error('两次输入密码不一致'))
    return
  }
  callback()
}

const rules = reactive({
  password: [{ validator: validatePass, trigger: 'blur' }],
  password2: [{ validator: validatePass, trigger: 'blur' }],
  verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
})

const getCode = async (formEl?: FormInstance | undefined) => {
  ruleForm.verifyCode = ''
  sendVerificationing.value = true
  const params = [6, 7].includes(props.userType) ? { phoneNo: ruleForm.email.replace(/\s+/g, "") } : { mail: ruleForm.email }
  const postCode = [6, 7].includes(props.userType) ? postPhoneRestVerifyCode : postResetMail
  postCode(params).then((res: any) => {
    timer = setInterval(() => {
      seconds.value -= 1
      if (seconds.value == 0) {
        codeText.value = '重新获取'
        sendVerificationing.value = false
        seconds.value = 60
        clearInterval(timer)
      }
    }, 1000)
  })
}

const requestVerifyUser = () => {
  return new Promise(resolve => {
    verifyPhoneResetPasswordCode({ code: ruleForm.verifyCode, phoneNo: ruleForm.email }).then((res) => {
      if (res.data) {
        resolve(1)
      } else {
        store.state.showTips = '验证码校验失败'
      }
    });
  })
}

const requestVerify = () => {
  return new Promise(resolve => {
    verifyResetMailCode({ code: ruleForm.verifyCode, mail: ruleForm.email }).then((res) => {
      if (res.data) {
        resolve(1)
      } else {
        store.state.showTips = '验证码校验失败'
      }
    });
  })
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (ruleForm.verifyCode) {
        if ([6, 7].includes(props.userType)) {
          await requestVerifyUser()
          modiftUserWithPasswordAndVerify({ verifyCode: ruleForm.verifyCode, phoneNo: ruleForm.email, password: ruleForm.password }).then((res) => {
            router.push('login')
          })
        } else {
          await requestVerify()
          modifyPasswordByMailCode({ code: ruleForm.verifyCode, mail: ruleForm.email, password: ruleForm.password }).then((res) => {
            router.push('login')
          })
        }
      }
    } else {

    }
  })
}

onMounted(() => {
  if ([6, 7].includes(props.userType)) {
    ruleForm.email = localStorage.getItem('phoneNo')
  } else {
    ruleForm.email = localStorage.getItem('userName')
  }
  ruleForm.verifyCode = ''
  ruleForm.password = ''
  ruleFormRef.value.resetFields()
})

const changeState = () => {
  props.changeEvent('info')
}

</script>

<style scoped lang="less">
.my-btn {
  width: 92px !important;
  border: 4px !important;
}

:deep(.el-form-item) {
  margin-top: 20px !important;
}

.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 11;

  .code-box {
    position: relative;

    .code-text {
      position: relative;
      position: absolute;
      right: 16px;
      top: 50%;
      height: 32px;
      margin-top: -16px;
      font-weight: 600;
      font-size: 14px;
      color: #0060FF;
      cursor: pointer;
      text-align: center;

      &::before {
        content: '';
        width: 2px;
        height: 20px;
        background: rgba(216, 216, 216, 0.5);
        position: absolute;
        left: -18px;
        top: 6px;
      }
    }
  }

  .modal-content {
    width: 469px;
    height: 339px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-sizing: border-box;
    padding: 16px 24px;

    .modal-content-title {
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #333333;

      .icon-close {
        font-size: 26px;
        cursor: pointer;
        font-weight: 400;

        &:hover {
          color: #2E76FF;
        }
      }
    }

    .modal-form {


      .form-input {
        width: 432px;
        height: 36px;
        border-radius: 8px;
      }

      .form-submit {
        margin-top: 40px;
      }
    }
  }
}

.password-description {
  position: relative;
  font-size: 14px;
  font-weight: 400;
  color: #6F6F6F;
  text-align: left;
  margin: -10px 0 0 120px;
  padding-left: 19px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 3px;
    width: 13px;
    height: 13px;
    background-image: url(~@/assets/images/icon/info.png);
    background-size: 100% 100%;
  }
}
</style>