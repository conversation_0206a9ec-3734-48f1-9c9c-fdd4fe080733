<template>
  <div class="modal customer-style">
    <div class="modal-content" v-if="!showConfirm">
      <div class="modal-content-title">
        <div>{{ props.defaultValue.id ? '修改' : '新增' }}客户</div>
      </div>
      <div class="modal-form">
        <el-form
          ref="ruleFormRef"
          style="width: 295px"
          :model="ruleForm"
          :rules="rules"
          class="demo-ruleForm"
          label-position="top"
          require-asterisk-position="right">
          <div class="info-title">客户基本信息</div>
          <el-form-item label="邮箱账号" prop="contactEmail">
            <el-input
              v-if="!defaultValue.id"
              class="form-input"
              v-model="ruleForm.contactEmail"
              placeholder="请输入内容" />
            <span v-if="defaultValue.id">{{ ruleForm.contactEmail }}</span>
          </el-form-item>
          <el-form-item label="客户类型" prop="orgnizationType">
            <el-select
              v-if="!defaultValue.id"
              v-model="ruleForm.orgnizationType"
              clearable
              placeholder="请选择客户类型"
              class="select-default"
              popper-class="select-option"
              :suffix-icon="DropDown"
              style="width: 432px; height: 36px">
              <el-option
                v-for="(item, index) in orgnizationTypes"
                :key="index"
                :label="item.name"
                :value="item.value" />
            </el-select>
            <span v-if="defaultValue.id">{{ defaultValue.orgnizationType }}</span>
          </el-form-item>
          <el-form-item label="客户名称" prop="orgnizationName">
            <el-input
              class="form-input"
              v-model="ruleForm.orgnizationName"
              placeholder="请输入内容"
              :disabled="modalShow == 'reopen'" />
          </el-form-item>
          <el-form-item label="联系人" prop="contactName">
            <el-input
              class="form-input"
              v-model="ruleForm.contactName"
              placeholder="请输入联系人姓名"
              :disabled="modalShow == 'reopen'" />
          </el-form-item>
          <el-form-item label="电话" prop="contactPhone">
            <el-input
              class="form-input"
              v-model="ruleForm.contactPhone"
              placeholder="请输入联系电话"
              :disabled="modalShow == 'reopen'" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              class="form-textarea"
              v-model="ruleForm.remark"
              type="textarea"
              placeholder="请输入备注，最多200个字符"
              resize="none"
              :disabled="modalShow == 'reopen'" />
          </el-form-item>
          <div v-if="errorTips[0]" class="error-tips">{{ errorTips[0][0].message }}</div>
          <el-form-item class="form-submit form-submit1" v-if="!userId">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef, 1)" color="#2e76ff">确认</el-button>
            </div>
          </el-form-item>
        </el-form>
        <el-form
          ref="ruleFormRef2"
          :style="{ width: '398px', height: ruleForm2.spaceArService ? '825px' : '' }"
          :model="ruleForm2"
          :rules="rules2"
          class="demo-ruleForm"
          label-position="top"
          require-asterisk-position="right">
          <div class="info-title">账户权益信息</div>
          <el-form-item label="账户类型" prop="isOperate" class="is-radio-group">
            <div class="is-operate-change">
              <div
                v-for="(item, index) in operateTypes"
                :class="ruleForm2.isOperate == index ? 'active' : ''"
                :key="index"
                @click="modalShow != 'reopen' ? (ruleForm2.isOperate = index) : null">
                {{ item }}
              </div>
            </div>
            <span class="input-time" v-if="ruleForm2.isOperate">
              <span>每个项目体验时长上限</span>
              <input
                v-model="ruleForm2.experienceTime"
                type="number"
                placeholder="请输入"
                style="padding-left: 10px; outline: none; box-sizing: border-box"
                :disabled="modalShow == 'reopen'" />
              <span>分钟</span>
            </span>
          </el-form-item>
          <div class="experience-time-tips">{{ errorTips2.experienceTimeError }}</div>
          <el-form-item label="套餐类型" prop="" class="is-radio-group">
            <div class="is-operate-change">
              <div class="active">定制版</div>
            </div>
          </el-form-item>
          <el-form-item label="账号有效期" prop="endDate" class="is-radio-group">
            <span>{{ ruleForm2.startDate }}&nbsp;~&nbsp;</span>
            <el-date-picker
              style="width: 186px"
              v-model="ruleForm2.endDate"
              type="date"
              placeholder="请选择日期"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate" />
            <span class="error-tips" style="margin-left: 6px">{{ errorTips2.endDate }}</span>
          </el-form-item>
          <el-form-item label="项目数量" prop="" class="is-radio-group">
            <div class="is-operate-change">
              <div class="active">{{ packageData.sceneNum }}个</div>
            </div>
            <div class="expand-box">
              <div class="expand-title">扩展</div>
              <div>
                <div class="sub-style" @click="operateValue(-1, 'sceneNum')">
                  <img src="@/assets/images/icon/remove-icon.png" />
                </div>
                <input
                  placeholder="请输入"
                  min="0"
                  type="number"
                  v-model="ruleForm2.userBindPackage.sceneNum"
                  style="padding-left: 10px; outline: none; box-sizing: border-box"
                  :disabled="modalShow == 'reopen'" />
                <div class="add-style" @click="operateValue(1, 'sceneNum')">
                  <img src="@/assets/images/icon/add-icon.png" />
                </div>
                <span>个</span>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="素材总容量" prop="" class="is-radio-group">
            <div class="is-operate-change">
              <div class="active">{{ packageData.materialUploadSize }}GB</div>
            </div>
            <div class="expand-box">
              <div class="expand-title">扩展</div>
              <div>
                <div class="sub-style" @click="operateValue(-1, 'materialUploadSize')">
                  <img src="@/assets/images/icon/remove-icon.png" />
                </div>
                <input
                  placeholder="请输入"
                  type="number"
                  v-model="ruleForm2.userBindPackage.materialUploadSize"
                  style="padding-left: 10px; outline: none; box-sizing: border-box"
                  :disabled="modalShow == 'reopen'" />
                <div class="add-style" @click="operateValue(1, 'materialUploadSize')">
                  <img src="@/assets/images/icon/add-icon.png" />
                </div>
                <span>GB</span>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="单个素材上限" prop="" class="is-radio-group">
            <div class="is-operate-change">
              <div class="active">{{ packageData.singleUploadSize }}MB</div>
            </div>
            <div class="expand-box">
              <div class="expand-title">扩展</div>
              <div>
                <div class="sub-style" @click="operateValue(-1, 'singleUploadSize')">
                  <img src="@/assets/images/icon/remove-icon.png" />
                </div>
                <input
                  placeholder="请输入"
                  type="number"
                  v-model="ruleForm2.userBindPackage.singleUploadSize"
                  style="padding-left: 10px; outline: none; box-sizing: border-box"
                  :disabled="modalShow == 'reopen'" />
                <div class="add-style" @click="operateValue(1, 'singleUploadSize')">
                  <img src="@/assets/images/icon/add-icon.png" />
                </div>
                <span>MB</span>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="项目分享上限" prop="" class="is-radio-group">
            <div class="is-operate-change">
              <div class="active">每天{{ packageData.shareVisitCount }}次访问</div>
            </div>
            <div class="expand-box" style="left: 172px">
              <div class="expand-title">扩展</div>
              <div>
                <div class="sub-style" @click="operateValue(-1, 'shareExtensionVisitCount')">
                  <img src="@/assets/images/icon/remove-icon.png" />
                </div>
                <input
                  placeholder="请输入"
                  type="number"
                  v-model="ruleForm2.userBindPackage.shareExtensionVisitCount"
                  style="padding-left: 10px; outline: none; box-sizing: border-box"
                  :disabled="modalShow == 'reopen'" />
                <div class="add-style" @click="operateValue(1, 'shareExtensionVisitCount')">
                  <img src="@/assets/images/icon/add-icon.png" />
                </div>
                <span>次</span>
              </div>
            </div>
          </el-form-item>
          <div class="ar-service">
            <span class="service-title">空间识别AR服务</span>
            <el-switch
              style="--el-switch-on-color: #2e76ff; --el-switch-off-color: #c2d4fe"
              v-model="ruleForm2.spaceArService"
              :disabled="
                (ruleForm2.spaceArService &&
                  defaultValue &&
                  defaultValue.userBindPackageDto?.spaceAr) ||
                modalShow == 'reopen'
              " />
            <div class="help-tips">
              <div class="help-tips-box">
                <div class="title">支持项目类型</div>
                <div>
                  <span>所有端空间AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
                <div>
                  <span>小程序平面AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
                <div>
                  <span>小程序图像AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
                <div>
                  <span>小程序人脸AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
              </div>
            </div>
            <div class="space-box" v-if="ruleForm2.spaceArService">
              <el-form-item label="空间定位容量" prop="" class="is-radio-group">
                <div class="is-operate-change">
                  <div class="active">
                    {{ packageData.spacePicTotalNum }}张/{{
                      (packageData.spacePicTotalNum / 166) * 100
                    }}平
                  </div>
                </div>
                <div class="expand-box" style="left: 150px">
                  <div class="expand-title">扩展</div>
                  <div>
                    <div class="sub-style" @click="operateValue(-166, 'spacePicTotalNum')">
                      <img src="@/assets/images/icon/remove-icon.png" />
                    </div>
                    <input
                      placeholder="请输入"
                      type="number"
                      v-model="ruleForm2.userBindPackage.spacePicTotalNum"
                      style="padding-left: 10px" />
                    <div class="add-style" @click="operateValue(166, 'spacePicTotalNum')">
                      <img src="@/assets/images/icon/add-icon.png" />
                    </div>
                    <span>张</span>
                    <div class="space-model"></div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="并发数量QPS" prop="" class="is-radio-group">
                <div class="is-operate-change">
                  <div class="active">{{ packageData.vpsQps }}个</div>
                </div>
                <div class="expand-box" style="left: 150px">
                  <div class="expand-title">扩展</div>
                  <div>
                    <div class="sub-style" @click="operateValue(-5, 'vpsQps')">
                      <img src="@/assets/images/icon/remove-icon.png" />
                    </div>
                    <input
                      placeholder="请输入"
                      type="number"
                      v-model="ruleForm2.userBindPackage.vpsQps"
                      style="padding-left: 10px" />
                    <div class="add-style" @click="operateValue(5, 'vpsQps')">
                      <img src="@/assets/images/icon/add-icon.png" />
                    </div>
                    <span>个</span>
                    <div class="space-model"></div>
                  </div>
                </div>
                <div class="qps-menu" v-if="packageData.extends?.length">
                  <div
                    class="qps-menu-box"
                    :style="{ top: packageData.extends?.length > 1 ? '-94px' : '-30px' }">
                    <div v-for="(item, index) in packageData.extends" :key="index">
                      <div>{{ getDayTime(item.createTime) }}扩展</div>
                      <div>
                        <span>{{ item.vpsQps }}个/{{ item.vpsQps / 5 }}组</span>
                        <span style="font-size: 12px; font-weight: 400">
                          {{ getDayTime(item.qpsUseStartTime) }} ~
                          {{ getDayTime(item.qpsUseEndTime) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item
                label=" "
                prop=""
                class="is-radio-group"
                style="height: 52px; margin-top: 20px">
                <div class="expand-box" style="left: 150px">
                  <div class="expand-title">有效期</div>
                  <div>
                    <span>{{ ruleForm2.userBindPackage.qpsUseStartTime }}&nbsp;~&nbsp;</span>
                    <el-date-picker
                      style="width: 120px"
                      v-model="ruleForm2.userBindPackage.qpsUseEndTime"
                      type="date"
                      placeholder="请选择日期"
                      value-format="YYYY-MM-DD"
                      :disabled="modalShow == 'reopen'"
                      :disabled-date="disabledDate" />
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="ar-service">
            <span class="service-title">单点识别AR服务</span>
            <el-switch
              style="--el-switch-on-color: #2e76ff; --el-switch-off-color: #c2d4fe"
              v-model="ruleForm2.singleIdentifyArService"
              :disabled="
                (ruleForm2.singleIdentifyArService &&
                  defaultValue &&
                  defaultValue.userBindPackageDto?.singleIdentify) ||
                modalShow == 'reopen'
              " />
            <div class="help-tips">
              <div class="help-tips-box" style="top: -150px">
                <div class="title">支持项目类型</div>
                <div>
                  <span>小程序平面AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
                <div>
                  <span>小程序图像AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
                <div>
                  <span>小程序单场景AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
                <div>
                  <span>小程序人脸AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
                <div>
                  <span>小程序身体AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
                <div>
                  <span>小程序手势AR</span>
                  <img src="@/assets/images/icon/check-mark.png" />
                </div>
              </div>
            </div>
            <span class="error-tips" style="margin-left: 6px">
              {{ errorTips2.identifyArService }}
            </span>
          </div>
          <el-form-item class="form-submit" style="margin-bottom: 0" v-if="userId">
            <div v-if="defaultValue.id" class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef2, 2)" color="#2e76ff">确认</el-button>
            </div>
          </el-form-item>
          <div class="form-disable" v-if="!userId"></div>
        </el-form>
      </div>
    </div>
    <div
      class="confirm-again"
      v-if="showConfirm"
      :class="
        ruleForm2.spaceArService
          ? ruleForm2.singleIdentifyArService
            ? 'both-style'
            : 'only-space'
          : 'only-single'
      ">
      <div class="modal-content-title">
        <div>确认更新内容</div>
        <div class="content-table">
          <div class="title">
            <div>服务项</div>
            <div>当前服务</div>
            <div>新增服务</div>
            <div>更新后服务</div>
          </div>
          <div class="item-list">
            <div>
              <div>项目数量</div>
              <div>{{ packageData.sceneNum }}个</div>
              <div>{{ ruleForm2.userBindPackage.sceneNum }}个</div>
              <div>{{ packageData.sceneNum + ruleForm2.userBindPackage.sceneNum }}个</div>
            </div>
            <div>
              <div>素材总容量</div>
              <div>{{ packageData.materialUploadSize }}GB</div>
              <div>{{ ruleForm2.userBindPackage.materialUploadSize }}GB</div>
              <div>
                {{
                  packageData.materialUploadSize + ruleForm2.userBindPackage.materialUploadSize
                }}GB
              </div>
            </div>
            <div>
              <div>单个素材上限</div>
              <div>{{ packageData.singleUploadSize }}MB</div>
              <div>{{ ruleForm2.userBindPackage.singleUploadSize }}MB</div>
              <div>
                {{ packageData.singleUploadSize + ruleForm2.userBindPackage.singleUploadSize }}MB
              </div>
            </div>
            <div>
              <div>项目分享次数</div>
              <div>{{ packageData.shareVisitCount }}次</div>
              <div>{{ ruleForm2.userBindPackage.shareExtensionVisitCount }}次</div>
              <div>
                {{
                  packageData.shareVisitCount + ruleForm2.userBindPackage.shareExtensionVisitCount
                }}次
              </div>
            </div>
            <div
              v-if="ruleForm2.spaceArService"
              style="
                justify-content: flex-start;
                padding-left: 8px;
                color: #1e1e1e;
                font-weight: bold;
              ">
              空间识别AR服务
            </div>
            <div v-if="ruleForm2.spaceArService">
              <div>空间容量</div>
              <div>{{ packageData.spacePicTotalNum }}张</div>
              <div>{{ ruleForm2.userBindPackage.spacePicTotalNum }}张</div>
              <div>
                {{ packageData.spacePicTotalNum + ruleForm2.userBindPackage.spacePicTotalNum }}张
              </div>
            </div>
            <div v-if="ruleForm2.spaceArService">
              <div>并发数量容量</div>
              <div>{{ packageData.vpsQps }}个</div>
              <div>{{ ruleForm2.userBindPackage.vpsQps }}个</div>
              <div>{{ packageData.vpsQps + ruleForm2.userBindPackage.vpsQps }}个</div>
            </div>
            <div style="height: 76px" v-if="ruleForm2.spaceArService">
              <div>并发数量有效期</div>
              <div>-</div>
              <div>
                {{
                  ruleForm2.userBindPackage.qpsUseEndTime
                    ? `${ruleForm2.userBindPackage.qpsUseStartTime} ~ ${ruleForm2.userBindPackage.qpsUseEndTime}`
                    : '-'
                }}
              </div>
              <div>-</div>
            </div>
            <div
              v-if="ruleForm2.singleIdentifyArService"
              style="
                justify-content: flex-start;
                padding-left: 8px;
                color: #1e1e1e;
                font-weight: bold;
              ">
              单点识别AR服务
            </div>
          </div>
        </div>
        <div class="submit-btn">
          <div class="btn-default el-size3">
            <el-button @click="showConfirm = false">取消</el-button>
          </div>
          <div class="btn-primary el-size3">
            <el-button @click="updatePackage" color="#2e76ff">确认</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules, type UploadUserFile } from 'element-plus';
import DropDown from '@/components/DropDown.vue';
import { orgnizationTypes } from '@/config';
import {
  createOrgnizationV2,
  addUserBindPackage,
  getUserBindPackage,
  addUserBindPackageExtension,
  updateOrgnizaiton,
} from '@/api';
import { getDayTime } from '@/utils';

const emits = defineEmits(['updateValue']);
const props = defineProps({
  handleShow: {
    default: null,
    type: Function,
  },
  defaultValue: {
    default: null,
    type: Object,
  },
  modalShow: {
    default: 'add',
    type: String,
  },
});

const errorTips: any = ref([]);
const errorTips2: any = ref({});
const operateTypes = ['非运营', '运营'];
const userId = ref(null);
const showConfirm = ref(false);
const orgnizationTypeMap: any = {
  文娱: 1,
  工业: 2,
  教育: 3,
  其他: 4,
};

const packageData: any = ref({
  sceneNum: 50,
  materialUploadSize: 30,
  singleUploadSize: 100,
  shareVisitCount: 1000,
  spacePicTotalNum: 1660,
  vpsQps: 5,
  extends: [],
});

interface RuleForm {
  orgnizationName: string;
  orgnizationType: string;
  contactEmail: string;
  contactName: string;
  contactPhone: string;
  remark: string;
}

interface RuleForm2 {
  isOperate: number;
  startDate: string;
  endDate: string;
  experienceTime: string;
  userBindPackage: Object;
  spaceArService: number;
  singleIdentifyArService: number;
}

const ruleFormRef = ref<FormInstance>();
const ruleFormRef2 = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  orgnizationName: '',
  orgnizationType: '',
  contactEmail: '',
  contactName: '',
  contactPhone: '',
  remark: '',
});

const ruleForm2: any = reactive<RuleForm2>({
  isOperate: 0,
  startDate: getDayTime(new Date()),
  endDate: '',
  experienceTime: '',
  userBindPackage: {
    sceneNum: 0,
    materialUploadSize: 0,
    singleUploadSize: 0,
    shareExtensionVisitCount: 0,
    spacePicTotalNum: 0,
    vpsQps: 0,
    qpsUseStartTime: getDayTime(new Date()),
    qpsUseEndTime: null,
  },
  spaceArService: 0,
  singleIdentifyArService: 0,
});

const changeState = () => {
  props.handleShow('');
};

// 自定义校验函数
const validateEmail = (_rule: any, value: string, callback: (error?: Error) => void) => {
  if (!value) {
    callback(new Error('请输入邮箱账号'));
  } else if (value.length > 40) {
    callback(new Error('邮箱账号最多40个字符'));
  } else {
    callback();
  }
};

const validateOrganizationName = (_rule: any, value: string, callback: (error?: Error) => void) => {
  if (!value) {
    callback(new Error('请输入客户名称'));
  } else if (value.length > 20) {
    callback(new Error('客户名称最多20个字符'));
  } else {
    callback();
  }
};

const validateContactName = (_rule: any, value: string, callback: (error?: Error) => void) => {
  if (!value) {
    callback(new Error('请输入联系人'));
  } else if (value.length > 20) {
    callback(new Error('联系人最多20个字符'));
  } else {
    callback();
  }
};

const validatePhone = (_rule: any, value: string, callback: (error?: Error) => void) => {
  if (!value) {
    callback(new Error('请输入联系电话'));
  } else {
    // 检查是否包含汉字
    const chineseRegex = /[\u4e00-\u9fa5]/;
    if (chineseRegex.test(value)) {
      callback(new Error('电话号码不能包含汉字'));
    } else {
      callback();
    }
  }
};

const validateRemark = (_rule: any, value: string, callback: (error?: Error) => void) => {
  if (value && value.length > 200) {
    callback(new Error('备注最多200个字符'));
  } else {
    callback();
  }
};

const rules = reactive<FormRules<RuleForm>>({
  contactEmail: [{ validator: validateEmail, trigger: 'blur' }],
  orgnizationType: [{ required: true, message: '请选择客户类型', trigger: 'blur' }],
  orgnizationName: [{ validator: validateOrganizationName, trigger: 'blur' }],
  contactName: [{ validator: validateContactName, trigger: 'blur' }],
  contactPhone: [{ validator: validatePhone, trigger: 'blur' }],
  remark: [{ validator: validateRemark, trigger: 'blur' }],
});

const rules2 = reactive<FormRules<RuleForm2>>({
  isOperate: [{ required: true, message: '请选择账号类型', trigger: 'blur' }],
  endDate: [{ required: true, message: '请设置账号有效期', trigger: 'blur' }],
});

const disabledDate = (time: Date) => {
  return time.getTime() < Date.now();
};

const submitForm = async (formEl: FormInstance | undefined, type: number) => {
  if (!formEl) return;
  await formEl.validate((valid: boolean, fields: any) => {
    if (type == 2) {
      if (!ruleForm2.spaceArService && !ruleForm2.singleIdentifyArService) {
        errorTips2.value['identifyArService'] = '至少选择一个AR服务类型';
      } else {
        delete errorTips2.value.identifyArService;
      }
      if (ruleForm2.isOperate && !ruleForm2.experienceTime) {
        errorTips2.value['experienceTimeError'] = '体验时间不能为空';
      } else {
        delete errorTips2.value.experienceTimeError;
      }

      if (ruleForm2.userBindPackage.vpsQps && !ruleForm2.userBindPackage.qpsUseEndTime) {
        errorTips2.value['identifyArService'] = 'QPS到期时间不能为空';
      }
    }
    if (valid) {
      if (type == 1) {
        createOrgnizationV2({ ...ruleForm }).then((res: any) => {
          userId.value = res.data;
        });
      }
      if (type == 2) {
        delete errorTips2.value.endDate;
        if (!showConfirm.value && Object.keys(errorTips2.value).length == 0) {
          showConfirm.value = true;
          return;
        }
      }
    } else {
      if (type == 2) {
        Object.keys(fields).forEach((key) => {
          errorTips2.value[key] = fields[key][0].message;
        });
      } else {
        errorTips.value = Object.values(fields);
      }
    }
  });
};

const updatePackage = () => {
  const parameData = JSON.parse(JSON.stringify(ruleForm2));
  parameData.userBindPackage.sceneNum = Number(parameData.userBindPackage.sceneNum);
  parameData.userBindPackage.materialUploadSize =
    Number(parameData.userBindPackage.materialUploadSize) * 1024;
  parameData.userBindPackage.singleUploadSize = Number(parameData.userBindPackage.singleUploadSize);
  parameData.userBindPackage.shareExtensionVisitCount = Number(
    parameData.userBindPackage.shareExtensionVisitCount
  );
  parameData.userBindPackage.spacePicTotalNum = Number(parameData.userBindPackage.spacePicTotalNum);
  parameData.userBindPackage.vpsQps = Number(parameData.userBindPackage.vpsQps);
  if (parameData.spaceArService && !props.defaultValue?.userBindPackageDto?.spaceAr) {
    parameData.userBindPackage.functionAddDescription = '新增空间识别AR服务';
  }
  if (
    parameData.singleIdentifyArService &&
    !props.defaultValue?.userBindPackageDto?.singleIdentify
  ) {
    parameData.userBindPackage.functionAddDescription = '新增单点识别AR服务';
  }
  if (!parameData.userBindPackage.qpsUseEndTime) {
    parameData.userBindPackage.qpsUseStartTime = null;
  }
  parameData.userBindPackageExtensionDtoList = [parameData.userBindPackage];
  parameData.spaceArService = parameData.spaceArService ? 1 : 0;
  parameData.singleIdentifyArService = parameData.singleIdentifyArService ? 1 : 0;
  delete parameData.userBindPackage;
  if (props.defaultValue.id) {
    addUserBindPackageExtension({ ...parameData, userId: userId.value }).then((res: any) => {
      if (res.code == 200) {
        props.handleShow('', true);
        errorTips.value = [];
      }
    });
    updateOrgnizaiton({ ...ruleForm, id: props.defaultValue.id }).then((res: any) => {
      // props.handleShow('', true)
    });
  } else {
    addUserBindPackage({ ...parameData, userId: userId.value }).then((res: any) => {
      if (res.code == 200) {
        props.handleShow('', true);
        errorTips.value = [];
      }
    });
  }
};

const operateValue = (sign: any, key: string) => {
  if (props.modalShow == 'reopen') return;
  let curValue = +ruleForm2.userBindPackage[key] + sign;
  if (curValue < 0) {
    curValue = 0;
  }
  ruleForm2.userBindPackage[key] = curValue;
};

onMounted(() => {
  // 编辑页初始数据
  if (props.defaultValue.id) {
    userId.value = props.defaultValue.userBindPackageDto.userId;
    ruleForm.orgnizationName = props.defaultValue.orgnizationName;
    ruleForm.orgnizationType = orgnizationTypeMap[props.defaultValue.orgnizationType];
    ruleForm.contactEmail = props.defaultValue.mail;
    ruleForm.contactName = props.defaultValue.contactName;
    ruleForm.contactPhone = props.defaultValue.contactPhone;
    ruleForm.remark = props.defaultValue.remark;

    ruleForm2.isOperate = props.defaultValue.userBindPackageDto.isOperate ? 1 : 0;
    ruleForm2.experienceTime = props.defaultValue.userBindPackageDto.experienceTime;
    ruleForm2.spaceArService = !!props.defaultValue.userBindPackageDto.spaceAr;
    ruleForm2.singleIdentifyArService = !!props.defaultValue.userBindPackageDto.singleIdentify;

    getUserBindPackage({ userId: userId.value }).then((res) => {
      res.data.secondData.forEach((e: any) => {
        packageData.value.sceneNum += e.sceneNum;
        packageData.value.materialUploadSize += Math.round(e.materialUploadSize / 1024);
        packageData.value.singleUploadSize += e.singleUploadSize;
        packageData.value.shareVisitCount += e.shareExtensionVisitCount;
        packageData.value.spacePicTotalNum += e.spacePicTotalNum;
        packageData.value.vpsQps += e.vpsQps;
      });
      ruleForm2.startDate = getDayTime(res.data.firstData.packageStartTime);
      ruleForm2.endDate = getDayTime(res.data.firstData.packageEndTime);
      packageData.value.extends = res.data.secondData.filter((e: any) => e.vpsQps > 0);
    });
    console.log(props.defaultValue, ruleForm2);
  }
});

watch(
  () => ruleForm2.userBindPackage.qpsUseEndTime,
  (newState) => {
    if (newState) {
      if (!ruleForm2.userBindPackage.vpsQps) {
        ruleForm2.userBindPackage.vpsQps = 5;
      }
    } else {
      ruleForm2.userBindPackage.vpsQps = 0;
    }
  }
);

watch(
  () => ruleForm2.singleIdentifyArService,
  (newState) => {
    if (newState && errorTips2.value['identifyArService']) {
      delete errorTips2.value['identifyArService'];
    }
  }
);

watch(
  () => ruleForm2.spaceArService,
  (newState) => {
    if (newState && errorTips2.value['identifyArService']) {
      delete errorTips2.value['identifyArService'];
    }
    if (!newState) {
      ruleForm2.userBindPackage.spacePicTotalNum = 0;
      ruleForm2.userBindPackage.vpsQps = 0;
      ruleForm2.userBindPackage.qpsUseEndTime = null;
    }
  }
);
</script>

<style scoped lang="less">
/* 取消上下箭头 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

input[type='number'] {
  -moz-appearance: textfield;
}
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .confirm-again {
    width: 590px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    padding: 24px;
    box-sizing: border-box;

    &.both-style {
      height: 549px;
    }

    &.only-space {
      height: 512px;
    }

    &.only-single {
      height: 362px;
    }

    .submit-btn {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 24px;
    }
    .content-table {
      border: 1px solid #e6edf7;
      border-radius: 4px;
      overflow: hidden;
      width: 542px;
      margin-top: 12px;

      .title {
        height: 32px;
        background: rgba(230, 237, 247, 0.3);
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-weight: 400;
        font-size: 12px;
        color: #797979;

        & > div {
          width: 23%;
          padding-left: 8px;
        }

        & > div:first-child {
          width: 31%;
        }
      }

      .item-list {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        font-weight: 400;
        font-size: 14px;
        color: #1e1e1e;

        & > div {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          height: 37px;
          box-sizing: border-box;
          border-top: 1px solid #e6edf7;

          & > div {
            width: 23%;
            padding-left: 8px;
          }

          & > div:first-child {
            width: 31%;
          }
        }
      }
    }
  }

  .modal-content-title {
    font-weight: bold;
    font-size: 18px;
    color: #1e1e1e;
    text-align: left;
    height: 27px;
    line-height: 27px;
    margin-bottom: 24px;
  }

  .modal-content {
    width: 773px;
    // height: 710px;
    max-height: 90%;
    background: #fff;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    border: 1px solid #edeff2;
    position: relative;
    padding: 24px 24px 24px 24px;
    box-sizing: border-box;
    overflow: hidden;
    overflow-y: auto;

    .modal-form {
      width: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      text-align: left;

      .form-disable {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 546px;
        background-color: #fff;
        background-image: url(~@/assets/images/form-disable.png);
        background-size: 100% 100%;
        z-index: 1;
      }

      .experience-time-tips {
        margin-top: -10px;
        width: 100%;
        text-align: right;
        padding-right: 10px;
        box-sizing: border-box;
        font-weight: 400;
        font-size: 12px;
        color: #ba1e1e;
      }

      .ar-service {
        font-weight: 500;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);

        .service-title {
          margin-right: 10px;
        }

        .space-box {
          width: 394px;
          height: 204px;
          background: #efefef;
          border-radius: 4px 4px 4px 4px;
          padding: 10px;
          box-sizing: border-box;
        }

        .help-tips {
          display: inline-block;
          width: 16px;
          height: 16px;
          background-image: url(~@/assets/images/icon/help.png);
          background-size: 100% 100%;
          position: relative;
          top: 5px;
          margin-left: 6px;

          .help-tips-box {
            position: absolute;
            left: 26px;
            top: -17px;
            width: 154px;
            font-weight: 400;
            font-size: 12px;
            color: #ffffff;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 4px 4px 4px 4px;
            z-index: 10;
            padding: 12px;
            box-sizing: border-box;
            display: none;

            .title {
              font-weight: bold;
              font-size: 14px;
              color: #ffffff;
              margin-top: 0;
            }

            & > div {
              margin-top: 8px;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }

            img {
              width: 16px;
              height: 16px;
            }
          }

          &:hover {
            background-image: url(~@/assets/images/icon/helpA.png);
            background-size: 100% 100%;

            .help-tips-box {
              display: block;
            }

            &::after {
              display: inline-block;
            }
          }

          &::after {
            content: '';
            position: absolute;
            left: 14px;
            top: 2px;
            border: 6px solid transparent;
            border-right-color: rgba(0, 0, 0, 0.7);
            display: none;
          }
        }

        .qps-menu {
          width: 16px;
          height: 16px;
          display: inline-block;
          background-image: url(~@/assets/images/icon/menu-icon.png);
          background-size: 100% 100%;
          position: relative;
          top: -26px;
          left: 10px;
          margin: 0 5px;
          color: #fff;

          .qps-menu-box {
            position: absolute;
            left: 26px;
            top: -94px;
            width: 240px;
            max-height: 193px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 4px 4px 4px 4px;
            padding: 12px;
            padding-right: 0;
            box-sizing: border-box;
            overflow: hidden;
            overflow-y: auto;
            display: none;
            z-index: 10;

            & > div {
              margin-bottom: 8px;

              & > div {
                display: flex;
                justify-content: flex-start;
                align-items: center;

                & > span:first-child {
                  margin-right: 10px;
                }
              }

              & > div:first-child {
                height: 21px;
                line-height: 21px;
                font-weight: bold;
                padding-left: 4px;
                border-left: 2px solid #2e76ff;
                margin-bottom: 8px;
              }

              span {
                display: inline-block;
                vertical-align: middle;
              }
            }

            & > div:last-child {
              margin-bottom: 0;
            }
          }

          &:hover {
            background-image: url(~@/assets/images/icon/menu-iconA.png);
            background-size: 100% 100%;

            .qps-menu-box {
              display: block;
            }

            &::after {
              display: inline-block;
            }
          }

          &::after {
            content: '';
            position: absolute;
            left: 14px;
            top: 2px;
            border: 6px solid transparent;
            border-right-color: rgba(0, 0, 0, 0.7);
            display: none;
          }
        }
      }

      .expand-box {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        position: absolute;
        left: 124px;
        top: -18px;
        font-weight: 400;
        font-size: 12px;
        color: #797979;

        .expand-title {
          margin-bottom: 2px;
        }

        & > div {
          height: auto;
          line-height: 1.5;
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .space-model {
            width: 106px;
            height: 40px;
            position: absolute;
            left: 38px;
            top: 19px;
          }

          .sub-style,
          .add-style {
            width: 32px;
            height: 32px;
            background: #ffffff;
            border-radius: 3px 3px 3px 3px;
            border: 1px solid #dcdcdc;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            img {
              width: 16px;
              height: 16px;
            }
          }

          input {
            width: 88px;
            height: 32px;
            background: #ffffff;
            border-radius: 3px 3px 3px 3px;
            border: 1px solid #dcdcdc;
            margin: 0 4px 0 8px;
          }

          span {
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9);
            margin-left: 4px;
          }
        }
      }

      .error-tips {
        font-weight: 400;
        font-size: 12px;
        color: #ba1e1e;
      }

      & > form {
        position: relative;
        height: 100%;
        height: 632px;
      }

      .info-title {
        text-align: left;
        font-weight: bold;
        font-size: 14px;
        color: #000000;
        margin-bottom: 12px;
      }

      .is-operate-change {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        & > div {
          position: relative;
          font-weight: 400;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.9);
          padding-left: 24px;
          margin-right: 16px;
          cursor: pointer;
        }

        & > div::before {
          content: '';
          width: 16px;
          height: 16px;
          background-image: url(~@/assets/images/icon/radio.png);
          background-size: 100% 100%;
          position: absolute;
          left: 0;
          top: 8px;
        }

        & > div.active::before {
          background-image: url(~@/assets/images/icon/radioA.png);
          background-size: 100% 100%;
        }
      }

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-textarea {
        width: 432px;
        height: 91px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-submit {
        width: 100%;
        margin-top: 82px;
        position: absolute;
        bottom: 2px;

        ::v-deep(.el-item__content) {
          justify-content: flex-start;
        }
      }

      ::v-deep(.el-input__wrapper .el-input__inner) {
        font-weight: 400;
        font-size: 14px !important;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }
}

::v-deep(.el-form-item--label-top) {
  text-align: left;
  position: relative;
}

.input-time {
  position: absolute;
  left: 145px;
  top: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  input {
    width: 63px;
    height: 32px;
    background: #ffffff;
    border-radius: 3px 3px 3px 3px;
    border: 1px solid #dcdcdc;
    margin: 0 4px 0 8px;
  }
}

::v-deep(.is-radio-group .el-form-item__content) {
  height: 32px;
  // overflow: hidden;
  text-align: left;
  margin-top: -10px;

  .el-radio__input {
    position: relative;
    top: 8px;
  }

  .el-radio {
    margin-right: 16px;
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;
}
</style>
