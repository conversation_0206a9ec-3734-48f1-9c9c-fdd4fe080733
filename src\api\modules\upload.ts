import request from '../request';
import { createUuid } from '@/utils';

// 上传模板视频
export function uploadTemplateVideo(data: any) {
  return request({
    url: '/scene/uploadTemplateVideo',
    method: 'post',
    data,
  });
}

// 上传模板图片
export function uploadTemplatePic(data: any) {
  return request({
    url: '/scene/uploadTemplatePic',
    method: 'post',
    data,
  });
}

// 生成Polycam
export function genPolycam(data: any) {
  return request({
    url: `/space/genPolycam?spaceName=${data.spaceName}&uuid=${data.uuid}`,
    method: 'post',
    data: data.formData,
  });
}

// 生成B2G
export function genB2g(data: any) {
  return request({
    url: `/space/genB2g?spaceName=${data.spaceName}&uuid=${data.uuid}`,
    method: 'post',
    data: data.formData,
  });
}

// 生成57
export function gene57(data: any) {
  return request({
    url: `/space/gene57?spaceName=${data.spaceName}&uuid=${data.uuid}`,
    method: 'post',
    data: data.formData,
  });
}

// 生成奇遇Qiyu
export function genQiyu(data: any) {
  return request({
    url: `/space/genQiyu?spaceName=${data.spaceName}`,
    method: 'post',
    data: data.formData,
  });
}
