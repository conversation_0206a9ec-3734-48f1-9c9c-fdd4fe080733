import {
  BufferGeometry,
  Mesh,
  Intersection,
  MeshBasicMaterial,
  Raycaster,
  RepeatWrapping,
  TextureLoader,
  Vector2,
  Vector3,
  Matrix4,
  Quaternion,
  Shape,
  ShapeGeometry,
  Plane,
  CatmullRomCurve3,
  LineBasicMaterial,
  Line,
  Group,
} from "three";
import {
  screenToWorldVector,
  point2DByPoint3D,
  areaOfTriangle,
  cacCrossPointLineWithLine,
  point3DByPoint2D,
  getOffsetPoint,
} from "@/utils";
import { CLine } from "./CLine";

export type CPolygonAttrs = {
  vertexs?: Array<Vector3>;
  normal?: Vector3;
  color?: number;
  uuid?: string;
  transparent?: boolean;
  url?: string;
  depthTest?: boolean;
  constraint?: any; // 约束缓存
  marks?: any; // 标注缓存
  id?: number;
  type?: string;
  isWall?: boolean;
};
export class CPolygon extends Mesh {
  geometry: ShapeGeometry;
  material: MeshBasicMaterial;
  name: string;
  userData: { [k: string]: any };
  constructor(attrs?: CPolygonAttrs) {
    super();

    this.name = "cpolygon";
    this.geometry = new ShapeGeometry(new Shape());
    this.material = new MeshBasicMaterial({ side: 2 });
    this.userData = {};
    this.initialize(attrs);
  }
  getType() {
    // 获取类型
    return "Point";
  }

  initData(attrs?: CPolygonAttrs) {
    // 约束/标注/uuid/索引缓存
    this.userData = {
      constraint: {
        links: [],
      },
      marks: [],
      uuid: "",
      index: -1,
      points: [],
      isWall: attrs ? attrs.isWall : false,
    };
    if (attrs && attrs.constraint !== undefined)
      this.userData.constraint = attrs.constraint; // constraint data cache
    if (attrs && attrs.marks !== undefined) this.userData.marks = attrs.marks; // marks data cache
    const normal =
      attrs && attrs.normal !== undefined
        ? attrs.normal.normalize()
        : new Vector3(0, 1, 0); // 当前planeGeometry传入的法向量
    const matrix = new Matrix4().makeRotationFromQuaternion(
      new Quaternion().setFromUnitVectors(
        new Vector3(0, 0, 1), // 初始向量
        normal // 面所在的法向量
      )
    );
    this.userData.matrix = matrix.clone();
    const line = new CLine({
      vertexs: attrs?.vertexs,
      color: 0x00ffff,
    });
    line.userData.parent = "cpolygon";
    this.add(line);
  }

  initUI(attrs?: CPolygonAttrs) {
    // 默认坐标原点
    if (attrs && attrs.vertexs?.length) {
      this.setVertexs(attrs.vertexs);
    }

    attrs && attrs.transparent !== undefined
      ? this.setTransparent(attrs.transparent)
      : this.setTransparent(true); // 透明

    if (attrs && attrs.url !== undefined) this.loadTexture(attrs.url); // 默认纹理
    if (attrs && attrs.color !== undefined) this.setColor(attrs.color); // 颜色

    // - 所有情况下点都是 depthtest 为false
    this.setDepthTest(false);
  }

  initialize(attrs?: CPolygonAttrs) {
    this.initData(attrs);
    this.initUI(attrs);
  }

  // @override 射线拾取点
  raycast(raycaster: Raycaster, intersects: Intersection[]) {
    const ori = (raycaster as any).ray.origin;
    // const oriCurrent = ori.clone().project((window as any).camera)
    // const v1 =  new Vector2((0.5 + oriCurrent.x / 2) * window.innerWidth, (0.5 - oriCurrent.y / 2) * window.innerHeight)
    // const position = new Vector3(this.geometry.attributes.position.array[0], this.geometry.attributes.position.array[1], this.geometry.attributes.position.array[2])
    // const current = position.clone().project((window as any).camera)
    // const v2 = new Vector2((0.5 + current.x / 2) * window.innerWidth, (0.5 - current.y / 2) * window.innerHeight)
    // if (v1.distanceToSquared(v2) <= ADSORPTION_RADIUS * ADSORPTION_RADIUS) {
    //     intersects.push({
    //         distance: current.clone().distanceTo((window as any).camera.position),
    //         object: this,
    //         point: position
    //     } as any)
    // }
  }

  loadTexture(url: string) {
    const t = new TextureLoader().load(url);
    t.wrapS = t.wrapT = RepeatWrapping;
    this.material.map = t;
    return this;
  }

  //
  setDepthTest(depthTest: boolean) {
    this.material.depthTest = depthTest;
    return this;
  }

  setTransparent(transparent: boolean) {
    this.material.transparent = transparent;
    this.material.opacity = 0.2;
    return this;
  }

  // 通过线所在的点生成图形
  setVertexs(vertexs: Array<any>, curveIndexArr?: any) {
    const vertexsFor2D: any = [];
    curveIndexArr = curveIndexArr?.filter(
      (arr: any) => arr.slice(-1)[0] && arr.slice(-1)[0] < vertexs.length
    );
    if (
      this.userData.points.length &&
      vertexs[0].equals(this.userData.points[0]) &&
      !this.userData.points[this.userData.points.length - 1].equals(
        vertexs[vertexs.length - 1]
      )
    ) {
      vertexs[0] = vertexs[vertexs.length - 1].clone();
    } else if (
      this.userData.points.length &&
      this.userData.points[this.userData.points.length - 1].equals(
        vertexs[vertexs.length - 1]
      ) &&
      !vertexs[0].equals(this.userData.points[0])
    ) {
      vertexs[vertexs.length - 1] = vertexs[0].clone();
    }

    // 转换成计算另外一半顶点所需2D点位
    for (let i = 0; i < vertexs.length; i++) {
      const p = point2DByPoint3D(vertexs[i], this.userData.matrix);
      vertexsFor2D.push(p);
    }
    const shapes = new Shape();

    // ====== 画普通多边形 =========
    if (!this.userData.isWall || !curveIndexArr) {
      vertexsFor2D.forEach((point: Vector2, index: number) => {
        if (index == 0) {
          shapes.moveTo(point.x, point.y);
        } else {
          shapes.lineTo(point.x, point.y);
        }
      });
      this.geometry = new ShapeGeometry(shapes);
      this.geometry.lookAt((window as any).camera.up);
      (this.children[0] as any).setVertexs([...vertexs, vertexs[0]]);
      this.userData.points = [...vertexs];
      return;
    }

    // =========== 画墙计算点位 ==========
    const currentPoints: Array<Vector3> = [];
    const lineGroup = new Group();
    const normal = new Vector3(0, 1, 0);
    for (let i = 0; i < vertexs.length - 1; i++) {
      const [p1, p2] = getOffsetPoint(vertexs[i], vertexs[i + 1], normal, 0.25);
      currentPoints.push(p1, p2);
    }

    // 2D图形计算部分，通过一半的顶点计算出另外一半的顶点位置
    let pointFor3D: Array<Vector3> = [];
    if (currentPoints.length) {
      const current2D = [];
      for (let i = 0; i < currentPoints.length; i++) {
        const p = point2DByPoint3D(currentPoints[i], this.userData.matrix);
        current2D.push(p);
      }

      const newPoint: Array<Vector2> = [current2D[0]];
      for (let i = 0; i < current2D.length - 3; i += 2) {
        const v1 = current2D[i].clone();
        const v2 = current2D[i + 1].clone();
        const v3 = current2D[i + 2].clone();
        const v4 = current2D[i + 3].clone();
        const crossPoint = cacCrossPointLineWithLine([v1, v2], [v3, v4]);
        if (crossPoint) {
          newPoint.push(crossPoint);
        }
      }
      newPoint.push(current2D[current2D.length - 1]);
      vertexsFor2D.push(...newPoint.reverse());
      pointFor3D = vertexsFor2D.map((p: any) =>
        point3DByPoint2D(p, this.userData.matrix)
      );
      pointFor3D.push(pointFor3D[0]);
    } else {
      pointFor3D = [...vertexs];
    }

    // ======== 画不含曲线的墙 ======
    if (!curveIndexArr.length) {
      vertexsFor2D.forEach((point: Vector2, index: number) => {
        if (index == 0) {
          shapes.moveTo(point.x, point.y);
        } else {
          shapes.lineTo(point.x, point.y);
        }
      });
      this.geometry = new ShapeGeometry(shapes);
      this.geometry.lookAt((window as any).camera.up);
      (this.children[0] as any).setVertexs(pointFor3D);
      this.userData.points = [...pointFor3D];
      return;
    }

    // ======== 画含有曲线的墙 ========
    this.userData.points = [];
    const curveIndexArrInvert = curveIndexArr
      .map((v: any) => v.map((i: any) => vertexs.length * 2 - 1 - i).reverse())
      .reverse();
    vertexsFor2D.forEach((point: Vector2, index: number) => {
      if (index == 0) {
        shapes.moveTo(point.x, point.y);
      } else {
        let hasIndex = -1;
        [...curveIndexArr, ...curveIndexArrInvert].forEach(
          (curveIndex: any, i: number) => {
            if (curveIndex.includes(index)) {
              if (index === curveIndex[0]) {
                const arr = curveIndex.map((v: any) =>
                  point3DByPoint2D(vertexsFor2D[v], this.userData.matrix)
                );
                // 三维样条曲线
                const curve = new CatmullRomCurve3(arr);
                const pointsArr = curve.getPoints(100);
                const geometry = new BufferGeometry();
                //读取坐标数据赋值给几何体顶点
                geometry.setFromPoints(pointsArr);
                // 线材质
                const material = new LineBasicMaterial({
                  color: 0x00ffff,
                });
                // 线模型
                const line = new Line(geometry, material);
                lineGroup.add(line);
                pointsArr.forEach((e: any) => {
                  const px = point2DByPoint3D(e, this.userData.matrix);
                  shapes.lineTo(px.x, px.y);
                });
              }
              hasIndex = index
            }
          }
        );

        if(hasIndex == -1) {
          shapes.lineTo(point.x, point.y);
        }
      }
    });

    this.geometry = new ShapeGeometry(shapes);
    this.geometry.lookAt((window as any).camera.up);

    // 获取直线段的顶点集合
    curveIndexArr.forEach((curveIndex: any, i: number) => {
      if (i == 0) {
        if (curveIndex[0] != 0) {
          this.userData.points.push(pointFor3D.slice(0, curveIndex[0] + 1));
        }
      } else {
        this.userData.points.push(
          pointFor3D.slice(curveIndexArr[i - 1].slice(-1)[0], curveIndex[0] + 1)
        );
      }
    });

    curveIndexArrInvert.forEach((curveIndex: any, i: number) => {
      if (i == 0) {
        this.userData.points.push(
          pointFor3D.slice(
            curveIndexArr.slice(-1)[0].slice(-1)[0],
            curveIndex[0] + 1
          )
        );
      } else {
        this.userData.points.push(
          pointFor3D.slice(
            curveIndexArrInvert[i - 1].slice(-1)[0],
            curveIndex[0] + 1
          )
        );
      }
      let lastLine: any = [];
      if (
        curveIndexArrInvert.slice(-1)[0].slice(-1)[0] !==
        vertexs.length * 2 - 1
      ) {
        lastLine = [
          ...pointFor3D.slice(
            curveIndexArrInvert.slice(-1)[0].slice(-1)[0],
            vertexs.length * 2
          ),
        ];
      } else {
        lastLine = pointFor3D.slice(-1);
      }
      lastLine.push(pointFor3D[0]);
      this.userData.points.push(lastLine);
    });

    this.remove(this.children[0]);
    this.add(lineGroup);

    this.userData.points.forEach((pArr: any) => {
      lineGroup.add(
        new CLine({
          vertexs: pArr,
          color: 0x00ffff,
        })
      );
    });
    return this;
  }

  // 计算多边形面积
  getArea() {
    // 先拿到index
    const indexs = this.geometry.getIndex()?.array || [];
    const vertexs = [...this.userData.points];
    let areaValue = 0;
    for (let i = 0; i < indexs?.length; i += 3) {
      areaValue += areaOfTriangle(
        vertexs[indexs[i]],
        vertexs[indexs[i + 1]],
        vertexs[indexs[i + 2]]
      );
    }
    return areaValue;
  }

  getVertexs() {
    // const vertexs = []
    // if (!this.geometry.attributes.position) return []
    // const bufferData = this.geometry.attributes.position.array
    // for (let i = 0; i < bufferData.length; i += 3) {
    //     vertexs.push(new Vector3(bufferData[i], bufferData[i + 1], bufferData[i + 2]))
    // }
    // return vertexs
  }

  // 颜色
  setColor(color: number) {
    this.material.color.set(color);
    return this;
  }

  getColor() {
    return this.material.color;
  }

  // 克隆过滤一些属性后的自身信息
  cloneSelffilter(filters: any) {
    const Json = this.toJSON();
    for (const key in Json) {
      if (filters && filters.indexOf(key) > -1) Json[key] = undefined;
    }
    return new CPolygon(Json);
  }

  cloneSelf() {
    return new CPolygon(this.toJSON());
  }

  toJSON() {
    const json: { [k: string]: any } = {
      name: this.name,
      color: this.getColor(),
    };

    for (const key in this.userData) {
      json[key] = this.userData[key];
    }
    return json;
  }
}
