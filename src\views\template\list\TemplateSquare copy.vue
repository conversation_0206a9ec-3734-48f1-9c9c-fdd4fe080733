<template>
  <div class="template-square">
    <header class="box__">
      <div class="menu_box" style="margin-bottom: 10px">
        <div
          class="menu_list"
          :class="{ active_menu_list: activeTopMenuTab && activeTopMenuTab.name == menu.name }"
          v-for="(menu, index) in arTopMenuList"
          :key="index"
        >
          {{ menu.name }}
        </div>
      </div>
      <div class="menu_box">
        <div
          class="menu_list"
          :class="{ active_menu_list: activeMenuTab && activeMenuTab.name == menu.name }"
          v-for="(menu, index) in arMenuList"
          :key="index"
          @click="activeTab(menu)"
        >
          {{ menu.name }}
        </div>
      </div>
    </header>
    <main class="box_content">
      <div
        class="div"
        v-for="(item, i) in data"
        :key="item.id"
        :class="{ active_fff_mask: item.isHover }"
        @mouseenter="mouseenterTemp(item, i)"
        @mouseleave="mouseoverTemp(item)"
        :id="'item' + i"
      >
        <!-- <div class="back_video"
                    :class="{ newBack_video: domMaskIndexs.includes(i), lineBack_video: item.sceneType == 1 && item.scenePlatform != 3, itemWechatLeft: itemLeftWechat(item), itemWechatTop: itemTopWechat(item), itemEyeLeft: itemLeftEye(item), itemEyeTop: itemTopEye(item) }"
                    v-if="item.templateVideoKey && item.isHover">
                    <video :src="item.templateVideoKey.split('?')[0]" autoplay muted loop></video>
                </div> -->
        <div class="fff-mask">
          <div class="hover-mask" v-if="item.isHover" :class="{ active_hover_mask: item.isHover }">
            <div
              class="learn-btn"
              v-if="userType == 1"
              style="margin-bottom: 16px"
              @click="editTemplate(item)"
            >
              修改
            </div>
            <div
              class="learn-btn"
              @click="startLearn(item)"
              :class="{ btn_top: item.sceneType != 2 }"
            >
              体验
            </div>
            <div class="create-btn" @click="openCreateScene(item)" v-if="item.sceneType != 1">
              创建同款
            </div>
          </div>
        </div>
        <div class="img-box" style="position: relative">
          <img
            :src="item.templatePicKey.split('?')[0]"
            alt=""
            :class="{ active_img: item.isHover }"
          />
          <div
            :class="[
              { green: item?.sceneType === 1 },
              { 'ar-type': !item?.isHover },
              { 'ar-type-hover': item?.isHover },
            ]"
          >
            {{ _sceneType(item) }}
          </div>
        </div>
        <div class="desc-box">
          <div class="desc">
            <div class="desc-box-icon">
              <div class="desc-box-icon-item" v-if="item.scenePlatform == 1">
                <img src="@/assets/images/home/<USER>" />
              </div>
              <div class="desc-box-icon-item" v-if="item.scenePlatform == 2">
                <img src="@/assets/images/home/<USER>" />
              </div>
              <div class="desc-box-icon-item" v-if="item.scenePlatform == 3">
                <img src="@/assets/images/home/<USER>" />
              </div>

              <div class="desc-box-name">
                <span>
                  {{ item.templateName }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="bottom-mask temp-bottom-mask" :class="{ mask_active: item.isHover }"></div>
      </div>
    </main>
  </div>
  <create-scene
    v-if="modalShow"
    :modal-show="modalShow"
    :handle-hide="handleHide"
    :requestCopyWxScene="requestCopyWxScene"
    :sceneType="sceneType"
    :isCopyTemp="isCopyTemp"
    :hasRoomScene="hasRoomScene"
    :hasPlaneScene="hasPlaneScene"
  ></create-scene>
  <QrCode
    v-if="isOpenQR"
    :hide-add-mask="hideQrCode"
    :codeUrl="codeUrl"
    :name="qrName"
    :hoverCurrentTemplate="hoverCurrentTemplate"
    :sceneDataPlatform="sceneDataPlatform"
    :activeTempSceneId="activeTempSceneId"
    :sceneType="sceneType"
  ></QrCode>
  <SetTemplate
    v-if="isOpenSetTemp"
    ref="setTemplateRef"
    :hide-add-mask="hideSetTemp"
    @confirm="confirm"
    @update="update"
    :currentScene="hoverCurrentScene"
  ></SetTemplate>
</template>

<script lang="ts" setup>
// @ts-nocheck
import '@/assets/media/style.css';
import CreateScene from '@/views/scene/create/index.vue';
import SetTemplate from '@/components/experience/create/SetTemplate.vue';
import QrCode from '@/components/experience/create/QrCode.vue';
import { onMounted, ref, watch, computed, nextTick } from 'vue';
import { arMenus, scenePlatformTabs } from '@/config';
import { useRouter } from 'vue-router';
import { Search } from '@element-plus/icons-vue';
import {
  queryTemplateScene,
  updateTemplateInfo,
  getWxAppQrCode,
  setSceneAsTemplate,
  copyWxScene,
} from '@/api';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import { image } from 'html2canvas/dist/types/css/types/image';
const firstTopTab = { name: '所有平台', sceneType: -1, scenePlatform: -1 };
const firstTab = { name: '所有类型', sceneType: -1, scenePlatform: -1 };
const arMenuList = ref([firstTab, ...arMenus]);
const arTopMenuList = ref([firstTopTab, ...scenePlatformTabs]);
const activeMenuTab = ref(firstTab);
const activeTopMenuTab = ref(firstTopTab);
const searchTemplateValue = ref('');
const platformIndexValue = ref(3);
const data = ref([]);
const activeTempSceneId = ref(0);
const sceneDataPlatform = ref(3);
const domMaskIndexs = ref([]);
const modalShow = ref(false);
const sceneType = ref(1);
const qrName = ref('');
const userType = 1; // Assuming userType is always 1 for the given code
const hoverCurrentTemplate = ref();
const isOpenQR = ref(false);
const isOpenSetTemp = ref(false);
const hoverCurrentScene = ref();
const setTemplateRef = ref();
const codeUrl = ref('');
const isCopyTemp = ref(false);
let tempCopyScene = {};
const router = useRouter();
const store = useStore();
const hasRoomScene = ref(true);
const hasPlaneScene = ref(true);

const itemLeftWechat = computed(() => {
  return (item) => {
    return item.isLeft && item.scenePlatform == 3;
  };
});

const itemTopWechat = computed(() => {
  return (item) => {
    return item.isTop && item.scenePlatform == 3;
  };
});

const itemLeftEye = computed(() => {
  return (item) => {
    return item.isLeft && item.sceneType == 1 && item.scenePlatform != 3;
  };
});

const itemTopEye = computed(() => {
  return (item) => {
    return item.isTop && item.sceneType == 1 && item.scenePlatform != 3;
  };
});

const _sceneType = computed(() => {
  return (item) => {
    switch (item.sceneType) {
      case 1:
        return '空间AR';
      case 2:
        return '平面AR';
      case 3:
        return '图像AR';
      case 5:
        return '身体AR';
      case 6:
        return '人脸AR';
      case 7:
        return '手势AR';
      case 8:
        return '单场景AR';
    }
  };
});

const requestCopyWxScene = (sceneName) => {
  const params = {
    sourceSceneId: tempCopyScene.sceneId,
    sceneName,
  };
  copyWxScene(params).then((res) => {
    if (res.code == 200) {
      ElMessage({ type: 'success', message: '创建成功！' });
      isCopyTemp.value = false;
      router.push('/experience_edit?sceneid=' + res.data + '&sceneType=' + sceneType.value);
    }
  });
};

const mouseenterTemp = (item, i) => {
  const thresholdRight = item.sceneType == 1 && item.scenePlatform != 3 ? 544 : 280;
  const thresholdTop = item.sceneType == 1 && item.scenePlatform != 3 ? 300 : 375;
  item.isHover = true;
  sceneDataPlatform.value = item.scenePlatform;
  sceneType.value = item.sceneType;
  activeTempSceneId.value = item.sceneId;
  hoverCurrentTemplate.value = item;

  const node = document.getElementById('item' + i);
  const rect = node.getBoundingClientRect();
  const distanceFromTop = rect.top + window.scrollY;
  const distanceFromRight = document.documentElement.clientWidth - rect.right;
  if (distanceFromRight < thresholdRight) {
    item.isLeft = true;
  } else {
    item.isLeft = false;
  }
  if (distanceFromTop < thresholdTop) {
    item.isTop = true;
  } else {
    item.isTop = false;
  }
};

const mouseoverTemp = (item) => {
  item.isHover = false;
};

const handleHide = (renew?: boolean) => {
  modalShow.value = false;
  if (isCopyTemp.value) {
    // 如果是正在 创建模板同款
    isCopyTemp.value = false;
  } else {
    if (renew) {
      // 判断是否需要重新渲染
      requestGetTemplateSquares();
    }
  }
};

const update = (params) => {
  return new Promise((resolve) => {
    updateTemplateInfo(params).then((res) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '修改成功！' });
        isOpenSetTemp.value = false;
        requestGetTemplateSquares();
      }
    });
  });
};

const confirm = (params) => {
  return new Promise((resolve) => {
    setSceneAsTemplate(params).then((res) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '操作成功！' });
        isOpenSetTemp.value = false;
        requestGetTemplateSquares();
        hoverCurrentScene.value.sceneStatus = 3;
      }
    });
  });
};

const hideSetTemp = () => {
  isOpenSetTemp.value = false;
};

const hideQrCode = () => {
  isOpenQR.value = false;
};

const editTemplate = (item) => {
  hoverCurrentScene.value = item;
  isOpenSetTemp.value = true;
  nextTick(() => {
    setTemplateRef.value.setParmasEdit(JSON.parse(JSON.stringify(item)), true);
  });
};

const openCreateScene = (item) => {
  const userDto = store.state.userDto;
  const packageInfoDto = store.state.packageInfoDto;
  const userBindPackageDto = store.state.userBindPackageDto;
  const storageData = store.state.storageData.home;
  if ([6, 7].includes(userType)) {
    if (storageData.planeArSceneUsedNum >= 10) {
      store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
      return;
    }
  } else {
    if (userDto.packageVersion == 'V2') {
      if (userBindPackageDto.singleIdentify == 1) {
        // 单点服务
        if (storageData.planeArSceneUsedNum >= userBindPackageDto.sceneNum) {
          store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
          return;
        }
      } else {
        // 空间服务
        if ([2, 3, 6].includes(item.sceneType)) {
          if (storageData.planeArSceneUsedNum >= userBindPackageDto.sceneNum) {
            store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
            return;
          }
        } else {
          store.state.showTips = '创建同款失败，当前账号暂无创建该类型的项目权限';
          return;
        }
      }
    } else {
      if (storageData.planeArSceneUsedNum >= packageInfoDto.planeSceneNum) {
        store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
        return;
      }
    }
  }

  tempCopyScene = item;
  modalShow.value = true;
  isCopyTemp.value = true;
};

const startLearn = (item) => {
  isOpenQR.value = true;
  getWXCode(item);
};

const getWXCode = async (item) => {
  qrName.value = !activeTempSceneId.value ? item.sceneName : item.templateName;
  const { data } = await getWxAppQrCode({ sceneId: item.sceneId, path: 'pages/njyjxr/scene' });
  if (data) {
    codeUrl.value = 'data:image/jpeg;base64,' + data;
  }
};

const requestGetTemplateSquares = () => {
  const params = {
    name: searchTemplateValue.value,
    sceneType: activeMenuTab.value.sceneType,
    scenePlatform: platformIndexValue.value,
  };
  queryTemplateScene(params).then((res) => {
    if (res.code == '200') {
      data.value = res.data;
    }
  });
};

const activeTab = (menu) => {
  for (let i = 0; i < arMenuList.value.length; i++) {
    const item = arMenuList.value[i];
    item.isActive = false;
  }
  activeMenuTab.value = menu;
  requestGetTemplateSquares();
};

onMounted(() => {
  requestGetTemplateSquares();
  store.dispatch('updateCurrentData', {
    sceneId: router.currentRoute.value.query.sceneId,
    experienceId: router.currentRoute.value.query.experienceId,
    templateId: router.currentRoute.value.query.templateId,
    pageType: router.currentRoute.value.query.pageType,
  });
});

watch(
  () => store.state.activeSceneTab,
  (nv) => {
    platformIndexValue.value = nv.value;
    requestGetTemplateSquares();
  }
);
</script>

<style>
.el-input__inner {
  font-size: 14px !important;
}

.el-input__wrapper {
  border-radius: 4px;
}
</style>

<style lang="less">
.template-square {
  .fff-mask {
    position: absolute;
    top: 0px;
    left: 0px;
    background: #fff;
    height: 214.7px;
    width: calc(100%);
    border-radius: 10px;
    border: 2px solid transparent;
    box-sizing: border-box;
  }

  .green {
    background: rgba(21, 142, 100, 0.5) !important;
  }

  .active_fff_mask {
    border: 2px solid #2e76ff;
    border-radius: 10px;
  }

  .active_img {
    width: calc(100% - 6px);
    height: 200px;
    top: 2.9px;
    left: 3px;
  }

  .box__ {
    display: flex;
    margin-top: 10px;
    align-items: start;
    flex-direction: column;
    justify-content: start;
    padding-right: 20px;

    .menu_box {
      display: flex;
      align-items: center;

      .menu_list {
        margin-right: 15px;
        font-weight: 400;
        font-size: 12px;
        color: #737373;
        padding: 6px 8px;
        background: transparent;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          background: rgba(36, 118, 253, 0.1);
        }
      }

      .active_menu_list {
        background: rgba(36, 118, 253, 0.1);
        font-size: 12px;
        color: #2e76ff;
      }
    }
  }

  .box_content {
    display: flex;
    flex-wrap: wrap;
    padding-left: 4px;

    .div {
      position: relative;
      box-sizing: border-box;
      height: 255px;
      margin-right: 25px;
      cursor: pointer;
      margin-top: 24px;
      .img-box {
        position: relative;
        width: 100%;
        height: 100%;
      }

      .ar-type {
        position: absolute;
        left: 0px;
        bottom: 46px;
        height: 29px;
        line-height: 29px;
        padding: 0 10px;
        font-size: 12px;
        color: #ffffff;
        background: rgba(46, 118, 255, 0.5);
        border-radius: 0px 10px 0px 10px;
        z-index: 9;
      }
      .ar-type-hover {
        position: absolute;
        left: 2px;
        bottom: 48px;
        height: 29px;
        line-height: 29px;
        padding: 0 10px;
        font-size: 12px;
        color: #ffffff;
        background: rgba(46, 118, 255, 0.5);
        border-radius: 0px 10px 0px 10px;
        z-index: 9;
      }

      .view-style {
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        position: absolute;
        bottom: 15px;
        right: 16px;
        font-size: 12px;
        color: #ffffff;
        padding-left: 24px;
        z-index: 9;

        &::before {
          content: '';
          position: absolute;
          left: 3px;
          top: 1px;
          width: 14px;
          height: 14px;
          background: url('http://njyjxr.oss-cn-shanghai.aliyuncs.com/zhanghai/hk/xcx.png');
          background-size: 100% 100%;
        }
      }

      .eyeViewStyle {
        &::before {
          background: url('http://njyjxr.oss-cn-shanghai.aliyuncs.com/zhanghai/hk/yj.png') !important;
          background-size: 100% 100% !important;
        }
      }

      .moveViewStyle {
        &::before {
          background: url('http://njyjxr.oss-cn-shanghai.aliyuncs.com/zhanghai/hk/yd.png') !important;
          background-size: 100% 100% !important;
        }
      }

      .temp-view-style {
        bottom: 60px;
      }

      .bottom-mask {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background: linear-gradient(to top, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
        border-radius: 0 0 10px 10px;
      }

      .temp-bottom-mask {
        bottom: 46px;
      }

      .mask_active {
        bottom: 48px;
        left: 3px;
        width: calc(100% - 6px);
        border-radius: 10px;
      }

      .hover-mask {
        width: 100%;
        height: 210px;
        background: rgba(0, 0, 0, 0.6);
        z-index: 9;
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        justify-content: center;
        align-items: center;
        border-radius: 10px;

        .learn-btn {
          box-sizing: border-box;
          font-weight: bold;
          font-size: 14px;
          color: #ffffff;
          line-height: 32px;
          text-align: center;
          margin-bottom: 8px;
          cursor: pointer;
          width: 92px;
          height: 32px;
          background: #2e76ff;
          border-radius: 4px 4px 4px 4px;

          &:hover {
            background: #1251cb;
          }
        }

        .desc_temp {
          width: 152px;
          font-weight: bold;
          font-size: 12px;
          color: #ffffff;
          margin-top: 16px;
          overflow: hidden;
        }

        .create-btn {
          font-weight: bold;
          line-height: 32px;
          text-align: center;
          margin-top: 8px;
          cursor: pointer;
          width: 92px;
          height: 32px;
          background: #669aff;
          border-radius: 4px 4px 4px 4px;
          font-weight: 500;
          font-size: 14px;
          color: #fff;

          &:hover {
            color: #fff;
            background: #2e76ff;
          }
        }
      }

      .active_hover_mask {
        width: calc(100% - 2px);
        height: 200px;
        top: 2px;
        left: 1px;
        z-index: 99;
        transition: all 0.3s ease-in-out;
      }

      img {
        width: 100%;
        height: 209px;
        -o-object-fit: cover;
        object-fit: cover;
        vertical-align: middle;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 10px;
      }

      .active_img {
        width: calc(100% - 4.5px);
        height: 200px;
        top: 2.6px;
        left: 2.5px;
      }

      .desc {
        width: 100%;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        position: absolute;
        bottom: 2px;
        left: 5px;
        height: 45px;
        line-height: 45px;
        text-align: center;
        font-style: normal;
        height: 14%;
        opacity: 0.8;
        text-align: left;
        font-size: 14px;
        font-weight: bold;
        font-size: 14px;
        color: #1e1e1e;
        .desc-box-icon {
          width: 100%;
          height: 32px;
          display: flex;
          flex-direction: row;
          align-items: center;
          .desc-box-icon-item {
            width: 32px;
            height: 32px;
            position: relative;
            img {
              position: absolute;
              top: 4px;
              left: 4px;
              width: 24px;
              height: 24px;
            }
          }

          .desc-box-name {
            width: 300px;
            display: flex;
            align-self: center;
            margin-left: 10px;
          }
        }
      }
    }
  }

  .back_video {
    position: absolute;
    right: -270px;
    width: 252px;
    height: 370px;
    background: #fff;
    z-index: 999;
    display: flex;
    justify-content: center;
    top: -156px;
    box-shadow: 0px 0px 10px 0px #2e76ff;
    border-radius: 12px 12px 12px 12px;
    border: 2px solid #ffffff;

    video {
      width: 252px;
      height: 370px;
      object-fit: cover;
      border-radius: 12px 12px 12px 12px;
    }
  }

  .lineBack_video {
    width: 516px;
    height: 290px;
    top: -80px;
    right: -534px;

    video {
      width: 516px;
      height: 290px;
    }
  }

  .newBack_video {
    left: -291px !important;
    background: url('@/assets/images/new_right.png') no-repeat;
    background-size: 100%;

    video {
      transform: translate(-5px, 14px);
    }
  }
}
</style>
