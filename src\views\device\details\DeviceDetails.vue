<template>
  <div class="table-box">
    <div class="table-content">
      <div class="title">
        <span>浏览项目详情</span>
        <el-icon class="close-icon" @click="closeEvent"><Close /></el-icon>
      </div>
      <div>
        <search-form :form-keys-data="keysData" :search-data-event="searchData"></search-form>
        <table-list ref="tableRefs" :data="tableData" :column-list="columnList" :change-page="changePage"
          :data-total="pageTotal" :page-size="searchForm.pageSize" :page-no="searchForm.pageNo" :show-select="true"
          :handle-filter="handleFilter" :search-form="searchForm" :heightSize="'420px'"></table-list>
        <div class="export-data" :class="tableRefs?.selectDataArr ? 'active' : ''" @click="handleExportData">导出数据</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, reactive, watch } from 'vue'
import TableList from '@/components/TableList.vue'
import SearchForm from '@/components/SearchForm.vue'
import { Close } from '@element-plus/icons-vue'
import { getEquipmentVistStatisticExcel, getSceneMetaPageForWeb, getEquipmentVistStatistic } from '@/api'

const props = defineProps({
  hideDetails: {
    default: null,
    type: Function
  }
})

const tableData: any = ref([]) // 表格数据
const pageTotal = ref(0) // 总计数据条数
const keysData: any = reactive([
  {
    key: 'equipmentSncode',
    type: 'input',
    label: '序列号'
  },
  {
    key: 'date',
    type: 'date-picker',
    label: '时间段',
    keys: ['startDate', 'endDate']
  },
])
const searchForm: any = reactive({ // 查询对象
  pageSize: 9,
  pageNo: 1
})
const columnList: any = ref(
  [
    {
      prop: 'statisticData',
      label: '设备序列号'
    },
    {
      prop: 'sceneName',
      resetKey: 'sceneId', // 重置搜索key
      label: '项目名称',
      // filters: []
    },
    {
      prop: 'equipmentType',
      label: '设备类型',
      list: {
        2: 'Android',
        3: 'IOS',
        4: 'Rokid',
        5: 'Pico 4E',
        6: 'Pico 4UE'
      },
      filters: [
        { text: 'Android', value: 2 },
        { text: 'IOS', value: 3 },
        { text: 'Rokid', value: 4 },
        { text: 'Pico 4E', value: 5 },
        { text: 'Pico 4UE', value: 6 }
      ]
    },
    {
      prop: 'createTimeStr',
      label: '浏览时间',
      isDeviceTime: true,
      width: 178
    },
  ]
)
const tableRefs = ref()

const handleExportData = () => {
  const exportDataArr: any = []
  if (tableRefs.value?.selectDataArr) {
    Object.values(tableRefs.value.selectDataArr).forEach((e: any) => {
      exportDataArr.push(...e)
    })
    getEquipmentVistStatisticExcel(exportDataArr).then((res) => {
      window.open(res.data)
    })
  }
}

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getDataList()
}

const getDataList = () => {
  getEquipmentVistStatistic({ ...searchForm }).then((res) => {
    if (res.data) {
      tableData.value = [...res.data.records];
      pageTotal.value = res.data.total;
    }
  })
}

const searchData = (data: any, type?: string) => {
  for (const key in data) {
    if (key == 'date' && data[key] && data[key][0]) {
      searchForm.startDate = getCurrentDate(data[key][0]);
      searchForm.endDate = getCurrentDate(data[key][1]);
    } else if (key != 'startDate' && key != 'endDate') {
      searchForm[key] = data[key];
    }
  }
  if (type == 'reset') {
    searchForm.equipmentType = '';
    tableRefs.value.clearFilterEvent()
  }
  getDataList()
  tableRefs.value.selectDataArr = null;
}

function getCurrentDate(dateObj: any) {
  if (!dateObj) return '';
  const date = new Date(dateObj);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
  const day = String(date.getDate()).padStart(2, '0'); // 获取当天的日期
  return `${year}-${month}-${day}`;
}

const closeEvent = () => {
  props.hideDetails();
}

const handleFilter = (key: string, value: any) => {
  searchForm[key] = value ? value[0] : null;
  searchForm.pageNo = 1;
  getDataList();
  tableRefs.value.selectDataArr = null;
}

onMounted(async () => {
  const sceneData = await getSceneMetaPageForWeb({ pageNo: 1, pageSize: 999 });
  const userDto = JSON.parse(window.sessionStorage.getItem('userDto') || '{}');
  // columnList.value[1].filters = sceneData.data.records.map((e: any) => ({
  //   text: e.sceneName, value: e.id
  // }))
  if (userDto.userType == 1) { // 超管账号
    const keyItem: any = {
      key: 'mail',
      type: 'input',
      label: '账号名称'
    }
    keysData.unshift(keyItem)
    columnList.value.unshift({
      prop: 'mail',
      label: '账号名称'
    })
  }
  getDataList()

})
</script>
<style scoped lang="less">
.table-box {
  position: fixed !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100vw;
  height: 100vh;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5) !important;

  .table-content {
    position: relative;
    // width: 1015px;
    height: 604px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #E1E1E1;
    padding: 18px 24px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
    color: #797979;
    z-index: 999;

    .title {
      font-weight: bold;
      font-size: 18px;
      color: #1E1E1E;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .close-icon {
        cursor: pointer;
        font-size: 20px;
        color: #666;
        &:hover {
          color: #333;
        }
      }
    }

    .export-data {
      position: absolute;
      right: 24px;
      bottom: 24px;
      width: 120px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: #DADADA;
      border-radius: 4px;
      font-weight: bold;
      font-size: 14px;
      color: #FFFFFF;
      cursor: pointer;

      &.active {
        background-color: #2E76FF;
      }
    }

    .search-form {
      width: 100%;
      top: 0;
      margin-top: 0;


      :deep(.el-form-item__content) {
        width: 182px;
      }

      :deep(.form-btn) {
        width: 158px;
        position: relative;
        left: 6px;
      }
    }

    :deep(.pagination) {
      justify-content: flex-start;
      margin: 16px 0 0;
    }

    :deep(.styleTable) {
      height: 420px;
    }

    :deep(.el-table tr:last-child td) {
      border-bottom: none;
    }

    :deep(.table-module) {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      height: 466px;
      margin-top: 16px;

      .create {
        display: none;
      }
    }
  }
}
</style>