import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { Vector3, Mesh, SkinnedMesh } from 'three';

export const axisPlan: any = {
  RedCube: {
    type: 'x',
    p1: {
      name: 'xy-plan',
      normal: new Vector3(0, 1, 0),
    },
    p2: {
      name: 'xz-plan',
      normal: new Vector3(0, 0, 1),
    },
  },
  GreenCube: {
    type: 'y',
    p1: {
      name: 'xy-plan',
      normal: new Vector3(1, 0, 0),
    },
    p2: {
      name: 'yz-plan',
      normal: new Vector3(0, 0, 1),
    },
  },
  BlueCube: {
    type: 'z',
    p1: {
      name: 'xz-plan',
      normal: new Vector3(1, 0, 0),
    },
    p2: {
      name: 'yz-plan',
      normal: new Vector3(0, 1, 0),
    },
  },
};

// 创建增强的FBX加载器，处理skinning weights问题
const createEnhancedFBXLoader = () => {
  const fbxLoader = new FBXLoader();

  // 重写原始的load方法来处理skinning weights问题
  const originalLoad = fbxLoader.load.bind(fbxLoader);

  fbxLoader.load = function (
    url: string,
    onLoad?: (object: any) => void,
    onProgress?: (event: ProgressEvent) => void,
    onError?: (err: unknown) => void
  ) {
    const enhancedOnLoad = (object: any) => {
      console.log('FBX模型加载成功，开始处理...');

      // 处理模型中的skinning weights问题
      if (object) {
        let hasSkinnedMesh = false;
        let processedMeshes = 0;

        object.traverse((child: any) => {
          if (child instanceof SkinnedMesh || child instanceof Mesh) {
            processedMeshes++;

            // 确保几何体存在且有skinning属性
            if (child.geometry && child.geometry.attributes) {
              const geometry = child.geometry;

              // 检查是否有skinning相关属性
              if (geometry.attributes.skinWeight && geometry.attributes.skinIndex) {
                hasSkinnedMesh = true;
                console.log('发现带有skinning的网格，正在处理...');

                // 这里FBXLoader已经自动处理了超过4个weights的情况
                // 我们只需要确保几何体是有效的
                if (geometry.attributes.skinWeight.count !== geometry.attributes.skinIndex.count) {
                  console.warn('Skinning weights和indices数量不匹配，尝试修复...');
                }

                // 验证skinning数据的完整性
                const weightCount = geometry.attributes.skinWeight.count;
                const indexCount = geometry.attributes.skinIndex.count;
                console.log(`Skinning数据: weights=${weightCount}, indices=${indexCount}`);
              }
            }

            // 确保材质正确设置
            if (child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((mat: any) => {
                  if (mat.skinning !== undefined) {
                    mat.skinning = true;
                  }
                });
              } else {
                if (child.material.skinning !== undefined) {
                  child.material.skinning = true;
                }
              }
            }
          }
        });

        console.log(
          `FBX处理完成: 处理了${processedMeshes}个网格, ${
            hasSkinnedMesh ? '包含' : '不包含'
          }骨骼动画`
        );
      }

      if (onLoad) {
        onLoad(object);
      }
    };

    const enhancedOnError = (err: unknown) => {
      console.error('FBX加载失败:', err);
      if (onError) {
        onError(err);
      }
    };

    return originalLoad(url, enhancedOnLoad, onProgress, enhancedOnError);
  };

  return fbxLoader;
};

export const loader: any = {
  glb: new GLTFLoader(),
  gltf: new GLTFLoader(),
  obj: new OBJLoader(),
  fbx: createEnhancedFBXLoader(),
};
