<template>
  <div class="hello">
    <h1>{{ msg }}</h1>
    <el-button type="primary">主要按钮</el-button>
    <div id="canvas-h"></div>
    <div>
      <div>正方体</div>
      <div>球体</div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls"
// import { useStore } from 'vuex'
import { Vector3, Scene, BoxGeometry, MeshLambertMaterial, Mesh, PointLight, OrthographicCamera, WebGLRenderer, BufferGeometry, Line, LineBasicMaterial } from 'three'
import { CPoint, CLine } from '@/core/primitives'
import { POINT_CLICK_GEOMETRY } from '../core/config'

export default defineComponent({
  name: 'HelloWorld',
  props: {
    msg: String,
  },
  setup() {
    onMounted(() => {
      let scene = new Scene()

      let canvas = document.getElementById('canvas-h')
      let width = canvas?.getBoundingClientRect().width || 0
      let height = canvas?.getBoundingClientRect().height || 0
      let k = width / height
      let s = 200


      let camera = new OrthographicCamera(-s * k, s * k, s, -s, 1, 1000)
      camera.position.set(0, 750, 0)
      camera.lookAt(scene.position)


      let renderer = new WebGLRenderer({ antialias: true })
      renderer.setSize(width, height)
      renderer.setClearColor(0xb9d3ff, 1)
      canvas?.appendChild(renderer.domElement)
      renderer.render(scene, camera)

      // 添加控制器
      const controls = new OrbitControls(camera, renderer.domElement);
      // 使用控制器
      controls.enableDamping = true;
      // controls.enabled = false;
     
      (window as any).controls = controls;


      let geometry = new BoxGeometry(100, 100, 100)
      let material = new MeshLambertMaterial({
        color: 0xff00ff,
        side: 2
      })
      let mesh = new Mesh(geometry, material)
      scene.add(mesh)

      let geometry2 = new BoxGeometry(10, 10, 10)
      let material2 = new MeshLambertMaterial({
        color: 0xff00ff,
        side: 2
      })
      let mesh2 = new Mesh(geometry2, material2)
      mesh2.position.set(100, 100, 100)
      // scene.add(mesh2)

      const cpoint = new CPoint({ vertexs: [new Vector3(100, 100, 100), new Vector3(120, 100, 100)], url: POINT_CLICK_GEOMETRY, size: 10 })
      const cline  = new CLine({ vertexs: [new Vector3(100, 100, 100), new Vector3(160, 100, 100), new Vector3(150, 120, 160)], color: 0x000000 })
      // scene.add(cpoint)
      scene.add(cline)

      // let directionalLight = new DirectionalLight(0xffffff)
      // directionalLight.position.set(150, 50, 50)
      // scene.add(directionalLight)

      let pointLight = new PointLight(0xffffff)
      pointLight.position.set(150, 180, 150)
      scene.add(pointLight)

  

      
      animate()
      function animate() {
        // 使用 requestAnimationFrame 执行动画
        requestAnimationFrame(animate)

        controls.update()
        renderer.render(scene, camera)
      }

      canvas?.addEventListener('mousemove', (e) => {
        const x = e.clientX - 8;//鼠标单击坐标X
        const y = e.clientY - 401;//鼠标单击坐标Y

        // 屏幕坐标转标准设备坐标
        const x1 = (x / width) * 2 - 1;
        const y1 = -(y / height) * 2 + 1;
        //标准设备坐标(z=0.5这个值并没有一个具体的说法)
        const stdVector = new Vector3(x1, y1, 0.5);
        const worldVector = stdVector.unproject(camera);
        mesh2.position.set(worldVector.x, worldVector.y, worldVector.z)
      }, false);
    })
  }
});


</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
h3 {
  margin: 40px 0 0;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  display: inline-block;
  margin: 0 10px;
}

a {
  color: #42b983;
}

#canvas {
  width: 500px;
  height: 500px;
  background-color: #ddd;
}
</style>
