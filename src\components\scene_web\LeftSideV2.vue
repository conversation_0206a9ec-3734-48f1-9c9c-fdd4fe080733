<template>
  <div class="canvas-left" :class="hide_side ? 'hide-left-side' : ''">
    <!-- 左侧菜单栏 (76px宽) -->
    <div class="left-menu-bar">
      <!-- 主菜单项 -->
      <div class="menu-items">
        <div
          class="menu-item project-layer-item"
          :class="activeMenu === 'project-layer' ? 'active' : ''"
          @click="switchMenu('project-layer')">
          <div class="menu-icon">
            <img
              :src="
                activeMenu === 'project-layer'
                  ? require('@/assets/images/xmtc1.png')
                  : require('@/assets/images/xmtc.png')
              "
              alt="项目图层" />
          </div>
          <div class="menu-text">项目图层</div>
        </div>

        <!-- 公共库菜单组 -->
        <div class="menu-group" :class="activeMenu === 'public-lib' ? 'active' : ''">
          <div class="menu-item" @click="switchMenu('public-lib')">
            <div class="menu-icon">
              <img src="@/assets/images/ggk.png" alt="公共库" />
            </div>
            <div class="menu-text">公共库</div>
          </div>
          <div v-if="activeMenu === 'public-lib'" class="menu-divider"></div>
          <!-- 公共库二级按钮区域 -->
          <div v-if="activeMenu === 'public-lib'" class="secondary-buttons">
            <div
              v-for="item in materialTypeList"
              :key="'public-' + item.value"
              class="secondary-button"
              :class="item.value == searchForm.materialType ? 'active' : ''"
              @click="changeMateriaType(item.value)">
              {{ item.title }}
            </div>
          </div>
        </div>

        <!-- 个人库菜单组 -->
        <div class="menu-group" :class="activeMenu === 'personal-lib' ? 'active' : ''">
          <div class="menu-item" @click="switchMenu('personal-lib')">
            <div class="menu-icon">
              <img src="@/assets/images/grk.png" alt="个人库" />
            </div>
            <div class="menu-text">个人库</div>
          </div>
          <div v-if="activeMenu === 'personal-lib'" class="menu-divider"></div>
          <!-- 个人库二级按钮区域 -->
          <div v-if="activeMenu === 'personal-lib'" class="secondary-buttons">
            <div
              v-for="item in materialTypeList"
              :key="'personal-' + item.value"
              class="secondary-button"
              :class="item.value == searchForm.materialType ? 'active' : ''"
              @click="changeMateriaType(item.value)">
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 (241px宽) -->
    <div class="right-content-area">
      <!-- 项目图层内容 -->
      <div v-if="activeMenu === 'project-layer'" class="project-layer-content">
        <!-- 容量显示 (仅scenePlatform==3时显示) -->
        <div class="capacity-display">
          <div class="capacity-title">项目图层</div>
          <div class="capacity-info">
            <span class="capacity-text">容量</span>
            <span class="capacity-numbers">{{ usedSize }}/{{ totalSize }}</span>
            <span class="capacity-unit">MB</span>
          </div>
        </div>

        <!-- 原有的区域列表内容 -->
        <div class="area-list-container" @scroll="tabsContentScroll">
          <!-- 公共区域标题 (2D模式显示) -->
          <div class="area-header">
            <img src="@/assets/images/ggqy.png" />
            <span>公共区域</span>
          </div>

          <!-- 公共区域素材（可与互动区域互相拖拽） -->
          <draggable
            v-model="editSceneData.outerMaterialMetaDtoList"
            group="materials"
            handle=".drag-handle"
            @start="onMaterialDragStart"
            @end="onMaterialDragEnd"
            item-key="uuid"
            class="public-material-draggable-list">
            <template #item="{ element: material, index }">
              <div
                class="source-list"
                :class="store.state.activeMaterial == material?.uuid ? 'active' : ''"
                @click.stop="handleSelected(material)">
                <div class="material-info">
                  <img
                    :src="getMaterialTypeIcon(material)"
                    class="material-type-icon"
                    alt="类型图标" />
                  <span class="material-name">
                    {{ material.elementName || material.materialDto.materialName }}
                  </span>
                </div>
                <!-- 操作按钮区域 -->
                <div class="material-actions">
                  <template v-if="store.state.activeMaterial == material?.uuid">
                    <img
                      src="@/assets/images/shanchu1.png"
                      class="action-btn"
                      title="删除"
                      @click.stop="deleteSourse($event, 2, material, index)" />
                    <img
                      src="@/assets/images/tuodong.png"
                      class="action-btn drag-handle"
                      title="拖动" />
                    <img
                      :src="
                        material.isVisible === 0
                          ? require('@/assets/images/yingcang.png')
                          : require('@/assets/images/xianshi.png')
                      "
                      class="action-btn"
                      :title="material.isVisible === 0 ? '隐藏' : '显示'"
                      @click.stop="toggleMaterialVisibility(material, 2, index)" />
                  </template>
                </div>
              </div>
            </template>
          </draggable>

          <!-- 互动区域素材（平级展示，每个区域的素材直接显示） -->
          <template v-for="(item, index) in editSceneData.interactionDtoList" :key="item.uuid">
            <div
              v-if="item.flag != 'delete'"
              class="interaction-section"
              :class="{ selected: store.state.activeAreaUuid === item.uuid }">
              <!-- 互动区域标题 (2D模式显示) -->
              <div
                v-if="isPlanStyle"
                class="area-header interaction-header"
                @click="handleInteractionHeaderClick(item.uuid)">
                <!-- 左侧内容 -->
                <div class="area-left-content">
                  <!-- 展开/收起箭头 -->
                  <el-icon
                    class="expand-arrow"
                    :class="{ expanded: isInteractionExpanded(item.uuid) }">
                    <ArrowRight />
                  </el-icon>
                  <img src="@/assets/images/ggqy.png" />
                  <span>{{ item.interactionName }}</span>
                </div>

                <!-- 删除图标 - 只在选中时显示 -->
                <img
                  v-if="store.state.activeAreaUuid === item.uuid"
                  src="@/assets/images/shanchu1.png"
                  class="delete-icon"
                  title="删除互动区域"
                  @click.stop="deleteInteractionConfirm($event, index)" />
              </div>

              <!-- 互动区域素材 -->
              <draggable
                v-show="isInteractionExpanded(item.uuid)"
                v-model="item.materialMetaDtoList"
                group="materials"
                handle=".drag-handle"
                @start="onMaterialDragStart"
                @end="onMaterialDragEnd"
                item-key="uuid"
                class="interaction-material-draggable-list">
                <template #item="{ element: material, index: materialIndex }">
                  <div
                    class="source-list"
                    :class="store.state.activeMaterial == material?.uuid ? 'active' : ''"
                    @click.stop="handleSelected(material, item)">
                    <div class="material-info">
                      <img
                        :src="getMaterialTypeIcon(material)"
                        class="material-type-icon"
                        alt="类型图标" />
                      <span class="material-name">
                        {{ material.elementName || material.materialDto.materialName }}
                      </span>
                    </div>
                    <!-- 操作按钮区域 -->
                    <div class="material-actions">
                      <template v-if="store.state.activeMaterial == material?.uuid">
                        <img
                          src="@/assets/images/shanchu1.png"
                          class="action-btn"
                          title="删除"
                          @click.stop="deleteSourse($event, 1, material, index, materialIndex)" />
                        <img
                          src="@/assets/images/tuodong.png"
                          class="action-btn drag-handle"
                          title="拖动" />
                        <img
                          :src="
                            material.isVisible === 0
                              ? require('@/assets/images/yingcang.png')
                              : require('@/assets/images/xianshi.png')
                          "
                          class="action-btn"
                          :title="material.isVisible === 0 ? '隐藏' : '显示'"
                          @click.stop="
                            toggleMaterialVisibility(material, 1, index, materialIndex)
                          " />
                      </template>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </template>
        </div>

        <!-- 更多操作弹窗 -->
        <div
          v-if="showMoreTop - 119 >= -26"
          class="more-list-box"
          :style="{ top: showMoreTop - 119 + 'px' }"
          @mousemove="hoverDelete"
          @mouseleave="leaveDelete">
          <div>
            <div @click="dialogVisible = true">删除</div>
          </div>
        </div>
      </div>

      <!-- 公共库内容 -->
      <div v-else-if="activeMenu === 'public-lib'" class="public-lib-content">
        <!-- 标题区域 -->
        <div class="lib-header">
          <span class="lib-title">公共库</span>
          <span class="lib-type-name">{{ getCurrentTypeName() }}</span>
          <!-- 调试按钮 -->
          <button
            class="debug-btn"
            @click="printModelsInEditor"
            title="打印编辑器中的模型信息到控制台">
            🐛
          </button>
          <!-- 具体模型按钮 -->
          <button class="debug-btn" @click="printSpecificModels" title="打印具体的3D模型对象详情">
            🎮
          </button>
        </div>

        <!-- 筛选下拉组件 -->
        <div class="filter-dropdown">
          <el-select v-model="filterValue" placeholder="请选择筛选条件" class="filter-select">
            <el-option label="全部" value="all"></el-option>
            <el-option label="最新" value="latest"></el-option>
            <el-option label="热门" value="popular"></el-option>
          </el-select>
        </div>

        <!-- 素材网格布局 -->
        <div class="material-grid-container">
          <div :class="showLoading ? 'show-loading' : ''">
            <div
              class="source-pool"
              v-for="(item, index) in sourceList"
              :style="{ marginRight: (index + 1) % 3 == 0 ? '0' : '7px' }"
              :key="index">
              <div
                :class="hoverImage == index + '' ? 'hoverSource' : ''"
                @mousedown="handleMouseDown($event, materialUrls[item.materialType].url_a, item)"
                @mousemove="handleMouseMove(index)"
                @mouseleave="handleMouseLeave">
                <img
                  :src="
                    item.thumbnailOssAccessUrl ||
                    materialUrls[item.materialType][hoverImage == index + '' ? 'url_a' : 'url']
                  "
                  draggable="false" />
                <div class="preview" @mousedown.stop="handlePreview(item)"></div>
              </div>
              <div class="source-name">{{ item.materialName }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人库内容 -->
      <div v-else-if="activeMenu === 'personal-lib'" class="personal-lib-content">
        <!-- 标题区域 -->
        <div class="lib-header">
          <div class="lib-title-group">
            <span class="lib-title">个人库</span>
            <span class="lib-type-name">{{ getCurrentTypeName() }}</span>
          </div>
          <img
            src="@/assets/images/tjsc.png"
            class="lib-icon"
            alt="添加素材"
            @click.stop="modalShow = true" />
        </div>

        <!-- 素材网格布局 -->
        <div class="material-grid-container">
          <div :class="showLoading ? 'show-loading' : ''">
            <div
              class="source-pool personal-material"
              v-for="(item, index) in sourceList"
              :style="{ marginRight: (index + 1) % 3 == 0 ? '0' : '7px' }"
              :key="index">
              <div
                :class="hoverImage == index + '' ? 'hoverSource' : ''"
                @mousedown="handleMouseDown($event, materialUrls[item.materialType].url_a, item)"
                @mousemove="handleMouseMove(index)"
                @mouseleave="handleMouseLeave">
                <!-- 紫色标识装饰 -->
                <div class="purple-badge">💜</div>
                <img
                  :src="
                    item.thumbnailOssAccessUrl ||
                    materialUrls[item.materialType][hoverImage == index + '' ? 'url_a' : 'url']
                  "
                  draggable="false" />
                <div class="preview" @mousedown.stop="handlePreview(item)"></div>
              </div>
              <div class="source-name">{{ item.materialName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <create-source-material v-if="modalShow" :handle-hide="handleHide"></create-source-material>
  <material-preview
    :source-type="showSourceType"
    :handle-hide="closedPreview"
    :model-type="modelType"
    v-if="sourceUrl"
    :source-url="sourceUrl"></material-preview>
  <img
    class="side-toggle-btn"
    :class="{ 'side-hidden': hide_side }"
    :src="
      hide_side ? require('@/assets/images/zhankai.png') : require('@/assets/images/shouqi.png')
    "
    @click="hide_side = !hide_side" />
  <div class="dialog-box" v-show="dialogVisible">
    <div>
      <div class="title">
        {{ deleteSourceIndex ? '确认要删除当前素材？' : '确认要删除当前互动区域？' }}
      </div>
      <div>{{ deleteSourceIndex ? '删除后将不可恢复' : '删除后，区域中的素材都将删掉' }}</div>
      <div class="sure-btn" @click="deleteSourceIndex ? deleteSourceEvent() : deleteInteraction()">
        确定
      </div>
      <div class="close-icon" @click="dialogVisible = false"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, reactive, computed } from 'vue';
import draggable from 'vuedraggable';
import { ArrowRight } from '@element-plus/icons-vue';

import { materialUrls } from '@/config';
import { getMaterialPageByUser, getOssAccessPath, getDefaultMaterial } from '@/api';
import CreateSourceMaterial from '@/views/material/create/CreateSourceMaterial.vue';
import MaterialPreview from '@/components/MaterialPreview.vue';

import { useStore } from 'vuex';
import { debounce } from 'lodash-es';
import { ElMessage } from 'element-plus';
import { Box3, Vector3 } from 'three';

const store = useStore();

// TypeScript 接口定义
interface Material {
  id: string;
  materialName: string;
  materialType: string;
  materialFormat?: string;
  thumbnailOssAccessUrl?: string;
  materialOssAccessUrl?: string;
  materialAffiliation?: number;
  isVisible: number;
  uuid?: string;
}

interface SearchForm {
  pageSize: number;
  pageNum: number;
  materialType: string;
  activeLibrary: number;
}

interface InteractionItem {
  id: string;
  interactionName: string;
  materialMetaDtoList: Material[];
}

// 常量定义
const CONSTANTS = {
  PAGE_SIZE: 50,
  MATERIAL_TYPES: {
    VIDEO: '1',
    AUDIO: '2',
    IMAGE: '3',
    MODEL: '4',
    TEXT: '5',
  },
  LIBRARY_TYPES: {
    PUBLIC: 1,
    PERSONAL: 2,
  },
  MODEL_FORMATS: ['fbx', 'glb', 'gltf', 'obj'],
};

const props = defineProps({
  sourcePoolMourseDown: {
    default: null,
    type: Function,
  },
  deleteAssets: {
    default: null,
    type: Function,
  },
  headerRef: {
    default: null,
    type: Object,
  },
});

// 新增状态管理
const activeMenu = ref('project-layer'); // 当前选中的菜单项
const filterValue = ref('all'); // 筛选下拉选中值

// Computed 属性优化
const editSceneData = computed(() => store.state.editSceneData);
const isPlanStyle = computed(() => store.state.isPlanStyle);
const scenePlatform = computed(() => {
  return editSceneData.value?.scenePlatform || 1;
});

// 当前选中的素材类型名称
const currentTypeName = computed(() => {
  const typeNames = {
    [CONSTANTS.MATERIAL_TYPES.VIDEO]: '视频',
    [CONSTANTS.MATERIAL_TYPES.AUDIO]: '音频',
    [CONSTANTS.MATERIAL_TYPES.IMAGE]: '图片',
    [CONSTANTS.MATERIAL_TYPES.MODEL]: '模型',
    [CONSTANTS.MATERIAL_TYPES.TEXT]: '文本',
  };
  return typeNames[searchForm.materialType as keyof typeof typeNames] || '全部类型';
});

// 是否显示二级按钮
const shouldShowSecondaryButtons = computed(() => {
  return activeMenu.value === 'public-lib' || activeMenu.value === 'personal-lib';
});
const totalSize = ref(102433); // 总容量
const usedSize = ref(5024); // 已使用容量

// 移除了 showSecondaryButtons 计算属性，现在使用独立的条件判断

// 原有状态变量
let timer: any = null;
let pageTotal = 0;
let isPending = false;
const showLoading = ref(false);
const hide_side = ref(false);
const modalShow = ref(false); // 显示新建
const modalShow2: any = ref(null); // 显示素材组合
const sourceType = ref(''); // 资源类型
const sourceList: any = ref([]);
const hoverImage: any = ref(''); // 悬浮在照片上
const sourceUrl = ref('');
const showSourceType = ref('');
const modelType = ref('');
const interactionStatus: any = ref({});
// 互动区域展开/收起状态管理
const interactionExpandStatus: any = ref({});
const showMoreTop = ref(0);
const activeMoreTarget: any = ref(null);
const deleteInteractionIndex = ref(-1);
const dialogVisible = ref(false);
const deleteSourceIndex = ref('');
const searchForm: any = reactive({
  // 查询对象
  pageSize: 50,
  materialType: '4',
  activeLibrary: 1,
});
const currentPageNo = ref(1); // 设置一个加载步长
const materialTypeList = ref([
  {
    title: '模型',
    value: '4',
  },
  {
    title: '视频',
    value: '1',
  },
  {
    title: '音频',
    value: '2',
  },
  {
    title: '图片',
    value: '3',
  },
  {
    title: '文本',
    value: '5',
  },
]);

const scenePlatformMap: any = {
  1: '1,2',
  2: '1,2,3',
  3: '2',
};
const platformV2 = ref('');
let tipsText = '';

// 新增方法：菜单切换
/**
 * 切换左侧菜单项
 * @param menuType 菜单类型 ('project-layer' | 'public-lib' | 'personal-lib')
 */
const switchMenu = (menuType: string) => {
  if (activeMenu.value === menuType) return;

  activeMenu.value = menuType;

  // 根据菜单类型设置对应的库类型和加载素材
  if (menuType === 'public-lib') {
    searchForm.activeLibrary = 1;
    currentPageNo.value = 1;
    sourceList.value = [];
    getMaterialTotal(1);
  } else if (menuType === 'personal-lib') {
    searchForm.activeLibrary = 2;
    currentPageNo.value = 1;
    sourceList.value = [];
    getMaterialTotal(2);
  }
  // project-layer 不需要加载素材
};

/**
 * 互动区域拖拽开始事件处理
 * @param evt 拖拽事件对象
 */
const onInteractionDragStart = (evt: any) => {
  console.log('互动区域拖拽开始:', evt);
};

/**
 * 互动区域拖拽结束事件处理
 * @param evt 拖拽事件对象
 */
const onInteractionDragEnd = (evt: any) => {
  console.log('互动区域拖拽结束:', evt);
  // 更新 store 中的数据
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData.value));
};

// 新增方法：素材拖拽开始
const onMaterialDragStart = (evt: any) => {
  console.log('素材拖拽开始:', evt);
};

// 新增方法：素材拖拽结束
const onMaterialDragEnd = (evt: any) => {
  console.log('素材拖拽结束:', evt);
  // 这里可以添加拖拽结束后的处理逻辑，比如保存新的排序
  // 更新 store 中的数据
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData.value));
};

/**
 * 切换素材类型
 * @param value 素材类型 ('1'-视频, '2'-音频, '3'-图片, '4'-模型, '5'-文本)
 */
const changeMateriaType = (value: string) => {
  if (searchForm.materialType == value) return;
  searchForm.materialType = value;
  currentPageNo.value = 1;
  sourceList.value = [];
  getMaterialTotal(searchForm.activeLibrary);
};

/**
 * 切换素材库类型
 * @param value 库类型 (1-公共库, 2-个人库)
 */
const activeLibraryEvent = (value: number) => {
  if (searchForm.activeLibrary == value) return;
  searchForm.activeLibrary = value;

  // 同步更新菜单状态
  if (value === 1) {
    activeMenu.value = 'public-lib';
  } else if (value === 2) {
    activeMenu.value = 'personal-lib';
  }

  currentPageNo.value = 1;
  sourceList.value = [];
  getMaterialTotal(searchForm.activeLibrary);
};

/**
 * 删除互动区域确认
 * @param event 鼠标事件
 * @param index 互动区域索引
 */
const deleteInteractionConfirm = (event: any, index: number) => {
  console.log('🗑️ 点击删除互动区域:', index);
  deleteInteractionIndex.value = index;
  deleteSourceIndex.value = ''; // 清空素材删除索引，表示删除的是互动区域
  dialogVisible.value = true;
};

/**
 * 鼠标悬停在更多操作按钮上
 * @param event 鼠标事件
 * @param index 互动区域索引
 */
const hoverMoreList = (event: any, index: number) => {
  try {
    if (event && event.target) {
      const rect = event.target.getBoundingClientRect();
      if (rect && rect.top !== undefined) {
        showMoreTop.value = rect.top;
        activeMoreTarget.value = event.target;
        deleteInteractionIndex.value = index;
      }
    }
  } catch (error) {
    console.warn('获取悬停元素位置信息失败:', error);
    showMoreTop.value = 0;
    activeMoreTarget.value = null;
  }
};

const hoverDelete = () => {
  clearTimeout(timer);
};

const leaveDelete = () => {
  showMoreTop.value = 0;
  activeMoreTarget.value = null;
};

const tabsContentScroll = () => {
  if (activeMoreTarget.value) {
    try {
      const rect = activeMoreTarget.value.getBoundingClientRect();
      if (rect && rect.top !== undefined) {
        showMoreTop.value = rect.top;
      }
    } catch (error) {
      console.warn('获取元素位置信息失败:', error);
      showMoreTop.value = 0;
      activeMoreTarget.value = null;
    }
  }
};

const deleteInteraction = async () => {
  dialogVisible.value = false;
  const curEditSceneData = {
    ...store.state.editSceneData,
    changeTime: new Date().getTime(),
    changeType: 'deleteInteraction',
  };
  const deleteData: any = { ...store.state.deleteData };
  const deleteInteractionData: any =
    curEditSceneData.interactionDtoList[deleteInteractionIndex.value];
  // 新增的互动区域直接删除，原有的互动区域删除并保存删除的数据
  if (deleteInteractionData.flag != 'add') {
    deleteData[deleteInteractionData.uuid] = { ...deleteInteractionData };
  }
  const currentDeleteData = curEditSceneData.interactionDtoList.splice(
    deleteInteractionIndex.value,
    1
  )[0];
  store.state.editSceneData = JSON.parse(JSON.stringify(curEditSceneData));
  store.state.deleteData = { ...deleteData };
  store.state.currentDeleteData = {
    ...currentDeleteData,
    deleteIndex: deleteInteractionIndex.value,
  };
  deleteInteractionIndex.value = -1;
  store.state.randerTotal = 0;
  await props.deleteAssets();
};

/**
 * 删除素材二次确认
 * @param event 事件对象（未使用）
 * @param type 删除类型 (1-互动区域素材, 2-公共区域素材)
 * @param data 素材数据（未使用）
 * @param index 互动区域索引
 * @param i 素材在互动区域中的索引（可选）
 */
const deleteSourse = (event: any, type: number, data: any, index: number, i?: number) => {
  // type1=互动区域素材，type2=公共区域素材
  dialogVisible.value = true;
  if (type == 1) {
    //删除互动区域里的素材
    deleteSourceIndex.value = `${index},${i}`;
  } else if (type == 2) {
    deleteSourceIndex.value = index + '';
  }
};

/**
 * 执行删除素材操作
 */
const deleteSourceEvent = async () => {
  dialogVisible.value = false;
  let curEditSceneData = {
    ...store.state.editSceneData,
    changeTime: new Date().getTime(),
    changeType: 'deleteSource',
  };
  const deleteData: any = { ...store.state.deleteData };
  let deleteSourceData = null;
  const deleteIndexs = deleteSourceIndex.value.split(',');
  let currentDeleteData = null;
  if (deleteIndexs.length == 2) {
    // 互动区域里的素材
    deleteSourceData =
      curEditSceneData.interactionDtoList[deleteIndexs[0]].materialMetaDtoList[deleteIndexs[1]];
    currentDeleteData = curEditSceneData.interactionDtoList[
      deleteIndexs[0]
    ].materialMetaDtoList.splice(deleteIndexs[1], 1)[0];
  } else if (deleteIndexs.length == 1) {
    // 公共区域里的素材
    deleteSourceData = curEditSceneData.outerMaterialMetaDtoList[deleteIndexs[0]];
    if (
      deleteSourceData.materialDto.materialType == '2' &&
      deleteSourceData.materialDto.id == curEditSceneData.backgroundMusic
    ) {
      // 音乐删除后，背景音乐需要更新
      curEditSceneData = { ...curEditSceneData, backgroundMusic: null };
    }
    currentDeleteData = curEditSceneData.outerMaterialMetaDtoList.splice(deleteIndexs[0], 1)[0];
  }
  if (deleteSourceData.flag != 'add') {
    deleteData[deleteSourceData.uuid] = {
      ...deleteSourceData,
      interactionId: deleteIndexs[1]
        ? curEditSceneData.interactionDtoList[deleteIndexs[0]].id
        : 'public',
    };
  }
  store.state.deleteData = { ...deleteData };

  store.state.editSceneData = JSON.parse(JSON.stringify(curEditSceneData));
  store.state.currentDeleteData = { ...currentDeleteData, deleteIndex: deleteSourceIndex.value };
  deleteSourceIndex.value = '';
  store.state.randerTotal = 0;
  await props.deleteAssets();
};

/**
 * 处理互动区域标题栏点击事件
 * 同时处理选中互动区域和展开/收起状态
 * @param interactionId 互动区域ID
 */
const handleInteractionHeaderClick = (interactionId: string) => {
  console.log('🖱️ 点击互动区域标题栏:', interactionId);

  // 1. 选中互动区域（高亮显示）
  handleClick(interactionId);

  // 2. 切换展开/收起状态
  toggleInteractionExpand(interactionId);

  console.log('✅ 互动区域标题栏点击处理完成, activeAreaUuid:', store.state.activeAreaUuid);
};

// 互动区域展开/收起切换方法
const toggleInteractionExpand = (interactionId: string) => {
  if (!interactionExpandStatus.value[interactionId]) {
    // 第一次点击，从默认展开状态切换到收起状态
    interactionExpandStatus.value[interactionId] = { expanded: false };
  } else {
    // 切换展开/收起状态
    interactionExpandStatus.value[interactionId].expanded =
      !interactionExpandStatus.value[interactionId].expanded;
  }
};

// 获取互动区域展开状态
const isInteractionExpanded = (interactionId: string) => {
  // 如果没有设置状态，默认为展开（true）
  if (!interactionExpandStatus.value[interactionId]) {
    return true;
  }
  return interactionExpandStatus.value[interactionId].expanded;
};

// 根据素材类型获取对应的图标
const getMaterialTypeIcon = (material: any) => {
  if (!material) return require('@/assets/images/wz.png'); // 默认文字图标

  // 正确获取素材类型：优先从 materialDto 中获取
  const materialType = material.materialDto?.materialType || material.materialType || material.type;

  console.log('🔍 获取图标 - 素材类型:', materialType);

  switch (materialType) {
    case '5': // 文本
    case 'text':
    case '文字':
      console.log('✅ 匹配到文本类型');
      return require('@/assets/images/wz.png');
    case '4': // 模型
    case 'model':
    case '模型':
      console.log('✅ 匹配到模型类型');
      return require('@/assets/images/moxing.png');
    case '3': // 图片
    case 'image':
    case '图片':
      console.log('✅ 匹配到图片类型');
      return require('@/assets/images/icon_pic.png');
    case '2': // 音频
    case 'audio':
    case '音频':
      console.log('✅ 匹配到音频类型');
      return require('@/assets/images/yingping.png');
    case '1': // 视频
    case 'video':
    case '视频':
      console.log('✅ 匹配到视频类型');
      return require('@/assets/images/shiping.png');
    default:
      console.log('❌ 未匹配到任何类型，使用默认图标，materialType:', materialType);
      return require('@/assets/images/wz.png'); // 默认文字图标
  }
};

// 切换素材显示隐藏状态
const toggleMaterialVisibility = (
  material: any,
  type: number,
  index: number,
  materialIndex?: number
) => {
  console.log('🔄 切换素材显示状态:', { material, type, index, materialIndex });

  // 切换 isVisible 状态：0=隐藏，1=显示
  const newVisibility = material.isVisible === 0 ? 1 : 0;

  // 更新数据
  const editSceneData = { ...store.state.editSceneData };

  if (type === 1) {
    // 互动区域素材
    if (materialIndex !== undefined) {
      editSceneData.interactionDtoList[index].materialMetaDtoList[materialIndex].isVisible =
        newVisibility;
      editSceneData.interactionDtoList[index].materialMetaDtoList[materialIndex].flag = 'update';
    }
  } else if (type === 2) {
    // 公共区域素材
    editSceneData.outerMaterialMetaDtoList[index].isVisible = newVisibility;
    editSceneData.outerMaterialMetaDtoList[index].flag = 'update';
  }

  // 更新 store
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));

  // 控制编辑器中模型的显示隐藏
  const scene = (window as any).scene;
  const scene2 = (window as any).scene2;

  if (scene || scene2) {
    const modelObject =
      scene?.getObjectByName(material.uuid) || scene2?.getObjectByName(material.uuid);
    if (modelObject) {
      modelObject.visible = newVisibility === 1;

      // 同时控制模型下方的文字标注显示隐藏
      modelObject.traverse((child: any) => {
        if (
          child.element &&
          child.element.classList &&
          child.element.classList.contains('source-title')
        ) {
          child.visible = newVisibility === 1;
        }
      });

      console.log(`🎯 模型 ${material.uuid} 可见性设置为:`, newVisibility === 1);
    } else {
      console.warn('⚠️ 未找到对应的模型对象:', material.uuid);
    }
  }
};

// 获取当前类型名称 - 使用 computed 属性替代
const getCurrentTypeName = () => {
  return currentTypeName.value;
};

// 优化滚动事件处理 - 添加防抖
const handleScroll = debounce((event: Event) => {
  const target = event.target as HTMLElement;
  if (!target) return;

  const { scrollTop, scrollHeight, clientHeight } = target;
  const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

  if (isNearBottom && !showLoading.value && sourceList.value.length < pageTotal) {
    loadMoreMaterials();
  }
}, 200);

// 统一错误处理
const handleApiError = (error: any, operation: string) => {
  console.error(`${operation} 失败:`, error);
  ElMessage.error(`${operation}失败，请重试`);
};

// 加载更多素材
const loadMoreMaterials = async () => {
  if (showLoading.value) return;

  showLoading.value = true;
  try {
    searchForm.pageNum++;
    await getMaterialTotal(searchForm.activeLibrary);
  } catch (error) {
    handleApiError(error, '加载更多素材');
    searchForm.pageNum--; // 回滚页码
  } finally {
    showLoading.value = false;
  }
};

/**
 * 处理素材选中事件
 * @param data 素材数据
 * @param inter 互动区域数据（可选）
 */
const handleSelected = (data: any, inter?: any) => {
  if (store.state.isDragLoading || store.state.isRequesting)
    return ElMessage({ type: 'warning', message: '数据加载中，请勿频繁操作' });

  // 如果点击的是已选中的素材，则取消选中
  if (store.state.activeMaterial == data.uuid) {
    store.state.activeMaterial = '';
    // 清除互动区域选中状态，因为没有选中任何素材了
    store.state.activeAreaUuid = '';
    Object.keys(interactionStatus.value).forEach((key: string) => {
      interactionStatus.value[key].active = false;
    });
    console.log('🔄 取消选中素材，已清除所有区域选中状态');
    return;
  }

  // 选中新的素材
  store.state.activeMaterial = data.uuid || '';
  store.state.operateType = '移动';

  // 选中素材时，清除所有互动区域的选中状态（蓝色背景）
  // 这样可以明确区分"选中互动区域容器"和"选中具体素材"两种状态
  store.state.activeAreaUuid = '';
  Object.keys(interactionStatus.value).forEach((key: string) => {
    interactionStatus.value[key].active = false;
  });

  // 如果素材属于某个互动区域，更新编辑器中的当前数据（但不显示区域选中状态）
  if (inter) {
    store.dispatch('updateCurrentData', { interactionId: inter.id });
  }

  console.log(
    '🎯 选中素材:',
    data.uuid,
    '所属互动区域:',
    inter?.uuid || '公共区域',
    '已清除区域选中状态'
  );
};

/**
 * 选中互动区域（用于标题栏点击）
 * @param interactionId 互动区域ID
 */
const handleClick = (interactionId: string) => {
  // 清除选中的素材
  if (store.state.activeMaterial) {
    store.state.activeMaterial = '';
  }

  // 取消其他互动区域的选中状态
  Object.keys(interactionStatus.value).forEach((key: string) => {
    interactionStatus.value[key].active = false;
  });

  // 确保当前互动区域的状态对象存在
  if (!interactionStatus.value[interactionId]) {
    interactionStatus.value[interactionId] = {
      active: false,
      closed: false,
    };
  }

  // 选中当前互动区域
  interactionStatus.value[interactionId].active = true;
  store.state.activeAreaUuid = interactionId;

  console.log('🎯 选中互动区域:', interactionId, 'activeAreaUuid:', store.state.activeAreaUuid);
};

/**
 * 切换互动区域的展开/收起状态
 * @param interactionId 互动区域ID
 */
const handleClickOpen = (interactionId: string) => {
  Object.keys(interactionStatus.value).forEach((key: string) => {
    if (key == interactionId) {
      interactionStatus.value[key].closed = !interactionStatus.value[key].closed;
    }
  });
  if (!interactionStatus.value[interactionId]) {
    interactionStatus.value[interactionId] = {
      closed: true,
    };
  }
};

/**
 * 隐藏新建素材弹窗
 * @param renew 是否需要刷新素材列表
 * @param materialType 素材类型（可选）
 */
const handleHide = (renew?: boolean, materialType?: string) => {
  modalShow.value = false;
  if (renew) {
    // 判断是否需要重新渲染
    searchForm.activeLibrary = 2;
    searchForm.materialType = materialType;
    currentPageNo.value = 1;
    sourceList.value = [];
    getMaterialTotal(searchForm.activeLibrary);
    sourceType.value = '';
  }
};

// 拆分获取素材的函数
const fetchPublicMaterials = async (params: any) => {
  const res = await getDefaultMaterial({
    ...params,
    pageNo: currentPageNo.value,
    platformV2: platformV2.value,
  });
  let data = res.data.records.map((d: any) => {
    d.materialAffiliation = 2;
    return d;
  });
  if (scenePlatform.value == '3' && params.materialType == '4') {
    data = data.filter((e: any) => e.materialFormat == 'glb' || e.materialFormat == 'gltf');
  }
  return { data, total: res.data.total };
};

const fetchPersonalMaterials = async (params: any) => {
  const res = await getMaterialPageByUser({
    ...params,
    pageNo: currentPageNo.value,
    platformV2: platformV2.value,
  });
  let data = res.data.records.map((d: any) => {
    d.materialAffiliation = 1;
    return d;
  });
  if (scenePlatform.value == '3' && params.materialType == '4') {
    data = data.filter((e: any) => e.materialFormat == 'glb' || e.materialFormat == 'gltf');
  }
  return { data, total: res.data.total };
};

const getMaterialTotal = async (type: number) => {
  try {
    showLoading.value = true;
    let result;

    if (type == CONSTANTS.LIBRARY_TYPES.PUBLIC) {
      result = await fetchPublicMaterials(searchForm);
    } else if (type == CONSTANTS.LIBRARY_TYPES.PERSONAL) {
      result = await fetchPersonalMaterials(searchForm);
    } else {
      return;
    }

    sourceList.value.push(...result.data);
    pageTotal = result.total;
  } catch (error) {
    handleApiError(error, '获取素材列表');
  } finally {
    showLoading.value = false;
  }
};

/**
 * 预览素材
 * @param data 素材数据
 */
const handlePreview = (data: any) => {
  showSourceType.value = data.materialFormat ? data.materialFormat.split('/')[0] : 'text';
  if (CONSTANTS.MODEL_FORMATS.includes(showSourceType.value)) {
    showSourceType.value = 'model';
    modelType.value = data.materialFormat;
  }
  let ossKey = '';
  if (showSourceType.value == 'model') {
    ossKey = data.modelStorageMap?.web?.ossKey || '';
  } else {
    ossKey = data.ossKey;
  }
  showSourceType.value = showSourceType.value.toUpperCase().toLowerCase();
  if (showSourceType.value == 'png' || showSourceType.value == 'jpg') {
    showSourceType.value = 'image';
  }
  if (showSourceType.value == 'mp4') {
    showSourceType.value = 'video';
  }
  if (showSourceType.value == 'mp3') {
    showSourceType.value = 'audio';
  }
  if (ossKey) {
    getOssAccessPath({ key: ossKey }).then((res1: any) => {
      sourceUrl.value = res1.data;
    });
  }
};

const handleMouseMove = (index: number, index2?: number, type?: string) => {
  if (!hoverImage.value) {
    hoverImage.value = index + '';
  }
  if (type) {
    hoverImage.value = `${index}${index2}${type}`;
  }
};

const handleMouseLeave = () => {
  hoverImage.value = '';
};

const handleMouseDown = (e: any, url: string, data: any) => {
  if (store.state.isDragLoading || store.state.isRequesting) {
    return ElMessage({ message: '数据加载中，请勿频繁操作', type: 'warning' });
  }
  store.state.isDragLoading = true;
  if (data.thumbnail) {
    getOssAccessPath({ key: data.thumbnail }).then((res: any) => {
      props.sourcePoolMourseDown(e, res.data, data);
    });
  } else {
    props.sourcePoolMourseDown(e, url, data);
  }
};

const closedPreview = () => {
  sourceUrl.value = '';
};

/**
 * 打印编辑器中的所有模型信息
 * 用于调试和查看当前场景中的模型数据
 */
const printModelsInEditor = () => {
  console.log('=== 编辑器模型信息打印 ===');

  const editData = store.state.editSceneData;
  if (!editData) {
    console.log('❌ 没有找到编辑场景数据');
    return;
  }

  console.log('📊 场景基本信息:');
  console.log('  - 场景ID:', editData.id);
  console.log('  - 场景名称:', editData.sceneName);
  console.log('  - 场景平台:', editData.scenePlatform);
  console.log('  - 是否2D模式:', store.state.isPlanStyle);

  // 打印公共区域模型
  console.log('\n🌍 公共区域模型 (outerMaterialMetaDtoList):');
  const outerMaterials = editData.outerMaterialMetaDtoList || [];
  if (outerMaterials.length === 0) {
    console.log('  ❌ 没有公共区域模型');
  } else {
    outerMaterials.forEach((material: any, index: number) => {
      console.log(`  📦 模型 ${index + 1}:`);
      console.log('    - UUID:', material.uuid);
      console.log(
        '    - 名称:',
        material.elementName || material.materialDto?.materialName || '未命名'
      );
      console.log('    - 类型:', material.materialDto?.materialType);
      console.log('    - 格式:', material.materialDto?.materialFormat);
      console.log('    - 位置:', material.location);
      console.log('    - 缩放:', material.scale);
      console.log('    - 可见性:', material.isVisible === 1 ? '显示' : '隐藏');
      console.log('    - OSS路径:', material.materialDto?.ossKey);
      console.log('    - 模型存储:', material.materialDto?.modelStorageMap);
    });
  }

  // 打印互动区域模型
  console.log('\n🎯 互动区域模型 (interactionDtoList):');
  const interactions = editData.interactionDtoList || [];
  if (interactions.length === 0) {
    console.log('  ❌ 没有互动区域');
  } else {
    interactions.forEach((interaction: any, interIndex: number) => {
      console.log(`  🏷️ 互动区域 ${interIndex + 1}: ${interaction.interactionName}`);
      console.log('    - UUID:', interaction.uuid);

      const materials = interaction.materialMetaDtoList || [];
      if (materials.length === 0) {
        console.log('    ❌ 该区域没有模型');
      } else {
        materials.forEach((material: any, matIndex: number) => {
          console.log(`    📦 模型 ${matIndex + 1}:`);
          console.log('      - UUID:', material.uuid);
          console.log(
            '      - 名称:',
            material.elementName || material.materialDto?.materialName || '未命名'
          );
          console.log('      - 类型:', material.materialDto?.materialType);
          console.log('      - 格式:', material.materialDto?.materialFormat);
          console.log('      - 位置:', material.location);
          console.log('      - 缩放:', material.scale);
          console.log('      - 可见性:', material.isVisible === 1 ? '显示' : '隐藏');
          console.log('      - OSS路径:', material.materialDto?.ossKey);
          console.log('      - 模型存储:', material.materialDto?.modelStorageMap);
        });
      }
    });
  }

  // 打印Three.js场景中的模型对象
  console.log('\n🎮 Three.js场景中的模型对象:');
  const scene1 = (window as any).scene; // 2D场景
  const scene2 = (window as any).scene2; // 3D场景

  if (scene1) {
    console.log('  🎨 2D场景对象:');
    printSceneObjects(scene1, '    ');
  }

  if (scene2) {
    console.log('  🎯 3D场景对象:');
    printSceneObjects(scene2, '    ');
  }

  console.log('\n=== 打印完成 ===');

  // 返回数据供进一步处理
  return {
    sceneData: editData,
    outerMaterials,
    interactions,
    scene1,
    scene2,
  };
};

/**
 * 详细打印Three.js场景对象信息
 * @param scene Three.js场景对象
 * @param indent 缩进字符串
 */
const printSceneObjects = (scene: any, indent: string = '') => {
  if (!scene || !scene.children) return;

  scene.children.forEach((child: any, index: number) => {
    // 跳过系统对象
    if (child.name && (child.name.includes('-init') || child.name === 'model-init')) {
      return;
    }

    console.log(`${indent}- 对象 ${index}: ${child.name || '未命名'} (${child.type})`);

    // 打印对象详细信息
    if (child.name) {
      console.log(
        `${indent}  📍 位置: (${child.position.x.toFixed(2)}, ${child.position.y.toFixed(
          2
        )}, ${child.position.z.toFixed(2)})`
      );
      console.log(
        `${indent}  📏 缩放: (${child.scale.x.toFixed(2)}, ${child.scale.y.toFixed(
          2
        )}, ${child.scale.z.toFixed(2)})`
      );
      console.log(
        `${indent}  🔄 旋转: (${child.rotation.x.toFixed(2)}, ${child.rotation.y.toFixed(
          2
        )}, ${child.rotation.z.toFixed(2)})`
      );
      console.log(`${indent}  👁️ 可见: ${child.visible ? '是' : '否'}`);

      // 如果是网格对象，打印几何体和材质信息
      if (child.isMesh) {
        console.log(`${indent}  🔺 几何体: ${child.geometry?.type || '未知'}`);
        if (child.geometry) {
          const vertices = child.geometry.attributes?.position?.count || 0;
          console.log(`${indent}    - 顶点数: ${vertices}`);
        }

        if (child.material) {
          if (Array.isArray(child.material)) {
            console.log(`${indent}  🎨 材质: ${child.material.length}个材质`);
            child.material.forEach((mat: any, matIndex: number) => {
              console.log(`${indent}    - 材质${matIndex}: ${mat.type}`);
            });
          } else {
            console.log(`${indent}  🎨 材质: ${child.material.type}`);
            if (child.material.map) {
              console.log(`${indent}    - 纹理: 已加载`);
            }
          }
        }
      }

      // 如果有用户数据，打印相关信息
      if (child.userData && Object.keys(child.userData).length > 0) {
        console.log(`${indent}  📊 用户数据:`);
        Object.keys(child.userData).forEach((key) => {
          if (key !== 'boxInfos' && key !== 'oldRotate') {
            console.log(`${indent}    - ${key}: ${JSON.stringify(child.userData[key])}`);
          }
        });
      }
    }

    // 递归打印子对象
    if (child.children && child.children.length > 0) {
      console.log(`${indent}  📁 子对象 (${child.children.length}个):`);
      child.children.forEach((subChild: any, subIndex: number) => {
        if (!subChild.name || !subChild.name.includes('box-helper')) {
          console.log(
            `${indent}    - 子对象 ${subIndex}: ${subChild.name || '未命名'} (${subChild.type})`
          );

          if (subChild.isMesh) {
            console.log(`${indent}      🔺 几何体: ${subChild.geometry?.type || '未知'}`);
            if (subChild.material) {
              const matType = Array.isArray(subChild.material)
                ? `${subChild.material.length}个材质`
                : subChild.material.type;
              console.log(`${indent}      🎨 材质: ${matType}`);
            }
          }
        }
      });
    }

    console.log(''); // 空行分隔
  });
};

/**
 * 专门打印具体的3D模型对象
 * 只显示实际的模型，过滤掉容器和系统对象
 */
const printSpecificModels = () => {
  console.log('=== 具体模型对象详情 ===');

  const scene1 = (window as any).scene; // 2D场景
  const scene2 = (window as any).scene2; // 3D场景

  // 获取所有模型UUID
  const editData = store.state.editSceneData;
  const allModelUuids = new Set();

  if (editData) {
    // 收集公共区域模型UUID
    (editData.outerMaterialMetaDtoList || []).forEach((material: any) => {
      if (material.uuid) allModelUuids.add(material.uuid);
    });

    // 收集互动区域模型UUID
    (editData.interactionDtoList || []).forEach((interaction: any) => {
      (interaction.materialMetaDtoList || []).forEach((material: any) => {
        if (material.uuid) allModelUuids.add(material.uuid);
      });
    });
  }

  console.log(`📊 预期模型数量: ${allModelUuids.size}`);
  console.log(`📋 模型UUID列表:`, Array.from(allModelUuids));

  // 在3D场景中查找这些模型
  if (scene2) {
    console.log('\n🎯 3D场景中的具体模型:');
    let foundModels = 0;

    const findModelsInScene = (obj: any, depth: number = 0) => {
      if (obj.name && allModelUuids.has(obj.name)) {
        foundModels++;
        const indent = '  '.repeat(depth);
        console.log(`${indent}🎮 模型: ${obj.name}`);
        console.log(
          `${indent}  📍 位置: (${obj.position.x.toFixed(2)}, ${obj.position.y.toFixed(
            2
          )}, ${obj.position.z.toFixed(2)})`
        );
        console.log(
          `${indent}  📏 缩放: (${obj.scale.x.toFixed(2)}, ${obj.scale.y.toFixed(
            2
          )}, ${obj.scale.z.toFixed(2)})`
        );
        console.log(`${indent}  👁️ 可见: ${obj.visible ? '是' : '否'}`);
        console.log(`${indent}  🏷️ 类型: ${obj.type}`);

        // 统计子网格
        let meshCount = 0;
        let materialCount = 0;
        obj.traverse((child: any) => {
          if (child.isMesh) {
            meshCount++;
            if (child.material) {
              materialCount += Array.isArray(child.material) ? child.material.length : 1;
            }
          }
        });

        console.log(`${indent}  🔺 网格数量: ${meshCount}`);
        console.log(`${indent}  🎨 材质数量: ${materialCount}`);

        // 打印边界框信息
        if (obj.userData && obj.userData.boxInfos) {
          const box = obj.userData.boxInfos;
          console.log(`${indent}  📦 边界框:`);
          console.log(
            `${indent}    - 最小值: (${box.min?.x?.toFixed(2) || 'N/A'}, ${
              box.min?.y?.toFixed(2) || 'N/A'
            }, ${box.min?.z?.toFixed(2) || 'N/A'})`
          );
          console.log(
            `${indent}    - 最大值: (${box.max?.x?.toFixed(2) || 'N/A'}, ${
              box.max?.y?.toFixed(2) || 'N/A'
            }, ${box.max?.z?.toFixed(2) || 'N/A'})`
          );
        }

        console.log('');
      }

      // 递归查找子对象
      if (obj.children) {
        obj.children.forEach((child: any) => {
          findModelsInScene(child, depth + 1);
        });
      }
    };

    findModelsInScene(scene2);
    console.log(`✅ 在3D场景中找到 ${foundModels} 个模型`);
  }

  // 在2D场景中查找
  if (scene1) {
    console.log('\n🎨 2D场景中的具体模型:');
    let found2DModels = 0;

    const find2DModels = (obj: any) => {
      if (obj.name && allModelUuids.has(obj.name)) {
        found2DModels++;
        console.log(`  🎮 2D模型: ${obj.name} (${obj.type})`);
        console.log(
          `    📍 位置: (${obj.position.x.toFixed(2)}, ${obj.position.y.toFixed(
            2
          )}, ${obj.position.z.toFixed(2)})`
        );
        console.log(`    👁️ 可见: ${obj.visible ? '是' : '否'}`);
      }

      if (obj.children) {
        obj.children.forEach((child: any) => {
          find2DModels(child);
        });
      }
    };

    find2DModels(scene1);
    console.log(`✅ 在2D场景中找到 ${found2DModels} 个模型`);
  }

  // 查找空间地图
  console.log('\n🗺️ 空间地图信息:');
  const mapObject2D = scene1?.getObjectByName('model-init');
  const mapObject3D = scene2?.getObjectByName('model-init');

  if (mapObject2D || mapObject3D) {
    console.log('✅ 找到空间地图对象');

    if (mapObject3D) {
      console.log('  🎯 3D空间地图:');
      console.log(
        `    📍 位置: (${mapObject3D.position.x.toFixed(2)}, ${mapObject3D.position.y.toFixed(
          2
        )}, ${mapObject3D.position.z.toFixed(2)})`
      );
      console.log(
        `    📏 缩放: (${mapObject3D.scale.x.toFixed(2)}, ${mapObject3D.scale.y.toFixed(
          2
        )}, ${mapObject3D.scale.z.toFixed(2)})`
      );
      console.log(`    👁️ 可见: ${mapObject3D.visible ? '是' : '否'}`);
      console.log(`    🏷️ 类型: ${mapObject3D.type}`);

      // 统计地图的网格数量
      let mapMeshCount = 0;
      let mapMaterialCount = 0;
      mapObject3D.traverse((child: any) => {
        if (child.isMesh) {
          mapMeshCount++;
          if (child.material) {
            mapMaterialCount += Array.isArray(child.material) ? child.material.length : 1;
          }
        }
      });

      console.log(`    🔺 网格数量: ${mapMeshCount}`);
      console.log(`    🎨 材质数量: ${mapMaterialCount}`);

      // 计算边界框
      try {
        const mapBox = new Box3().setFromObject(mapObject3D);
        const mapSize = mapBox.getSize(new Vector3());
        console.log(
          `    📦 地图尺寸: (${mapSize.x.toFixed(2)} x ${mapSize.y.toFixed(
            2
          )} x ${mapSize.z.toFixed(2)})`
        );
      } catch (error) {
        console.log('    📦 地图尺寸: 无法计算 (边界框计算失败)');
      }
    }

    if (mapObject2D) {
      console.log('  🎨 2D空间地图:');
      console.log(
        `    📍 位置: (${mapObject2D.position.x.toFixed(2)}, ${mapObject2D.position.y.toFixed(
          2
        )}, ${mapObject2D.position.z.toFixed(2)})`
      );
      console.log(`    👁️ 可见: ${mapObject2D.visible ? '是' : '否'}`);
    }

    // 打印地图来源信息
    const sceneData = store.state.editSceneData;
    if (sceneData && sceneData.spaceDto) {
      console.log('  📂 地图来源信息:');
      console.log(`    - 空间ID: ${sceneData.spaceDto.id}`);
      console.log(`    - 空间名称: ${sceneData.spaceDto.descriptionName}`);
      console.log(`    - 地图路径: ${sceneData.spaceDto.roomStructurePath}`);
    }
  } else {
    console.log('❌ 未找到空间地图对象 (model-init)');
  }

  console.log('\n=== 具体模型打印完成 ===');
};

/**
 * 检查摄像机位置和视角
 */
const checkCameraPosition = () => {
  console.log('=== 摄像机位置检查 ===');

  const camera2 = (window as any).camera2;
  const controls2 = (window as any).controls2;

  if (!camera2 || !controls2) {
    console.log('❌ 未找到摄像机或控制器');
    return;
  }

  console.log('📷 当前摄像机状态:');
  console.log(`- 类型: ${camera2.type}`);
  console.log(
    `- 位置: (${camera2.position.x.toFixed(2)}, ${camera2.position.y.toFixed(
      2
    )}, ${camera2.position.z.toFixed(2)})`
  );
  console.log(
    `- 目标: (${controls2.target.x.toFixed(2)}, ${controls2.target.y.toFixed(
      2
    )}, ${controls2.target.z.toFixed(2)})`
  );

  // 计算距离
  const distance = camera2.position.distanceTo(controls2.target);
  console.log(`- 距离目标: ${distance.toFixed(2)} 单位`);

  // 检查摄像机参数
  if (camera2.type === 'PerspectiveCamera') {
    console.log(`- 视野角度: ${camera2.fov}°`);
    console.log(`- 宽高比: ${camera2.aspect.toFixed(2)}`);
    console.log(`- near: ${camera2.near}, far: ${camera2.far}`);
  }

  // 给出建议
  if (distance > 50) {
    console.log('⚠️ 摄像机距离过远，场景可能看起来很小');
    console.log('💡 建议: 将摄像机移近一些，或调整场景大小');
  } else if (distance < 5) {
    console.log('⚠️ 摄像机距离过近，可能看不到完整场景');
    console.log('💡 建议: 将摄像机移远一些');
  } else {
    console.log('✅ 摄像机距离合适');
  }

  console.log('=== 检查完成 ===');
};

/**
 * 重置摄像机到合适位置
 */
const resetCameraPosition = () => {
  console.log('=== 重置摄像机位置 ===');

  const camera2 = (window as any).camera2;
  const controls2 = (window as any).controls2;

  if (!camera2 || !controls2) {
    console.log('❌ 未找到摄像机或控制器');
    return;
  }

  console.log('📷 重置前位置:', camera2.position);

  // 设置合适的摄像机位置
  camera2.position.set(10, 10, 10);
  camera2.lookAt(0, 0, 0);

  // 设置控制器目标
  controls2.target.set(0, 0, 0);
  controls2.update();

  console.log('📷 重置后位置:', camera2.position);
  console.log('🎯 控制器目标:', controls2.target);
  console.log('✅ 摄像机位置已重置');
};

// 将函数暴露到全局，方便在控制台调用
(window as any).printModelsInEditor = printModelsInEditor;
(window as any).printSceneObjects = printSceneObjects;
(window as any).printSpecificModels = printSpecificModels;
(window as any).checkCameraPosition = checkCameraPosition;
(window as any).resetCameraPosition = resetCameraPosition;

const scrollEvent = (e: any) => {
  const scrollHeight = e.target.scrollHeight;
  const height = e.target.getBoundingClientRect().height;
  const scrollTop = e.target.scrollTop;
  if (
    scrollHeight - height - scrollTop < 10 &&
    showLoading.value == false &&
    currentPageNo.value * searchForm.pageSize < pageTotal
  ) {
    currentPageNo.value += 1;
    getMaterialTotal(searchForm.activeLibrary);
  }
};

onMounted(() => {
  // 为素材网格容器添加滚动事件监听
  const materialGridContainers = document.querySelectorAll('.material-grid-container');
  materialGridContainers.forEach((container) => {
    container.addEventListener('scroll', scrollEvent, false);
  });

  // editSceneData 现在是 computed 属性，不需要手动初始化

  // 初始化时加载默认素材（项目图层不需要加载素材）
  if (activeMenu.value !== 'project-layer') {
    getMaterialTotal(searchForm.activeLibrary);
  }
});

// 监听场景平台变化
watch(
  () => store.state.editSceneData?.scenePlatform,
  (newPlatform) => {
    if (!platformV2.value && newPlatform) {
      platformV2.value = scenePlatformMap[newPlatform];
      if (platformV2.value) {
        getMaterialTotal(searchForm.activeLibrary);
      }
    }
  }
);

watch(
  () => store.state.activeMaterial,
  (newState: any) => {
    setTimeout(() => {
      const targetElement = document.querySelector('.area-list-container .source-list.active');
      const boxElement = document.querySelector('.area-list-container');

      // 检查元素是否存在
      if (!targetElement || !boxElement) {
        return;
      }

      const targetObj = targetElement.getBoundingClientRect();
      const boxObj = boxElement.getBoundingClientRect();

      // 检查getBoundingClientRect()返回的对象是否有效
      if (!targetObj || !boxObj || targetObj.top === undefined || boxObj.top === undefined) {
        return;
      }

      const disDiff = targetObj.top - (boxObj.top + boxObj.height);
      const st = boxElement.scrollTop || 0;

      if (disDiff > -36) {
        boxElement.scrollTo(0, st + disDiff + 136);
      } else if (targetObj.top - boxObj.top < 60) {
        boxElement.scrollTo(0, st + (targetObj.top - boxObj.top) - 60);
      }
    });
  }
);

watch(isPlanStyle, (newState) => {
  store.state.isPlanStyle = newState;
});

watch(
  () => store.state.activeAreaUuid,
  (newState) => {
    Object.keys(interactionStatus.value).forEach((key: string) => {
      interactionStatus.value[key].active = false;
    });
    if (!interactionStatus.value[newState]) {
      interactionStatus.value[newState] = {
        active: true,
      };
    } else if (newState) {
      interactionStatus.value[newState].active = true;
    }
  }
);

watch(
  () => store.state.randerTotal,
  (newState) => {
    if (newState == 2) {
      store.state.editSceneData.changeType = '';
    }
  }
);

watch(
  () => store.state.showAreaUuidData,
  (newState: any) => {
    if (newState.id) {
      const data = newState.materialMetaDtoList.filter(
        (e: any) => e.uuid == newState.activeChild
      )[0];
      handleSelected(data, newState);
    } else {
      const data = store.state.editSceneData.outerMaterialMetaDtoList.filter(
        (e: any) => e.uuid == newState.activeChild
      )[0];
      handleSelected(data, null);
    }
  }
);

watch(
  () => store.state.showTips,
  (newState) => {
    if (newState) {
      tipsText = newState;
    } else if (newState == '' && tipsText == '已上传的素材取消保存') {
      modalShow.value = false;
      tipsText = '';
    }
  }
);
</script>
<style scoped lang="less">
/* 新增样式：左侧菜单栏 */
.left-menu-bar {
  width: 76px;
  height: 100%;
  background: #fff;
  border-right: 1px solid #dadada;

  display: flex;
  flex-direction: column;

  .menu-items {
    flex: 1;
    padding-top: 8px;
    display: flex;
    flex-direction: column;

    .menu-group {
      border-radius: 4px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 8px;
      min-height: 60px; /* 确保最小高度，防止布局跳动 */
      // 优化：激活时整个菜单组（含二级按钮）背景色、宽度、圆角、居中
      &.active {
        background: #f9f9f9;
        width: 56px;
        border-radius: 4px;
        // padding: 4px 0 4px 0;
        margin: 0 auto 8px auto; /* 保持与普通态相同的底部边距 */
        min-height: 60px; /* 保持与普通态相同的最小高度 */
        .menu-item,
        .secondary-buttons {
          background: transparent;
        }
      }
    }

    .menu-item {
      width: 56px;
      height: 60px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin: 0 auto;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        // background: rgba(54, 113, 254, 0.1);
      }

      // 默认不使用激活样式
      &.active {
        background: transparent;
        color: inherit;
      }

      // 只有项目图层才有蓝色激活样式
      &.project-layer-item.active {
        background: rgba(0, 96, 255, 0.06);
        color: #2e76ff;

        .menu-icon {
          transform: scale(1.1);
        }
      }

      .menu-icon {
        font-size: 20px;
        margin-bottom: 4px;
        transition: transform 0.3s ease;

        img {
          width: 20px;
          height: 20px;
          object-fit: contain;
        }
      }

      .menu-text {
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        line-height: 1.2;
      }
    }

    /* 项目图层菜单项固定位置 */
    .project-layer-item {
      flex-shrink: 0; /* 防止被压缩 */
      order: -1; /* 确保始终在最上方 */
    }

    .secondary-buttons {
      width: 100%;
      padding: 8px 0 0 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      .secondary-button {
        width: 40px;
        box-sizing: border-box;
        height: 26px;
        line-height: 26px;
        text-align: center;
        font-size: 12px;
        color: #666;
        cursor: pointer;
        margin-bottom: 4px;
        border-radius: 4px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 0;
        transform: translateY(-10px);
        animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;

        &:hover {
          background: rgba(0, 96, 255, 0.06);
          color: #000000;
        }

        &.active {
          background: rgba(0, 96, 255, 0.06);
          color: #2e76ff;
        }

        &:last-child {
          margin-bottom: 0;
        }

        /* 为每个按钮添加不同的延迟 */
        &:nth-child(1) {
          animation-delay: 0.1s;
        }
        &:nth-child(2) {
          animation-delay: 0.15s;
        }
        &:nth-child(3) {
          animation-delay: 0.2s;
        }
        &:nth-child(4) {
          animation-delay: 0.25s;
        }
        &:nth-child(5) {
          animation-delay: 0.3s;
        }
      }
    }
  }
}

/* 新增样式：右侧内容区域 */
.right-content-area {
  width: 280px;
  height: 100%;
  background: #fff;
  overflow: hidden;

  .project-layer-content,
  .public-lib-content,
  .personal-lib-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

/* 项目图层内容样式 */
.project-layer-content {
  .capacity-display {
    padding: 0px 0px 12px 0;
    background: #fff;

    .capacity-title {
      font-size: 14px;
      margin: 8px 0;
      display: flex;
      align-self: start;
      padding-left: 16px;
      font-weight: 600;
      font-size: 14px;
      color: #000000;
    }

    .capacity-info {
      display: flex;
      width: 86%;
      margin-left: 8px;
      padding: 6px 8px;
      align-items: center;
      border-top: 1px solid #e9e9e9;
      border-bottom: 1px solid #e9e9e9;
      .capacity-text {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
      }

      .capacity-numbers {
        font-weight: bold;
        font-size: 12px;
        color: #1e1e1e;
        padding: 0 6px;
      }

      .capacity-unit {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
      }
    }
  }

  .project-layer-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-bottom: 1px solid #d0d0d0;

    .layer-title {
      font-weight: 500;
      font-size: 17px;
      color: #414141;
    }

    .add-interaction {
      width: 24px;
      height: 24px;
      background: url(~@/assets/images/icon/add-interaction.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
  }

  .area-list-container {
    flex: 1;
    overflow-y: auto;
    padding: 0px 8px;

    .interaction-draggable-list {
      min-height: 20px;

      .interaction-item {
        cursor: move;
        transition: all 0.3s ease;

        &.sortable-ghost {
          opacity: 0.5;
        }

        &.sortable-chosen {
          background: rgba(54, 113, 254, 0.05);
        }
      }
    }

    .area-header {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      // background: #eaf1ff;
      border-radius: 4px;
      margin-bottom: 4px;
      cursor: pointer;
      user-select: none;

      /* 互动区域标题特有样式 */
      &.interaction-header {
        justify-content: space-between;

        /* 左侧内容区域 */
        .area-left-content {
          display: flex;
          align-items: center;
          flex: 1;
        }
      }

      .expand-arrow {
        font-size: 14px;
        color: #666;
        margin-right: 8px;
        transition: transform 0.3s ease;
        transform: rotate(0deg);
        cursor: pointer;

        &.expanded {
          transform: rotate(90deg);
        }
      }

      /* 互动区域删除图标样式 */
      &.interaction-header .delete-icon {
        width: 16px;
        height: 16px;
        cursor: pointer;
        opacity: 0.6;
        transition: opacity 0.3s ease;
        margin-left: 8px;

        &:hover {
          opacity: 1;
        }
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      span:not(.expand-arrow) {
        font-size: 13px;
        font-weight: 500;
        color: #333;
      }

      &:hover {
        background: rgba(234, 241, 255, 0.5); /* 浅蓝色背景，透明度降低 */
      }

      /* 选中状态的互动区域标题 */
      .selected & {
        background: #eaf1ff;
      }
    }

    .interaction-section {
      margin-bottom: 8px;
      border-radius: 4px;
      transition: background-color 0.3s ease;

      /* 只有在选中状态时才显示蓝色背景 */
      &.selected {
        background: #eaf1ff;
      }
    }

    .material-draggable-list,
    .public-material-draggable-list,
    .interaction-material-draggable-list {
      min-height: 20px;

      .source-list {
        transition: all 0.3s ease;

        &:hover {
          background: rgba(54, 113, 254, 0.05);
        }

        &.sortable-ghost {
          opacity: 0.5;
          background: rgba(54, 113, 254, 0.1);
        }

        &.sortable-chosen {
          background: rgba(54, 113, 254, 0.1);
        }
      }
    }

    .interaction-item {
      margin-bottom: 8px;

      &.active {
        background: rgba(54, 113, 254, 0.1);
        border-radius: 4px;
      }
    }

    .area-name {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 4px;
      cursor: pointer;

      &.has-draw-down {
        background: #e8f4fd;
      }

      &.hide {
        opacity: 0.6;
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      span {
        font-size: 14px;
        color: #414141;
        flex: 1;
      }
    }

    .source-list {
      width: 100%;
      min-height: 26px;
      border-radius: 4px;
      padding: 6px 8px;
      display: grid;
      grid-template-columns: 1fr auto;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      margin-bottom: 2px;
      box-sizing: border-box;

      &:hover {
        background: rgba(54, 113, 254, 0.05);
      }

      &.active {
        background: #e8f4fd;
      }

      .material-info {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 13px;
        color: #666;
        overflow: hidden;
        text-align: left;
        min-width: 0;
        padding-left: 26px;

        .material-type-icon {
          width: 14px;
          height: 14px;
          flex-shrink: 0;
          object-fit: contain;
        }

        .material-name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
        }
      }

      // 兼容旧的样式（如果还有直接使用 div:first-child 的地方）
      div:first-child:not(.material-info) {
        font-size: 13px;
        color: #666;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
        min-width: 0;
      }

      .material-actions {
        display: flex;
        gap: 4px;
        align-items: center;
        min-width: 60px;
        max-width: 60px;
        justify-content: flex-end;
        transition: opacity 0.2s ease;

        .action-btn {
          width: 16px;
          height: 16px;
          cursor: pointer;
          opacity: 0.7;
          transition: opacity 0.3s ease;

          &:hover {
            opacity: 1;
          }

          &.drag-handle {
            cursor: grab;

            &:active {
              cursor: grabbing;
            }
          }
        }
      }
    }
  }
}

/* 公共库内容样式 */
.public-lib-content {
  .lib-header {
    padding: 12px 10px 8px 10px;
    background: #fff;
    display: flex;
    align-items: center;
    gap: 8px;

    .lib-title {
      font-weight: 600;
      font-size: 14px;
      color: #1e1e1e;
    }

    .lib-type-name {
      font-weight: 400;
      font-size: 12px;
      color: #797979;
      flex: 1;
    }

    .debug-btn {
      width: 24px;
      height: 24px;
      border: none;
      background: #f0f0f0;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        background: #e0e0e0;
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .filter-dropdown {
    padding: 0 10px;
    background: #fff;
    display: flex;
    justify-content: flex-start; /* 靠左显示 */

    .filter-select {
      width: 140px;
    }
  }

  .material-grid-container {
    flex: 1;
    overflow-y: auto;
    padding: 12px 8px;

    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    .source-pool {
      width: calc(33.333% - 5px);
      height: 80px;
      margin-bottom: 12px;
      display: inline-block;
      vertical-align: top;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s ease;

      & > div:first-child {
        width: 60px;
        height: 60px;
        background: #dadada;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        border: 1px solid transparent;
        transition: all 0.3s ease;
        margin: 0 auto;

        &.hoverSource {
          border: 1px solid #2e76ff;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .preview {
          position: absolute;
          bottom: 0px;
          right: 0px;
          width: 16px;
          height: 16px;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 2px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;

          &::before {
            content: '👁';
            color: white;
            font-size: 10px;
          }
        }
      }

      .source-name {
        font-size: 11px;
        color: #666;
        text-align: center;
        margin-top: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 1.2;
        height: 26px;
      }
    }
  }
}

/* 个人库内容样式 */
.personal-lib-content {
  .lib-header {
    padding: 12px 10px 8px 10px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .lib-title-group {
      display: flex;
      align-items: center;
      gap: 8px;

      .lib-title {
        font-weight: 600;
        font-size: 14px;
        color: #1e1e1e;
      }

      .lib-type-name {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
      }
    }

    .lib-icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      transition: opacity 0.3s ease;

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .material-grid-container {
    flex: 1;
    overflow-y: auto;
    padding: 12px 8px;

    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    .source-pool.personal-material {
      width: calc(33.333% - 5px);
      height: 80px;
      margin-bottom: 12px;
      display: inline-block;
      vertical-align: top;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s ease;

      & > div:first-child {
        width: 60px;
        height: 60px;
        background: #dadada;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        border: 1px solid transparent;
        transition: all 0.3s ease;
        margin: 0 auto;

        &.hoverSource {
          border: 1px solid #2e76ff;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .preview {
          position: absolute;
          bottom: 0px;
          right: 0px;
          width: 16px;
          height: 16px;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 2px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;

          &::before {
            content: '👁';
            color: white;
            font-size: 10px;
          }
        }

        .purple-badge {
          position: absolute;
          top: -4px;
          right: -4px;
          width: 16px;
          height: 16px;
          background: #8b5cf6;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          z-index: 3;
          border: 2px solid white;
        }
      }

      .source-name {
        font-size: 11px;
        color: #666;
        text-align: center;
        margin-top: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 1.2;
        height: 26px;
      }
    }
  }
}

.dialog-box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: space-around;
  align-items: center;

  & > div {
    position: relative;
    width: 524px;
    height: 186px;
    background: #ffffff;
    box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.27);
    border-radius: 6px;
    padding: 32px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 400;
    font-size: 16px;
    color: #6f6f6f;

    & > .title {
      font-weight: 500;
      font-size: 20px;
      color: #0f0f0f;
      margin-bottom: 14px;
    }

    .sure-btn {
      width: 96px;
      height: 32px;
      line-height: 32px;
      background: linear-gradient(225deg, #0375ff 0%, #3c96ff 100%);
      box-shadow: inset -1px -1px 0px 0px rgba(255, 255, 255, 0.2),
        inset 1px 1px 0px 0px rgba(11, 91, 225, 0.4);
      border-radius: 4px;
      font-weight: 400;
      font-size: 17px;
      color: #ffffff;
      text-align: center;
      position: absolute;
      right: 17px;
      bottom: 21px;
      cursor: pointer;
    }

    .close-icon {
      width: 20px;
      height: 20px;
      background: url(~@/assets/images/icon/close.png);
      background-size: 100% 100%;
      position: absolute;
      right: 17px;
      top: 15px;
      cursor: pointer;
    }
  }
}

.interaction-title {
  margin: 16px 16px 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  font-size: 17px;
  color: #414141;
}

.interaction-title > div:last-child {
  width: 168px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.tab-bar {
  font-weight: bold;
  font-size: 16px;
  color: #414040;
  width: 136px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  overflow: hidden;

  & > div {
    position: relative;
    width: 50%;
    height: 26px;
    line-height: 26px;
    padding-left: 34px;
    box-sizing: border-box;
    text-align: left;
    background: #d1d4d6;
    cursor: pointer;
  }

  & > div.active {
    color: #2e76ff;
    background-color: #f3f4f5;
  }

  & > div:first-child::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 5px;
    width: 16px;
    height: 16px;
    background: url(~@/assets/images/icon/room-icon.png);
    background-size: 100% 100%;
  }

  & > div:last-child::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 5px;
    width: 16px;
    height: 16px;
    background: url(~@/assets/images/icon/plan-icon.png);
    background-size: 100% 100%;
  }

  & > div:first-child.active::before {
    background: url(~@/assets/images/icon/room-iconA.png);
    background-size: 100% 100%;
  }

  & > div:last-child.active::before {
    background: url(~@/assets/images/icon/plan-iconA.png);
    background-size: 100% 100%;
  }
}

.add-interaction {
  width: 24px;
  height: 24px;
  margin-left: 8px;
  background: url(~@/assets/images/icon/add-interaction.png);
  background-size: 100% 100%;
  cursor: pointer;
}

.edit-area-name,
.edit-route-name {
  width: 160px;
  height: 100%;
  box-sizing: border-box;
  display: inline-block;

  ::v-deep(.el-input__inner) {
    color: #fff !important;
    height: 18px;
    line-height: 18px;
    font-weight: 400;

    &::placeholder {
      color: #ddd !important;
    }
  }

  ::v-deep(.el-input__wrapper) {
    background: rgba(0, 0, 0, 0) !important;
    border: none !important;
  }
}

#app .source-type {
  ::v-deep(.el-input__wrapper) {
    height: 34px;
  }

  ::v-deep(.el-input__wrapper .el-input__inner) {
    font-size: 14px;
  }
}

.canvas-left {
  position: fixed;
  left: 0;
  top: 63px;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 356px;
  height: calc(100% - 59px);
  z-index: 10;
  background: #dfe0e3;
  display: flex;
  border-top: 1px solid #dadada;

  .material-library {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .material-library-tab {
    width: 184px;
    border-radius: 6px;
    overflow: hidden;
    background: #d1d4d6;
    height: 34px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    & > div {
      position: relative;
      width: 50%;
      height: 34px;
      line-height: 34px;
      color: #414141;
      font-weight: 500;
      font-size: 14px;
      color: #414141;
      padding-right: 12px;
      box-sizing: border-box;
      text-align: right;
      cursor: pointer;
    }

    & > div:first-child:before {
      content: '';
      position: absolute;
      left: 12px;
      top: 9px;
      width: 18px;
      height: 16px;
      background: url(~@/assets/images/experience-icon/common-library-icon.png);
      background-size: 100% 100%;
    }

    & > div:last-child:before {
      content: '';
      position: absolute;
      left: 12px;
      top: 9px;
      width: 18px;
      height: 16px;
      background: url(~@/assets/images/experience-icon/personal-library.png);
      background-size: 100% 100%;
    }

    & > div.active {
      background-color: rgba(255, 255, 255, 0.56);
      color: #2e76ff;
    }

    & > div:first-child.active:before {
      background: url(~@/assets/images/experience-icon/common-library-iconA.png);
      background-size: 100% 100%;
    }

    & > div:last-child.active:before {
      background: url(~@/assets/images/experience-icon/personal-libraryA.png);
      background-size: 100% 100%;
    }
  }

  .material-type-tab {
    display: flex;
    justify-content: space-between;
    align-items: center;

    & > div {
      padding: 10px 7px 3px;
      font-weight: 500;
      font-size: 14px;
      color: #414141;
      cursor: pointer;
    }

    & > div.active {
      border-bottom: 1px solid #000;
    }
  }
}

.canvas-left.hide-left-side {
  left: -356px;
}

.side-toggle-btn {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  width: auto;
  height: 50px;
  cursor: pointer;
  z-index: 11;
  left: 356px;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.side-hidden {
    left: 0;
  }
}

.left-tabs-content {
  position: relative;
  height: calc(55% - 120px);
  min-height: 232px;

  .add {
    height: 39px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, #ffffff 100%);
    padding-left: 47px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    color: #4a4a4a;
    cursor: pointer;

    span {
      margin-left: 12px;
    }
  }

  .more-list-box {
    padding: 8px 0;
    width: 120px;
    background: #ffffff;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    position: absolute;
    right: -126px;
    top: -10px;
    z-index: 12;
    font-weight: 400;
    font-size: 14px;
    color: #414141;

    & > div {
      height: 32px;
      line-height: 32px;
      text-align: center;
      cursor: pointer;
    }

    & > div:hover {
      background: rgba(54, 113, 254, 0.1);
    }
  }

  .area-list-box {
    padding: 16px 16px 0 16px;
    box-sizing: border-box;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;

    & > div:not(.source-list) {
      position: relative;
      background: rgba(255, 255, 255, 0.234);
      border-radius: 6px;
      padding: 5px 0 1px;
      margin-bottom: 8px;

      .more-list {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 10px;
        top: 10px;
        background: url(~@/assets/images/icon/more-icon.png);
        background-size: 100% 100%;
        cursor: pointer;
      }

      .more-list.active {
        background: url(~@/assets/images/icon/more-iconA.png);
        background-size: 100% 100%;
      }

      .more-list.active {
        background: url(~@/assets/images/icon/more-iconA.png);
        background-size: 100% 100%;
      }

      & > .source-list {
        margin-bottom: 8px;
      }

      & > .source-list:last-child {
        margin-bottom: 0;
      }
    }

    & > div:not(.source-list).active {
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%
      );
    }

    & > div.source-list {
      margin-bottom: 8px;
    }

    .area-name {
      position: relative;
      width: 263px;
      height: 34px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      padding-left: 36px;
      padding-right: 12px;
      color: #414040;
      box-sizing: border-box;
      cursor: pointer;

      & > div {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        img {
          vertical-align: middle;
        }

        span {
          vertical-align: middle;
          display: inline-block;
          height: 18px;
          line-height: 18px;
          margin-left: 8px;
        }

        & > div:first-child {
          width: 36px;
          height: 34px;
          position: absolute;
          left: 0;
          top: 0;
          background-color: transparent;
        }
      }

      .tool-box {
        width: 38px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .delete {
        width: 20px;
        height: 20px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        cursor: pointer;
      }
    }

    .route-list-style {
      margin-bottom: 8px;
    }

    .has-draw-down::before {
      content: '';
      width: 8px;
      height: 8px;
      position: absolute;
      left: 15px;
      top: 12px;
      background: url(~@/assets/images/icon/draw-down.png);
      transform: rotate(0deg);
    }

    .hide.has-draw-down::before {
      transform: rotate(-90deg);
    }

    .source-list {
      height: 14px;
      font-size: 14px;
      font-weight: 400;
      color: #414040;
      padding: 10px 15px 10px 35px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      cursor: pointer;
      margin: 0 15px;

      & > div {
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: keep-all;
        text-align: left;
      }

      &:hover {
        background-color: rgba(60, 150, 255, 0.1);
      }

      &.hoveActive {
        background-color: rgba(60, 150, 255, 0.1);
      }

      &.active {
        background: linear-gradient(225deg, #3c96ff 0%, #0375ff 100%);
        color: #ffffff;
      }

      img {
        width: 14px;
        height: 14px;
        cursor: pointer;
      }
    }
  }
}

.left-source-box {
  padding: 18px 20px 12px;
  border-top: 1px solid rgba(200, 200, 200, 0.6);

  .add-source,
  .add-source-combination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    margin-bottom: 12px;
    height: 32px;

    .add {
      width: 32px;
      height: 32px;
      background: url(~@/assets/images/icon/add.png);
      background-size: 100% 100%;
      position: relative;
      z-index: 1;
      cursor: pointer;

      &:hover {
        &::before {
          content: '\65b0\589e\7d20\6750';
          position: absolute;
          left: -17px;
          top: 39px;
          font-size: 12px;
          width: 66px;
          text-align: center;
          height: 28px;
          line-height: 28px;
          background-color: #333537;
          letter-spacing: 1px;
          border-radius: 4px;
          color: #fff;
        }

        &::after {
          content: '';
          position: absolute;
          left: 10px;
          top: 27px;
          width: 0;
          height: 0;
          border: 6px solid transparent;
          border-bottom-color: #333537;
        }
      }
    }
  }

  .add-source-combination .add:hover::before {
    content: '\65b0\589e\7d20\6750\7ec4\5408';
    width: 90px;
    left: -29px;
  }
}

.source-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-top: 13px;

  .select-default {
    height: 34px;
  }

  & > span {
    font-size: 12px;
    font-weight: 400;
    color: #000000;
  }
}

.source-pool-box {
  height: calc(45% - 65px);
  min-height: 208px;
  padding-left: 20px;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;

  & > div {
    position: relative;
    height: auto;
    overflow: hidden;
    padding-bottom: 52px;
    box-sizing: border-box;

    &.show-loading::after {
      content: '';
      width: 32px;
      height: 32px;
      background-image: url(~@/assets/images/icon/loading-b.png);
      background-size: 100% 100%;
      display: inline-block;
      position: absolute;
      bottom: 10px;
      left: 50%;
      margin-left: -16px;
      animation: rotate 1.5s linear infinite;
    }
  }
}

.source-pool {
  float: left;
  width: 64px;
  height: 84px;
  margin-right: 7px;
  margin-bottom: 20px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  & > div:first-child {
    position: relative;
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 5px;
    margin-bottom: 5px;
    overflow: hidden;

    .default-source {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 14px;
      line-height: 14px;
      background-image: url(~@/assets/images/icon/default-source-icon.png);
      background-size: 100% 100%;
      font-size: 10px;
      font-weight: 600;
      color: #2166ea;
      text-align: center;
    }

    &.hoverSource {
      position: relative;
      background: #d8d8d8
        linear-gradient(180deg, #ffffff 0%, #a8ddff 67%, #2c8cff 100%, #2c8cff 100%);

      .preview {
        width: 12px;
        height: 12px;
        position: absolute;
        right: 0px;
        bottom: 0px;
        background-image: url(~@/assets/images/preview.png);
        background-size: 100% 100%;
        cursor: pointer;
      }

      .default-source {
        background-image: url(~@/assets/images/icon/default-source-iconA.png);
        color: #fff;
      }
    }

    img {
      width: 100%;
      height: 100%;
    }
  }

  & > div:last-child {
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    color: #414040;
  }

  .source-name {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    word-break: break-all;
  }
}

.delete-box {
  margin-top: 22px;
  margin-bottom: 5px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .el-size {
    width: 43px;
    height: 20px;
    font-weight: 400;
    text-shadow: 0px 5px 10px rgba(200, 200, 200, 0.5);
    padding: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;

    span {
      font-size: 10px;
      transform: scale(0.83333);
      transform-origin: 0 0;
    }

    &.btn-sure {
      background: #e84a4b;
      box-shadow: 0px 5px 10px 0px rgba(200, 200, 200, 0.5),
        0px 5px 9px 0px rgba(235, 159, 159, 0.3);
      border-radius: 3px;
      color: #ffffff;
      border: none;

      &:hover {
        background: #e84a4b;
        box-shadow: 0px 0px 4px 4px rgba(255, 35, 35, 0.3);
      }

      &:active {
        background: #cf4243;
        box-shadow: 0px 5px 10px 0px rgba(255, 35, 35, 0.5);
      }
    }

    &.btn-cancle {
      background: #f7f8fa;
      box-shadow: 0px 5px 10px 0px rgba(200, 200, 200, 0.5),
        0px 5px 10px 0px rgba(185, 203, 225, 0.5);
      border-radius: 3px;
      color: #333333;
      border: none;

      &:hover {
        background: #f7f8fa;
        box-shadow: 0px 0px 4px 4px rgba(185, 203, 225, 0.3);
      }

      &:active {
        background: #edeff2;
        box-shadow: 0px 5px 10px 0px rgba(185, 203, 225, 0.5);
      }
    }
  }
}

.popover-btn {
  color: #f23333 !important;
}

/* 媒体查询，适配ipad端和小电脑页面 */
@media screen and (max-width: 1200px) {
  .canvas-left {
    width: 300px;
    transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .left-tabs-content .area-list-box .area-name {
      width: 246px;
    }

    .source-pool {
      width: calc(33.333% - 4px);
      height: 80px;

      & > div:first-child {
        width: 60px;
        height: 60px;
        background: #dadada;
        border-radius: 4px;
        border: 1px solid transparent;
        margin: 0 auto;

        &.hoverSource {
          border: 1px solid #2e76ff;
        }
      }
    }

    .edit-area-name {
      width: 120px;
    }

    .source-pool.personal-material {
      width: calc(33.333% - 4px);
      height: 80px;

      & > div:first-child {
        width: 60px;
        height: 60px;
        background: #dadada;
        border-radius: 4px;
        border: 1px solid transparent;
        margin: 0 auto;
        position: relative;

        &.hoverSource {
          border: 1px solid #2e76ff;
        }

        .purple-badge {
          position: absolute;
          top: -4px;
          right: -4px;
          width: 16px;
          height: 16px;
          background: #8b5cf6;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          z-index: 3;
          border: 2px solid white;
        }
      }
    }
  }

  .canvas-left.hide-left-side {
    left: -300px;
  }

  .side-toggle-btn {
    left: 300px;

    &.side-hidden {
      left: 0;
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.menu-divider {
  width: 40px;
  height: 1px;
  background: #d9d9d9;
  border-radius: 0;
  margin: 4px auto 4px auto;
}

/* 菜单切换动画 */
@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

<style lang="less">
/* 仅在当前页面生效的筛选下拉框样式 */
.canvas-left .public-lib-content .filter-dropdown .filter-select {
  /* 针对 el-select__wrapper */
  .el-select__wrapper {
    background: #f5f5f5 !important;
    background-color: #f5f5f5 !important;
    border: none !important;
    border-radius: 4px !important;
    height: 26px !important;
    box-shadow: none !important;
    width: 140px !important;
  }

  /* 针对 el-input__wrapper (如果存在) */
  .el-input__wrapper {
    background: #f5f5f5 !important;
    background-color: #f5f5f5 !important;
    border: none !important;
    border-radius: 4px !important;
    height: 26px !important;
    box-shadow: none !important;
  }

  .el-input__inner {
    font-size: 12px !important;
    color: #333 !important;
    height: 26px !important;
    line-height: 26px !important;
  }

  .el-input__suffix {
    height: 26px !important;
    line-height: 26px !important;
  }
}
</style>
