import {
  Vector3,
  Raycaster,
  Vector2,
  Matrix4,
  Quaternion,
  LoaderUtils,
  FileLoader,
  LoadingManager,
  Group,
} from 'three';
import JSZip from 'jszip';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import store from '@/store';
import { CLine } from '@/core/primitives';
import axios from 'axios';

// ===== 常量定义 =====
const baseURL = '/api';
const v2 = new Vector2();
const EPSILON = 0.001; // 允许的误差
let arrayBuffers: any = [];
let fileMap: any = {};
let modelUrl: any = '';

// ===== 几何计算工具 =====

/**
 * 计算点到直线的距离（三维空间）
 * @param v1 直线上第一个点
 * @param v2 直线上第二个点
 * @param raycaster 射线投射器，包含目标点信息
 * @returns 点到直线的距离
 */
export const pointToLineDistance = (v1: Vector3, v2: Vector3, raycaster: Raycaster) => {
  const p = (raycaster as any).ray.origin;
  const v12 = v2.clone().sub(v1);
  const width = v12.length(); // AB两点距离
  // ApB构建一个三角形，其中两条边构建向量a、向量b
  const a = v1.clone().sub(p);
  const b = v2.clone().sub(p);
  const c = a.clone().cross(b);
  const H = c.length() / width; // 点到直线的距离
  return H;
};

/**
 * 计算点到直线的距离（二维空间）
 * @param p1 直线上第一个点
 * @param p2 直线上第二个点
 * @param p3 目标点
 * @returns 点到直线的距离
 */
export const pointToLine = (p1: Vector2, p2: Vector2, p3: Vector2) => {
  let len;

  // 如果p1.x==p2.x 说明是条竖着的线
  if (p1.x - p2.x == 0) {
    len = Math.abs(p3.x - p1.x);
  } else {
    const A = (p1.y - p2.y) / (p1.x - p2.x);
    const B = p1.y - A * p1.x;

    len = Math.abs((A * p3.x + B - p3.y) / Math.sqrt(A * A + 1));
  }
  return len;
};

// ===== 坐标转换工具 =====

/**
 * 屏幕坐标转世界坐标
 * @param evX 屏幕X坐标
 * @param evY 屏幕Y坐标
 * @returns 世界坐标Vector3
 */
export const screenToWorldVector = (evX: number, evY: number) => {
  const width = window.innerWidth;
  const height = window.innerHeight;
  const x = evX;
  const y = evY;
  const x1 = (x / width) * 2 - 1;
  const y1 = -(y / height) * 2 + 1;
  const stdVector = new Vector3(x1, y1, 0.5);
  const worldVector = stdVector.unproject((window as any).camera);
  worldVector.x = +worldVector.x.toFixed(4);
  worldVector.y = 0;
  worldVector.z = +worldVector.z.toFixed(4);
  return worldVector;
};

/**
 * 世界坐标转屏幕坐标
 * @param point 世界坐标点
 * @returns 屏幕坐标Vector2
 */
export const worldToScreenVector = (point: Vector3) => {
  const worldVector = point.clone();
  const vector = worldVector.project((window as any).camera); // 通过世界坐标获取转标准设备坐标
  const width = window.innerWidth;
  const height = window.innerHeight;
  const w = width / 2;
  const h = height / 2;
  const x = Math.round(vector.x * w + w); // 标准设备坐标转屏幕坐标
  const y = Math.round(-vector.y * h + h);
  return new Vector2(x, y);
};

/**
 * 将3D点转化为2D点
 * @param point 3D点
 * @param matrix 变换矩阵
 * @returns 2D点Vector2
 */
export const point2DByPoint3D = (point: Vector3, matrix: Matrix4) => {
  const matrixInverse = matrix.clone().invert();
  const p = point.clone().applyMatrix4(matrixInverse);
  return new Vector2(p.x, p.y);
};

/**
 * 将2D点转化为3D点
 * @param point 2D点
 * @param matrix 变换矩阵
 * @returns 3D点Vector3
 */
export const point3DByPoint2D = (point: Vector2, matrix: Matrix4) => {
  const p = new Vector3(point.x, point.y, 0).applyMatrix4(matrix);
  return p;
};

// ===== 几何面积计算 =====

/**
 * 三角形面积计算公式
 * @param p1 三角形顶点1
 * @param p2 三角形顶点2
 * @param p3 三角形顶点3
 * @returns 三角形面积
 */
export const areaOfTriangle = (p1: Vector3, p2: Vector3, p3: Vector3) => {
  let v1 = new Vector3();
  let v2 = new Vector3();

  // 通过两个顶点坐标计算其中两条边构成的向量
  v1 = p1.clone().sub(p2);
  v2 = p1.clone().sub(p3);
  const v3 = new Vector3();

  // 三角形面积计算
  v3.crossVectors(v1, v2);
  const s = v3.length() / 2;
  return s;
};

// ===== 线段交点计算 =====

/**
 * 2D：计算两条线段的交点
 * @param linePoints1 第一条线段的两个端点
 * @param linePoints2 第二条线段的两个端点
 * @returns 交点坐标Vector2，如果没有交点则返回undefined
 */
export function cacCrossPointLineWithLine(
  linePoints1: Array<Vector2>,
  linePoints2: Array<Vector2>
) {
  let q1, q2, p1, p2;
  if (linePoints1 instanceof Array) {
    q1 = new Vector2(linePoints1[0].x, linePoints1[0].y);
    q2 = new Vector2(linePoints1[1].x, linePoints1[1].y);
  } else {
    throw new Error('cacCrossPointLineWithLine data error.');
  }

  if (linePoints2 instanceof Array) {
    p1 = new Vector2(linePoints2[0].x, linePoints2[0].y);
    p2 = new Vector2(linePoints2[1].x, linePoints2[1].y);
  } else {
    throw new Error('cacCrossPointLineWithLine data error.');
  }
  let k1, k2, b1, b2;
  let x0, y0;
  if (numbersEqual(q1.x, q2.x)) {
    if (numbersEqual(p1.x, p2.x)) {
      return;
    } else {
      k2 = (p1.y - p2.y) / (p1.x - p2.x);
      b2 = p2.y - k2 * p2.x;
      x0 = q1.x;
      y0 = q1.x * k2 + b2;
    }
  } else {
    k1 = (q1.y - q2.y) / (q1.x - q2.x);
    b1 = q1.y - k1 * q1.x;
    if (numbersEqual(p1.x, p2.x)) {
      x0 = p1.x;
      y0 = p1.x * k1 + b1;
    } else {
      k2 = (p1.y - p2.y) / (p1.x - p2.x);
      b2 = p2.y - k2 * p2.x;
      x0 = (b2 - b1) / (k1 - k2);
      y0 = k1 * x0 + b1;
    }
  }

  // 直线不用判断点是否在线段上
  if (x0 !== undefined && y0 !== undefined) {
    return v2.clone().set(x0, y0);
  }
}

/**
 * 判断两个数字是否相等（考虑浮点数误差）
 * @param n1 第一个数字
 * @param n2 第二个数字
 * @returns 是否相等
 */
export function numbersEqual(n1: number, n2: number) {
  return Math.abs(n1 - n2) < EPSILON;
}

// ===== 向量偏移计算 =====

/**
 * 已知两点的位置，求在法线为normal方向上向（内、外）偏移d距离的另外两点位置
 * @param v1 第一个点
 * @param v2 第二个点
 * @param normal 法线方向
 * @param d 偏移距离
 * @returns 偏移后的两个点 [点1, 点2]
 */
export const getOffsetPoint = (v1: Vector3, v2: Vector3, normal: Vector3, d: number) => {
  const p1 = v1.clone();
  const p2 = v2.clone();
  const p3 = p1.clone().add(normal.clone().setLength(10));
  const ab = p1.clone().sub(p2);
  const ac = p1.clone().sub(p3);
  const cnormal = ab.clone().cross(ac);

  const cp1 = p1.clone().add(cnormal.clone().setLength(d));
  const cp2 = p2.clone().add(cnormal.clone().setLength(d));

  return [cp1, cp2];
};

// ===== 角度计算工具 =====

/**
 * 计算二维向量的夹角
 * @param m1 向量1
 * @param m2 向量2
 * @returns 角度（弧度）
 */
export const getAngle = (m1: Vector2, m2: Vector2) => {
  const dot = m1.x * m2.x + m1.y * m2.y;
  const det = m1.x * m2.y - m1.y * m2.x;
  const angle = (Math.atan2(det, dot) / Math.PI) * 180; // 顺时针为正，逆时针为负
  // return Math.round(angle + 360) % 360;
  return ((Math.round(angle + 360) % 360) * Math.PI) / 180; // 角度转弧度
};

/**
 * 弧度转角度
 * @param radians 弧度值
 * @returns 角度值
 */
export const radiansToAngle = (radians: number) => {
  const degrees = (radians * (180 / Math.PI)) % 360;
  return +degrees.toFixed(12);
};

/**
 * 角度转弧度
 * @param angle 角度值
 * @returns 弧度值
 */
export const angleToRadians = (angle: number) => {
  const degrees = angle * (Math.PI / 180);
  return +degrees.toFixed(2);
};

// ===== 字符串处理工具 =====

/**
 * 获取字符串结尾的数字，便于生成新名称
 * @param str 输入字符串
 * @returns 字符串结尾的数字
 */
export const cropNumber = (str: string) => {
  if (typeof str === 'string') {
    const arr = str.match(/[1-9]\d{0,}/g) || '';
    return arr.slice(-1)[0];
  } else {
    return '';
  }
};

// ===== 3D变换工具 =====

/**
 * 四元数旋转生成新的点位
 * @param startPoint 起始点
 * @param centerPoint 旋转中心点
 * @param angle 旋转角度（弧度）
 * @returns 旋转后的新点位
 */
export const pointRotate = (startPoint: Vector3, centerPoint: Vector3, angle: number) => {
  // 起始点， 旋转点, 旋转弧度
  const quaternion = new Quaternion();
  quaternion.setFromAxisAngle(new Vector3(0, 1, 0), -angle);
  const targetPoint = startPoint
    .clone()
    .sub(centerPoint.clone())
    .applyQuaternion(quaternion)
    .clone()
    .add(centerPoint.clone());
  targetPoint.x = +targetPoint.x.toFixed(4);
  targetPoint.y = +targetPoint.y.toFixed(4);
  targetPoint.z = +targetPoint.z.toFixed(4);
  return targetPoint;
};

/**
 * 画导览路线的吸附功能
 * @param vertexs 顶点数组
 * @param currentAngle 当前角度
 * @param lastPointObject 最后一个点对象
 * @returns 处理后的顶点数组
 */
export const adsorption = (vertexs: Array<Vector3>, currentAngle: number, lastPointObject: any) => {
  // currentAngle表示辅助线的旋转角度
  const currentVertexs = vertexs.map((e: any) => e.clone());
  const lastPoints = [...currentVertexs].slice(-2);
  if (lastPoints.length == 2) {
    const pz = lastPoints[0].clone().add(new Vector3(0, 0, -1));
    const p1 = worldToScreenVector(pz);
    const p2 = worldToScreenVector(lastPoints[0]);
    const p3 = worldToScreenVector(lastPoints[1]);
    const v1 = p1.clone().sub(p2);
    const v2 = p3.clone().sub(p2);
    const angle = getAngle(v1, v2);
    if (Math.abs(angle - currentAngle) < 0.087) {
      // 判断吸附条件,大概在10度范围内吸附
      const len = lastPoints[0].distanceTo(lastPoints[1]);
      const targetPoint = pointRotate(pz, lastPoints[0], currentAngle);
      const normal = targetPoint.clone().sub(lastPoints[0]).normalize();
      const lastPoint = lastPoints[0].clone().add(normal.setLength(len));
      lastPoint.x = +lastPoint.x.toFixed(4);
      lastPoint.y = +lastPoint.y.toFixed(4);
      lastPoint.z = +lastPoint.z.toFixed(4);
      currentVertexs[currentVertexs.length - 1] = lastPoint.clone();

      const p4 = worldToScreenVector(lastPoints[0].clone().add(new Vector3(1, 0, 0)));
      const angle2 = getAngle(p3.clone().sub(p2), p4.clone().sub(p2));
      lastPointObject.setRotate(angle2);
      lastPointObject.setVertex(lastPoint);
    }
  }
  return currentVertexs;
};

// ===== 通用工具函数 =====

/**
 * 转换字符串为小写
 * @param str 输入字符串
 * @returns 小写字符串
 */
export const convertToLowerCase = (str: string) => {
  return str.toLowerCase();
};

/**
 * 字节数的单位转换（带HTML标签）
 * @param value 字节数值
 * @returns 格式化后的字符串（包含HTML标签）
 */
export const calculateSize = (value: any) => {
  if (value < 1024) {
    return value + "<span class='font20'>MB</span>";
  } else {
    if (value / (1024 * 1024) < 1) {
      return +(value / 1024).toFixed(2) + "<span class='font20'>GB</span>";
    } else {
      return +(value / (1024 * 1024)).toFixed(2) + "<span class='font20'>TB</span>";
    }
  }
};

/**
 * 字节数的单位转换（纯文本）
 * @param value 字节数值
 * @returns 格式化后的字符串
 */
export const calculateSize_ = (value: any) => {
  if (value < 1024) {
    return value + 'MB';
  } else {
    if (value / (1024 * 1024) < 1) {
      return +(value / 1024).toFixed(2) + 'GB';
    } else {
      return +(value / (1024 * 1024)).toFixed(2) + 'TB';
    }
  }
};

// ===== 文件处理工具 =====

/**
 * 加载并解析ZIP文件为3D模型
 * @param url ZIP文件URL
 * @param callback 加载完成后的回调函数
 */
export const loadZipFileForJSZip = async (url: string, callback: any) => {
  fileMap = {};
  await loadZipFile(url);
  await fileToBlob(url);
  findFile();
  runLoader((glb: any) => {
    callback(glb);
  });
};

/**
 * 生成UUID
 * @returns 唯一标识符字符串
 */
export const createUuid = () => {
  const s: any = [];
  const hexDigits = '0123456789abcdef';
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-';

  const uuid = s.join('');
  return uuid;
};

/**
 * 加载ZIP文件为ArrayBuffer
 * @param url 文件URL
 * @returns Promise<ArrayBuffer>
 */
const loadZipFile = (url: string) => {
  return new Promise((resolve) => {
    const fileLoader = new FileLoader();
    fileLoader.setResponseType('arraybuffer').load(url, (data) => {
      arrayBuffers = data;
      resolve(data);
    });
  });
};

/**
 * 将ZIP文件内容转换为Blob对象
 * @param url 文件URL
 */
const fileToBlob = async (url: string) => {
  // zip.js加载文件流生成对应文件:
  const zip = new JSZip();
  const promise: any = JSZip.external.Promise;
  const baseUrl = 'blob:' + LoaderUtils.extractUrlBase(url);
  const pendings = [];
  await zip.loadAsync(arrayBuffers);
  // 转成blob文件，用URL.createObjectURL创建文件的url
  for (const file in zip.files) {
    const entry = zip.file(file);
    if (entry === null) continue;
    pendings.push(
      entry.async('blob').then(
        ((file: any, blob: any) => {
          fileMap[baseUrl + file] = URL.createObjectURL(blob);
        }).bind(this, file)
      )
    );
  }
  // 监听所有请求结束
  await promise.all(pendings);
};

/**
 * 查找ZIP文件中的模型文件
 */
const findFile = () => {
  // 模型文件url
  const urlArrs = Object.keys(fileMap);
  const urlFormat = urlArrs.map((e: any) => e.split('/').length) || [];
  urlArrs.forEach((e) => {
    if (
      e.includes('MACOSX') &&
      Object.keys(fileMap).length > 1 &&
      Math.min(...urlFormat) != e.split('/').length
    ) {
      delete fileMap[e];
    }
  });
  modelUrl = Object.keys(fileMap).find((item) => /\.(glb)$/.test(item));
};

/**
 * 运行GLB模型加载器
 * @param callback 加载完成后的回调函数
 */
const runLoader = (callback: any) => {
  const manager = new LoadingManager();
  // 转换处理，传入的是后台返回的路径，需找到对应blob
  manager.setURLModifier((url) => {
    return fileMap[url] ? fileMap[url] : url;
  });

  const loader = new GLTFLoader(manager);
  loader.load(modelUrl, (glb) => {
    callback(glb);
  });
};

// ===== 场景数据查询工具 =====

/**
 * 通过模型名字与uuid关联来找对应的模型
 * @param editSceneData 场景编辑数据
 * @param activeModelName 激活的模型名称
 * @returns 搜索到的模型信息
 */
export const searchMaterialFromUuid = (editSceneData: any, activeModelName: string) => {
  let searchInfo: any = {};
  if (editSceneData.outerMaterialMetaDtoList) {
    editSceneData.outerMaterialMetaDtoList.forEach((ele: any, index: number) => {
      if (ele.uuid == activeModelName) {
        searchInfo = { ele, key: 'outerMaterialMetaDtoList', index };
      }
    });
  }
  if (editSceneData.interactionDtoList && !searchInfo.key) {
    editSceneData.interactionDtoList.forEach((ele: any, index: number) => {
      if (ele.materialMetaDtoList) {
        ele.materialMetaDtoList.forEach((e: any, i: number) => {
          if (e.uuid == activeModelName) {
            searchInfo = { ele: e, key: 'interactionDtoList', index, i, data: ele };
          }
        });
      }
    });
  }
  return searchInfo;
};

/**
 * 根据场景选中元素的名字查询对应的信息
 * @returns 搜索到的素材数据
 */
export const searchMaterial = () => {
  const activeMaterial = store.state.activeMaterial;
  const editSceneData = { ...store.state.editSceneData };
  let searchData = null;
  if (editSceneData.outerMaterialMetaDtoList) {
    editSceneData.outerMaterialMetaDtoList.forEach((ele: any) => {
      if (ele.uuid == activeMaterial) {
        searchData = { ...ele };
      }
    });
  }
  if (editSceneData.interactionDtoList) {
    editSceneData.interactionDtoList.forEach((ele: any) => {
      if (ele.materialMetaDtoList) {
        ele.materialMetaDtoList.forEach((e: any) => {
          if (e.uuid == activeMaterial) {
            searchData = { ...e };
          }
        });
      }
    });
  }

  return searchData;
};

/**
 * 通过素材uuid查询素材在场景中的位置索引
 * @param materialName 素材UUID
 * @returns 位置索引字符串
 */
export const searchMaterialIndex = (materialName: string) => {
  let dataIndex = '';
  store.state.editSceneData.interactionDtoList.forEach((ele: any, index: number) => {
    if (ele.materialMetaDtoList) {
      ele.materialMetaDtoList.forEach((e: any, i: number) => {
        if (e.uuid == materialName) {
          dataIndex = `${index},${i}`;
        }
      });
    }
  });
  store.state.editSceneData.outerMaterialMetaDtoList.forEach((e: any, i: number) => {
    if (e.uuid == materialName) {
      dataIndex = i + '';
    }
  });
  return dataIndex;
};

/**
 * base64转成二进制数据流
 * @param base64Data base64编码的图片数据
 * @param fileName 文件名（可选）
 * @returns FormData对象
 */
export const base64ToBlob = (base64Data: string, fileName?: string) => {
  const base64 = base64Data.replace(/^data:image\/\w+;base64,/, '');
  const binaryString = window.atob(base64);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  const blob = new Blob([bytes], { type: 'image/png' });

  const data = new FormData();
  data.append(fileName || 'file', blob, 'image.png');
  return data;
};

// ===== 3D渲染工具 =====

/**
 * 创建3D边界框线条
 * @param boxInfo 边界框信息
 * @param color 线条颜色
 * @param lineWidth 线条宽度
 * @returns Three.js Group对象
 */
export const createBox = (boxInfo: any, color: number, lineWidth: number) => {
  const topPoints = [
    new Vector3(boxInfo.min.x, boxInfo.max.y, boxInfo.max.z),
    new Vector3(boxInfo.max.x, boxInfo.max.y, boxInfo.max.z),
    new Vector3(boxInfo.max.x, boxInfo.max.y, boxInfo.min.z),
    new Vector3(boxInfo.min.x, boxInfo.max.y, boxInfo.min.z),
  ];
  const bottomPoints = [
    new Vector3(boxInfo.min.x, boxInfo.min.y, boxInfo.max.z),
    new Vector3(boxInfo.max.x, boxInfo.min.y, boxInfo.max.z),
    new Vector3(boxInfo.max.x, boxInfo.min.y, boxInfo.min.z),
    new Vector3(boxInfo.min.x, boxInfo.min.y, boxInfo.min.z),
  ];
  const group = new Group();
  const clineTop = new CLine({
    vertexs: [...topPoints, topPoints[0]],
    color,
    lineWidth,
    reversalDepthTest: true,
  });
  const clineBottom = new CLine({
    vertexs: [...bottomPoints, bottomPoints[0]],
    color,
    lineWidth,
    reversalDepthTest: true,
  });
  const sideLine1 = new CLine({
    vertexs: [topPoints[0], bottomPoints[0]],
    color,
    lineWidth,
    reversalDepthTest: true,
  });
  const sideLine2 = new CLine({
    vertexs: [topPoints[1], bottomPoints[1]],
    color,
    lineWidth,
    reversalDepthTest: true,
  });
  const sideLine3 = new CLine({
    vertexs: [topPoints[2], bottomPoints[2]],
    color,
    lineWidth,
    reversalDepthTest: true,
  });
  const sideLine4 = new CLine({
    vertexs: [topPoints[3], bottomPoints[3]],
    color,
    lineWidth,
    reversalDepthTest: true,
  });
  group.add(clineTop);
  group.add(clineBottom);
  group.add(sideLine1);
  group.add(sideLine2);
  group.add(sideLine3);
  group.add(sideLine4);
  return group;
};

/**
 * 重置模型尺寸为最大值为1m时的原始缩放比例，并存入userData里
 * @param boxInfo 边界框信息
 * @param obj 3D对象
 */
export const initSize = (boxInfo: any, obj: any) => {
  const xLength = boxInfo.max.x - boxInfo.min.x;
  const yLength = boxInfo.max.y - boxInfo.min.y;
  const zLength = boxInfo.max.z - boxInfo.min.z;
  const xyzArray = [xLength, yLength, zLength];
  let maxLength = Math.max(...xyzArray);
  if (maxLength < 0.05) {
    const dataRange: any = {
      x: [0, 0],
      y: [0, 0],
      z: [0, 0],
    };
    obj.traverse((e: any) => {
      if (e.geometry) {
        const boundingBox = e.geometry.boundingBox;
        Object.keys(dataRange).forEach((key: any) => {
          if (!dataRange[key][0] || boundingBox.min[key] < dataRange[key][0]) {
            dataRange[key][0] = boundingBox.min[key];
          }
          if (!dataRange[key][1] || boundingBox.max[key] > dataRange[key][1]) {
            dataRange[key][1] = boundingBox.max[key];
          }
        });
      }
    });
    maxLength = Math.max(
      dataRange.x[1] - dataRange.x[0],
      dataRange.y[1] - dataRange.y[0],
      dataRange.z[1] - dataRange.z[0]
    );
  }
  obj.userData.initScale = 1;
};

/**
 * 创建地面网格线
 * @param ground 地面对象
 * @param len 网格线长度
 * @param isPlan 是否为平面模式
 */
export const createGroundLine = (ground: any, len: number, isPlan?: boolean) => {
  for (let i = 1; i < 100; i++) {
    const cline1 = new CLine({
      vertexs: [new Vector3(-len, i, 0), new Vector3(len, i, 0)],
      color: 0xffffff,
      lineWidth: 1,
      reversalDepthTest: true,
      transparent: !isPlan,
    });
    const cline2 = new CLine({
      vertexs: [new Vector3(-len, -i, 0), new Vector3(len, -i, 0)],
      color: 0xffffff,
      lineWidth: 1,
      reversalDepthTest: true,
      transparent: !isPlan,
    });
    const cline3 = new CLine({
      vertexs: [new Vector3(i, -len, 0), new Vector3(i, len, 0)],
      color: 0xffffff,
      lineWidth: 1,
      reversalDepthTest: true,
      transparent: !isPlan,
    });
    const cline4 = new CLine({
      vertexs: [new Vector3(-i, -len, 0), new Vector3(-i, len, 0)],
      color: 0xffffff,
      lineWidth: 1,
      reversalDepthTest: true,
      transparent: !isPlan,
    });
    ground.add(cline1, cline2, cline3, cline4);
  }
  const cline0 = new CLine({
    vertexs: [new Vector3(-len, 0, 0), new Vector3(len, 0, 0)],
    color: 0xffffff,
    lineWidth: 3,
    reversalDepthTest: true,
    transparent: !isPlan,
  });
  const cline0_v = new CLine({
    vertexs: [new Vector3(0, -len, 0), new Vector3(0, len, 0)],
    color: 0xffffff,
    lineWidth: 3,
    reversalDepthTest: true,
    transparent: !isPlan,
  });
  ground.add(cline0, cline0_v);
};

/**
 * 根据交互元素名称切换激活区域
 * @param intersectName 交互元素名称
 */
export const changeActiveWithArea = (intersectName: string) => {
  const showAreaUuidData = store.state.editSceneData.interactionDtoList.filter((area: any) => {
    const targetObj = area.materialMetaDtoList.filter((o: any) => o.uuid == intersectName)[0];
    return targetObj;
  })[0];

  if (showAreaUuidData) {
    store.state.showAreaUuidData = { ...showAreaUuidData, activeChild: intersectName };
  } else {
    (store.state.showAreaUuidData as any) = { activeChild: intersectName };
  }
};

// ===== 内容审核工具 =====

/**
 * 敏感词检测
 * @param value 待检测的文本
 * @param title 错误提示标题
 * @returns Promise<boolean> 是否包含敏感词
 */
export const desensitizte = async (value: string, title: string) => {
  return new Promise(async (resolve) => {
    const res = await axios.post(baseURL + '/sensitive/sensitiveWord/contains?text=' + value);
    const hasSensitiveWords = res.data.data;
    if (hasSensitiveWords) {
      store.state.showTips = title;
      resolve(true);
    }
    resolve(false);
  });
};

// ===== 时间处理工具 =====

/**
 * 获取格式化的日期时间字符串
 * @param time 时间戳或Date对象（可选）
 * @param showTime 是否显示时间部分
 * @returns 格式化的日期时间字符串
 */
export const getDayTime = (time?: any, showTime?: boolean) => {
  if (!time) return '';
  const times = time ? new Date(time) : new Date();
  const y = times.getFullYear();
  const m = times.getMonth() + 1;
  const d = times.getDate();
  const h = times.getHours();
  const ms = times.getMinutes();
  if (showTime) {
    return `${y}-${String(m).padStart(2, '0')}-${String(d).padStart(2, '0')} ${String(h).padStart(
      2,
      '0'
    )}:${String(ms).padStart(2, '0')}`;
  } else {
    return `${y}-${String(m).padStart(2, '0')}-${String(d).padStart(2, '0')}`;
  }
};

/**
 * 格式化时间字符串为年月日格式
 * @param timeStr 时间字符串
 * @returns 格式化后的年月日字符串 (YYYY-MM-DD)
 */
export const formatDateOnly = (timeStr?: string | null) => {
  if (!timeStr) return '';
  try {
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) return '';
    const y = date.getFullYear();
    const m = date.getMonth() + 1;
    const d = date.getDate();
    return `${y}-${String(m).padStart(2, '0')}-${String(d).padStart(2, '0')}`;
  } catch (error) {
    return '';
  }
};

/**
 * 格式化时间字符串为年月日时分格式
 * @param timeStr 时间字符串
 * @returns 格式化后的年月日时分字符串 (YYYY-MM-DD HH:mm)
 */
export const formatDateTime = (timeStr?: string | null) => {
  if (!timeStr) return '';
  try {
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) return '';
    const y = date.getFullYear();
    const m = date.getMonth() + 1;
    const d = date.getDate();
    const h = date.getHours();
    const ms = date.getMinutes();
    return `${y}-${String(m).padStart(2, '0')}-${String(d).padStart(2, '0')} ${String(h).padStart(
      2,
      '0'
    )}:${String(ms).padStart(2, '0')}`;
  } catch (error) {
    return '';
  }
};

// ===== 加密解密工具 =====

/**
 * AES解密
 * @param word 待解密的密文
 * @returns 解密后的明文
 */
export const decrypt = (word: any) => {
  const CryptoJS = (window as any).CryptoJS;
  const key = CryptoJS.enc.Utf8.parse('1234567890123456');
  // 使用CryptoJS进行AES解密
  const options = {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  };
  const decryptedText = CryptoJS.AES.decrypt(word, key, options).toString(CryptoJS.enc.Utf8);
  return decryptedText;
};
