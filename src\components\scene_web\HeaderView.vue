<template>
  <div class="edit-title">
    <div class="header-left">
      <div class="back-btn" @click="goHome">
        <el-icon><ArrowLeft /></el-icon>
        <span>返回</span>
      </div>
      <span class="scene-name">{{ store.state.editSceneData.sceneName }}</span>
    </div>

    <!-- 中间编辑工具区域 -->
    <div class="header-center">
      <!-- 固定位置的模式切换按钮 -->
      <div class="mode-toggle-fixed">
        <div :class="['mode-btn', !store.state.isPlanStyle ? 'active' : '']" @click="switchTo3D">
          3D模式
        </div>
        <div :class="['mode-btn', store.state.isPlanStyle ? 'active' : '']" @click="switchTo2D">
          2D模式
        </div>
      </div>

      <!-- 动态工具区域 -->
      <div class="dynamic-tools-area">
        <!-- 2D图标（仅在2D模式显示） -->
        <div class="mode-2d-icon" v-if="store.state.isPlanStyle">
          <div class="edit-tool-btn" @click="handleCreateInteraction">
            <img src="@/assets/images/2dtj.png" alt="2D模式图标" />
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="divider"></div>

        <!-- 编辑工具组 -->
        <div class="edit-tools">
          <template v-for="(tool, index) in editTools" :key="index">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="tool.name === '全屏' && has_full ? '退出全屏' : tool.title"
              placement="bottom">
              <div
                :class="['edit-tool-btn', activeEditTool === tool.name ? 'active' : '']"
                @click="handleEditTool(tool.name)">
                <img
                  :src="
                    (activeEditTool === tool.name && tool.activeIcon) ||
                    (tool.name === '全屏' && has_full && tool.activeIcon)
                      ? tool.activeIcon
                      : tool.icon
                  "
                  :alt="tool.title" />
              </div>
            </el-tooltip>

            <!-- 在全屏工具后添加分隔线 -->
            <div v-if="tool.name === '全屏'" class="divider"></div>
          </template>
        </div>
      </div>
      <!-- dynamic-tools-area 结束 -->
    </div>

    <div class="header-right">
      <div class="share-btn" @click="shareScene" :class="{ loadBtn: isLoading }">
        <span style="color: #2e76ff">分享</span>
      </div>
      <div id="save-edit-btn" class="save-btn" @click="() => saveEditInfo()">
        <span v-if="!saveloading">保存</span>
        <img v-if="saveloading" src="@/assets/images/icon/loading.png" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, defineExpose } from 'vue';
import {
  saveScene,
  updateSceneMeta,
  getUserTypeByToken,
  updateWxIdentifyAttachInfo,
} from '@/api/index';
import { useStore } from 'vuex';
import { ElMessage, ElIcon } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import TipsView from '@/components/TipsView.vue';
import { Quaternion, Vector3, Matrix4 } from 'three';
import SelfProgress from '@/components/SelfProgress.vue';
import { cropNumber } from '@/utils/index';

const scene = ref();

// 创建互动区域相关变量
let isAddPlaying = false;

const props = defineProps({
  showShare: {
    default: null,
    type: Function,
  },
  sceneData: {
    default: null,
    type: Object,
  },
});

const isLoading = ref(false);
const isOpenTip = ref(false);
const saveloading = ref(true); // 场景保存是否在请求中
const submitData: any = {}; // 要提交的数据
const router = useRouter();
const userType = ref(-1);
const totalSize = ref(0);

const store = useStore();

// 编辑工具相关
const activeEditTool = ref('');
const has_full = ref(false);
const editTools = ref([
  {
    name: '移动旋转',
    icon: require('@/assets/images/edith1.png'),
    activeIcon: require('@/assets/images/edit6.png'),
    title: '移动旋转',
  },
  {
    name: '缩放',
    icon: require('@/assets/images/edith2.png'),
    activeIcon: require('@/assets/images/edit7.png'),
    title: '缩放',
  },
  {
    name: '全屏',
    icon: require('@/assets/images/edith3.png'),
    activeIcon: require('@/assets/images/edit8.png'),
    title: '全屏',
  },
  {
    name: '文本',
    icon: require('@/assets/images/edith4.png'),
    title: '文本',
  },
  {
    name: '遮罩',
    icon: require('@/assets/images/edith5.png'),
    title: '遮罩',
  },
]);

// 切换3D/2D模式
const switchTo3D = () => {
  store.state.isPlanStyle = false;
};

const switchTo2D = () => {
  store.state.isPlanStyle = true;
};

// 处理编辑工具点击
const handleEditTool = (toolName: string) => {
  if (toolName === '全屏') {
    let element = document.documentElement;
    if (!has_full.value) {
      element.requestFullscreen && element.requestFullscreen();
    } else {
      document.exitFullscreen && document.exitFullscreen();
    }
    return;
  }

  // 工具4和工具5暂时无操作
  if (toolName === '工具4' || toolName === '工具5') {
    return;
  }

  activeEditTool.value = toolName;
  // 更新 store 中的操作类型
  if (toolName === '移动旋转') {
    // 移动旋转工具，设置为统一的"移动旋转"模式
    store.state.operateType = '移动旋转';
  } else {
    store.state.operateType = toolName;
  }

  console.log(`🎮 HeaderView: 切换到操作模式 "${store.state.operateType}"`);
  console.log(`🔧 工具名称: "${toolName}"`);
  console.log(`✅ 激活的编辑工具: "${activeEditTool.value}"`);

  // 如果是移动旋转模式，提供额外的提示信息
  if (toolName === '移动旋转') {
    console.log('📝 移动旋转模式说明:');
    console.log('  - 可以拖拽箭头进行移动操作');
    console.log('  - 可以拖拽旋转环进行旋转操作');
    console.log('  - 两种操作在同一模式下都可用');
  }
};

/**
 * 处理2D图标点击，创建互动区域
 */
const handleCreateInteraction = async () => {
  if (isAddPlaying)
    return ElMessage({ type: 'warning', message: '上一个互动区域正在生成中，请稍等...' });
  isAddPlaying = true;

  const editSceneData = store.state.editSceneData;
  let interactionName = '';

  if (!editSceneData.interactionDtoList || !editSceneData.interactionDtoList.slice(-1)[0]) {
    interactionName = '互动区域01';
  } else {
    const nameNumber = +cropNumber(editSceneData.interactionDtoList.slice(-1)[0]?.interactionName);
    if (nameNumber) {
      interactionName =
        editSceneData.interactionDtoList
          .slice(-1)[0]
          .interactionName.slice(0, -(nameNumber + '').length) +
        (nameNumber + 1);
    } else {
      interactionName = editSceneData.interactionDtoList.slice(-1)[0].interactionName + '01';
    }
  }

  await saveEditInfo();

  const lastInteraction = {
    location: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    scale: { x: 1, y: 1, z: 1 },
    materialMetaDtoList: [],
    materialGroupDtoList: [],
    interactionName,
    flag: 'add',
  };

  const interactionDtoList = [...(editSceneData.interactionDtoList || [])];
  interactionDtoList.push(lastInteraction);
  editSceneData.interactionDtoList = [...interactionDtoList];
  editSceneData.changeTime = new Date().getTime();
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));

  isAddPlaying = false;
};

const exitEdit = () => {
  // 更新场景之前先截图保存
  const pageQuery: any = router.currentRoute.value.query;
  const sceneId = pageQuery.sceneid || '';
  const isPlanStyle = store.state.isPlanStyle;
  const renderer = isPlanStyle ? (window as any).renderer : (window as any).renderer2;
  const camera = isPlanStyle ? (window as any).camera : (window as any).camera2;
  const scene = isPlanStyle ? (window as any).scene : (window as any).scene2;
  const token = window.localStorage.getItem('token');
  if (!token) return;
  renderer.render(scene, camera);
  let imgData = renderer.domElement.toDataURL('image/jpeg');
  saveScene({ sceneId: sceneId, base64Pic: imgData }).then((res: any) => {
    if ([6, 7].includes(userType.value)) {
      router.push('experience_home');
    } else {
      router.push('home');
    }
  });
};

const goHome = () => {
  document.exitFullscreen && document.exitFullscreen();
  exitEdit();
};

const shareScene = () => {
  if (isLoading.value) return;
  props.showShare();
};

watch(
  () => props.sceneData,
  (nv) => {
    scene.value = nv;
  }
);

// 移动旋转缩放数据保留2位小数
const changeDecimalForTow = (obj: any, num = 2) => {
  return {
    x: Number(obj.x).toFixed(num),
    y: Number(obj.y).toFixed(num),
    z: Number(obj.z).toFixed(num),
  };
};

const saveEditInfo = async (isTip = true, callback?: any) => {
  return new Promise(async (resolve) => {
    // 保存时候防抖
    if (saveloading.value) return;
    store.state.isRequesting = true;
    store.state.isDragLoading = false;
    const isPlanStyle = store.state.isPlanStyle;
    const editSceneData = JSON.parse(JSON.stringify(store.state.editSceneData));
    const deleteData = { ...store.state.deleteData };
    if (editSceneData.interactionDtoList) {
      submitData.interactionDtoList = editSceneData.interactionDtoList.map((e: any) => {
        const data = {
          flag: e.flag,
          interactionName: e.interactionName,
          location: changeDecimalForTow(e.location),
          scale: changeDecimalForTow(e.scale),
          rotation: changeDecimalForTow(e.rotation, 4),
          id: e.id,
          sceneId: editSceneData.id,
          storageUuid: e.flag == 'add' ? e.uuid : null,
          backgroundMusic: e.backgroundMusic || 0,
          isMusicCycle: e.isMusicCycle,
          materialMetaDtoList: e.materialMetaDtoList.map((f: any) => {
            const d = {
              location: changeDecimalForTow(f.location),
              scale: changeDecimalForTow(f.scale),
              rotation: changeDecimalForTow(f.rotation, 4),
              materialId: f.materialDto.id,
              sceneId: editSceneData.id,
              metaInfo: f.metaInfo,
              materialAffiliation: f.materialAffiliation || f.materialDto.materialAffiliation,
              id: f.id,
              flag: f.flag,
              storageUuid: f.flag == 'add' ? f.uuid : null,
              cyclePlay: f.cyclePlay,
              autoPlay: f.autoPlay,
              elementName: f.elementName,
              isStatic: f.isStatic,
            };
            return d;
          }),
        };
        return data;
      });
    }
    submitData.outerMaterialMetaDtoList = editSceneData.outerMaterialMetaDtoList?.map((f: any) => {
      const d = {
        location: changeDecimalForTow(f.location),
        scale: changeDecimalForTow(f.scale),
        rotation: changeDecimalForTow(f.rotation, 4),
        materialId: f.materialDto.id,
        sceneId: editSceneData.id,
        materialAffiliation: f.materialAffiliation || f.materialDto.materialAffiliation,
        id: f.id,
        flag: f.flag,
        metaInfo: f.metaInfo,
        storageUuid: f.flag == 'add' ? f.uuid : null,
        cyclePlay: f.cyclePlay,
        autoPlay: f.autoPlay,
        elementName: f.elementName,
        isStatic: f.isStatic,
      };
      return d;
    });

    Object.values(deleteData).forEach((e: any) => {
      if (e.interactionName) {
        submitData.interactionDtoList.push({ ...e, flag: 'delete' });
      } else if (e.interactionId == 'public') {
        delete e.interactionId;
        submitData.outerMaterialMetaDtoList.push({ ...e, flag: 'delete' });
      } else if (e.interactionId) {
        submitData.interactionDtoList = submitData.interactionDtoList.map((d: any) => {
          if (d.id == e.interactionId) {
            delete e.interactionId;
            d.materialMetaDtoList.push({ ...e, flag: 'delete' });
          }
          return d;
        });
      }
    });

    // 更新场景之前先截图保存
    if (store.state.showSaveTips) {
      const renderer = isPlanStyle ? (window as any).renderer : (window as any).renderer2;
      const camera = isPlanStyle ? (window as any).camera : (window as any).camera2;
      const scene = isPlanStyle ? (window as any).scene : (window as any).scene2;
      renderer.render(scene, camera);
      let imgData = renderer.domElement.toDataURL('image/jpeg');
      submitData.base64Pic = imgData;
      saveloading.value = true;
    }
    submitData.sceneId = editSceneData.id;
    if (submitData.interactionDtoList) {
      submitData.interactionDtoList.forEach((parentMater: any) => {
        if (parentMater.materialMetaDtoList) {
          parentMater.materialMetaDtoList.forEach((mater: any) => {
            handleMatrixCompose(mater);
          });
        }
      });
    }
    if (submitData.outerMaterialMetaDtoList) {
      submitData.outerMaterialMetaDtoList.forEach((mater: any) => {
        handleMatrixCompose(mater);
      });
    }
    saveScene({ ...submitData }).then((res: any) => {
      const { sceneName, stadiumName, backgroundMusic, isMusicCycle, takePhotoButtonVisible } =
        editSceneData;
      callback && callback(res.data);
      updateSceneMeta({
        sceneName,
        stadiumName,
        backgroundMusic: backgroundMusic || 0,
        isMusicCycle,
        id: submitData.sceneId,
        takePhotoButtonVisible: takePhotoButtonVisible ? 1 : 0,
      }).then((res2: any) => {
        if (store.state.showSaveTips && isTip) {
          ElMessage.success('项目保存成功！');
        }
        isOpenTip.value = false;

        saveloading.value = false;
        if (!res.data) {
          store.state.isRequesting = false;
          resolve('success');
          return;
        }
        const oldSceneData = { ...store.state.editSceneData, changeTime: 0 };
        if (oldSceneData.interactionDtoList) {
          oldSceneData.interactionDtoList = oldSceneData.interactionDtoList.map((ele: any) => {
            delete ele.flag;
            if (ele.uuid && res.data[ele.uuid]) {
              ele.id = res.data[ele.uuid];
            }
            if (ele.materialMetaDtoList) {
              ele.materialMetaDtoList = ele.materialMetaDtoList.map((e: any) => {
                delete e.flag;
                if (e.uuid && res.data[e.uuid]) {
                  e.id = res.data[e.uuid];
                }
                return e;
              });
            }
            return ele;
          });
        }

        oldSceneData.outerMaterialMetaDtoList = oldSceneData.outerMaterialMetaDtoList.map(
          (e: any) => {
            delete e.flag;
            if (e.uuid && res.data[e.uuid]) {
              e.id = res.data[e.uuid];
            }
            return e;
          }
        );
        store.state.editSceneData = JSON.parse(JSON.stringify(oldSceneData));
        store.state.showSaveTips = true;
        store.state.isRequesting = false;
        resolve('success');
      });
      // TODO 提交绑定关系
      updateWxIdentifyAttachInfo({
        sceneId: editSceneData.id,
        bodyIdentifyPoints:
          editSceneData.sceneType == 5 ? Object.values(editSceneData.identifyPoints || {}) : null,
        faceIdentifyPoints:
          editSceneData.sceneType == 6 ? Object.values(editSceneData.identifyPoints || {}) : null,
        handIdentifyPoints:
          editSceneData.sceneType == 7 ? Object.values(editSceneData.identifyPoints || {}) : null,
      }).then((res) => {});
    });
  });
};

const handleMatrixCompose = (mater: any) => {
  let matrixStr = mater.metaInfo;
  if (!matrixStr || matrixStr == '') {
    matrixStr = '0,0,0,1,1';
  }
  let mataInfoList = matrixStr.split(',');
  matrixStr =
    mataInfoList[0] +
    ',' +
    mataInfoList[1] +
    ',' +
    mataInfoList[2] +
    ',' +
    mataInfoList[3] +
    ',' +
    mataInfoList[4] +
    ',';
  for (let i = 0; i < mataInfoList.length; i++) {
    mataInfoList[i] = Number(mataInfoList[i]);
  }
  let matrixCompose = new Matrix4();
  let modelPosition = new Vector3(mater.location.x, mater.location.y, mater.location.z);
  let modelQuaternion = new Quaternion(
    mataInfoList[0],
    mataInfoList[1],
    mataInfoList[2],
    mataInfoList[3]
  );
  let modelScale = new Vector3(mater.scale.x, mater.scale.y, mater.scale.z);
  matrixCompose.compose(modelPosition, modelQuaternion, modelScale);

  matrixStr += matrixCompose.elements.join(',');
  mater.metaInfo = matrixStr;
  if (mater.flag != 'add' && mater.flag != 'delete') {
    mater.flag = 'update';
  }
};

onMounted(() => {
  saveloading.value = false;
  getUserTypeByToken().then((res: any) => {
    userType.value = res.data;
  });

  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', (e: any) => {
    if (document.fullscreenElement) {
      has_full.value = true;
    } else {
      has_full.value = false;
    }
  });
});

watch(
  () => store.state.editSceneData,
  (newState) => {
    let _totalSize = 0;
    isLoading.value = false;

    if (newState) {
      // 获取总素材容量
      if (newState.outerMaterialMetaDtoList) {
        _totalSize = newState.outerMaterialMetaDtoList.reduce((pre: any, cur: any) => {
          pre += cur.materialDto?.storageSize || 0;
          return pre;
        }, 0);
        if (newState.interactionDtoList) {
          newState.interactionDtoList.forEach((inter: any) => {
            _totalSize += inter.materialMetaDtoList.reduce((pre: any, cur: any) => {
              pre += cur.materialDto?.storageSize || 0;
              return pre;
            }, 0);
          });
        }
        totalSize.value = _totalSize;
        store.state.totalSize = totalSize.value || 0;
      } else {
        store.state.totalSize = 0;
      }
    }
  },
  { deep: true }
);

// 监听 store 中的操作类型变化
watch(
  () => store.state.operateType,
  (newOperateType) => {
    // 处理"移动旋转"模式的映射
    let targetTool = newOperateType;
    if (newOperateType === '移动旋转') {
      targetTool = '移动旋转';
    } else if (newOperateType === '移动' || newOperateType === '旋转') {
      // 如果外部设置为单独的移动或旋转，也映射到移动旋转工具
      targetTool = '移动旋转';
    }

    if (targetTool !== activeEditTool.value) {
      activeEditTool.value = targetTool;
      console.log(`🔄 HeaderView: 操作类型同步更新为 "${targetTool}"`);
    }
  }
);

defineExpose({
  saveEditInfo,
});
</script>
<style scoped lang="less">
.edit-title {
  min-width: 100%;
  width: 100vw;
  height: 63px;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 11;
  padding: 0 16px;
  box-sizing: border-box;
  background: #fff;
  backdrop-filter: blur(6px);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-left {
    display: flex;
    align-items: center;

    .back-btn {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 4px;
      transition: all 0.3s ease;
      width: 60px;
      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }

      .el-icon {
        font-size: 16px;
        color: #666;
      }

      span {
        margin-left: 4px;
        margin-right: 12px;
        font-size: 14px;
        color: #666;
      }
    }

    .bias {
      font-weight: 400;
      font-size: 24px;
      color: #000000;
      margin-right: 10px;
    }

    .scene-name {
      font-weight: 400;
      font-size: 17px;
      color: #000000;
      display: inline-block;
      margin-left: 0;
    }
  }

  .header-center {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;

    /* 固定位置的模式切换按钮 */
    .mode-toggle-fixed {
      display: flex;
      background: #e5e5e5;
      border-radius: 4px;
      padding: 2px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      margin-left: -120px; /* 向左偏移，固定在中心偏左位置 */
      z-index: 2;

      .mode-btn {
        padding: 6px 16px;
        border-radius: 3px;
        font-size: 14px;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background: #fff;
          color: #333;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        &:hover:not(.active) {
          color: #333;
        }
      }
    }

    /* 动态工具区域 */
    .dynamic-tools-area {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-left: 200px; /* 为mode-toggle-fixed留出足够空间 */
      transition: all 0.3s ease;
      z-index: 1;
    }

    /* 2D图标样式 */
    .mode-2d-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      animation: slideInFromLeft 0.3s ease-out;

      .edit-tool-btn {
        width: 34px;
        height: 34px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        border-radius: 4px;

        img {
          width: 24px;
          height: 24px;
          transition: all 0.3s ease;
        }

        &:hover {
          background: #f3f3f3;
        }
      }
    }

    .divider {
      width: 1px;
      height: 34px;
      background: #dadada;
      border-radius: 0px 0px 0px 0px;
    }

    .edit-tools {
      display: flex;
      gap: 8px;
      align-items: center;

      .edit-tool-btn {
        width: 34px;
        height: 34px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        border-radius: 4px;

        img {
          width: 24px;
          height: 24px;
          transition: all 0.3s ease;
        }

        &:hover {
          width: 34px;
          height: 34px;
          background: #f3f3f3;
          border-radius: 4px;
        }

        &.active {
          width: 34px;
          height: 34px;
          background: #f3f3f3;
          border-radius: 4px;
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .save-btn,
    .share-btn {
      width: 68px;
      height: 34px;
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      cursor: pointer;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease;

      img {
        width: 16px;
        height: 16px;
        animation: rotate 1.5s linear infinite;
        margin: 0;
      }
    }

    .loadBtn {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .share-btn {
      background: #d5e4ff;
    }

    .save-btn {
      background: #2e76ff;
      color: #ffffff;
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
