<template>
  <div class="package">
    <header class="header_">
      <div class="search">
        <div class="label" style="margin-right: 10px">套餐名称</div>
        <el-input
          placeholder="请输入要查询的套餐名称"
          v-model="packageKeyword"
          style="
            width: 240px;
            height: 34px;
            border-radius: 4px;
            border: 1px solid #dadada;
          "></el-input>
        <el-button
          type="primary"
          style="width: 68px; border-radius: 10px; margin-left: 24px"
          color="#2E76FF"
          @click="searchPackages">
          查询
        </el-button>
        <el-button
          style="width: 68px; border-radius: 10px; margin-left: 10px"
          @click="resetPackage">
          重置
        </el-button>
      </div>
      <el-button
        type="primary"
        style="width: 112px; border-radius: 10px"
        @click="dialogVisible = true"
        color="#2E76FF">
        添加套餐
      </el-button>
    </header>
    <el-table :data="packages" :header-cell-style="headerStyle" class="styleTable">
      <el-table-column prop="packageName" label="套餐名称" min-width="200" />
      <el-table-column label="套餐时长" width="160" align="center">
        <template #default="{ row }">
          {{ row.expireDay == 99999 ? '不限' : row.expireDay + '天' }}
        </template>
      </el-table-column>
      <el-table-column label="创建项目数量" width="250" align="center">
        <template #default="scope">
          <div class="small">小程序平面识别AR: {{ scope.row.planeSceneNum || '--' }}</div>
          <div class="big">大空间定位AR: {{ scope.row.arSceneNum || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="大空间定位支持平台" width="230" align="center">
        <template #default="scope">
          <div class="bo" v-if="scope.row.packagePlatformList">
            <div class="line" v-for="line in scope.row.packagePlatformList" :key="line">
              {{ line == 1 ? '眼镜端' : line == 2 ? '移动端' : '微信小程序' }}
            </div>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="空间限制" width="180" align="center">
        <template #default="{ row }">
          <div class="spacex" v-if="row.spacePicSingleNum || row.spacePicTotalNum">
            <div class="sing" v-if="row.spacePicSingleNum">
              单次上传限制 {{ row.spacePicSingleNum }} 张
            </div>
            <div class="sing" v-if="row.spacePicTotalNum">
              总数上传限制 {{ row.spacePicTotalNum }} 张
            </div>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="空间数量" width="180" align="center">
        <template #default="{ row }">
          <template v-if="row.spaceNum == null">--</template>
          <template v-else>
            <div class="space_num" v-if="row.spaceNum">限制 {{ row.spaceNum }} 个</div>
            <span v-else>不限-照片用完为止</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="素材空间总容量" width="150" align="center">
        <template #default="{ row }">
          <div class="allPack">{{ storageSize(row.materialUploadSize) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="单个素材上传上限" width="150" align="center">
        <template #default="{ row }">
          <div class="singPack">{{ storageSize(row.singleUploadSize) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="130" align="center">
        <template #default="scope">
          <div class="edit_name" @click="editName(scope.row)">编辑名称</div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加套餐 -->
    <div class="new-mask" v-if="dialogVisible">
      <main :class="{ initHeight: !isShowCenter }">
        <header class="header__">
          <div class="new_header">添加套餐</div>
          <img
            class="closed"
            src="http://njyjxr.oss-cn-shanghai.aliyuncs.com/mask-close.png"
            alt=""
            @click="dialogVisible = false" />
        </header>
        <el-form
          ref="ruleFormRef"
          :model="form"
          label-width="auto"
          style="max-width: 600px"
          :rules="rules">
          <el-form-item label="套餐名称：" prop="shareName">
            <el-input v-model="form.shareName" />
          </el-form-item>
          <el-form-item label="设置套餐时长：" class="top_form">
            <label class="line_radio">
              <input type="radio" name="1" :checked="form.time == 30" @change="changeRadio(30)" />
              <span>1个月</span>
            </label>
            <label class="line_radio">
              <input type="radio" name="1" :checked="form.time == 90" @change="changeRadio(90)" />
              <span>3个月</span>
            </label>
            <label class="line_radio">
              <input type="radio" name="1" :checked="form.time == 180" @change="changeRadio(180)" />
              <span>6个月</span>
            </label>
            <label class="line_radio">
              <input type="radio" name="1" :checked="form.time == 365" @change="changeRadio(365)" />
              <span>一年</span>
            </label>
            <label class="line_radio">
              <input
                type="radio"
                name="1"
                :checked="form.time == 99999"
                @change="changeRadio(99999)" />
              <span>不限</span>
            </label>
            <el-input-number
              class="numberCount"
              v-model="form.time"
              :min="30"
              :max="99999"
              controls-position="right" />
          </el-form-item>
          <el-form-item label="创建项目数量：" class="top_second" prop="sceneNum">
            <div class="arSceneNum">
              <span>大空间定位AR</span>
              <el-input-number class="roomInput" :min="0" v-model="form.arSceneNum" />
              个
            </div>
            <br />
            <div class="planeSceneNum">
              <span>小程序平面AR</span>
              <el-input-number class="planeInput" :min="0" v-model="form.planeSceneNum" />
              个
            </div>
          </el-form-item>
          <el-form-item label="空间定位支持平台：" class="center">
            <label class="line_radio">
              <input
                type="checkbox"
                name="1"
                :checked="form.packagePlatformList.includes('1')"
                @change="(flag) => changeCheckPlat('1', flag)" />
              眼镜端
            </label>
            <label class="line_radio">
              <input
                type="checkbox"
                name="1"
                :checked="form.packagePlatformList.includes('2')"
                @change="(flag) => changeCheckPlat('2', flag)" />
              移动端
            </label>
            <label class="line_radio">
              <input
                type="checkbox"
                name="1"
                :checked="form.packagePlatformList.includes('3')"
                @change="(flag) => changeCheckPlat('3', flag)" />
              微信小程序端
            </label>
          </el-form-item>
          <div class="back-form" v-if="isShowCenter">
            <el-form-item
              label="空间限制："
              class="last_second"
              :class="{ disableForm: !form.packagePlatformList.length }">
              <label
                class="line_check"
                :class="{ line_check_disabled: !form.packagePlatformList.length }">
                <input
                  type="checkbox"
                  name="2"
                  :checked="form.assetsLimitTypes.includes(1)"
                  @change="(flag) => changeCheckAssets(1, flag)"
                  :disabled="!form.packagePlatformList.length" />
                单次上传限制
              </label>
              <div
                class="checkInputOne"
                :style="{ color: form.assetsLimitTypes.includes(1) ? '#409eff' : '' }">
                <el-input-number
                  :disabled="!form.packagePlatformList.length || !form.assetsLimitTypes.includes(1)"
                  class="roomInput"
                  :min="0"
                  v-model="form.spacePicSingleNum" />
                处理照片数量
              </div>
              <label
                class="line_check"
                style="margin-top: 14px"
                :class="{ line_check_disabled: !form.packagePlatformList.length }">
                <input
                  type="checkbox"
                  name="2"
                  :checked="form.assetsLimitTypes.includes(2)"
                  @change="(flag) => changeCheckAssets(2, flag)"
                  :disabled="!form.packagePlatformList.length" />
                总数上传限制
              </label>
              <div
                class="checkInputTwo"
                :style="{ color: form.assetsLimitTypes.includes(2) ? '#409eff' : '' }">
                <el-input-number
                  :disabled="!form.packagePlatformList.length || !form.assetsLimitTypes.includes(2)"
                  class="planeInput"
                  :min="0"
                  v-model="form.spacePicTotalNum" />
                处理照片数量
              </div>
            </el-form-item>
            <el-form-item
              label="token："
              prop="immersalToken"
              :class="{ disableForm: !form.packagePlatformList.length }">
              <el-input
                v-model="form.immersalToken"
                style="width: 375px"
                :disabled="!form.packagePlatformList.length" />
            </el-form-item>
            <el-form-item
              label="空间数量："
              class="last_first"
              :class="{ disableForm: !form.packagePlatformList.length }">
              <label
                class="line_radio"
                :class="{ line_check_disabled: !form.packagePlatformList.length }">
                <input
                  type="radio"
                  name="2"
                  :checked="form.spaceLimitType == 1"
                  @change="changeSizeRadio(1)"
                  :disabled="!form.packagePlatformList.length" />
                <span>不限-照片用完为止</span>
              </label>
              <label
                class="line_radio"
                :class="{ line_check_disabled: !form.packagePlatformList.length }">
                <input
                  type="radio"
                  name="2"
                  :checked="form.spaceLimitType == 2"
                  @change="changeSizeRadio(2)"
                  :disabled="!form.packagePlatformList.length" />
                限制
                <el-input-number
                  class="roomInput"
                  :min="0"
                  v-model="form.spaceNum"
                  :disabled="!form.packagePlatformList.length || form.spaceLimitType != 2" />
                个
              </label>
            </el-form-item>
          </div>
          <el-form-item label="素材空间总容量：" class="lastFlex" prop="materialUploadSize">
            <el-input-number
              :min="0"
              v-model="form.materialUploadSize"
              class="numberCount tempNumberCount"
              controls-position="right" />
            <span style="position: absolute; right: -1px">MB</span>
          </el-form-item>
          <el-form-item label="单个素材上传上限：" class="last lastFlex" prop="singleUploadSize">
            <el-input-number
              :min="0"
              v-model="form.singleUploadSize"
              class="numberCount tempNumberCount"
              controls-position="right" />
            <span style="position: absolute; right: -1px">MB</span>
          </el-form-item>
        </el-form>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePackage(ruleFormRef)" color="#2E76FF">
            确认
          </el-button>
        </div>
      </main>
    </div>

    <!-- 修改名字 -->
    <el-dialog v-model="dialogNameVisible" title="修改名称" width="453" align-center>
      <main style="margin: 20px 0">
        <el-form-item label="套餐名称：">
          <el-input v-model="currentPack.packageName" placeholder="请输入项目名称" />
        </el-form-item>
      </main>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogNameVisible = false">取消</el-button>
          <el-button type="primary" @click="updateName" color="#2E76FF">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
// @ts-check
import { ref, onMounted, reactive, watch, computed } from 'vue';
import { getPackageList, addPackage, updatePackage } from '@/api';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import type { Package } from '@/types/api';

let originPackages: Package[] = [];
const packages = ref<Package[]>([]);
const dialogVisible = ref(false);
const dialogNameVisible = ref(false);
const ruleFormRef = ref<FormInstance>();
const currentPack = ref();
const packageKeyword = ref('');
const isShowCenter = ref(false);

const form: any = reactive({
  shareName: '',
  time: 30,
  arSceneNum: 0,
  planeSceneNum: 0,
  packagePlatformList: [], // 大空间定位支持平台

  assetsLimitTypes: [], // 单次上传 / 多次上传
  spacePicSingleNum: 0, // 单次照片上传
  spacePicTotalNum: 0, // 多次照片上传
  immersalToken: '',

  spaceLimitType: 1,
  spaceNum: 0,

  materialUploadSize: 0,
  singleUploadSize: 0,
});

const headerStyle = {
  // height: '32px',
  // background: 'rgba(230,237,247,0.3)',
  // border: '1px solid #E6EDF7',
  // textAlign: 'center'
};

const searchPackages = () => {
  if (!packageKeyword.value) {
    return (packages.value = originPackages);
  }
  packages.value = originPackages.filter((pack) => {
    console.log(
      pack,
      pack.packageName.indexOf(packageKeyword.value),
      'pack.packageName.indexOf(packageKeyword.value)'
    );
    return pack.packageName.indexOf(packageKeyword.value) >= 0;
  });
};

const resetPackage = () => {
  packages.value = originPackages;
};

const changeRadio = (val) => {
  form.time = val;
};

const changeSizeRadio = (val) => {
  form.spaceLimitType = val;
};

const changeCheckPlat = (val, flag) => {
  if (flag.target.checked) {
    form.packagePlatformList.push(val);
  } else {
    form.packagePlatformList = form.packagePlatformList.filter((item) => item != val);
  }
};

const changeCheckAssets = (val, flag) => {
  if (flag.target.checked) {
    form.assetsLimitTypes.push(val);
  } else {
    form.assetsLimitTypes = form.assetsLimitTypes.filter((item) => item != val);
  }
};

const convertStorageSize = (sizeInMB) => {
  let size = sizeInMB;
  let unit = 'MB';
  if (size >= 1024) {
    size /= 1024; // 转换为 GB
    unit = 'GB';
  }
  if (size >= 1024) {
    size /= 1024; // 转换为 TB
    unit = 'TB';
  }
  return `${size.toFixed(2)} ${unit}`;
};

const updateName = () => {
  const params = currentPack.value;
  updatePackage(params).then((res) => {
    if (res.code == 200) {
    }
  });
  dialogNameVisible.value = false;
};

const storageSize = computed(() => {
  return (size) => {
    return convertStorageSize(size);
  };
});

const editName = (item) => {
  currentPack.value = item;
  dialogNameVisible.value = true;
};

const requestAddPackage = () => {
  return new Promise((resolve) => {
    form.immersalToken = form.immersalToken.trim();
    if (form.packagePlatformList.length) {
      if (form.assetsLimitTypes.includes(1) && form.spacePicSingleNum <= 0) {
        return ElMessage({ type: 'warning', message: '单次上传限制需要大于0' });
      }
      if (!form.assetsLimitTypes.includes(2)) {
        return ElMessage({ type: 'warning', message: '总数上传限制为必选' });
      }
      if (form.spacePicTotalNum <= 0) {
        return ElMessage({ type: 'warning', message: '总数上传数量限制需要大于0' });
      }
      if (form.spacePicTotalNum < form.spacePicSingleNum && form.assetsLimitTypes.includes(1)) {
        return ElMessage({ type: 'warning', message: '单次上传数量不能大于总数上传数量' });
      }
      if (!form.immersalToken) {
        return ElMessage({ type: 'warning', message: '请输入token' });
      }
      if (form.spaceLimitType == 2 && form.spaceNum <= 0) {
        return ElMessage({ type: 'warning', message: '空间数量限制需要大于0' });
      }
      if (form.spaceLimitType != 2) {
        form.spaceNum = 0;
      }
    } else {
      if (form.arSceneNum > 0) {
        return ElMessage({ type: 'warning', message: '请至少选择一个空间定位支持平台' });
      }
      form.spacePicSingleNum = 0;
      form.spacePicTotalNum = 0;
      form.immersalToken = '';
      form.spaceNum = null;
    }
    if (!form.assetsLimitTypes.includes(1)) {
      form.spacePicSingleNum = 0;
    }
    if (!form.assetsLimitTypes.includes(2)) {
      form.spacePicTotalNum = 0;
    }
    const params = {
      packageName: form.shareName,
      expireDay: form.time,
      arSceneNum: form.arSceneNum,
      planeSceneNum: form.planeSceneNum,
      packagePlatformList: form.packagePlatformList,
      spacePicSingleNum: form.spacePicSingleNum,
      spacePicTotalNum: form.spacePicTotalNum,
      immersalToken: form.immersalToken,
      spaceNum: form.spaceNum,
      materialUploadSize: form.materialUploadSize,
      singleUploadSize: form.singleUploadSize,
    };
    addPackage(params).then((res) => {
      if (res.code == 200) {
        dialogVisible.value = false;
        getInitData();
      }
    });
  });
};

const savePackage = async (formEl: any) => {
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      requestAddPackage();
    } else {
    }
  });
};

const validatePass = (rule: any, value: any, callback: any) => {
  if (value <= 0 && rule.field === 'materialUploadSize') {
    callback(new Error('素材空间总容量不为0'));
    return;
  }
  if (value <= 0 && rule.field === 'singleUploadSize') {
    callback(new Error('单个素材上传容量不为0'));
    return;
  }
  callback();
};

const validateSceneNum = (rule: any, value: any, callback: any) => {
  if (form.arSceneNum <= 0 && form.planeSceneNum <= 0) {
    callback(new Error('项目数量不为0'));
    return;
  }
  callback();
};

const rules = reactive<FormRules<RuleForm>>({
  shareName: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
  materialUploadSize: [{ required: true, validator: validatePass, trigger: 'blur' }],
  singleUploadSize: [{ required: true, validator: validatePass, trigger: 'blur' }],
  sceneNum: [{ required: true, validator: validateSceneNum, trigger: 'change' }],
});

const getInitData = () => {
  getPackageList({}).then((res) => {
    if (res.code == 200) {
      packages.value = res.data;
      originPackages = res.data;
    }
  });
};

onMounted(() => {
  getInitData();
});

watch(
  () => form.packagePlatformList,
  (nv) => {
    isShowCenter.value = nv.length ? true : false;
  },
  { deep: true }
);
</script>

<style lang="less" scoped>
:deep(.el-input) {
  border: none !important;
}

.back-form {
  background: #f7f7f7;
  padding-top: 13px;
}

:deep(.el-dialog) {
  border-radius: 8px;
  height: 176px;
  padding-top: 17px !important;
}

:deep(.el-dialog__header) {
  padding: 0 !important;
}

:deep(.el-dialog__body) {
  transform: translateY(-20px);
  padding: 0 !important;
}

:deep(.el-table__cell) {
  border-right: none !important;
}

.search {
  display: flex;
  align-items: center;

  .label {
    font-weight: 500;
    font-size: 14px;
    color: #797979;
  }
}

.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .header__ {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: relative;

    img {
      width: 16px !important;
      height: 16px !important;
      position: absolute;
      right: -20px;
      top: -30px;
      cursor: pointer;
    }

    .new_header {
      font-weight: 700;
      color: #0675ff;
      position: absolute;
      left: -20px;
      top: -30px;
      font-weight: bold;
      font-size: 18px;
      color: #1e1e1e;
    }
  }

  & > main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 625px;
    height: 782px;
    background: #ffffff;
    border-radius: 8px;
    box-sizing: border-box;
    text-align: left;
    font-size: 18px;
    color: #3d566c;
  }
}

.initHeight {
  height: 521px !important;
}

.line_radio {
  cursor: pointer;
  margin-right: 15px;
}

.line_check {
  transform: translateX(-145px);
  display: flex;
  width: 100%;
  margin-left: 142px;
  cursor: pointer;
}

.line_check_disabled {
  cursor: not-allowed;
}

:deep(.el-table__cell) {
  border: none;
}

:deep(.el-dialog) {
  padding: 24px;
}

:deep(.el-dialog__title) {
  font-weight: bold;
  font-size: 18px;
  color: #0675ff;
}

:deep(.show-close) {
  text-align: left;
}

:deep(.el-dialog__headerbtn) {
  top: 8px;
  right: 2px;
}

:deep(.el-radio-group) {
  margin-bottom: 6px;
}

:deep(.el-input-number__decrease) {
  z-index: 3;
}

:deep(.el-input-number__increase) {
  z-index: 3;
}

:deep(.el-input__wrapper) {
  z-index: 2;
}

.numberCount {
  :deep(.el-input__inner) {
    width: 358px;
  }

  :deep(.el-input-number__decrease) {
    left: 365px !important;
  }

  :deep(.el-input-number__increase) {
    left: 365px !important;
  }
}

.tempNumberCount {
  :deep(.el-input__inner) {
    width: 318px;
  }

  :deep(.el-input-number__decrease) {
    left: 326px !important;
  }

  :deep(.el-input-number__increase) {
    left: 326px !important;
  }
}

:deep(.el-dialog__footer) {
  transform: translateY(-18px);
}

.disableForm {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.package {
  min-height: 500px;
  min-width: 852px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-size: 100% 470px;
  padding: 0px 32px 24px 32px;
  height: 100%;
  box-sizing: border-box;
  .edit_name {
    font-weight: 600;
    font-size: 14px;
    color: #3671fe;
    cursor: pointer;
  }

  header {
    font-weight: bold;
    font-size: 32px;
    color: #0675ff;
    margin-bottom: 24px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    span {
      font-weight: 500;
      font-size: 24px;
      color: #8195a9;
    }
  }

  .header_ {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  main {
    padding: 20px 45px;

    .checkInputOne {
      position: absolute;
      top: 0;
      left: 105px;
    }

    .checkInputTwo {
      position: absolute;
      left: 105px;
      top: 46px;
    }

    .top_form {
      padding-bottom: 7px;
    }

    .top_second {
      :deep(.el-form-item__content) {
        display: block;
        line-height: 12px;
      }

      // padding-bottom: 20px;
    }

    .last_first {
      padding-bottom: 15px !important;

      :deep(.el-form-item__content) {
        line-height: 33px;
      }
    }

    .last_second {
      :deep(.el-checkbox-group) {
        display: flex;
        flex-direction: column;
      }

      :deep(.el-checkbox__label) {
        width: 314px;
        z-index: 1;
        text-align: left;
      }
    }

    .arSceneNum,
    .planeSceneNum {
      display: flex;
      align-items: center;
    }

    .lastFlex {
      :deep(.el-form-item__content) {
        display: flex;
      }
    }

    .roomInput,
    .planeInput {
      width: 132px;
      margin-left: 16px;
      margin-right: 10px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    position: absolute;
    right: 42px;
    bottom: 22px;

    button {
      width: 112px;

      &:first-child {
        margin-right: 32px;
      }
    }
  }
}

.styleTable {
  background-color: transparent !important;
  border: 1px solid #e6edf7;
  border-radius: 4px;
  overflow: hidden;
  overflow-y: auto;

  .view-details {
    font-weight: 400;
    font-size: 12px;
    color: #2e76ff;
    text-decoration-line: underline;
    margin-left: 10px;
    cursor: pointer;
  }

  ::v-deep(.el-table__inner-wrapper::before) {
    content: none;
  }
}

::v-deep(.el-table tr:last-child td.el-table__cell) {
  border-bottom-color: transparent;
}

.styleTable::v-deep(.el-table th.el-table__cell),
::v-deep(.el-table tr) {
  height: 42px;
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;
}

.styleTable::v-deep(.el-table__header-wrapper th) {
  background-color: rgba(230, 237, 247, 0.3) !important;
  font-weight: 400;
  font-size: 12px;
  color: #797979;
}

.styleTable::v-deep(.el-table__header-wrapper tr) {
  height: 32px;
}

::v-deep(.el-table th) {
  text-align: center;
}

::v-deep(.el-table th:first-child) {
  text-align: left;
}

::v-deep(.el-table th:last-child) {
  text-align: right;
}

::v-deep(.el-table td) {
  text-align: center;
}

::v-deep(.el-table td:first-child) {
  text-align: left;
}

::v-deep(.el-table td:last-child) {
  text-align: right;
}
</style>
