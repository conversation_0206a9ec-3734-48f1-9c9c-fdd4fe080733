<template>
  <div class="progress-modal-mask">
    <div class="progress-modal">
      <div class="modal-bg"></div>
      <div class="modal-gradient"></div>
      <div class="close-icon-wrap">
        <img
          src="@/assets/images/close-tips.png"
          @click="$emit('close')"
          alt="关闭"
          class="close-icon" />
      </div>
      <img :src="icon" class="progress-icon" />
      <div class="progress-bar-wrap">
        <div class="progress-bar-bg"></div>
        <div class="progress-bar" :style="{ width: currentProgress + '%' }"></div>
      </div>
      <div class="progress-text">{{ computedText }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';

interface Props {
  icon: string;
  progress?: number;
  text?: string;
  status: 'uploading' | 'processing' | 'completed' | 'error';
}

const props = defineProps<Props>();
const emit = defineEmits<{
  close: [];
}>();

const currentProgress = ref(0);
let progressInterval: ReturnType<typeof setInterval> | null = null;

// 计算显示文本
const computedText = computed(() => {
  switch (props.status) {
    case 'uploading':
      return `上传中... ${Math.round(currentProgress.value)}%`;
    case 'processing':
      if (currentProgress.value <= 20) {
        return '模型上传完成，开始处理...';
      } else if (currentProgress.value <= 50) {
        return '模型处理中，请稍候...';
      } else {
        return '模型即将处理完成...';
      }
    case 'completed':
      return '模型处理完成';
    case 'error':
      return '模型处理失败';
    default:
      return props.text || '处理中...';
  }
});

// 更新进度条
const updateProgress = () => {
  if (props.status === 'completed') {
    currentProgress.value = 100;
    return;
  }

  if (props.status === 'error') {
    return; // 错误状态不更新进度
  }

  const targetProgress = props.progress || 0;
  const current = currentProgress.value;

  if (current < targetProgress) {
    // 平滑增长到目标进度
    const increment = Math.min(1, (targetProgress - current) * 0.1);
    currentProgress.value = Math.min(targetProgress, current + increment);
  }
};

// 监听进度变化
watch(
  () => props.progress,
  (newProgress) => {
    if (newProgress !== undefined) {
      // 如果是上传完成，立即设置为100%
      if (props.status === 'completed') {
        currentProgress.value = 100;
      }
    }
  },
  { immediate: true }
);

// 监听状态变化
watch(
  () => props.status,
  (newStatus) => {
    if (newStatus === 'completed') {
      currentProgress.value = 100;
    }
  },
  { immediate: true }
);

// 启动进度更新定时器
onMounted(() => {
  currentProgress.value = 0;
  progressInterval = setInterval(updateProgress, 50);
});

// 清理定时器
onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval);
    progressInterval = null;
  }
});
</script>

<style scoped>
.progress-modal-mask {
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.35);
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-modal {
  position: relative;
  min-width: 520px;
  min-height: 300px;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px 32px 32px 32px;
  overflow: hidden;
  background: transparent;
}

.modal-bg {
  position: absolute;
  inset: 0;
  background: #fff;
  border-radius: 10px;
  z-index: 0;
}

.modal-gradient {
  position: absolute;
  inset: 0;
  border-radius: 10px;
  background: linear-gradient(180deg, rgba(46, 118, 255, 0.65) 0%, rgba(107, 211, 255, 0) 92%),
    #ffffff;
  z-index: 1;
  pointer-events: none;
}

.progress-modal > *:not(.modal-bg):not(.modal-gradient) {
  position: relative;
  z-index: 2;
}

.close-icon-wrap {
  width: 100%;
  height: 48px;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-top: 10px;
  box-sizing: border-box;
}

.close-icon {
  width: 48px;
  height: 48px;
  cursor: pointer;
  position: relative;
  left: 24px;
}

.progress-icon {
  width: 140px;
  height: 140px;
  margin-bottom: 24px;
  margin-top: 8px;
}

.progress-bar-wrap {
  width: 280px;
  height: 4px;
  position: relative;
  margin-bottom: 24px;
}

.progress-bar-bg {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border-radius: 4px;
}

.progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #3c96ff 0%, #1e7ce8 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: center;
}
</style>
