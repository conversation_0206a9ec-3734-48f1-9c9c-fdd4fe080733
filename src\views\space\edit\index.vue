<template>
  <div @mousemove="handleWrapMove">
    <header-view :scene-data="sceneData" :show-share="showShare" ref="headerRef"></header-view>
    <left-side :source-pool-mourse-down="sourcePoolMourseDown" :deleteAssets="deleteAssets"
      :headerRef="headerRef"></left-side>
    <SpaceEdit2D ref="sceneEdit2DRef" :scene-data="sceneData"></SpaceEdit2D>
    <SpaceEdit3D ref="sceneEdit3DRef" :scene-data="sceneData"></SpaceEdit3D>
    <right-side :change-scene-value="changeSceneValue" :change-active-value="changeActiveValue"></right-side>
    <edit-tools ref="editToolsRef" :change-operate-type="changeOperateType"></edit-tools>
    <div class="show-icon" :style="{ backgroundImage: `url(${dragingImg})` }" @mousemove="handleIconMove"
      @mouseup="handleIconUp">
    </div>
    <share-view v-if="showShareMask" :hide-mask="hideMask" :scene-data="sceneData"></share-view>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, onBeforeMount, watch } from 'vue'
import SpaceEdit3D from '@/components/scene_web/SpaceEdit3D.vue'
import SpaceEdit2D from '@/components/scene_web/SpaceEdit2D.vue'
import LeftSide from '@/components/scene_web/LeftSide.vue'
import RightSide from '@/components/scene_web/RightSide.vue'
import HeaderView from '@/components/scene_web/HeaderView.vue'
import { getSceneMetaPageForWeb, getWebScene } from '@/api/index'
import ShareView from '@/components/experience/ShareView.vue'
import { useRouter, useRoute } from 'vue-router'
import { loadZipFileForJSZip, screenToWorldVector, searchMaterialIndex, createUuid } from "@/utils";
import { Scene, Box3 } from 'three'
import { useStore } from "vuex";
import EditTools from '@/components/scene_web/EditTools.vue'
import { MouseStyle, updateMouseStyle } from '@/core/mouse'

const store = useStore();
const route = useRoute();
const headerRef = ref()

const sceneData: any = ref({}) // 当前场景信息
const router = useRouter()
let scene1 = new Scene()
let scene2 = new Scene()
const dragingImg = ref('')
let Icondom: any = null
const currentMaterialData: any = ref({})
const showShareMask = ref(false)
const sceneEdit2DRef = ref()
const sceneEdit3DRef = ref()
let isDrag = false
let data = {}

// 修改高亮的数据
const changeActiveValue = (val: number, key: string, type: string, showType: number) => {
  const newValue = type == 'rotation' ? (val / 180) * Math.PI : val
  const editSceneData = { ...store.state.editSceneData, changeTime: new Date().getTime() }
  if (showType == 1) {
    const activeIndexs = searchMaterialIndex(store.state.activeMaterial).split(',');
    if (activeIndexs.length > 1) {
      editSceneData.interactionDtoList[activeIndexs[0]].materialMetaDtoList[activeIndexs[1]][type][key] = newValue
      if (editSceneData.interactionDtoList[activeIndexs[0]].materialMetaDtoList[activeIndexs[1]].flag !== 'add') {
        editSceneData.interactionDtoList[activeIndexs[0]].materialMetaDtoList[activeIndexs[1]].flag = 'update'
      }
    } else {
      editSceneData.outerMaterialMetaDtoList[activeIndexs[0]][type][key] = newValue
      if (editSceneData.outerMaterialMetaDtoList[activeIndexs[0]].flag != 'add') {
        editSceneData.outerMaterialMetaDtoList[activeIndexs[0]].flag = 'update'
      }
    }
  } else if (showType == 2) {
    editSceneData.interactionDtoList = editSceneData.interactionDtoList.map((e: any) => {
      if (e.uuid == store.state.activeAreaUuid) {
        e[type][key] = newValue
        if (e.flag != 'add') {
          e.flag = 'update'
        }
      }
      return e;
    })
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData))
}

const changeSceneValue = (val: string, key: string) => {
  store.state.editSceneData[key] = val;
}

const deleteAssets = () => {
  return new Promise(async resolve => {
    await headerRef.value.saveEditInfo(false)
    resolve(1)
  })
}


const showShare = async () => {
  await headerRef.value.saveEditInfo(false)
  showShareMask.value = true
}
const hideMask = () => {
  showShareMask.value = false
}

const changeOperateType = (name: string) => {
  store.state.operateType = name;
}

// 点击图片时候修改交互图片的位置
const sourcePoolMourseDown = (e: any, url: string, data: any) => {
  updateMouseStyle(MouseStyle.mouseDrag, true)
  dragingImg.value = url
  handleIconMove(e)
  currentMaterialData.value = data;
}

// 拖动素材时候悬浮交互
const handleIconMove = (e: any) => {
  if (dragingImg.value) {
    Icondom.style.left = e.clientX - 25 + 'px'
    Icondom.style.top = e.clientY - 25 + 'px'
  }
}

const handleWrapMove = (e: any) => {
  if (dragingImg.value) {
    handleIconMove(e)
  }
}

const handleIconUp = (e: any) => {
  isDrag = true
  if (dragingImg.value) {
    const canvasLeftWidth = (document.querySelector('.canvas-left') as any).getBoundingClientRect().width
    updateMouseStyle(MouseStyle.default, true)
    if ((currentMaterialData.value.storageSize || 1) + store.state.totalSize > 100 && store.state.editSceneData.scenePlatform == 3) {
      store.state.isDragLoading = false
      store.state.showTips = '素材添加失败，当前项目容量上限100MB';
      hideDragImg()
      return
    }
    if (canvasLeftWidth < e.clientX) {
      const point = screenToWorldVector(e.clientX, e.clientY)
      const editSceneData = { ...store.state.editSceneData };
      const uuid = createUuid()
      if (!store.state.activeAreaUuid) {
        // 创建公共区域
        data = { materialDto: { ...currentMaterialData.value }, location: { x: point.x, y: point.y, z: point.z }, rotation: { x: 0, y: 0, z: 0 }, scale: { x: 1, y: 1, z: 1 }, flag: 'add', uuid, isStatic: 1 }
        if (editSceneData.outerMaterialMetaDtoList) {
          editSceneData.outerMaterialMetaDtoList.push(data)
        } else {
          editSceneData.outerMaterialMetaDtoList = [data]
        }
        store.state.editSceneData = { ...editSceneData, changeTime: new Date().getTime() }
      } else {
        editSceneData.interactionDtoList.forEach((ele: any, index: number) => {
          if (ele.uuid == store.state.activeAreaUuid) {
            data = { materialDto: { ...currentMaterialData.value }, location: { x: point.x, y: point.y, z: point.z }, rotation: { x: 0, y: 0, z: 0 }, scale: { x: 1, y: 1, z: 1 }, flag: 'add', uuid, isStatic: 1 }
            if (editSceneData.interactionDtoList[index].materialMetaDtoList) {
              editSceneData.interactionDtoList[index].materialMetaDtoList.push(data)
            } else {
              editSceneData.interactionDtoList[index].materialMetaDtoList = [data]
            }
          }
        });
        store.state.editSceneData = { ...editSceneData, changeTime: new Date().getTime() }
      }
    } else {
      store.state.isDragLoading = false
    }
    hideDragImg()
  }
}

watch(() => store.state.isFinishModel, async (nv) => {
  if (isDrag) {
    store.state.showSaveTips = false;
    await headerRef.value.saveEditInfo(false)
    store.state.activeMaterial = (data as any).uuid
    isDrag = false
  }
})

// 清空素材交互
const hideDragImg = () => {
  dragingImg.value = ''
  Icondom.style.left = '-50%'
  Icondom.style.top = "-50%"
}

const getData = (sceneId: string) => {
  getWebScene({ id: sceneId }).then((res: any) => {
    const data = { ...res.data }
    if (data.interactionDtoList) {
      data.interactionDtoList = data.interactionDtoList.map((ele: any) => {
        if (ele.materialMetaDtoList) {
          ele.materialMetaDtoList = ele.materialMetaDtoList.map((e: any) => {
            e.uuid = createUuid()
            return e;
          })
        } else {
          ele.materialMetaDtoList = []
        }
        return ele;
      })
    } else {
      data.interactionDtoList = []
    }
    if (data.outerMaterialMetaDtoList) {
      data.outerMaterialMetaDtoList = data.outerMaterialMetaDtoList.map((e: any) => {
        e.uuid = createUuid()
        return e;
      })
    } else {
      data.outerMaterialMetaDtoList = []
    }
    sceneData.value = { ...sceneData.value, ...data }
    store.state.editSceneData = JSON.parse(JSON.stringify(sceneData.value))
  })
}


onBeforeMount(() => {
  store.dispatch('resetEditData')
})

onMounted(() => {
  Icondom = (document.querySelector('.show-icon') as any)
  const pageQuery: any = router.currentRoute.value.query;
  const sceneId = pageQuery.sceneid || '';
  const interactionId = pageQuery.interactionid
  store.dispatch('updateCurrentData', {
    spaceId: pageQuery.spaceId
  });
  getSceneMetaPageForWeb({ pageNo: 1, pageSize: 999 }).then((res: any) => {
    sceneData.value = res.data.records.filter((e: any) => e.id == sceneId)[0] || { uuid: '' }
    if (sceneData.value.spaceDto?.roomStructurePath) {
      // 加载地图
      loadZipFileForJSZip(sceneData.value.spaceDto.roomStructurePath, (glb: any) => {
        scene1 = (window as any).scene;
        scene2 = (window as any).scene2;
        glb.scene.name = 'model-init'
        scene1.add(glb.scene.clone());
        scene2.add(glb.scene.clone());
        const boxInfo = new Box3().expandByObject(glb.scene)
        if (scene2.getObjectByName('ground-init')) {
          (scene2.getObjectByName('ground-init') as any).position.y = boxInfo.min.y - 0.1;
        }
        if (scene1.getObjectByName('ground-init')) {
          (scene1.getObjectByName('ground-init') as any).position.y = boxInfo.min.y - 0.1;
        }
        getData(sceneId)
      })
    } else {
      getData(sceneId)
    }
  })

  if (window.history) {
    history.pushState(null, '', document.URL)
    window.addEventListener('popstate', function () {
      history.pushState(null, '', document.URL)
      if (store.state.isPlanStyle) {
        sceneEdit2DRef.value && sceneEdit2DRef.value.exitEdit()
      } else {
        sceneEdit3DRef.value && sceneEdit3DRef.value.exitEdit()
      }
    })
  }
})
</script>
<style scoped lang="less">
.show-icon {
  width: 50px;
  height: 50px;
  position: fixed;
  left: -50%;
  top: -50%;
  background-size: 100% 100%;
  z-index: 11;
  border-radius: 5px;
}
</style>