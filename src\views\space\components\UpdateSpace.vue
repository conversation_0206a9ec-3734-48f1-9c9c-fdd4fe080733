<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div>修改空间名称</div>
      </div>
      <div class="modal-form">
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" class="demo-ruleForm">
          <el-form-item prop="spaceName">
            <el-input
              class="form-input"
              v-model="ruleForm.spaceName"
              placeholder="请输入空间名称"
              maxlength="20"
              clearable />
          </el-form-item>
          <el-form-item class="form-submit">
            <div class="btn-default el-size3" style="position: relative; top: 10px">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3" style="position: relative; top: 10px">
              <el-button @click="submitForm(ruleFormRef)">确认</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { updateSpaceName } from '@/api';
import { desensitizte } from '@/utils';

const props = defineProps({
  handleHide: {
    default: null,
    type: Function,
  },
  defaultValue: {
    default: null,
    type: Object,
  },
});
const spaceNameError = ref(false);
interface RuleForm {
  spaceName: string;
}

const validatePass = (rule: any, value: any, callback: any) => {
  const reg = /^[a-zA-Z0-9]+$/;
  if (!value) {
    callback(new Error('*支持1~20个字符的名称'));
    spaceNameError.value = true;
    return;
  }
  if (value.length < 1 || value.length > 20) {
    callback(new Error('*支持1~20个字符的名称'));
    spaceNameError.value = true;
    return;
  }
  if (!reg.test(value)) {
    callback(new Error('*仅支持大小写英文字母、数字或组合，不含特殊字符'));
    spaceNameError.value = true;
    return;
  }
  spaceNameError.value = false;
  callback();
};

const ruleFormRef = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  spaceName: '',
});

const rules = reactive<FormRules<RuleForm>>({
  spaceName: [{ required: true, validator: validatePass, trigger: 'blur' }],
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      ruleForm.spaceName = ruleForm.spaceName.trim();
      const hasSensitiveWords = await desensitizte(
        ruleForm.spaceName,
        '空间名称不可包含敏感词汇！'
      );
      if (hasSensitiveWords) return;
      updateSpaceName({ ...ruleForm }).then((res: any) => {
        props.handleHide(true);
      });
    } else {
      console.log('error submit!', fields);
    }
  });
};

const changeState = () => {
  props.handleHide();
};

onMounted(() => {
  // 编辑页初始数据
  if (props.defaultValue.id) {
    ruleForm.spaceName = props.defaultValue.descriptionName || '';
    ruleForm.spaceId = props.defaultValue.id;
  }
});
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;

  .modal-content {
    width: 470px;
    height: 199px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -235px;
    margin-top: -116px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;

    .modal-content-title {
      padding: 16px 0 0 24px;
      font-weight: bold;
      font-size: 20px;
      color: #1e1e1e;
      text-align: left;
    }

    .modal-form {
      width: 100%;
      padding: 0 24px;
      box-sizing: border-box;
      position: relative;
      top: 20px;

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-submit {
        margin-top: 92px;
      }

      .tips-text {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        margin-top: -19px;
        text-align: left;
        position: relative;
        top: 8px;
      }
    }
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;

  & > button {
    box-shadow: none;
    border-radius: 4px;
  }
}

.btn-primary .el-size3 > button {
  background: #2e76ff;
}
</style>
