<template>
  <div class="base-header">
    <img
      class="logo"
      src="@/assets/images/mixspace-logo.png"
      @click="[6, 7].includes(userType) ? router.push('/experience_home') : router.push('/home')" />
    <div class="page-title">
      <!-- <span
        v-if="
          routeNameMap[currentRoute.value] != '首页' &&
          routeNameMap[currentRoute.value] != '我的项目'
        "
        >{{ routeNameMap[currentRoute.value] }}1</span
      > -->
      <!-- <div class="show-storage">
        <div v-if="routeNameMap[currentRoute.value] == '模板广场'" class="tab-box">
          <div class="tab-text" :class="{ tabTextActive: activeSceneTab.name == item.name }"
            v-for="item in scenePlatformTabs" :key="item.value" @click="activeTab(item)">{{ item.name }}11</div>
        </div>
      </div> -->
    </div>
    <div class="four-query">
      <div class="temp-tip" v-if="isShowExpireDay && expireDay <= 30">
        <span>当前账号剩余：{{ expireDay }}天</span>
        <img src="../../assets/images/chahao.png" alt="" @click="isShowExpireDay = false" />
      </div>
      <div v-loading="isUserLoading" @click="showUserInfo" style="cursor: pointer" class="head">
        <img
          class="avatar"
          :src="profilePic"
          :style="{ marginRight: userName ? '4px' : 0 }"
          @load="loadImage" />
        <div class="user-name" v-if="![6, 7].includes(userType)">
          {{ userName }}
        </div>
        <div class="user-phone" v-else>{{ phoneNumber }}</div>
      </div>
    </div>
  </div>
  <!-- 旧用户信息弹窗，已被新组件替换 -->
  <!--
  <user-info
    v-if="
      (modalShow == 'info' || modalShow == 'experience') &&
      userInfo.userDto.packageVersion != 'V2'
    "
    :change-event="changeEvent"
    :data="userInfo"
    :userType="userType"
  ></user-info>
  <user-info2
    v-if="
      (modalShow == 'info' || modalShow == 'experience') &&
      userInfo.userDto.packageVersion == 'V2'
    "
    :change-event="changeEvent"
    :data="userInfo"
    :userType="userType"
  ></user-info2>
  -->
  <user-info-new
    v-if="modalShow == 'info' || modalShow == 'experience'"
    :change-event="changeEvent"
    :data="userInfo"
    :user-type="userType" />
  <change-pass-word
    v-if="modalShow == 'password'"
    :change-event="changeEvent"
    :userType="userType"></change-pass-word>
</template>

<script lang="ts" setup>
import { onMounted, watch, ref } from 'vue';
import { onBeforeRouteLeave, onBeforeRouteUpdate, useRouter, useRoute } from 'vue-router';
import UserInfoNew from '@/views/user/components/profile/UserInfoNew.vue';
import ChangePassWord from '@/views/auth/ChangePassWord.vue';
import PersonalAccount from '@/components/experience/PersonalAccount.vue';
import AccountUpgrades from '@/components/experience/AccountUpgrade.vue';
import SelfProgress from '@/components/SelfProgress.vue';
import { getOrgnizationPackage, getUserTypeByToken, getOssAccessPath } from '@/api';
import { useStore } from 'vuex';
import { routeNameMap, scenePlatformTabs } from '@/config';

const props = defineProps({
  currentRoute: {
    default: null,
    type: Object,
  },
});
const phoneNumber = window.localStorage.getItem('phoneNo');
const expireDay = ref(31);
const isShowExpireDay = ref(true);
const store = useStore();
const userInfo: any = ref({});
const route = useRoute();
const router = useRouter();
const modalShow = ref(''); // 显示新建
const userName = ref('');
const userType = ref(0);

const isUserLoading = ref(true);
const profilePic = ref('');
const spaceStorage: any = ref({}); // 空间容量
const materialStorage: any = ref({}); // 素材容量
const homeStorage: any = ref({});
const activeSceneTab = ref(scenePlatformTabs[0]);

const loadImage = () => {
  isUserLoading.value = false;
};

const changeEvent = (value: string) => {
  modalShow.value = value;
};

const showUserInfo = () => {
  if (modalShow.value) {
    modalShow.value = '';
    return;
  }
  if (![6, 7].includes(userType.value)) {
    modalShow.value = 'info';
  } else {
    modalShow.value = 'experience';
  }
};

watch(
  () => store.state.profilePic,
  () => {
    profilePic.value = store.state.profilePic;
    userInfo.value.userDto.profilePic = store.state.profilePic;
    userInfo.value.userDto.profilePicture = store.state.profilePicture;
  }
);

watch(
  () => route.path,
  (nv) => {
    if (nv == '/square') {
      activeSceneTab.value = scenePlatformTabs[0];
      store.state.activeSceneTab = scenePlatformTabs[0];
    }
  }
);

const activeTab = (item: any) => {
  activeSceneTab.value = item;
  store.state.activeSceneTab = item;
};

onMounted(() => {
  activeSceneTab.value = scenePlatformTabs[0];
  getUserTypeByToken().then((res: any) => {
    userType.value = res.data;
    getOrgnizationPackage({}).then((res: any) => {
      expireDay.value = res.data.expireDay;
      userName.value = res.data.orgnizationDto
        ? res.data.orgnizationDto.orgnizationName
        : '体验用户';
      userInfo.value = { ...res.data };
      if (res.data.userDto.packageVersion == 'V2') {
        // 保持兼容性，使用 sessionStorage
        window.sessionStorage.setItem(
          'packageInfoDto',
          JSON.stringify(res.data.userBindPackageDto)
        );
        store.state.hasSpaceAr = !!res.data.userBindPackageDto.spaceAr;
      } else {
        // V1 版本的处理
        window.sessionStorage.setItem('packageInfoDto', JSON.stringify(res.data.packageInfoDto));
      }

      // 保持兼容性，使用 sessionStorage
      window.sessionStorage.setItem('userDto', JSON.stringify(res.data.userDto));
      window.sessionStorage.setItem(
        'userBindPackageDto',
        JSON.stringify(res.data.userBindPackageDto)
      );

      // 直接设置 store state（这些字段可能没有对应的 actions）
      store.state.isOperation = res.data.userDto.isOperation;
      if (res.data.userDto?.profilePicture) {
        getOssAccessPath({ key: res.data.userDto.profilePicture }).then((res: any) => {
          profilePic.value = res.data;
        });
      } else {
        profilePic.value = require('@/assets/images/ailongmask.png');
      }
    });
  });
  document?.addEventListener('click', (e: any) => {
    if (
      e.target.className == 'experience-home' ||
      e.target.parentNode?.className == 'experience-home' ||
      e.target.className == 'base-header' ||
      e.target.className == 'content-box'
    ) {
      modalShow.value = '';
    }
  });
});

watch(
  () => store.state.storageData,
  (newState) => {
    spaceStorage.value = newState.space;
    materialStorage.value = newState.material;
    homeStorage.value = newState.home;
  },
  { deep: true }
);
</script>

<style scoped lang="less">
.base-header {
  position: relative;
  height: 69px;
  // background-color: #F5F6F7;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px 0 28px;
  font-weight: 600;
  font-size: 20px;
  color: #3d566c;
  line-height: 28px;
  z-index: 1;
  padding-left: 41px;

  .head {
    position: relative;
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 14px;
    color: #000000;

    &:hover {
      color: #1251c8;

      &::before {
        content: '';
        width: 24px;
        height: 24px;
        position: absolute;
        left: 0;
        top: 2px;
        background-image: url(~@/assets/images/admin-mask.png);
        background-size: 100% 100%;
      }
    }
  }

  .temp-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 6px 12px;
    box-sizing: border-box;
    background: #ffe8cf;
    border-radius: 42px 42px 42px 42px;
    margin-right: 20px;

    span {
      font-weight: 500;
      font-size: 14px;
      color: #f25a14;
    }

    img {
      width: 20px;
      height: 20px;
      margin-left: 10px;
      transform: translateY(-1px);
    }
  }

  .tab-box {
    .tab-text {
      font-weight: 400;
      font-size: 12px;
      color: #797979;
      margin-right: 18px;
      line-height: 34px;
      text-align: center;
      border-radius: 4px;
      padding: 0 6px;

      &:hover {
        cursor: pointer;
        background: rgba(36, 118, 253, 0.1);
      }
    }
  }

  .tabTextActive {
    background: rgba(36, 118, 253, 0.1);
    color: #2e76ff !important;
  }

  .show-storage {
    div {
      display: flex;
      align-items: center;
    }

    .show-storage-progress {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      ::v-deep(.progress-box) {
        margin-left: 16px;
      }
    }
  }

  .block {
    position: absolute;
    top: -13px;
    left: 9px;
  }

  .progress {
    display: block;
    transform: translateY(7px);
    margin-left: 24px;
  }

  .add_btn {
    width: 83px;
    height: 34px;
    background: #2e76ff;
    border-radius: 10px;
    font-weight: 500;
    font-size: 12px;
    color: #ffffff;
    line-height: 26px;

    i {
      font-size: 11px;
      margin-right: 4px;
      position: relative;
      top: -1px;
    }

    &:hover {
      background-color: #1251c8;
    }
  }

  & > img {
    width: 142px;
    height: auto;
    cursor: pointer;
  }

  .page-title {
    position: absolute;
    left: 263px;
    top: 22px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    & > span {
      font-weight: bold;
      font-size: 16px;
      color: #1e1e1e;
    }
  }

  .four-query {
    display: flex;
    align-items: center;
    overflow: hidden;

    img {
      border-radius: 50%;
    }
  }

  .save-box {
    margin-left: 20px;
  }

  .button {
    width: 150px;
  }

  .account-upgrade {
    width: 76px;
    height: 30px;
    line-height: 30px;
    border-radius: 6px;
    border: 1px solid #3671fe;
    font-weight: bold;
    font-size: 14px;
    color: #3d566c;
    display: inline-block;
    margin-right: 20px;
    cursor: pointer;
  }

  & > div > div > img {
    width: 24px;
    height: 24px;
    object-fit: cover;
    vertical-align: middle;
    margin-right: 6px;
    cursor: pointer;
  }
}

.img_ {
  width: 212px !important;
  height: 24px;
  vertical-align: middle;
  margin-right: 15px;
}
</style>
