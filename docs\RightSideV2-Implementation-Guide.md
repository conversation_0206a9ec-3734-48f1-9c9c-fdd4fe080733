# RightSideV2 组件实现指南

## 🎯 组件概述

RightSideV2 是为 edit-v2 页面专门设计的右侧属性面板组件，提供了素材属性、互动区域属性和公共区域属性的编辑功能。

## 📋 主要功能

### 1. 属性面板类型
- **公共区域属性**：场景基本信息编辑
- **素材属性**：选中素材的详细属性配置
- **互动区域属性**：互动区域的配置选项

### 2. 素材类型支持
- **视频素材**：自动播放、循环播放控制
- **音频素材**：自动播放、循环播放控制
- **图片素材**：透明度调节
- **模型素材**：缩放比例调节

### 3. 交互功能
- **动态切换**：根据选中对象自动切换属性面板
- **实时更新**：属性修改实时同步到 Vuex 状态
- **侧边栏折叠**：支持隐藏/显示切换

## 🏗️ 技术架构

### 组件结构
```
RightSideV2.vue
├── 模板层 (Template)
│   ├── 属性面板标题
│   ├── 互动区域属性
│   ├── 素材属性
│   ├── 公共区域属性
│   ├── 行为树组件
│   └── 侧边栏切换按钮
├── 逻辑层 (Script)
│   ├── Props 定义
│   ├── 响应式数据
│   ├── 计算属性
│   ├── 方法定义
│   └── 生命周期钩子
└── 样式层 (Style)
    ├── 布局样式
    ├── 组件样式
    └── 响应式样式
```

### 核心数据流
```
Vuex Store ←→ RightSideV2 ←→ Parent Component
     ↓              ↓              ↓
  状态管理      属性编辑        事件回调
```

## 🔧 使用方法

### 基本用法
```vue
<template>
  <right-side-v2
    :change-scene-value="changeSceneValue"
    :change-active-value="changeActiveValue" />
</template>

<script>
import RightSideV2 from '@/components/scene_web/RightSideV2.vue';

export default {
  components: {
    RightSideV2
  },
  methods: {
    changeSceneValue(key, value) {
      // 处理场景属性变更
      console.log('场景属性变更:', key, value);
    },
    changeActiveValue() {
      // 处理活动对象属性变更
      console.log('活动对象属性变更');
    }
  }
};
</script>
```

### Props 配置
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `changeSceneValue` | Function | null | 场景属性变更回调 |
| `changeActiveValue` | Function | null | 活动对象属性变更回调 |

## 📊 状态管理

### Vuex 状态依赖
```javascript
// 监听的 Store 状态
store.state.activeMaterial        // 当前选中素材
store.state.activeInteraction     // 当前选中互动区域
store.state.editSceneData         // 编辑场景数据
store.state.materialAttributeInfo // 素材属性信息
store.state.interactionBackgroundMusic // 互动区域背景音乐
store.state.sceneType             // 场景类型
store.state.isTriggerAsset        // 是否触发资产
```

### 响应式数据
```javascript
const hide_side = ref(false);           // 侧边栏显示状态
const showType = ref(0);                // 属性面板类型
const activeMaterial = ref('');         // 当前活动素材
const activeInteraction = ref('');      // 当前活动互动区域
const materialAttributeInfo = ref({});  // 素材属性信息
const interactionBackgroundMusic = ref({}); // 互动区域信息
const sceneInfoList = ref([]);          // 场景信息列表
```

## 🎨 UI 设计

### 布局规范
- **面板宽度**：320px
- **面板高度**：calc(100vh - 63px)
- **位置**：固定在右侧，top: 63px
- **层级**：z-index: 100

### 样式特性
- **响应式设计**：支持侧边栏折叠动画
- **统一风格**：与 LeftSideV2 保持一致的设计语言
- **交互反馈**：按钮悬停、选中状态等视觉反馈

### 颜色规范
- **主色调**：#3671fe (蓝色)
- **背景色**：#ffffff (白色)
- **边框色**：#e8e8e8 (浅灰)
- **文字色**：#333 (深灰), #666 (中灰)

## 🔄 功能特性

### 1. 动态属性面板
```javascript
// 根据选中对象自动切换面板类型
watch(() => store.state.activeMaterial, (newVal) => {
  activeMaterial.value = newVal;
  showType.value = newVal ? 1 : 0; // 1: 素材属性
});

watch(() => store.state.activeInteraction, (newVal) => {
  activeInteraction.value = newVal;
  showType.value = newVal ? 2 : 0; // 2: 互动区域属性
});
```

### 2. 素材类型特定属性
```javascript
// 视频/音频属性
- 自动播放开关
- 循环播放开关

// 图片属性  
- 透明度滑块 (0-1)

// 模型属性
- 缩放比例输入 (0.1-10)
```

### 3. 实时数据同步
```javascript
// 属性变更自动同步到 Store
const changeMaterialProperty = (property) => {
  if (props.changeActiveValue) {
    props.changeActiveValue();
  }
};
```

## 🧪 测试验证

### 功能测试清单
- ✅ 属性面板类型切换
- ✅ 素材属性编辑
- ✅ 互动区域属性编辑
- ✅ 场景属性编辑
- ✅ 侧边栏折叠/展开
- ✅ 数据实时同步
- ✅ 行为树组件集成

### 兼容性测试
- ✅ 与 LeftSideV2 协同工作
- ✅ 与 edit-v2 页面集成
- ✅ Vuex 状态管理兼容
- ✅ 响应式布局适配

## 🚀 部署集成

### 1. 文件位置
```
src/components/scene_web/RightSideV2.vue
```

### 2. 页面集成
```vue
<!-- src/views/space/edit-v2/index.vue -->
<template>
  <right-side-v2
    :change-scene-value="changeSceneValue"
    :change-active-value="changeActiveValue" />
</template>
```

### 3. 依赖组件
- `BehaviorTree.vue` - 行为树组件
- `TipsView.vue` - 提示组件
- Element Plus - UI 组件库

## 📈 性能优化

### 1. 响应式优化
- 使用 `watch` 监听特定状态变化
- 避免不必要的深度监听
- 合理使用 `computed` 计算属性

### 2. 渲染优化
- 条件渲染减少 DOM 节点
- 合理使用 `v-show` 和 `v-if`
- 组件懒加载

### 3. 内存管理
- 组件卸载时清理监听器
- 避免内存泄漏

## 🔮 扩展计划

### 未来功能
1. **更多素材类型支持**
   - 3D 模型高级属性
   - 动画控制选项
   - 材质属性编辑

2. **高级交互功能**
   - 属性预设保存/加载
   - 批量属性编辑
   - 属性动画时间轴

3. **UI/UX 增强**
   - 属性分组折叠
   - 搜索过滤功能
   - 快捷键支持

## 📝 维护说明

### 代码规范
- 遵循 Vue 3 Composition API 最佳实践
- 使用 TypeScript 类型定义
- 保持代码注释完整性

### 更新指南
1. 修改组件时保持向后兼容
2. 新增功能时更新文档
3. 测试所有相关功能点
4. 确保与其他组件协调工作

这个 RightSideV2 组件为 edit-v2 页面提供了完整的右侧属性编辑功能，与 LeftSideV2 形成完整的编辑器界面体系。
