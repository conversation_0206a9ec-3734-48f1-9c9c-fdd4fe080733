<template>
  <div class="table-module" :class="changeBg ? 'tableBg' : 'noBg'">
    <!-- <div class="create">
      <div class="btn-primary el-size1" v-if="createList">
        <el-button @click="handleCreate" class="qr_code">
          <i class="icon iconfont icon-add"></i>{{ createList }}</el-button>
      </div>
      <div class="btn-primary el-size1" v-if="qrCode">
        <el-button @click="handleQrCode" class="qr_code" type="primary"
          ><i class="icon iconfont icon-add"></i>扫码添加设备</el-button
        >
      </div>
    </div> -->
    <el-table
      :data="data"
      ref="tableRef"
      :row-class-name="tableRowClassName"
      @select="handleSelectionChange"
      @select-all="handleSelectionChange"
      :style="{
        width: '100%',
        maxHeight: pageSize == 20 ? '880px' : '890px',
        height: (heightSize || tableHeight).slice(0, -2) + 'px',
      }"
      class="styleTable"
      @filter-change="filterChange"
      :class="!data.length && emptyImage ? 'noData' : ''">
      <el-table-column type="selection" width="45" v-if="showSelect" />
      <el-table-column
        v-for="item in columnList"
        :prop="item.prop"
        :key="item.prop"
        :label="item.label"
        :show-overflow-tooltip="true"
        :width="item.width"
        :fixed="item.prop === 'operate' ? 'right' : null"
        :filters="item.filters"
        :column-key="item.prop"
        :filter-method="item.filters ? filterHandler : null"
        :filter-multiple="false"
        :align="item.prop === 'operate' ? 'center' : 'left'"
        :header-align="item.prop === 'operate' ? 'center' : 'left'"
        filter-class-name="filter-style">
        <template #header>
          <span>{{ item.label }}</span>
          <span
            class="view-details"
            v-if="item.prop == 'visitCount'"
            @click="
              () => {
                showDetails();
              }
            ">
            查看详情
          </span>
        </template>
        <template #filter-icon>
          <img
            v-if="
              filterData[item.resetKey || item.prop] != 0 && !filterData[item.resetKey || item.prop]
            "
            class="filter-icon"
            :style="{
              paddingLeft: item.label.length * 12 + 2 + 'px',
              marginLeft: -item.label.length * 12 + 'px',
            }"
            src="@/assets/images/icon/filter-icon.png"
            @click="resetFilterStyle(item.label)" />
          <img
            v-if="
              filterData[item.resetKey || item.prop] || filterData[item.resetKey || item.prop] == 0
            "
            class="filter-icon"
            :style="{
              paddingLeft: item.label.length * 12 + 2 + 'px',
              marginLeft: -item.label.length * 12 + 'px',
            }"
            src="@/assets/images/icon/filter-iconA.png"
            @click="resetFilterStyle(item.label)" />
        </template>
        <template #default="scope">
          <!-- 操作栏按钮部分 -->
          <div v-if="item.prop === 'operate'" class="operate-column">
            <el-popover
              v-model:visible="scope.row.popoverVisible"
              :width="110"
              trigger="click"
              placement="left-start"
              popper-class="operate-popover"
              :teleported="true"
              :persistent="false"
              :hide-after="0"
              :popper-options="{
                strategy: 'fixed',
                modifiers: [
                  {
                    name: 'preventOverflow',
                    options: {
                      boundary: 'viewport',
                    },
                  },
                  {
                    name: 'computeStyles',
                    options: {
                      adaptive: false,
                    },
                  },
                ],
              }"
              :style="{ width: '110px', minWidth: '110px', maxWidth: '110px' }"
              @show="showOperatePopover(scope.row)"
              @hide="hideOperatePopover">
              <template #reference>
                <el-button class="more-btn">
                  <el-icon class="rotate-icon"><MoreFilled /></el-icon>
                </el-button>
              </template>
              <div class="operate-buttons">
                <div
                  v-for="op in operationItems"
                  :key="op.key"
                  v-show="!op.show || op.show(scope.row)"
                  class="operate-item"
                  :class="{ 'disabled-item': op.disabled && op.disabled(scope.row) }"
                  @click="handleOperationClick(op, scope.row)">
                  {{ typeof op.label === 'function' ? op.label(scope.row) : op.label }}
                </div>
              </div>
            </el-popover>
          </div>

          <!-- 客户名称 -->
          <span
            v-if="item.customType == 'orgnizationName'"
            :class="{ greyColor: [2, 4, 5].includes(scope.row.userStatus) }">
            {{ scope.row.orgnizationName }}
          </span>
          <!-- 客户类型 -->
          <span
            v-if="item.customType == 'orgnizationType'"
            :class="{ greyColor: [2, 4, 5].includes(scope.row.userStatus) }">
            {{ scope.row.orgnizationType }}
          </span>
          <!-- 套餐类型 -->
          <span
            v-if="item.customType == 'packageName'"
            :class="{ greyColor: [2, 4, 5].includes(scope.row.userStatus) }">
            {{ scope.row.adminUserInfo.packageVersion != 'V2' ? scope.row.packageName : '定制版' }}
          </span>
          <!-- 账号 -->
          <div
            v-if="item.customType == 'mail'"
            class="mail_box"
            :class="{ greyColor: [2, 4, 5].includes(scope.row.userStatus) }">
            {{ scope.row.mail }}
            <div
              class="operaIcon"
              v-if="
                scope.row.adminUserInfo.isOperation == 1 ||
                scope.row.userBindPackageDto?.isOperate == 1
              "
              :class="{ disableOpacity: [2, 4, 5].includes(scope.row.userStatus) }">
              运营
            </div>
          </div>
          <!-- 有效时间 -->
          <span
            v-if="item.customType == 'cycleTime'"
            :class="{ greyColor: [2, 4, 5].includes(scope.row.userStatus) }"
            :style="{ color: scope.row.userStatus == 3 ? '#F25A14' : '' }">
            {{ (scope.row.packageStartTimeStr || 0) + '-' + scope.row.expireTimeStr }}
          </span>
          <!-- 账号状态 -->
          <div v-if="item.customType == 'userStatus'" class="diymaic_status">
            <div class="cicle" :style="cicleStyle(scope.row)"></div>
            <span :style="fontStyle(scope.row)">
              {{ dymaicStatus(scope.row) }}
            </span>
          </div>

          <!-- 设备浏览时间 -->
          <span v-if="item.isDeviceTime" class="justify-text">
            <span
              v-for="itemText in scope.row.createTimeStr.split('')"
              :class="itemText == ' ' ? 'empty-style' : ''">
              {{ itemText }}
            </span>
          </span>

          <!-- 空间列表中的场景名称列表显示 -->
          <div v-if="item.spaceType == 'usedSpaceSceneNames'" class="used-space-scene-names">
            <div v-for="(e, i) in scope.row[item.spaceType].slice(0, 3)" :key="i">
              {{ e }}
            </div>
            <el-tooltip
              v-if="scope.row[item.spaceType].length > 3"
              class="box-item"
              effect="dark"
              placement="bottom"
              :disabled="scope.row[item.spaceType].length <= 3">
              <template #content>
                <div v-for="(sceneName, index) in scope.row[item.spaceType]" :key="index">
                  {{ sceneName }}
                </div>
              </template>
              <div class="ellipsis-style">……</div>
            </el-tooltip>
          </div>

          <span v-if="item.customType == 'platform' && getPlatformDisplay(scope.row) === '1,2,3'">
            全平台
          </span>
          <div v-if="item.type === 'image'" class="image-wrapper">
            <img
              :src="scope.row[item.prop] || (item.url && item.url[0][scope.row[item.url[1]]].url)"
              class="main-image"
              :style="{
                width: item.imageWidth ? item.imageWidth + 'px' : '90px',
                height: item.imageHeight ? item.imageHeight + 'px' : '90px',
              }" />
            <img v-if="scope?.row?.aiGen === 1" src="@/assets/images/isAiImg.png" class="ai-icon" />
          </div>
          <span v-if="item.list">{{ item.list[scope.row[item.prop]] }}</span>
          <span v-if="item.formatter">{{ item.formatter(scope.row[item.prop]) }}</span>
          <div v-if="item.segm && getPlatformDisplay(scope.row) !== '1,2,3'">
            <div v-for="it in getPlatformDisplay(scope.row)?.split(',') || []" :key="it">
              {{ platformsMap[it] }}
            </div>
          </div>
          <!-- 默认显示逻辑，处理空值情况，但排除操作栏 -->
          <span
            v-if="
              !item.customType &&
              !item.list &&
              !item.formatter &&
              !item.segm &&
              !item.isDeviceTime &&
              !item.spaceType &&
              !item.type &&
              item.prop !== 'operate'
            ">
            {{ scope.row[item.prop] || '-' }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <div class="empty-data-style" v-if="!data.length && emptyImage">
      <img :src="emptyImage" />
      <span>暂无数据</span>
    </div>
    <div v-if="!!changePage && data.length" class="pagination">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="dataTotal"
        :current-page="pageNo"
        prev-text="上一页"
        next-text="下一页"
        @current-change="changePageEvent"
        :pager-count="5"
        :page-size="pageSize"></el-pagination>
    </div>
  </div>
  <tips-view
    v-if="currentData"
    :tips-title="computedTipsTitle"
    :show-btn="showDelBtn ? '确定' : ''"
    :sure-event="deleteEvent"
    :cancle-event="() => (currentData = null)">
    <slot></slot>
  </tips-view>

  <el-dialog v-model="flagVisible" title="提示" width="453" top="32vh">
    <span>{{ tipText }}</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="flagVisible = false" style="width: 92px; height: 32px">取消</el-button>
        <el-button
          type="primary"
          @click="requestHandle"
          style="width: 92px; height: 32px"
          color="#2E76FF">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <preview-scene
    v-if="spaceInfo.roomStructureKey"
    :spaceInfo="spaceInfo"
    :hideModel="hidePreview"></preview-scene>
</template>

<script lang="ts" setup>
import {
  ref,
  reactive,
  computed,
  onMounted,
  watch,
  nextTick,
  onUnmounted,
  type Ref,
  type PropType,
} from 'vue';
import { useStore } from 'vuex';
import TipsView from './TipsView.vue';
import PreviewScene from '@/components/template/PreviewScene.vue';
import { MoreFilled } from '@element-plus/icons-vue';
import type { TableRow, ColumnItem, AsyncConfirmData, OperationItem } from '@/types/table';

const currentData: Ref<TableRow | null> = ref(null);
const tipsTitle = ref('');
const emit = defineEmits(['againStart']);

const tableRowClassName = (row: { row: TableRow }) => {
  // userStatus === 2: 已停用
  // userStatus === 4: 已过期
  return [2, 4].includes(row.row.userStatus) ? 'disabled-row' : '';
};
// 修改计算属性，优先使用 deleteContent.title（支持动态更新）
const computedTipsTitle = computed(() => {
  const result = props.deleteContent?.title || tipsTitle.value || '提示';
  return result;
});
const flagVisible = ref(false);
const tipText = ref('');
let asyncConfirmData: AsyncConfirmData = {};
const tableRef = ref();
const selectDataArr: any = ref(null); // 多选被选中的数据
const props = defineProps({
  data: {
    default: () => [],
    type: Array as PropType<TableRow[]>,
  },
  columnList: {
    default: () => [],
    type: Array as PropType<ColumnItem[]>,
  },
  // 新增操作项配置
  operationItems: {
    type: Array as PropType<OperationItem[]>,
    default: () => [],
  },
  changePage: {
    default: null,
    type: Function,
  },
  changeBg: {
    default: null,
    type: String,
  },
  createList: {
    default: '',
    type: String,
  },
  handleCreate: {
    default: null,
    type: Function,
  },
  dataTotal: {
    default: 0,
    type: Number,
  },
  deleteContent: {
    default: null,
    type: Object,
  },
  deleteDataEvent: {
    default: null,
    type: Function,
  },
  pageSize: {
    default: 20,
    type: Number,
  },
  pageNo: {
    default: 1,
    type: Number,
  },
  showDelBtn: {
    default: true,
    type: Boolean,
  },
  qrCode: {
    default: false,
    type: Boolean,
  },
  handleQrCode: {
    default: null,
    type: Function,
  },
  showDetails: {
    default: null,
    type: Function,
  },
  showSelect: {
    default: false,
    type: Boolean,
  },
  handleFilter: {
    default: null,
    type: Function,
  },
  searchForm: {
    default: null,
    type: Object,
  },
  emptyImage: {
    default: null,
    type: String,
  },
  updataData: {
    default: null,
    type: Function,
  },
  heightSize: {
    default: '',
    type: String,
  },
});

const tableHeight = ref('auto');
const filterData: any = ref({});
const spaceInfo: any = ref({});

const store = useStore();

const changePageEvent = (e: any) => {
  props.changePage && props.changePage(e);
};
const platformsMap: any = {
  '3': '微信小程序',
  '2': '移动端',
  '1': '眼镜端',
};

// 根据素材类型和格式动态计算平台显示
const getPlatformDisplay = (row: any) => {
  const materialType = row.materialType;
  const materialFormat = row.materialFormat?.toLowerCase();

  // 如果不是模型类型（materialType !== 4），显示全平台
  if (materialType !== 4 && materialType !== '4') {
    return '1,2,3';
  }

  // 如果是模型类型
  if (materialType === 4 || materialType === '4') {
    // 如果有 materialFormat 字段
    if (materialFormat) {
      // 如果是 GLB 格式，显示全平台
      if (materialFormat === 'glb' || materialFormat === 'gltf') {
        return '1,2,3';
      }
      // 如果是 FBX 格式，显示眼镜端和移动端
      if (materialFormat === 'fbx') {
        return '1,2';
      }
    }

    // 如果没有 materialFormat 字段，默认显示全平台
    return '1,2,3';
  }

  // 默认返回全平台
  return '1,2,3';
};

const fontStyle = computed(() => {
  let _style = {};
  return (row: TableRow) => {
    switch (row.userStatus) {
      case 1:
        _style = { color: '#000000' };
        break;
      case 2:
        _style = { color: '#000000' };
        break;
      case 3:
        _style = { color: '#F25A14' };
        break;
      case 4:
        _style = { color: '#BA1E45' };
        break;
      case 5:
        _style = { color: '#DADADA' };
    }
    return _style;
  };
});

const cicleStyle = computed(() => {
  let _style = {};
  return (row: TableRow) => {
    switch (row.userStatus) {
      case 1:
        _style = { backgroundColor: '#2CDD43' };
        break;
      case 2:
        _style = { backgroundColor: '#BA1E45' };
        break;
      case 3:
        _style = { backgroundColor: '#F25A14' };
        break;
      case 4:
        _style = { backgroundColor: '#BA1E45' };
        break;
      case 5:
        _style = { backgroundColor: '#DADADA' };
    }
    return _style;
  };
});

const dymaicHandleStatus = computed(() => {
  return (row: TableRow) => {
    const status = row.userStatus ?? 0;
    if ([1, 3].includes(status)) {
      // 使用中，快到期
      return '停用';
    } else if ([2].includes(status)) {
      // 已停用
      return '启用';
    } else if ([4].includes(status)) {
      // 已过期
      return '重新开启';
    } else if ([5].includes(status)) {
      // 已注销
      return '   ';
    }
    return '';
  };
});

const dymaicStatus = computed(() => {
  return (row: TableRow) => {
    switch (row.userStatus) {
      case 1:
        return '使用中';
      case 2:
        return '已停用';
      case 3:
        return '即将过期';
      case 4:
        return '已过期';
      case 5:
        return '已注销';
    }
  };
});

const disableToolHandle = (
  event: ((data: TableRow, isEdit?: boolean) => void) | null,
  data: TableRow,
  isEdit?: boolean
) => {
  const status = data.userStatus ?? 0;
  if ([2, 4, 5].includes(status)) {
    return;
  }
  if (event) handleEdit(event, data, isEdit);
};

const previewScene = (data: any, event?: any) => {
  if (event && typeof event === 'function') {
    // 路径导航等有自定义预览逻辑的页面
    console.log('✅ 调用自定义预览事件');
    event(data);
  } else {
    // 空间管理等使用TableList内置预览的页面
    console.log('✅ 使用内置预览逻辑');
    spaceInfo.value = { ...data };
  }
};

const hidePreview = () => {
  spaceInfo.value.roomStructureKey = '';
  if (props.updataData) {
    props.updataData();
  }
};

const handleEdit = (
  event: (data: TableRow, isEdit?: boolean) => void,
  data: TableRow,
  isEdit?: boolean,
  dymaicStatus?: string
) => {
  if (isEdit == false) {
    flagVisible.value = true;
    asyncConfirmData = {
      event,
      data,
      isEdit,
      dymaicStatus,
    };
  } else {
    event(data, isEdit);
  }
  const str =
    asyncConfirmData.dymaicStatus && asyncConfirmData.dymaicStatus == '启用'
      ? '启用后账号有效时间不变'
      : '';
  tipText.value = `确定要${asyncConfirmData.dymaicStatus || ''}该账号吗？${str}`;
};

const resetFilterStyle = (lable: string) => {
  const filterDoms: any = document.querySelectorAll('.filter-style');
  if (filterDoms.length) {
    setTimeout(() => {
      filterDoms.forEach((dom: any) => {
        if (dom.getBoundingClientRect().width) {
          if (lable.length == 2) {
            dom.style.marginLeft = '-66px';
          }
        }
      });
    });
  }
};

const requestHandle = () => {
  if (asyncConfirmData.dymaicStatus && asyncConfirmData.dymaicStatus == '重新开启') {
    emit('againStart', asyncConfirmData.data);
  } else if (asyncConfirmData.event && asyncConfirmData.data) {
    asyncConfirmData.event(asyncConfirmData.data, asyncConfirmData.isEdit);
  }
  flagVisible.value = false;
};

const customDelete = (event: any, data: any) => {
  currentData.value = data;
  if (event) {
    event(data);
  }
};

const handleDelete = (event: any, data: any) => {
  currentData.value = data;
  // 不再设置 tipsTitle.value，让它完全依赖 deleteContent.title 的动态变化
  store.state.showTips = props.deleteContent.content;
};

const deleteEvent = () => {
  props.deleteDataEvent(currentData.value);
  currentData.value = null;
  store.state.showTips = '';
};

const changeTableSize = () => {
  const el_table__body_wrapper: any = document
    .querySelector('.el-table__body-wrapper')
    ?.getBoundingClientRect().height;
  const el_table__body: any = document
    .querySelector('.el-table__body')
    ?.getBoundingClientRect().height;
  const styleTableDom: any = document.querySelector('.styleTable');
  const lastTd: any = document.querySelectorAll('.el-table tbody>tr:last-child td');
  if (el_table__body_wrapper > el_table__body) {
    styleTableDom.style.borderBottom = 'none';
    lastTd.forEach((dom: any) => {
      dom.style.borderBottom = '1px solid #E6EDF7';
    });
  } else {
    styleTableDom.style.borderBottom = '1px solid #E6EDF7';
    lastTd.forEach((dom: any) => {
      dom.style.borderBottom = 'none';
    });
  }
};

const handleSelectionChange = (val: any) => {
  if (selectDataArr.value) {
    selectDataArr.value[props.pageNo] = [...val];
  } else {
    selectDataArr.value = {
      [props.pageNo]: [...val],
    };
  }
  let sumSelectData = 0;
  Object.values(selectDataArr.value).forEach((e: any) => (sumSelectData += e.length));
  if (!sumSelectData) {
    selectDataArr.value = null;
  }
};

const filterHandler = (value: string, row: any, column: any) => {
  return true;
};

const filterChange = (newFilters: any) => {
  const currentKey = Object.keys(newFilters)[0];
  const currentObj = props.columnList.find((item: ColumnItem) => item.prop === currentKey);
  if (currentKey && currentObj) {
    props.handleFilter(currentObj.resetKey || currentKey, newFilters[currentKey]);
    filterData.value[currentObj.resetKey || currentKey] = newFilters[currentKey][0];
  }
};

const clearFilterEvent = () => {
  tableRef.value!.clearFilter();
  filterData.value = [];
};

// 添加新的响应式变量
const currentRow = ref<any>(null);
const currentItem = ref<any>(null);
const operatePopoverRef = ref<any>(null);

// 显示抽屉的方法
const showOperatePopover = (row: any) => {
  currentRow.value = row;
  currentItem.value = null; // Clear currentItem as it's no longer needed for the new operationItems
  // 确保 popoverVisible 属性存在并设置为 true
  if (!row.hasOwnProperty('popoverVisible')) {
    row.popoverVisible = true;
  } else {
    // 如果属性已存在，也要设置为 true 以确保弹框显示
    row.popoverVisible = true;
  }

  // 动态设置弹框宽度
  nextTick(() => {
    const popoverElement = document.querySelector('.operate-popover');
    if (popoverElement) {
      (popoverElement as HTMLElement).style.width = '110px';
      (popoverElement as HTMLElement).style.minWidth = '110px';
      (popoverElement as HTMLElement).style.maxWidth = '110px';
    }
  });
};

const hideOperatePopover = () => {
  currentRow.value = null;
  currentItem.value = null;
};

// 隐藏所有打开的弹框
const hideAllPopovers = () => {
  if (props.data && props.data.length > 0) {
    props.data.forEach((row: any) => {
      if (row.popoverVisible) {
        row.popoverVisible = false;
      }
    });
  }
  hideOperatePopover();
};

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 处理表格滚动事件（使用防抖优化性能）
const handleTableScroll = debounce(() => {
  hideAllPopovers();
}, 50);

// 处理操作按钮点击，自动隐藏弹框
const handleOperationClick = (op: any, row: any) => {
  if (op.disabled && op.disabled(row)) {
    return;
  }
  // 执行原始的回调函数
  if (op.onClick) {
    op.onClick(row);
  }
  // 隐藏弹框
  row.popoverVisible = false;
  hideOperatePopover();
};

// 抽屉中按钮的点击处理方法
const handlePreviewClick = () => {
  previewScene(currentRow.value, null); // Pass null for event as it's not a direct event handler
  hideOperatePopover();
};

const handleEditClick = () => {
  if (currentRow.value) {
    const status = currentRow.value.userStatus ?? 0;
    if (![2, 4, 5].includes(status)) {
      disableToolHandle(() => {}, currentRow.value, true);
    }
  }
  hideOperatePopover();
};

const handleDeleteClick = () => {
  if (currentItem.value && currentItem.value.resetHandle) {
    // currentItem.value is no longer used for operationItems
    customDelete(null, currentRow.value); // Pass null for event as it's not a direct event handler
  } else {
    handleDelete(null, currentRow.value); // Pass null for event as it's not a direct event handler
  }
  hideOperatePopover();
};

const handleExtendClick = () => {
  if (currentRow.value) {
    const status = currentRow.value.userStatus ?? 0;
    if (![2, 4, 5].includes(status)) {
      disableToolHandle(() => {}, currentRow.value);
    }
  }
  hideOperatePopover();
};

const handleExtendHistoryClick = () => {
  if (currentRow.value) {
    const status = currentRow.value.userStatus ?? 0;
    if (![2, 4, 5].includes(status)) {
      disableToolHandle(() => {}, currentRow.value);
    }
  }
  hideOperatePopover();
};

const handleRenameClick = () => {
  if (currentRow.value) {
    handleEdit(() => {}, currentRow.value, true);
  }
  hideOperatePopover();
};

onMounted(() => {
  window.addEventListener('resize', changeTableSize, false);
  // 监听页面滚动事件，确保弹框在任何滚动时都能隐藏
  window.addEventListener('scroll', handleTableScroll, true);

  // 监听表格滚动事件
  nextTick(() => {
    const tableElement = tableRef.value?.$el;
    if (tableElement) {
      const scrollContainer = tableElement.querySelector('.el-table__body-wrapper');
      if (scrollContainer) {
        scrollContainer.addEventListener('scroll', handleTableScroll, false);
      }
    }
  });

  // 监听DOM变化，确保弹框宽度设置
  const observer = new MutationObserver(() => {
    const popoverElement = document.querySelector('.operate-popover');
    if (popoverElement) {
      (popoverElement as HTMLElement).style.width = '110px';
      (popoverElement as HTMLElement).style.minWidth = '110px';
      (popoverElement as HTMLElement).style.maxWidth = '110px';
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', changeTableSize);
  // 移除页面滚动事件监听
  window.removeEventListener('scroll', handleTableScroll, true);

  // 移除表格滚动事件监听
  const tableElement = tableRef.value?.$el;
  if (tableElement) {
    const scrollContainer = tableElement.querySelector('.el-table__body-wrapper');
    if (scrollContainer) {
      scrollContainer.removeEventListener('scroll', handleTableScroll);
    }
  }
});

// 多选按钮的选择和记录选择状态
watch(
  () => props.data,
  (newState) => {
    if (selectDataArr.value && selectDataArr.value[props.pageNo]) {
      const currentSelectData: any = [];
      const dataIds = selectDataArr.value[props.pageNo].map((e: any) => e.id);
      newState.forEach((item: any) => {
        if (dataIds.includes(item.id)) {
          currentSelectData.push(item);
        }
      });
      nextTick(() => {
        currentSelectData.forEach((row: any) => {
          tableRef.value!.toggleRowSelection(row, undefined, undefined);
        });
      });
    }
    setTimeout(() => {
      changeTableSize();
    });
  }
);
defineExpose({
  selectDataArr,
  clearFilterEvent,
  currentData, // 暴露 currentData 以便父组件可以设置
});
</script>

<style lang="less">
.el-dialog__header {
  display: flex;
}

.el-dialog__body {
  text-align: left;
  padding: 20px 0px !important;
}

.el-popover.el-popper {
  background: #ffffff !important;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
  border: none !important;
  padding: 8px 0 !important;

  .el-popper__arrow {
    height: 0 !important;
    display: none !important;
  }
}

.operate-item {
  width: 100% !important;
  height: 26px !important;
  line-height: 26px !important;
  font-size: 14px !important;
  color: #1e1e1e !important;
  text-align: left !important;
  padding: 0 12px !important;
  margin: 0 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  box-sizing: border-box !important;
  &:hover {
    background-color: #f7fafd !important;
  }

  &.disabled-item {
    color: #999 !important;
    cursor: not-allowed !important;

    &:hover {
      background-color: transparent !important;
    }
  }
}
</style>

<style scoped lang="less">
.table-module {
  margin: 0 0 20px;

  .filter-icon {
    width: 16px;
    height: 16px;
    padding-left: 2px;
    margin-top: -3px;
  }

  .empty-data-style {
    margin-top: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    color: #797979;
    span {
      margin-top: 10px;
      font-size: 14px;
    }

    img {
      width: 140px;
      height: 140px;
    }
  }

  .justify-text {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

    .empty-style {
      margin: 0 3px;
    }
  }

  ::v-deep(input[aria-hidden='true']) {
    display: none !important;
  }

  ::v-deep(.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner) {
    box-shadow: none !important;
  }
}

:deep(.btn-next) {
  background: #fff !important;

  &:hover {
    background: #f0f2f5 !important;
  }
}

:deep(.btn-prev) {
  background: #fff !important;

  &:hover {
    background: #f0f2f5 !important;
  }
}

:deep(.el-dialog__title) {
  font-weight: bold;
  font-size: 18px;
  color: #1e1e1e;
}

.used-space-scene-names {
  height: 65px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  & > div {
    height: 16px;
    line-height: 16px;
  }

  & > .ellipsis-style {
    margin-top: -8px;
  }

  & > .ellipsis-mask {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }

  .scene-name-box {
    position: absolute;
    right: 0;
    top: 0;
    background-color: pink;
    transform: translateX(100%);
    z-index: 999999;
  }
}

.diymaic_status {
  display: flex;
  justify-content: center;
  align-items: center;
}

.cicle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.expiringSoon {
  color: #f25a14;
}

.disableBtn {
  color: #dadada !important;
  cursor: not-allowed;
}

.greyColor {
  color: #dadada !important;
}

.disableOpacity {
  opacity: 0.4;
}

.tableBg {
  background-image: url(~@/assets/images/background/home-title-bg3.png);
  background-size: 100% 100%;
  background-color: transparent;
}

.pagination {
  position: relative;
  margin: 32px 0 10px;
  display: flex;
  justify-content: center;
  align-items: center;

  .show-line {
    font-size: 14px;
    margin-left: 10px;
  }
}

.mail_box {
  display: flex;
  align-items: center;
}

.operaIcon {
  min-width: 42px;
  height: 24px;
  background: #d8c05e;
  border-radius: 8px 8px 8px 8px;
  font-size: 12px;
  color: #ffffff;
  line-height: 24px;
  margin-left: 10px;
}

.styleTable {
  background-color: transparent !important;
  border-bottom: 1px solid #e6edf7;
  border-radius: 4px;
  overflow: hidden;
  overflow-y: auto;
  height: calc(100vh - 322px);

  &.noData {
    height: auto;
  }

  .view-details {
    font-weight: 400;
    font-size: 12px;
    color: #2e76ff;
    text-decoration-line: underline;
    margin-left: 10px;
    cursor: pointer;
  }

  ::v-deep(.el-table__inner-wrapper::before) {
    content: none;
  }
}

::v-deep(.el-table__empty-block) {
  display: none;
}

::v-deep(.noData .el-scrollbar__wrap--hidden-default) {
  display: none;
}

.create {
  // text-align: left;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .qr_code {
    padding: 0 9px;

    i {
      font-size: 11px;
      margin-right: 4px;
      position: relative;
      top: -1px;
    }
  }

  .el-size1 {
    // width: 110px;
    height: 34px;
    box-sizing: border-box;
    margin-left: 0;
    display: inline-block;
    vertical-align: middle;
    z-index: 10;
  }
}

.table-box .create {
  margin-bottom: 16px;
}

.popover-btn {
  color: #f23333 !important;
}

.delete-box {
  margin-top: 22px;
  margin-bottom: 5px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .el-size {
    width: 43px;
    height: 20px;
    font-weight: 400;
    text-shadow: 0px 5px 10px rgba(200, 200, 200, 0.5);
    padding: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;

    span {
      font-size: 10px;
      transform: scale(0.83333);
      transform-origin: 0 0;
    }

    &.btn-sure {
      background: #e84a4b;
      box-shadow: 0px 5px 10px 0px rgba(200, 200, 200, 0.5),
        0px 5px 9px 0px rgba(235, 159, 159, 0.3);
      border-radius: 3px;
      color: #ffffff;
      border: none;

      &:hover {
        background: #e84a4b;
        box-shadow: 0px 0px 4px 4px rgba(255, 35, 35, 0.3);
      }

      &:active {
        background: #cf4243;
        box-shadow: 0px 5px 10px 0px rgba(255, 35, 35, 0.5);
      }
    }

    &.btn-cancle {
      background: #f7f8fa;
      box-shadow: 0px 5px 10px 0px rgba(200, 200, 200, 0.5),
        0px 5px 10px 0px rgba(185, 203, 225, 0.5);
      border-radius: 3px;
      color: #333333;
      border: none;

      &:hover {
        background: #f7f8fa;
        box-shadow: 0px 0px 4px 4px rgba(185, 203, 225, 0.3);
      }

      &:active {
        background: #edeff2;
        box-shadow: 0px 5px 10px 0px rgba(185, 203, 225, 0.5);
      }
    }
  }
}

.styleTable::v-deep(.el-table th.el-table__cell),
::v-deep(.el-table tr) {
  height: 42px;
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;

  & > td {
    border-bottom: 1px solid #e6edf7;
    box-sizing: border-box;
  }

  & > th {
    border-top: 1px solid #e6edf7 !important;
  }

  & > th:first-child {
    border-left: 1px solid #e6edf7 !important;
  }

  & > th:last-child {
    border-right: 1px solid #e6edf7 !important;
  }
}

:deep(.el-table tr td.el-table__cell:last-child) {
  border-right: 1px solid #e6edf7 !important;
}

:deep(.el-table tr td.el-table__cell:first-child) {
  border-left: 1px solid #e6edf7 !important;
}

.styleTable::v-deep(.el-table__header-wrapper th) {
  background-color: rgba(230, 237, 247, 0.3) !important;
  font-weight: 400;
  font-size: 12px;
  color: #797979;
}

.styleTable::v-deep(.el-table__header-wrapper tr) {
  height: 32px;
}

.styleTable::v-deep(.el-table__body tr.disabled-row),
.styleTable::v-deep(.el-table__body tr.disabled-row td) {
  background-color: #e6edf7 !important;
}

::v-deep(.el-table th) {
  text-align: center;
}

::v-deep(.el-table th:first-child) {
  text-align: left;
}

::v-deep(.el-table th:last-child) {
  text-align: center;
}

::v-deep(.el-table td) {
  text-align: center;
}

::v-deep(.el-table td:first-child) {
  text-align: left;
}

::v-deep(.el-table td:last-child) {
  text-align: center;
}

.operate-column {
  display: flex;
  justify-content: center;
  align-items: center;

  .more-btn {
    padding: 2px 4px;
    border: none;
    background: transparent;

    &:hover {
      background-color: #f7fafd;
    }
  }

  .rotate-icon {
    transform: rotate(90deg);
  }
}

:deep(.operate-popover) {
  padding: 8px 0;
  background: #ffffff !important;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
  border: none !important;
  position: fixed !important;
  z-index: 9999 !important;
  min-width: 90px !important;
  max-width: 90px !important;
  width: 90px !important;
  box-sizing: border-box !important;
  flex-shrink: 0 !important;

  .el-popper__arrow {
    height: 0 !important;
    display: none !important;
  }

  .operate-buttons {
    display: flex;
    flex-direction: column;
    width: 100%;

    .operate-item {
      width: 100% !important;
      height: 26px;
      line-height: 26px;
      font-size: 14px;
      color: #1e1e1e;
      text-align: left;
      padding: 0 12px;
      margin: 0;
      cursor: pointer;
      display: flex !important;
      align-items: center !important;
      justify-content: flex-start !important;

      &:hover {
        background-color: #f7fafd;
      }

      &.disabled-item {
        color: #999;
        cursor: not-allowed;

        &:hover {
          background-color: transparent;
        }
      }

      &.delete-item {
        color: #ba1e45;

        &:hover {
          color: #ba1e45;
        }
      }
    }
  }
}

.image-wrapper {
  position: relative;
  display: inline-block;

  .main-image {
    width: 90px;
    height: 90px;
    background: #d9d9d9;
    border-radius: 4px;
  }

  .ai-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 30px;
    z-index: 1;
  }
}

/* 强制设置弹框宽度 */
:deep(.el-popover.operate-popover) {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  box-sizing: border-box !important;
}

:deep(.el-popper.operate-popover) {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  box-sizing: border-box !important;
}
</style>
