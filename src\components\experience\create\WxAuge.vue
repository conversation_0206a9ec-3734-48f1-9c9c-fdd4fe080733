<template>
    <div class="new-mask">
        <div>
            <div class="left_top">
                <img src="@/assets/images/smallQR.png" alt="">
                <div class="name">{{ name }}</div>
            </div>
            <img class="closed" src="http://njyjxr.oss-cn-shanghai.aliyuncs.com/mask-close.png" alt=""
                @click="closeEvent">
            <div class="header">项目预览</div>
            <img class="p" :src="codeUrl" alt="">
            <div class="desc">使用微信扫描二维码</div>
        </div>
    </div>
</template>
<script lang="ts" setup>

const props = defineProps({
    hideAddMask: {
        default: null,
        type: Function
    },
    name: {
        default: '',
        type: String
    }
})


const closeEvent = () => {
    props.hideAddMask()
}
</script>
<style scoped lang="less">
.new-mask {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .left_top {
        display: flex;
        align-items: center;
        position: absolute;
        top: 31px;
        left: 31px;

        img {
            width: 24px;
            height: 24px;
        }

        .name {
            margin-left: 12px;
            font-weight: bold;
            font-size: 18px;
            color: #3D566C;
            transform: translateY(-1px);
        }
    }

    &>div {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 474px;
        height: 435px;
        background: #FFFFFF;
        border-radius: 8px;
        padding: 60px 40px;
        box-sizing: border-box;
        text-align: left;
        font-weight: 600;
        font-size: 18px;
        color: #3D566C;

        .header {
            font-weight: bold;
            font-size: 20px;
            color: #3D566C;
            margin-bottom: 14px;
            margin-top: 49px;
        }

        .p {
            width: 220px;
            height: 220px;
            margin-bottom: 14px;
        }

        .closed {
            width: 17px;
            height: 17px;
            position: absolute;
            right: 31px;
            top: 31px;
            cursor: pointer;
        }

        .desc {
            font-weight: bold;
            font-size: 14px;
            color: #3D566C;
        }
    }
}
</style>