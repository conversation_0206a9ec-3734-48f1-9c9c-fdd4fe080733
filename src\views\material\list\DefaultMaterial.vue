<template>
  <div class="source-box">
    <search-form
      :form-keys-data="keysData"
      :create-event="updateSource"
      :searchDataEvent="searchDataEvent"
      currentRoute="/default_material"
      :upload-buttons="defaultMaterialUploadButtons"
      :showSearchFormBottom="!showModelCategory"
      @toggleModelCategory="handleToggleModelCategory" />

    <template v-if="!showModelCategory">
      <table-list
        ref="tableRefs"
        :data="tableData"
        :column-list="columnList"
        :change-page="changePage"
        create-list="上传素材"
        :delete-content="deleteContent"
        :handle-create="handleCreate"
        :data-total="pageTotal"
        :delete-dataEvent="deleteDataEvent"
        :handle-filter="handleFilter"
        :page-size="searchForm.pageSize"
        :page-no="searchForm.pageNo"
        :operation-items="operationItems" />
    </template>
    <template v-else>
      <div class="model-category-container">
        <model-category-management />
      </div>
    </template>
    <create-source-material
      v-if="modalShow"
      :material-affiliation="2"
      :handle-hide="handleHide"
      :default-value="defaultValue" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import { useRouter } from 'vue-router';
import CreateSourceMaterial from '../create/CreateSourceMaterial.vue';
import ModelCategoryManagement from '@/views/material/components/ModelCategoryManagement.vue';
import { getDefaultMaterial, deleteDefaultMaterial, getUserStorage, getModeType } from '@/api';
import { materialUrls } from '@/config';
import { useStore } from 'vuex';
import type { OperationItems, TableRow } from '@/types/operation';
import type { ColumnItem } from '@/types/table';
import { formatDateOnly } from '@/utils';
const router = useRouter();
const modalShow = ref(false); // 显示新建
const tableData: any = ref([]); // 表格数据
const pageTotal = ref(0); // 总计数据条数
const defaultValue = ref({}); // 进入编辑页的初始值
const showModelCategory = ref(false); // 是否显示模型分类管理

// 素材类型选项数据
const materialTypeOptions = [
  { name: '所有类型', value: '' }, // 默认选项
  { name: '视频', value: 1 },
  { name: '音频', value: 2 },
  { name: '图片', value: 3 },
  { name: '模型', value: 4 },
];

// 模型分类选项数据
const modelCategoryOptions = ref([
  { name: '所有分类', value: '' }, // 默认选项
]);

const searchForm: any = reactive({
  // 查询对象
  pageSize: 10,
  pageNo: 1,
  materialType: '', // 素材类型筛选
  modelTypeId: '', // 模型分类筛选
});

// 公共素材页面的上传按钮配置（不包含AI按钮）
const defaultMaterialUploadButtons = [
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '上传',
    label: '模型素材',
    materialType: '4',
    action: 'upload',
    type: 'model',
  },
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '上传',
    label: '图片素材',
    materialType: '3',
    action: 'upload',
    type: 'image',
  },
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '上传',
    label: '视频素材',
    materialType: '1',
    action: 'upload',
    type: 'video',
  },
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '上传',
    label: '音频素材',
    materialType: '2',
    action: 'upload',
    type: 'audio',
  },
];
const tableRefs = ref();
const deleteContent = {
  // 删除tips
  title: '删除素材',
  content: '是否删除此素材？',
};

// 获取用户存储容量
const getCurrentUserStorage = () => {
  getUserStorage().then((res: any) => {
    if (res.code === 200) {
      const storageData = JSON.parse(JSON.stringify(store.state.storageData));
      storageData.material = { ...res.data };
      store.state.storageData = { ...storageData };
    }
  });
};

// 搜索数据
const keysData = reactive([
  {
    key: 'materialType',
    type: 'select',
    label: '模型',
    dataList: materialTypeOptions,
    placeholder: '模型',
  },
  {
    key: 'modelTypeId',
    type: 'select',
    label: '所有模型分类',
    dataList: modelCategoryOptions,
    placeholder: '所有模型分类',
    width: 170,
  },
  {
    key: 'materialName',
    type: 'input',
    label: '素材名称',
  },
]);

const editData = (data: any) => {
  modalShow.value = true;
  defaultValue.value = data;
};

const deleteDataEvent = (data: any) => {
  deleteDefaultMaterial({ materialId: data.id }).then((res: any) => {
    if (
      res.code == 200 &&
      searchForm.pageNo - 1 == (pageTotal.value - 1) / searchForm.pageSize &&
      searchForm.pageNo > 1
    ) {
      searchForm.pageNo -= 1;
    }
    getDataList();
  });
};

// Operation items configuration
const operationItems = reactive<OperationItems>([
  {
    label: '编辑',
    key: 'edit',
    onClick: (row: TableRow) => {
      editData(row);
    },
  },
  {
    label: '删除',
    key: 'delete',
    onClick: (row: TableRow) => {
      deleteDataEvent(row);
    },
  },
]);

// 表头列
const columnList = [
  {
    prop: 'thumbnailOssAccessUrl',
    label: '缩略图',
    type: 'image',
    url: [materialUrls, 'materialType'] as [Record<string, any>, string],
  },
  {
    prop: 'materialName',
    label: '素材名称',
  },
  {
    prop: 'materialTypeName',
    label: '素材类型',
  },
  {
    prop: 'materialFormat',
    label: '上传格式',
  },
  {
    prop: 'storageSize',
    label: '素材大小',
    formatter: (value: number) => `${value}MB`,
  },
  {
    prop: 'equipType',
    label: '应用平台',
    customType: 'platform',
    segm: true,
  },
  {
    prop: 'materialGenStatus',
    label: '当前状态',
    list: {
      1: '正在处理',
      2: '正在处理',
      3: '生成错误',
      4: '已保存',
    },
    formatter: (value: any) => {
      if (value === null || value === undefined || value === '' || value === '-') {
        return '-';
      }
      // 如果值在 list 中定义，返回对应的文本
      const statusMap: Record<number, string> = {
        1: '正在处理',
        2: '正在处理',
        3: '生成错误',
        4: '已保存',
      };
      return statusMap[value] || value;
    },
  },
  {
    prop: 'updatetime',
    label: '更新日期',
    formatter: (value: string) => formatDateOnly(value),
  },
  {
    prop: 'operate',
    label: '操作',
    width: 120,
    type: 'operation',
  },
] as ColumnItem[];

const handleFilter = (key: string, value: any) => {
  searchForm[key] = value ? value[0] : null;
  searchForm.pageNo = 1;
  getDataList();
  tableRefs.value.selectDataArr = null;
};

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getDataList();
};

const handleCreate = () => {
  modalShow.value = true;
};

const updateSource = () => {
  router.push('/upload_source');
};

const handleHide = (renew?: boolean) => {
  modalShow.value = false;
  defaultValue.value = {};

  if (renew) {
    // 判断是否需要重新渲染
    getDataList();
  }
};

const searchDataEvent = (data: any, type?: string) => {
  // 处理按钮点击事件
  if (data && typeof data === 'object' && data.action) {
    if (data.action === 'upload') {
      // 处理上传按钮点击，弹出上传素材弹框
      console.log('上传按钮点击:', data);
      if (data.materialType) {
        // 设置默认值，包含素材类型
        defaultValue.value = { materialType: data.materialType };
      }
      modalShow.value = true;
      return;
    }
  }

  // 处理搜索表单数据（兼容旧的调用方式）
  if (typeof data === 'string') {
    type = data;
  } else if (data && typeof data === 'object') {
    for (const key in data) {
      if (key !== 'action') {
        searchForm[key] = data[key];
      }
    }
  }

  if (type == 'reset') {
    searchForm.materialType = '';
    searchForm.modelTypeId = '';
    tableRefs.value.clearFilterEvent();
  }
  getDataList();
  tableRefs.value.selectDataArr = null;
};

// 获取模型分类数据
const getModelCategories = async () => {
  try {
    const response = await getModeType();
    if (response && response.data && Array.isArray(response.data)) {
      const categories = response.data.map((item: any) => ({
        name: item.typeName || item.name,
        value: item.id,
      }));
      modelCategoryOptions.value = [
        { name: '所有分类', value: '' }, // 默认选项
        ...categories,
      ];
    }
  } catch (error) {
    console.error('获取模型分类失败:', error);
  }
};

const getDataList = () => {
  getDefaultMaterial({ ...searchForm }).then((res) => {
    if (res.data) {
      // 为数据添加默认值处理
      tableData.value = res.data.records.map((item: any) => ({
        ...item,
        materialGenStatus: item.materialGenStatus || '-',
        materialFormat: item.materialFormat || '-',
        storageSize: item.storageSize || '-',
        materialDescribe: item.materialDescribe || '-',
        equipType: item.equipType || '-',
        updatetime: item.updatetime || '-',
        materialTypeName: item.materialTypeName || '-',
      }));
      pageTotal.value = res.data.total;
    }
  });
};

const store = useStore();

const showTable = ref(true);

const handleToggleModelCategory = (show: boolean) => {
  console.log('Toggle category management:', show); // Add logging for debugging
  showModelCategory.value = show;
};

const handleNewCategory = () => {
  // TODO: 实现新建分类的逻辑
  console.log('Creating new category');
};

onMounted(async () => {
  // 获取模型分类数据
  await getModelCategories();
  getDataList();
  getCurrentUserStorage();
});
</script>

<style scoped lang="less">
.source-box {
  height: 100%;
}

.model-category-container {
  padding: 20px 0;
}
</style>
