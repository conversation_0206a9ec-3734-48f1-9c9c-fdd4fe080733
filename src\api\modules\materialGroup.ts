import request from "../request";

// 创建资源组
export function createMaterialGroupInfo(data: any) {
  return request({
    url: "/scene/createMaterialGroupInfo",
    method: "post",
    data,
  });
}

// 更新资源组
export function updateMaterialGroup(data: any) {
  return request({
    url: "/scene/updateMaterialGroup",
    method: "post",
    data,
  });
}

// 获取资源组合列表
export function selectMaterialGroup(data: any) {
  return request({
    url: `/scene/selectMaterialGroup?pageNo=${data.pageNo}&pageSize=${data.pageSize}`,
    method: "get",
  });
}

// 删除资源组合列表
export function deleteMaterialGroup(data: any) {
  return request({
    url: `/scene/deleteMaterialGroup?groupId=${data.id}`,
    method: "post",
  });
}

// 查询场景里的素材（已移动到scene模块）
// export function getMaterialScene(data: any) {
//   return request({
//     url: `/scene/queryMaterialMetaInScene?sceneId=${data.sceneId}&materialType=${data.materialType}&interactionId=${data.interactionId}`,
//     method: 'get'
//   });
// } 