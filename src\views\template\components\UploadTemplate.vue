<template>
  <el-upload :class="classStyle" :action="baseURL" :before-upload="beforeUploadEvent" :name="fileName"
    :show-file-list="false" :accept="'.jpg,.png'" :on-success="handleAvatarSuccessEvent" :headers="headerObj"
    :on-change="handleChangeEvent" :disabled="loading">
    <slot></slot>
  </el-upload>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'

const props = defineProps({
  baseURL: {
    default: '',
    type: String
  },
  beforeUpload: {
    default: null,
    type: Function
  },
  handleChange: {
    default: null,
    type: Function
  },
  handleAvatarSuccess: {
    default: null,
    type: Function
  },
  classStyle: {
    default: '',
    type: String
  },
  fileName: {
    default: 'file',
    type: String
  },
  loading: {
    default: false,
    type: Boolean
  },
})

const store = useStore();

const token = window.localStorage.getItem('token');
const njyj_version = window.localStorage.getItem("njyj-version");
const headerObj: any = ref({
  token: token || ''
})
if (njyj_version) {
  headerObj.value["njyj-version"] = njyj_version;
}

const beforeUploadEvent = (uploadFile: any) => {
  let sourceName = uploadFile.name.split('.').slice(0, -1).join('.');
  if (sourceName.includes('&') || sourceName.includes('.')) {
    store.state.showTips = '请检查图片命名，避免使用一些特殊字符';
    return false
  }
  props.beforeUpload(uploadFile);
}

const handleAvatarSuccessEvent = (response: any, uploadFile: any) => {
  props.handleAvatarSuccess(response, uploadFile);
}

const handleChangeEvent = (file: any) => {
  props.handleChange && props.handleChange(file);
}
</script>
<style scoped lang="less"></style>