<template>
  <div class="modal">
    <div class="modal-content" v-show="!showFormat">
      <div class="modal-content-title">
        <div>上传素材</div>
      </div>
      <div class="modal-form">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="80px"
          class="demo-ruleForm">
          <el-form-item label="素材名称" prop="materialName">
            <el-input
              class="form-input"
              v-model="ruleForm.materialName"
              placeholder="请输入素材名称"
              maxlength="20" />
          </el-form-item>
          <el-form-item label="素材类型" prop="materialType">
            <el-select
              v-model="ruleForm.materialType"
              placeholder="请选择资源类型"
              class="select-default"
              popper-class="select-option"
              :suffix-icon="DropDown"
              style="width: 432px; height: 36px"
              @change="changeMaterialType">
              <el-option
                v-for="(item, index) in materialType"
                :key="index"
                :label="item.name"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="!['4', '5'].includes(ruleForm.materialType)"
            label="上传素材"
            prop="ossKey"
            class="upload-box-style">
            <div class="upload-material-tips">每个文件大小不超过{{ singleUploadSize }}MB</div>
            <div class="upload-material-style">
              <div class="upload-tips" v-if="uploadTips[ruleForm.materialType]">
                {{ uploadTips[ruleForm.materialType] }}
              </div>
              <el-upload
                class="upload-btn"
                :before-upload="beforeUpload"
                :action="uploadURL"
                :on-change="handleChange"
                :headers="headerObj"
                :show-file-list="false"
                :on-success="uploaded"
                :on-error="errorUpload"
                :disabled="uploadStatus[ruleForm.materialType] == 'uploading'">
                <el-button :class="uploadStatus[ruleForm.materialType]">选择</el-button>
              </el-upload>
              <div :class="uploadStatus[ruleForm.materialType] + '-style'">
                {{ uploadStatusMap[uploadStatus[ruleForm.materialType]] }}
              </div>
            </div>
          </el-form-item>
          <el-form-item
            v-if="ruleForm.materialType == '4' && showDeviceToUpload.length"
            label="上传素材"
            prop="ossKey"
            class="upload-box-style">
            <div class="upload-material-tips">每个文件大小不超过{{ singleUploadSize }}MB</div>
            <div class="upload-model-box">
              <div
                class="upload-material-style"
                :class="'styleFor' + showDeviceToUpload.length"
                v-for="(item, index) in showDeviceToUpload"
                :key="index">
                <div class="upload-tips" v-if="uploadTips[ruleForm.materialType]">
                  {{ loadMap[item].tag }}
                </div>
                <div v-if="uploadTips[ruleForm.materialType]">
                  {{
                    ruleForm.equipType.includes('3') && item == 2
                      ? loadMap[item].rule2
                      : loadMap[item].rule
                  }}
                </div>
                <el-upload
                  class="upload-btn"
                  :before-upload="beforeUpload"
                  :action="uploadURL"
                  :on-change="handleChange"
                  :headers="headerObj"
                  :show-file-list="false"
                  :on-success="(res: any) => { uploaded(res, loadMap[item].type); }"
                  @click.stop="updateType = loadMap[item].type"
                  :on-error="errorUpload"
                  :disabled="
                    uploadStatus[ruleForm.materialType][loadMap[item].type] == 'uploading'
                  ">
                  <el-button :class="uploadStatus[ruleForm.materialType][loadMap[item].type]">
                    选择
                  </el-button>
                </el-upload>
                <div :class="uploadStatus[ruleForm.materialType][loadMap[item].type] + '-style'">
                  {{ uploadStatusMap[uploadStatus[ruleForm.materialType][loadMap[item].type]] }}
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item
            v-if="ruleForm.materialType === '5'"
            label="上传素材"
            prop="materialWord"
            class="upload-box-style">
            <div class="upload-material-tips">每个文件大小不超过200字</div>
            <el-input
              class="form-input"
              type="textarea"
              style="height: 124px"
              resize="none"
              v-model="ruleForm.materialWord"
              placeholder="请输入文字"
              @input="changeText"
              maxlength="200" />
          </el-form-item>
          <el-form-item
            label="模型预览"
            :style="{ marginTop: '40px' }"
            v-if="ruleForm.materialType == '4' && uploadStatus[4].web == 'uploaded'">
            <div class="preview">
              <canvas-preview ref="canvasRef" :handle-mouse-up="handleMouseUp"></canvas-preview>
            </div>
            <div class="tips-text">
              <div>可滑动鼠标查看模型</div>
              <div>同步刷新素材封面</div>
            </div>
          </el-form-item>
          <el-form-item label="素材封面" prop="thumbnail">
            <div class="thumbnail-show">
              <div>
                <el-upload
                  v-if="ruleForm.materialType != 1"
                  :before-upload="beforeUploadThumbnail"
                  :action="uploadThumbnail"
                  :show-file-list="false"
                  :on-change="handleChangeThumbnail"
                  :headers="headerObj"
                  :on-success="uploadedThumbnail">
                  <div class="thumbnail-icon-upload">
                    <img
                      v-if="!loadedThumbnail"
                      :src="materialUrls[ruleForm.materialType].url_upload"
                      style="width: 24px; height: 24px" />
                    <img v-if="loadedThumbnail" :src="loadedThumbnail" />
                  </div>
                </el-upload>
                <div class="thumbnail-icon-upload" v-if="ruleForm.materialType == 1">
                  <img
                    v-if="!loadedThumbnail"
                    :src="materialUrls[ruleForm.materialType].url_upload"
                    style="width: 24px; height: 24px" />
                  <img v-if="loadedThumbnail" :src="loadedThumbnail" />
                </div>
              </div>
              <div class="thumbnail-text" v-if="ruleForm.materialType != 1">
                <div>点击图片可更换封面</div>
                <div>支持512KB的.png或.jpg图片</div>
              </div>
            </div>
          </el-form-item>
          <!-- <el-form-item label="备注" prop="materialDescribe">
            <el-input
              class="form-input"
              v-model="ruleForm.materialDescribe"
              placeholder="请输入描述信息"
              maxlength="60" />
          </el-form-item> -->
          <el-form-item class="form-submit">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef)">确认</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <text-canvas ref="textCanvasRef"></text-canvas>
    <tips-view v-if="!showTemporaryText"></tips-view>
    <tips-view
      v-if="showTemporaryText"
      show-btn="知道了"
      tips-title="取消上传素材"
      :sure-event="deleteTemporary"
      :cancle-event="() => (showTemporaryText = false)"></tips-view>
    <div class="pic-model">
      <div>
        <img :src="loadedThumbnail" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

import TipsView from '@/components/TipsView.vue';
import DropDown from '@/components/DropDown.vue';
import CanvasPreview from '@/components/CanvasPreview.vue';
import {
  getOrgnizationPackage,
  getOssAccessPath,
  addPersonMaterialMeta,
  updatePersonMaterial,
  getUserStorage,
  deleteTmpMaterial,
} from '@/api';
import { materialType, materialUrls } from '@/config';
import { convertToLowerCase, createUuid, desensitizte } from '@/utils/index';
import axios from 'axios';
import { useStore } from 'vuex';
import TextCanvas from '@/components/TextCanvas.vue';

const store = useStore();

// 导入增强的loader配置
import { loader } from '@/config/threeJs';

const token = window.localStorage.getItem('token');
const uploadURL = ref(''); // 上传素材接口地址
const uploadThumbnail = ref(''); // 上传缩略图接口地址
const baseURL = process.env.NODE_ENV === 'production' ? '/api' : '/api1'; // 基础url
const loadedURL = ref(''); // 预览素材照片地址
const loadedThumbnail = ref(''); // 显示上传的缩略图地址
const thumbnailkey = ref(' '); // 缩略图上传radio的值
const defaultKey = ref(materialUrls[1].url_a); // 默认缩略图的key
const headerObj: any = ref({
  token: token || '',
});
const previewType = ref('video'); // 预览的类型
const singleUploadSize = ref(0); // 单次可以上传的大小
const updateType = ref('');
const showFormat = ref(false); // 是否显示格式下拉框
const materialAnimations = ref([]);
const njyj_version = window.localStorage.getItem('njyj-version');
const platforms: any = {
  android: 1,
  web: 2,
  ios: 3,
};
const loadMap: any = ref({
  2: {
    type: 'web',
    tag: '3D模型',
    rule: 'glb/gltf/fbx格式',
    rule2: 'glb/gltf格式',
  },
});
const uploadTips: any = {
  1: '仅支持MP4格式视频',
  2: '仅支持MP3格式音频',
  3: '仅支持JPG、PNG格式图片',
  4: '仅支持FBX、OBJ、GLB、GLTF格式模型',
};
const packageInfoDto: any = ref({});
const canvasRef = ref();
const headers: any = {
  'Content-Type': 'text/plain',
  token: token,
};
if (njyj_version) {
  headers['njyj-version'] = njyj_version;
  headerObj.value['njyj-version'] = njyj_version;
}
const canUsed = ref(0);
const textCanvasRef = ref();
const temporaryUuidArr: any = []; //存放上传时的uuid
const showTemporaryText = ref(false);
const showError = ref(false);

// 上传素材的状态
const uploadStatus: any = ref({
  1: 'wait',
  2: 'wait',
  3: 'wait',
  4: {
    web: 'wait',
    android: 'wait',
    ios: 'wait',
  },
  5: 'wait',
});

// 上传的状态用来区分样式
const uploadStatusMap: any = ref({
  wait: '未上传',
  uploading: '上传中',
  uploaded: '上传成功',
});

const showDeviceToUpload: any = ref([2]); // 选择的设备对应需要上传的包

const props = defineProps({
  handleHide: {
    default: null,
    type: Function,
  },
  defaultValue: {
    default: null,
    type: Object,
  },
  materialAffiliation: {
    default: 3,
    type: Number,
  },
});

interface RuleForm {
  materialName: string;
  materialType: string;
  materialDescribe: string;
  ossKey: string;
  thumbnail: string;
  storageSize: string;
  materialFormat: string;
  equipType: string;
  materialWord: string;
}

const ruleFormRef = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  materialName: '',
  materialType: '1',
  materialDescribe: '',
  ossKey: '',
  thumbnail: '',
  storageSize: '',
  materialFormat: '',
  equipType: '3',
  materialWord: '',
});

const rules = reactive<FormRules<RuleForm>>({
  materialName: [
    { required: true, message: '请输入素材名称', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value && value.trim() === '') {
          callback(new Error('素材名称不能全部为空格'));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
  materialType: [{ required: true, message: '请选择素材类型', trigger: 'blur' }],
  // thumbnail: [
  //   { required: true, message: '请选择或上传缩略图', trigger: 'blur', }
  // ],
  ossKey: [{ required: true, message: '请选择或上传素材', trigger: 'blur' }],
  equipType: [{ required: true, message: '请选择设备端', trigger: 'blur' }],
  materialWord: [{ required: true, message: '请输入文字', trigger: 'blur' }],
});

const changeText = (val: string) => {
  textCanvasRef.value.canvasText(val, async (formData: any, dataURLBase64: string) => {
    const uuid = createUuid();
    const ossBack = await axios.post(
      `${baseURL}/material/uploadMaterial?fileName=${new Date().getTime()}&materialType=${
        ruleForm.materialType
      }&uuid=${uuid}`,
      formData,
      {
        headers,
      }
    );
    ruleForm.materialFormat = 'text';
    ruleForm.ossKey = ossBack.data.data.materialOssKey;
    temporaryUuidArr.push(uuid);

    const thumbnailBack = await axios.post(
      `${baseURL}/material/uploadThumbnail?fileName=${new Date().getTime()}`,
      dataURLBase64,
      {
        headers,
      }
    );
    thumbnailkey.value = thumbnailBack.data.data;
    loadedThumbnail.value = dataURLBase64;
  });
};

const errorUpload = (err: any, e2: any) => {
  if (e2.status == 'fail' && !String(err).includes('<!DOCTYPE html>') && !showError.value) {
    store.state.showTips = '上传失败，请检查格式或命名是否正确';
    if (ruleForm.materialType === '4') {
      uploadStatus.value[ruleForm.materialType][updateType.value] = 'wait';
    }
  }
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (!ruleForm.storageSize) {
        delete ruleForm.storageSize;
      }
      if (ruleForm.materialType === '4') {
        delete ruleForm.ossKey;
      }
      // 选择默认不传路径，默认的是在本地
      if (ruleForm.thumbnail == defaultKey.value) {
        ruleForm.thumbnail = '';
      }
      let animationInfoDto: any = {
        animationCount: materialAnimations.value.length,
        animationList: materialAnimations.value,
        materialAffiliation: props.materialAffiliation,
      };
      if (props.defaultValue?.id) {
        animationInfoDto.materialId = props.defaultValue.id;
      }
      if (ruleForm.materialType != '4') {
        animationInfoDto = {};
        showDeviceToUpload.value = [];
      } else {
        ruleForm.platformV2 = '2';
      }
      const hasSensitiveWords1 = await desensitizte(
        ruleForm.materialName,
        '素材名称不可包含敏感词汇！'
      );
      if (hasSensitiveWords1) return;

      if (ruleForm.materialType == '5') {
        const hasSensitiveWords2 = await desensitizte(
          ruleForm.materialWord,
          '文字素材名称不可包含敏感词汇！'
        );
        if (hasSensitiveWords2) return;
      }
      if (props.defaultValue?.id) {
        updatePersonMaterial({ ...ruleForm, id: props.defaultValue.id, animationInfoDto }).then(
          (res: any) => {
            props.handleHide(true);
          }
        );
      } else {
        addPersonMaterialMeta({ ...ruleForm, animationInfoDto }).then((res: any) => {
          props.handleHide(true, ruleForm.materialType);
        });
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

const changeState = () => {
  if (temporaryUuidArr.length) {
    showTemporaryText.value = true;
    store.state.showTips = '已上传的素材取消保存';
  } else {
    props.handleHide();
  }
};

const deleteTemporary = () => {
  deleteTmpMaterial(temporaryUuidArr).then((res) => {
    store.state.showTips = '';
    props.handleHide();
    showTemporaryText.value = false;
  });
};

const changeMaterialType = async (v: string) => {
  defaultKey.value = materialUrls[v].url_a;
  previewType.value = materialUrls[v].type;
  if (ruleForm.materialType === '4') {
    uploadStatus.value[ruleForm.materialType].web = 'wait';
    uploadStatus.value[ruleForm.materialType].android = 'wait';
    uploadStatus.value[ruleForm.materialType].ios = 'wait';
  } else {
    uploadStatus.value[ruleForm.materialType] = 'wait';
  }
  ruleForm.ossKey = '';
  loadedURL.value = '';
  loadedThumbnail.value = '';
  await getCurrentUserStorage();
};

const handleChange = (file: any) => {
  if (!uploadURL.value) {
    loadedURL.value = '';
    return;
  }
  loadedURL.value = URL.createObjectURL(file.raw);
};

const handleChangeThumbnail = (file: any) => {
  if (uploadThumbnail.value) {
    loadedThumbnail.value = URL.createObjectURL(file.raw);
  } else {
    loadedThumbnail.value = '';
  }
};

// 报错处理
const errorEvent = (msg: string, type?: number) => {
  store.state.showTips = msg;
  if (type) return;
  uploadURL.value = '';
  if (ruleForm.materialType === '4') {
    uploadStatus.value[ruleForm.materialType][updateType.value] = 'wait';
  } else {
    uploadStatus.value[ruleForm.materialType] = 'wait';
  }
};

const beforeUpload = (rawFile: any) => {
  const uuid = createUuid();
  if (rawFile.size / 1024 / 1024 > singleUploadSize.value) {
    errorEvent('您上传的资源大于您当前套餐单次可上传资源大小');
    return false;
  }

  if (rawFile.size / 1024 / 1024 > canUsed.value) {
    errorEvent('您上传的资源大于您当前套餐可上传资源总量');
    return false;
  }
  const expectFormat = materialUrls[ruleForm.materialType].format;
  let sourceFormat = rawFile.name.split('.')[1];
  if (sourceFormat) {
    sourceFormat = sourceFormat.toUpperCase().toLowerCase();
  }
  if (
    !expectFormat.includes(sourceFormat) &&
    ((updateType.value != 'android' && updateType.value != 'ios') || sourceFormat)
  ) {
    errorEvent('您上传的素材格式或命名不正确，请上传与要求相符的素材。');
    return false;
  }
  if (ruleForm.materialType === '4') {
    uploadStatus.value[ruleForm.materialType][updateType.value] = 'uploading';
    if (updateType.value == 'web') {
      if (ruleForm.equipType.includes('3')) {
        if (sourceFormat != 'glb' && sourceFormat != 'gltf') {
          errorEvent('您上传的素材格式或命名不正确，请上传与要求相符的素材。');
          return false;
        }
      } else {
        if (sourceFormat != 'glb' && sourceFormat != 'gltf' && sourceFormat != 'fbx') {
          errorEvent('您上传的素材格式或命名不正确，请上传与要求相符的素材。');
          return false;
        }
      }
      ruleForm.materialFormat = rawFile.name?.split('.').slice(-1)[0];
      uploadURL.value = `${baseURL}/material/uploadMaterial?fileName=${rawFile.name}&materialType=${ruleForm.materialType}&platformV2=2&modelFormat=${ruleForm.materialFormat}&uuid=${uuid}`;
      temporaryUuidArr.push(uuid);
    } else {
      if (
        ['fbx', 'obj', 'gltf', 'glb'].includes(
          convertToLowerCase(rawFile.name.split('.').slice(-1)[0])
        )
      ) {
        errorEvent('上传失败，请选择AssetBundle打包文件');
        uploadURL.value = '#';
        return false;
      } else {
        uploadURL.value = `${baseURL}/material/uploadMaterial?fileName=${rawFile.name}&materialType=${ruleForm.materialType}&platformV2=2&uuid=${uuid}`;
        temporaryUuidArr.push(uuid);
      }
    }
  } else {
    uploadURL.value = `${baseURL}/material/uploadMaterial?fileName=${rawFile.name}&materialType=${ruleForm.materialType}&uuid=${uuid}`;
    temporaryUuidArr.push(uuid);
    ruleForm.materialFormat = rawFile.name?.split('.').slice(-1)[0];
    uploadStatus.value[ruleForm.materialType] = 'uploading';
  }
  ruleForm.storageSize = rawFile.size;
  loadedURL.value = '';
};

const beforeUploadThumbnail = (rawFile: any) => {
  let sourceFormat = rawFile.name.split('.')[1];
  if (sourceFormat) {
    sourceFormat = sourceFormat.toUpperCase().toLowerCase();
  }
  if (!['png', 'jpg'].includes(sourceFormat)) {
    errorEvent('您上传的素材格式或命名不正确，请上传与要求相符的素材。', 1);
    uploadThumbnail.value = '';
    return;
  }
  if (rawFile.size / 1024 > 512) {
    errorEvent('您上传的素材大小超出了限制，请检查后再上传。', 1);
    uploadThumbnail.value = '';
    return;
  }
  uploadThumbnail.value = `${baseURL}/material/uploadThumbnail?fileName=${rawFile.name}`;
};

const uploaded = async (res: any, type?: string) => {
  await getCurrentUserStorage();
  if (
    (uploadStatus.value[ruleForm.materialType] == 'wait' && ruleForm.materialType != '4') ||
    (type &&
      uploadStatus.value[ruleForm.materialType][type] == 'wait' &&
      ruleForm.materialType == '4')
  )
    return;
  if (ruleForm.materialType === '4') {
    ruleForm[updateType.value + 'ModelStorageOssKey'] = res.data.materialOssKey;
    if (type) {
      uploadStatus.value[ruleForm.materialType][type] = 'uploaded';
    }
    if (updateType.value == 'web') {
      ruleForm.ossKey = ruleForm['webModelStorageOssKey'];
      getOssAccessPath({ key: res.data.materialOssKey }).then((res1: any) => {
        loader[ruleForm.materialFormat].load(res1.data, function (object: any) {
          const animations = object.animations;
          if (animations.length) {
            materialAnimations.value = animations.map((e: any) => ({ name: e.name }));
          } else {
            materialAnimations.value = [];
          }
          if (object.scene) {
            canvasRef.value.addMesh(object.scene, getImg);
          } else {
            canvasRef.value.addMesh(object, getImg);
          }
          function getImg() {
            setTimeout(() => {
              handleMouseUp();
            }, 200);
          }
        });
      });
    }
  } else {
    ruleForm.ossKey = res.data.materialOssKey;
    if (ruleForm.materialType === '1') {
      thumbnailkey.value = res.data.vedioFrameOssKey;
      getOssAccessPath({ key: res.data.vedioFrameOssKey }).then((res1: any) => {
        loadedThumbnail.value = res1.data;
      });
    }
    if (ruleForm.materialType === '3') {
      thumbnailkey.value = ruleForm.ossKey;
      getOssAccessPath({ key: ruleForm.ossKey }).then((res1: any) => {
        loadedThumbnail.value = res1.data;
      });
    }
    uploadStatus.value[ruleForm.materialType] = 'uploaded';
  }
};

const handleMouseUp = async () => {
  const renderer = canvasRef.value.getRenderer();
  const camera = canvasRef.value.getCamera();
  const scene = canvasRef.value.getScene();
  renderer.render(scene, camera);
  let imgData = renderer.domElement.toDataURL('image/png');
  loadedThumbnail.value = imgData;

  const { data } = await axios.post(
    `${baseURL}/material/uploadThumbnail?fileName=${new Date().getTime()}`,
    imgData,
    {
      headers,
    }
  );
  thumbnailkey.value = data.data;
};

const uploadedThumbnail = (res: any) => {
  thumbnailkey.value = res.data;
};

const getCurrentUserStorage = async () => {
  await getUserStorage().then((res: any) => {
    if (res.code == 200) {
      canUsed.value = res.data.userPackageStorage - res.data.userUsedStorage;
    }
  });
};

onMounted(async () => {
  uploadURL.value = '';
  uploadThumbnail.value = `${baseURL}/material/uploadThumbnail`;
  packageInfoDto.value = JSON.parse(window.sessionStorage.getItem('packageInfoDto') || '{}');
  // 编辑页初始数据
  if (props.defaultValue?.id) {
    if (props.defaultValue.ossKey && !props.defaultValue.modelStorageMap) {
      await getOssAccessPath({ key: props.defaultValue.ossKey }).then((res1: any) => {
        loadedURL.value = res1.data;
      });
    }

    ruleForm.materialName = props.defaultValue.materialName || '';
    ruleForm.materialType = props.defaultValue.materialType || '';
    ruleForm.materialWord = props.defaultValue.materialWord || '';
    ruleForm.materialDescribe = props.defaultValue.materialDescribe || '';
    // ruleForm.storageSize = props.defaultValue.storageSize || ''
    ruleForm.materialFormat = props.defaultValue.materialFormat || '';
    ruleForm.ossKey = props.defaultValue.ossKey || '';
    loadedThumbnail.value = props.defaultValue.thumbnailOssAccessUrl || '';
    if (props.defaultValue.thumbnail) {
      await getOssAccessPath({ key: props.defaultValue.thumbnail }).then((res1: any) => {
        loadedThumbnail.value = res1.data;
      });
    }

    if (ruleForm.materialType != 4) {
      uploadStatus.value[ruleForm.materialType] = 'uploaded';
    }
    thumbnailkey.value = props.defaultValue.thumbnail;
    if (loadedURL.value) {
      previewType.value = materialUrls[ruleForm.materialType].type;
    }

    if (props.defaultValue.modelStorageMap) {
      const { web, android, ios } = props.defaultValue.modelStorageMap;
      ruleForm.webModelStorageOssKey = web.ossKey;
      ruleForm.andriodModelStorageOssKey = android?.ossKey;
      ruleForm.iosModelStorageOssKey = ios?.ossKey;
      ruleForm.ossKey = web.ossKey;
      ruleForm.equipType = props.defaultValue.equipType || '3';
      Object.keys(props.defaultValue.modelStorageMap).forEach((e: any) => {
        uploadStatus.value[ruleForm.materialType][e] = 'uploaded';
      });
      getOssAccessPath({ key: props.defaultValue.modelStorageMap.web.ossKey }).then((res1: any) => {
        loader[ruleForm.materialFormat].load(res1.data, function (object: any) {
          const animations = object.animations;
          if (animations.length) {
            materialAnimations.value = animations.map((e: any) => ({ name: e.name }));
          } else {
            materialAnimations.value = [];
          }
          if (object.scene) {
            canvasRef.value.addMesh(object.scene);
          } else {
            canvasRef.value.addMesh(object);
          }
        });
      });
    }
  }

  getOrgnizationPackage({}).then((res: any) => {
    const packageInfoDto =
      res.data.userDto.packageVersion == 'V2'
        ? res.data.userBindPackageDto
        : res.data.packageInfoDto;
    const packageExtensionDtos =
      res.data.userDto.packageVersion == 'V2'
        ? res.data.userBindPackageExtensionDtos
        : res.data.packageExtensionDtos;
    singleUploadSize.value += packageInfoDto.singleUploadSize || 100;
    packageExtensionDtos.forEach((e: any) => {
      singleUploadSize.value += e.singleUploadSize;
    });
  });

  await getCurrentUserStorage();
});

watch(thumbnailkey, (newState) => {
  if (newState != defaultKey.value && newState) {
    ruleForm.thumbnail = newState;
  }
});

watch(loadedThumbnail, (newState) => {
  if (newState) {
    setTimeout(() => {
      const imgInfo = document.querySelector('.pic-model>div>img')?.getBoundingClientRect();
      if (ruleForm.materialType !== '1') {
        ruleForm.materialMediaInfoDto = {
          width: imgInfo?.width,
          height: imgInfo?.height,
        };
      }
    }, 200);
  } else {
    delete ruleForm.materialMediaInfoDto;
  }
});

watch(
  () => store.state.showTips,
  (newState) => {
    showError.value = !!newState;
  }
);
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;
  -webkit-user-select: none;
  /* Chrome, Safari, Opera */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;

  .modal-content {
    width: 453px;
    max-height: 90%;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #edeff2;
    overflow: hidden;
    overflow-y: auto;
    color: #1e1e1e;

    .modal-content-title {
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 18px;
      color: #1e1e1e;
      padding: 16px 0 8px 24px;
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      padding: 16px 24px;
      box-sizing: border-box;
      overflow: hidden;
      overflow-y: auto;

      .upload-tips {
        font-size: 14px;
        text-align: left;
        padding-left: 52px;
        margin-top: -5px;
        font-weight: bold;
        color: rgba(61, 86, 108, 0.3);
      }

      .upload-tips-text {
        position: absolute;
        top: 42px;
        left: 10px;
        font-size: 12px;
        color: #333;
        width: 100%;
        text-align: center;
      }

      .tips-text {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        line-height: 14px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        margin-left: 12px;

        & > div:last-child {
          margin-top: 8px;
        }
      }

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .loaded-img {
        width: 100%;
        height: 100%;
        display: inline-block;
      }

      .preview {
        position: relative;
        width: 146px;
        height: 146px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #dadada;
      }

      .preview-audio {
        background-color: #d0d0d0;
        background-image: url(~@/assets/images/background/audio.png);
        background-repeat: no-repeat;
        background-position: 50% 50%;
        border-radius: 8px;
      }

      .equipType-style {
        margin-right: 24px;

        & > span {
          vertical-align: middle;
        }

        .equipType-style-select {
          width: 16px;
          height: 16px;
          border-radius: 2px;
          display: inline-block;
          margin-right: 4px;
          cursor: pointer;
          background-image: url(~@/assets/images/checkbox-icon.png);
          background-size: 100% 100%;

          &.active {
            background-image: url(~@/assets/images/checkbox-iconA.png);
            background-size: 100% 100%;
          }
        }

        .active {
          color: #2e76ff;
        }
      }

      .upload-box-style {
        position: relative;
        display: flex;
        flex-direction: column;

        .upload-material-tips {
          position: absolute;
          left: 78px;
          top: -31px;
          font-weight: 400;
          font-size: 12px;
          color: #797979;
        }
      }

      .upload-material-style {
        position: relative;
        width: 100%;
        height: 124px;
        border-radius: 10px;
        border: 1px dashed #dadada;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        box-sizing: border-box;

        &:hover {
          border-color: #2e76ff;

          button {
            background-color: #2e76ff;
            color: #fff;
          }
        }

        & > div {
          line-height: 1.5;
        }

        .upload-btn {
          margin: 8px 0;
        }

        button {
          position: relative;
          border: none;
          background-color: rgba(46, 118, 255, 0.2);
          font-weight: 400;
          font-size: 12px;
          color: #2e76ff;
          padding: 0;
          width: 60px;
          height: 18px;
          text-align: center;

          &.uploading {
            color: #797979;
            background-color: rgba(0, 0, 0, 0.1);
            cursor: not-allowed;
          }
        }

        .upload-tips {
          padding-left: 0;
          color: #1e1e1e;
        }

        .uploading-style,
        .uploaded-style {
          position: relative;
          padding-left: 18px;
        }

        .uploading-style::before {
          content: '';
          width: 14px;
          height: 14px;
          background-image: url(~@/assets/images/icon/loading-icon.png);
          background-size: 100% 100%;
          position: absolute;
          left: 0;
          top: 2px;
          animation: rotate 1.5s linear infinite;
        }

        .uploaded-style::before {
          content: '';
          width: 14px;
          height: 14px;
          background-image: url(~@/assets/images/icon/complete-icon.png);
          background-size: 100% 100%;
          position: absolute;
          left: 0;
          top: 2px;
        }
      }

      .thumbnail-show {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        & > div:first-child {
          width: 92px;
          height: 92px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #dadada;

          .thumbnail-icon-upload {
            width: 92px;
            height: 92px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }

        .thumbnail-text {
          font-weight: 400;
          font-size: 12px;
          color: #797979;
          line-height: 14px;
          margin-left: 12px;
          text-align: left;

          & > div:last-child {
            margin-top: 8px;
          }
        }
      }

      .upload-model-box {
        width: 100%;
        height: 124px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        & > .upload-material-style.styleFor3 {
          width: 124px;
        }

        & > .upload-material-style.styleFor2 {
          width: 196px;
        }

        & > .upload-material-style.styleFor1 {
          width: 100%;
        }
      }
    }

    ::v-deep(.modal-form .el-form > div) {
      &.form-submit {
        margin: 24px 0 0 !important;
      }
    }

    ::v-deep(.el-form-item__label) {
      color: #797979;
    }

    ::v-deep(.el-input__wrapper .el-input__inner) {
      color: #1e1e1e;
    }
  }

  .pic-model {
    // position: relative;
    position: fixed;
    left: 0;
    top: 0;
    font-size: 0;
    z-index: -1;
    opacity: 0;

    img {
      display: block;
    }
  }
}

.thumbnail-icon {
  width: 49px;
  height: 49px;
  background-image: url(~@/assets/images/icon/source-bg.png);

  img {
    width: 100%;
    height: 100%;
  }
}

.uploadMask {
  position: absolute;
  left: 0;
  top: 0;
  width: 105px;
  height: 36px;
  background-color: transparent;
  z-index: 10;
  cursor: pointer;
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;
}

.el-size {
  width: 102px;
  height: 36px;
  box-sizing: border-box;
  margin-right: 95px;

  .el-button {
    background: rgba(46, 118, 255, 0.1) !important;
  }
}

.upload-demo {
  position: relative;

  .progress {
    position: absolute;
    left: 0px;
    top: -18px;
    width: 105px;
  }

  .complete-icon {
    width: 16px;
    position: absolute;
    left: 115px;
    height: 16px;
    line-height: 16px;
    top: -2px;
  }
}

.el-upload__text > span {
  margin-left: 3px;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
