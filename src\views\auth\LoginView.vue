<template>
  <div class="login">
    <div class="login-text">
      <div class="text1">混空 Mixed Space</div>
      <div class="text2">打造数实融合的空间计算平台</div>
    </div>
    <!-- 登录 -->
    <div class="login-box" v-if="!loginStep">
      <h3>登录</h3>
      <div>
        <el-form :model="form" :rules="rules">
          <el-form-item prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱账号" :prefix-icon="User" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="form.password" type="password" placeholder="请输入密码" show-password :prefix-icon="Lock" />
          </el-form-item>
          <el-checkbox v-model="remember">记住密码</el-checkbox>
          <div class="agree">
            <el-checkbox v-model="agree"></el-checkbox>
            <span>我已经阅读并同意</span>
            <span @click.stop="showAgree = 1">《服务协议》</span>
            <span>和</span>
            <span @click.stop="showAgree = 2">《隐私政策》</span>
          </div>
          <el-button class="submit-btn" type="primary" @click="onSubmit">登录</el-button>
        </el-form>
        <div class="forgot-password" @click="forgotPassword">忘记密码</div>
      </div>
    </div>

    <!-- 忘记密码 step1 -->
    <div class="login-box" v-if="loginStep == 1">
      <h3>找回密码</h3>
      <div>
        <el-form :model="form" :rules="rules">
          <el-form-item prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" :prefix-icon="MessageBox" />
          </el-form-item>
          <el-button class="submit-btn" type="primary" @click="onSubmit">下一步</el-button>
        </el-form>
      </div>
    </div>

    <!-- 忘记密码 step2 -->
    <div class="login-box" v-if="loginStep == 2">
      <h3>找回密码</h3>
      <div>
        <el-form :model="form">
          <el-form-item>
            <el-input v-model="form.code" placeholder="请输入验证码" :prefix-icon="Cellphone" />
          </el-form-item>
          <div class="login-tips2">我们已向您的绑定邮箱{{ form.email }}发送了验证码</div>
          <el-button class="submit-btn" type="primary" @click="onSubmit">下一步</el-button>
        </el-form>
      </div>
    </div>

    <!-- 忘记密码 step3 -->
    <div class="login-box" v-if="loginStep == 3">
      <div class="login-tips">您正在找回的账号是：{{ form.email }}</div>
      <h3>设置新密码</h3>
      <div>
        <el-form :model="form" :rules="rules" ref="ruleFormRef">
          <el-form-item prop="newPassword">
            <el-input v-model="form.newPassword" placeholder="请输入新密码" show-password :prefix-icon="Lock" />
          </el-form-item>
          <el-form-item prop="newPassword2">
            <el-input v-model="form.newPassword2" placeholder="请再次输入新密码" show-password :prefix-icon="Lock" />
          </el-form-item>
          <el-button class="submit-btn" type="primary" @click="onSubmit(ruleFormRef)">确认</el-button>
        </el-form>
      </div>
    </div>
  </div>

  <!-- 重置密码成功 -->
  <el-dialog v-model="centerDialogVisible" title="" width="460" align-center>
    <span class="login-success">恭喜，账号{{ form.email }}重置密码成功</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="centerDialogVisible = false">
          重新登录
        </el-button>
      </span>
    </template>
  </el-dialog>
  <consent-agreement v-if="showAgree" :show-type="showAgree" :handle-hide="() => showAgree = 0"></consent-agreement>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, onUnmounted } from 'vue'
import { User, Lock, MessageBox, Cellphone } from '@element-plus/icons-vue'
import { login, getUserByMail, postResetMail, modifyPasswordByMailCode, verifyResetMailCode } from '@/api'
import type { FormRules, FormInstance } from 'element-plus'
import { ElMessage } from "element-plus";
import ConsentAgreement from './components/ConsentAgreement.vue'

import { useRouter } from 'vue-router'

const router = useRouter()

const ruleFormRef = ref<FormInstance>()
// do not use same name with ref
const form = reactive({
  password: '',
  email: '',
  code: '',
  newPassword: '',
  newPassword2: ''
})

const initForm = () => {
  form.password = ''
  form.email = ''
  form.code = ''
  form.newPassword = ''
  form.newPassword2 = ''
}

interface RuleForm {
  email: '',
  newPassword: '',
  newPassword2: ''
}

const validatePass = (rule: any, value: any, callback: any) => {
  const reg = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9]{8}$/
  if (!reg.test(value)) {
    callback(new Error('密码为8位数字与字母组合，需包含大小写字母，不含特殊字符'))
    return
  }
  if (rule.field === 'newPassword2' && value !== form.newPassword) {
    callback(new Error('两次输入密码不一致'))
    return
  }
  callback()
}

const rules = reactive<FormRules<RuleForm>>({
  newPassword: [{ validator: validatePass, trigger: 'blur' }],
  newPassword2: [{ validator: validatePass, trigger: 'blur' }],
  email: [
    {
      type: 'email',
      required: true,
      message: '请输入正确的邮箱格式',
      trigger: 'change',
    },
  ]
})

const remember = ref(false)
const agree = ref(false)
const loginStep = ref(0)
const centerDialogVisible = ref(false)
const userId = ''
const showAgree = ref(0)

const onSubmit = async (formEl: FormInstance | undefined) => {
  form.email = form.email.trim()
  if (!loginStep.value) {
    if (!agree.value) {
      ElMessage.error(`请阅读并勾选《服务协议》和《隐私政策》`)
      return
    }
    login({ mail: form.email, password: form.password }).then((res: any) => {
      if (res.code === '2000' || res.code === '200') {
        window.localStorage.setItem('token', res.data)
        window.localStorage.setItem('userName', form.email)
        if (remember.value) {
          window.localStorage.setItem('password', form.password)
        } else {
          window.localStorage.setItem('password', '')
        }
        if (agree.value) {
          window.localStorage.setItem('agreeValue', '1')
        } else {
          window.localStorage.setItem('agreeValue', '')
        }
        router.push('/home')
        initForm()
      }
    })
  } else if (loginStep.value == 1) {
    postResetMail({ mail: form.email }).then((res: any) => {

    })
    loginStep.value = 2
  } else if (loginStep.value == 2) {
    if (form.code) {
      verifyResetMailCode({ code: form.code, mail: form.email }).then((res) => {
        if (res.data) {
          loginStep.value = 3;
        } else {
        }
      });
    }
  } else if (loginStep.value == 3) {
    if (!formEl) return
    await formEl.validate((valid: any, fields: any) => {
      if (valid) {
        modifyPasswordByMailCode({ code: form.code, mail: form.email, password: form.newPassword }).then((res) => {
          centerDialogVisible.value = true
          loginStep.value = 0
        })
      } else {

      }
    })

  }
}

// 调整介绍文字的大小
const adjustFontSize = () => {
  const screenWidth = window.innerWidth;
  const fontSize1 = Math.max(screenWidth * 0.0158, 15); // 根据需要调整比例
  const fontSize2 = Math.max(screenWidth * 0.01, 13); // 根据需要调整比例
  (document.querySelector('.text1') as any).style.fontSize = fontSize1 + 'px';
  (document.querySelector('.text2') as any).style.fontSize = fontSize2 + 'px';
}

const forgotPassword = () => {
  loginStep.value = 1
}

onMounted(() => {
  adjustFontSize();
  window.addEventListener('resize', adjustFontSize);
  window.localStorage.setItem('token', '')
  window.localStorage.setItem('njyj-version', '')

  const userName = window.localStorage.getItem('userName')
  const password = window.localStorage.getItem('password')
  agree.value = !!window.localStorage.getItem('agreeValue') || false

  if (password && userName) {
    form.password = password
    form.email = userName
    remember.value = true
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', adjustFontSize);
})

</script>
<style scoped lang="less">
.login {
  background-color: #efefef;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  // padding: 0 8% 0 10%;
  background: url(~@/assets/images/background/login.jpg);
  background-size: 100% 100%;

  ::v-deep(.el-input__wrapper) {
    height: 46px !important;
    font-size: 15px;
  }

  .login-text {
    position: fixed;
    left: 20.6%;
    font-weight: 500;
    color: #000;
    text-align: left;
    height: 50%;
    margin-top: -11.5%;
    width: 38vw;
    text-align: left;
    // margin-left: 12vw;

    .text1 {
      font-size: 1.58vw;
      margin-bottom: 10px;
      position: relative;

      &::before {
        content: '';
        width: 4px;
        height: 1.5vw;
        min-height: 13px;
        background: #426FD3;
        position: absolute;
        left: -9px;
        top: 0.45vw;
      }
    }

    .text2 {
      font-size: 1vw;
      margin-bottom: 10px;
    }
  }

  .login-box {
    position: relative;
    width: 438px;
    height: 486px;
    border-radius: 16px;
    background: #e7eef4;
    box-shadow: 0px 5px 30px 0px rgba(89, 132, 219, 0.08);
    margin-right: 21.5%;
    // margin-top: -5%;
    padding: 48px 38px 0;
    box-sizing: border-box;
    text-align: left;
    backdrop-filter: blur(3px);
  }

  .submit-btn {
    display: block;
    width: calc(100% - 100px);
    margin: 0 50px;
    height: 50px;
    position: absolute;
    left: 0;
    bottom: 95px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 6px;
    background: #3671FE;
  }

  form>div {
    margin: 25px 0;
  }

  .forgot-password {
    text-align: center;
    font-size: 12px;
    font-size: 16px;
    font-weight: 500;
    color: #236CFF;
    margin-top: 102px;
    cursor: pointer;
  }

  .login-tips {
    position: absolute;
    top: 50px;
    font-size: 12px;
  }

  .login-tips2 {
    font-size: 12px;
    color: #000;
    font-weight: 500;
  }

  .agree {
    color: #A3A3A3;
    cursor: default;
    margin: 0;
    font-size: 14px;
    font-weight: 500;

    span:nth-child(2n+1) {
      color: #236CFF;
      cursor: pointer;
    }

    .el-checkbox:last-of-type {
      margin-right: 8px;
    }
  }

  ::v-deep(.el-checkbox) {
    color: #A3A3A3;
    font-weight: 500;

    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #236CFF;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #236CFF;
      border-color: #236CFF;
    }
  }
}

.dialog-footer {
  display: block;
  text-align: center;
}

.login-success {
  font-size: 12px;
  color: #000;
  font-weight: 500;
}
</style>