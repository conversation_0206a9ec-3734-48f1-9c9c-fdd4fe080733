export class StateMachine<Any extends {} = {}> {
  interactiveAreaModifyObjectId: string
  routeModifyObjectId: string
  resourceModifyObjectId: string
  constructor() {
    this.interactiveAreaModifyObjectId = ''
    this.routeModifyObjectId = ''
    this.resourceModifyObjectId = ''
  }
  changeInteractiveArea(oldArray: any, newData: Object) {
    //
  }
  changeRoute(oldArray: any, newData: Object) {
    //
  }
  changeResource(oldArray: any, newData: Object) {
    //
  }
}