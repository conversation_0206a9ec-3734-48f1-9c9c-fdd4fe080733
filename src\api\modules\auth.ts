import request from "../request";

// 登录
export function login(data: any) {
  return request({
    url: "/authority/login",
    method: "post",
    data,
  });
}

// 根据邮箱获取用户
export function getUserByMail(parame: any) {
  return request({
    method: "get",
    url: `/authority/getUserByMail?mail=${parame.mail}`,
  });
}

// 忘记密码发送邮件
export function postResetMail(data: any) {
  return request({
    url: `/authority/postResetMail?mail=${data.mail}`,
    method: "post",
    data,
  });
}

// 根据验证码修改手机账号密码
export function modiftUserWithPasswordAndVerify(data: any) {
  return request({
    url: `/authority/modiftUserWithPasswordAndVerify?phoneNo=${data.phoneNo}&verifyCode=${data.verifyCode}&password=${data.password}`,
    method: "post",
    data,
  });
}

// 校验通过手机修改密码的验证码是否正确
export function verifyPhoneResetPasswordCode(data: any) {
  return request({
    url: `/authority/verifyPhoneResetPasswordCode?code=${data.code}&phone=${data.phoneNo}`,
    method: "post",
    data,
  });
}

// 通过邮件code找回密码
export function modifyPasswordByMailCode(data: any) {
  return request({
    url: `/authority/modifyPasswordByMailCode?code=${data.code}&mail=${data.mail}&password=${data.password}`,
    method: "post",
    data,
  });
}

// 邮件code验证
export function verifyResetMailCode(data: any) {
  return request({
    url: `/authority/verifyResetMailCode?code=${data.code}&mail=${data.mail}`,
    method: "post",
    data,
  });
}

// 注册
export function registerPasswordAndVerify(data: any) {
  return request({
    url: `/authority/addUserWithPasswordAndVerify?phoneNo=${data.phoneNo}&password=${data.password}&verifyCode=${data.verifyCode}`,
    method: "post",
  });
}

// 修改密码验证码获取
export function postPhoneRestVerifyCode(data: any) {
  return request({
    url: `/authority/postPhoneRestPasswordVerifyCode?phoneNo=${data.phoneNo}`,
    method: "post",
  });
}

// 注册密码验证码获取
export function postVerifyCodeForRegister(data: any) {
  return request({
    url: `/authority/postPhoneSetPasswordCode?phoneNo=${data.phoneNo}`,
    method: "post",
  });
}

// 登录验证码获取
export function postVerifyCodeForLogin(data: any) {
  return request({
    url: `/authority/postVerifyCodeForLogin?phoneNo=${data.phoneNo}`,
    method: "post",
  });
}

// 非企业用户登录入口
export function normalogin(data: any) {
  return request({
    url: `/authority/loginV2?phoneNo=${data.phoneNo}&verifyCode=${data.verifyCode}&loginTypeId=${data.loginTypeId}&password=${data.password}&mail=${data.mail}`,
    method: "post"
  });
}

// 获取用户类型
export function getUserTypeByToken() {
  return request({
    url: "/authority/getUserTypeByToken",
    method: "get",
  });
}

// 个人信息页更新密码
export function modifyPasswordByOrgnizationAdmin(data: any) {
  return request({
    url: `/authority/modifyPasswordByOrgnizationAdmin?mail=${data.mail}&password=${data.password}`,
    method: "post",
  });
}

// 客户账号状态的启用和停用
export function modifyUserStatus(data: any) {
  return request({
    url: `/authority/modifyUserStatus?userId=${data.userId}&userStatus=${data.userStatus}`,
    method: "post",
    data,
  });
} 