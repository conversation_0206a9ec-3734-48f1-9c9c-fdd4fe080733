<template>
  <div class="item-list">
    <div class="page-title">
      <span>我的项目</span>
      <div>
        <span class="show-storage-progress" v-if="userInfo.userDto?.packageVersion == 'V2'">
          <self-progress
            :sceneStorage="[homeStorage.userdSceneNum, homeStorage.packageSceneNum]"
            color="#2E76FF"
            titleText="项目数量"
            unitText="个"></self-progress>
        </span>
        <span class="show-storage-progress" v-if="userInfo.userDto?.packageVersion != 'V2'">
          <self-progress
            :sceneStorage="[
              homeStorage.spaceArSceneUsedNum + homeStorage.planeArSceneUsedNum,
              homeStorage.spaceArScenePackageNum + homeStorage.planeArScenePackageNum,
            ]"
            color="#2E76FF"
            titleText="项目数量"
            unitText="个"></self-progress>
        </span>
      </div>
      <div class="title-tips" v-if="expandGroup" @click="expandGroup = null">
        <img src="@/assets/images/icon/path-back.png" />
        视频号项目合集
      </div>
      <div class="filter-data-wrap">
        <div class="switching-modes">
          <div @click="modeType = 1" :class="{ active: modeType == 1 }">
            <img src="@/assets/images/home/<USER>" />
          </div>
          <div @click="modeType = 2" :class="{ active: modeType == 2 }">
            <img src="@/assets/images/home/<USER>" />
          </div>
        </div>
        <div class="select-platform">
          <el-select
            v-model="searchForm.scenePlatform"
            placeholder="所有平台"
            class="select-default"
            popper-class="select-option">
            <el-option
              v-for="(item, index) in scenePlatforms"
              :key="index"
              :label="item.name"
              :value="item.value" />
          </el-select>
        </div>
        <div class="select-platform">
          <el-select
            v-model="searchForm.sceneType"
            placeholder="所有类型"
            class="select-default"
            popper-class="select-option">
            <el-option
              v-for="(item, index) in sceneTypeListAll"
              :key="index"
              :label="item.name"
              :value="item.value" />
          </el-select>
        </div>
        <div class="search-scene">
          <el-input
            v-model="searchForm.sceneName"
            placeholder="搜索项目"
            :prefix-icon="Search"
            clearable />
          <div class="search-icon" @click="initData"></div>
        </div>
      </div>
    </div>
    <div class="list-title-box">
      <div class="create-wechat" @click="addNewProject(3, 1)">
        <img class="left-icon" src="@/assets/images/home/<USER>" />
        <div>新建微信小程序</div>
        <img class="right-icon" src="@/assets/images/home/<USER>" />
      </div>
      <div class="create-room" @click="addNewProject(2, 1)">
        <img class="left-icon" src="@/assets/images/home/<USER>" />
        <div>新建移动端</div>
        <img class="right-icon" src="@/assets/images/home/<USER>" />
      </div>
      <div class="create-glasses" @click="addNewProject(1, 1)">
        <img class="left-icon" src="@/assets/images/home/<USER>" />
        <div>新建眼镜端</div>
        <img class="right-icon" src="@/assets/images/home/<USER>" />
      </div>
      <div class="create-project" @click="showProject = true">
        <img class="left-icon" src="@/assets/images/home/<USER>" />
        <div>新建项目组</div>
        <img class="right-icon" src="@/assets/images/home/<USER>" />
      </div>
    </div>

    <div class="card-wrap" v-if="modeType == 2">
      <div class="group-list" v-if="sceneGroups.length && showGroup && !expandGroup">
        <div
          v-for="(item, index) in sceneGroups"
          :class="(!((index + 1) % columnLength) ? 'marR0' : '') + (' list-style-' + columnLength)"
          @click="expandGroup = { ...item }">
          <div
            class="list-content"
            v-if="item.scenePicUrls?.length && item.scenePicUrls.length >= 4">
            <div v-for="(e, i) in item.scenePicUrls">
              <img :src="e" />
            </div>
          </div>
          <div class="list-content" v-else>
            <div v-for="(e, i) in item.scenePicUrls || []">
              <img :src="e" />
            </div>
            <div v-for="(e, i) in 4 - (item.scenePicUrls?.length || 0)"></div>
          </div>
          <div class="group-title">
            <div>
              <img src="@/assets/images/home/<USER>" />
              <span>{{ item.groupName }}</span>
            </div>
            <div class="item-more">
              <div class="num">{{ item.sceneGroupCount || 0 }}</div>
              <img class="more-icon" src="@/assets/images/hengdian.png" />
              <div class="hover-card" @click.stop="null">
                <div>
                  <div @click="updateGroup(item)">修改名称</div>
                  <div @click="deleteGroup(item)">删除</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="scene-card" :class="!resultMap.latestUpdate ? 'empty-data' : ''">
        <div class="list-box" :style="{ height: heightCur + 55 + 'px' }">
          <div
            :class="'list-style-' + columnLength"
            v-for="(item, index) in resultMap.latestUpdate ? sceneList : columnLength"
            :key="index"
            :style="{ marginRight: !((index + 1) % columnLength) ? '0px' : '24px' }">
            <div :class="`style-color-${item.scenePlatform}`">{{ _sceneType(item) }}</div>
            <img
              v-if="resultMap.latestUpdate"
              :src="item.scenePic"
              alt=""
              :style="{ height: heightCur + 'px' }" />
            <img
              v-else
              src="@/assets/images/card-default.png"
              :style="{ height: heightCur + 'px' }" />
            <div
              class="hover-mask"
              @click.stop="resultMap.latestUpdate ? routerEdit(item) : null"
              :style="{ height: heightCur + 55 + 'px' }"></div>
            <div class="con_father">
              <div class="con_sheet">
                <div class="con_sheet_img">
                  <img
                    v-if="item.scenePlatform == 1"
                    src="@/assets/images/home/<USER>" />
                  <img
                    v-if="item.scenePlatform == 2"
                    src="@/assets/images/home/<USER>" />
                  <img
                    v-if="item.scenePlatform == 3"
                    src="@/assets/images/home/<USER>" />
                  <div v-if="!resultMap.latestUpdate" class="image-empty"></div>
                </div>
                <div>
                  <p v-if="resultMap.latestUpdate" class="p_sceneName">{{ item.sceneName }}</p>
                  <p v-if="resultMap.latestUpdate" class="p_updateTimeStr">
                    更新时间: {{ item.updateTimeStr }}
                  </p>
                  <div class="image-empty2" v-if="!resultMap.latestUpdate"></div>
                </div>
              </div>
            </div>
            <div class="cursor-box">
              <div class="hover-card">
                <div
                  class="share-btn"
                  @click="openShareCode(item)"
                  v-if="isOperation == 1 || userInfo?.userDto?.userType == 1">
                  分享
                </div>
                <div @click="requestCancelTemplate(item)" v-if="item.sceneStatus == 3">
                  取消模板
                </div>
                <div
                  @click="openSetTemplate(item)"
                  :class="{ isDisabled: item.sceneStatus == 3 }"
                  v-if="item.sceneStatus != 3 && isOperation != 1">
                  设为模板
                </div>
                <div class="move-to-style" v-if="!item.groupId">
                  移动至
                  <div v-if="sceneGroups.length">
                    <div>
                      <div
                        v-for="(e, i) in sceneGroups"
                        :key="i"
                        @click="changeGroup(`${item.id}_${e.id}`)">
                        {{ e.groupName }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="move-to-style" v-else @click="handleRemove(item)">移出</div>
                <div
                  class="active-delete"
                  @click="deleteDataEvent(item)"
                  v-if="item.sceneStatus != 3 && isOperation != 1">
                  删除
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="last-empty" v-show="pageTotal > sceneList.length">
            <img src="@/assets/images/card-default.png" :style="{height: heightCur + 'px'}"/>
            <div class="con_father">
              <div class="con_sheet">
                <div class="con_sheet_img">
                  <div class="image-empty"></div>
                </div>
                <div>
                  <div class="image-empty2" ></div>
                </div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
    <div class="list-wrap" v-if="modeType == 1" :class="expandGroup ? 'expand-style' : ''">
      <table-list2
        v-if="tableData.length"
        :data-total="pageTotal"
        :page-size="searchForm.pageSize"
        :page-no="searchForm.pageNo"
        :changePage="changePage"
        :tableData="tableData"
        :sceneGroups="sceneGroups"
        :expandChangeEvent="expandChangeEvent"
        :handleShare="handleShare"
        :handleMoveTo="handleMoveTo"
        :handleSetTemplate="handleSetTemplate"
        :deleteScene="deleteScene"
        :handleOpenScene="routerEdit"
        :handleRemove="handleRemove"
        :expandGroup="expandGroup"
        :updateGroup="updateGroup"
        :deleteGroup="deleteGroup"></table-list2>
    </div>
    <div
      class="empty-style"
      v-if="
        (modeType == 2 && !sceneList.length && !showGroup) ||
        (modeType == 1 && !tableData.length) ||
        (showGroup && !sceneList.length && !sceneGroups.length)
      ">
      <img src="@/assets/images/empty.png" />
    </div>
    <el-dialog v-model="flagVisible" title="删除项目组" width="524" top="32vh">
      <span>确认删除当前项目组，删除后不可恢复。</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="flagVisible = false" style="width: 92px; height: 32px">取消</el-button>
          <el-button
            type="primary"
            @click="requestHandle"
            style="width: 92px; height: 32px"
            color="#2E76FF">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
  <create-project
    v-if="showProject"
    :handleHide="handleHide"
    :user-info="userInfo"
    :defaultGroupData="defaultGroupData"></create-project>
  <create-scene
    v-if="modalShow"
    :modal-show="modalShow"
    :handle-hide="handleHide"
    :sceneType="sceneType"
    :platform="createScenePlatform || sceneDataPlatform"></create-scene>
  <SetTemplate
    v-if="isOpenSetTemp"
    ref="setTemplateRef"
    :hide-add-mask="hideSetTemp"
    @confirm="confirm"
    @update="update"
    :currentScene="hoverCurrentScene"></SetTemplate>
  <QrSceneIdCode
    v-if="isOpenShareCard"
    :hideAddMask="hideQrSceneIdCode"
    :scene="hoverCurrentScene"></QrSceneIdCode>
  <QrCode
    v-if="isOpenQR"
    :hide-add-mask="hideQrCode"
    :hoverCurrentTemplate="hoverCurrentTemplate"
    :codeUrl="codeUrl"
    :name="qrName"
    :sceneDataPlatform="sceneDataPlatform"
    :activeTempSceneId="activeTempSceneId"
    :sceneType="sceneType"></QrCode>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted, computed, reactive, nextTick, markRaw } from 'vue';
import {
  getOrgnizationPackage,
  getSceneMetaPageForWeb,
  getSceneStorage,
  querySceneGroups,
  getSceneInGroup,
  setSceneAsTemplate,
  cancelTemplate,
  deleteSceneMeta,
  updateTemplateInfo,
  getShareInfo,
  getOssAccessPath,
  getWxAppQrCode,
  addSceneGroupRelation,
  removeSceneGroup,
  deleteSceneGroup,
} from '@/api/index';
import CreateProject from '@/views/project/components/CreateProject.vue';
import CreateScene from '@/views/project/create/CreateScene2.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import SelfProgress from '@/components/SelfProgress.vue';
import { useStore } from 'vuex';
import { scenePlatforms, sceneTypeListAll } from '@/config';
import { Search } from '@element-plus/icons-vue';
import TableList2 from '@/components/TableList2.vue';
import { getDayTime } from '@/utils';
import SetTemplate from '@/components/experience/create/SetTemplate.vue';
import QrSceneIdCode from '@/components/experience/create/QrSceneIdCode.vue';
import { Delete } from '@element-plus/icons-vue';
import QrCode from '@/components/experience/create/QrCode.vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const store = useStore();
const userInfo: any = ref({});
const columnLength = ref(4);
let sceneList: any = ref([]); // 场景列表
const showProject = ref(false);
const modalShow = ref(false);
const sceneType = ref(1);
const createScenePlatform = ref(-1);
const sceneStorage: any = ref({});
const sceneDataPlatform = ref(3);
const homeStorage: any = ref({});
const sceneGroups: any = ref([]);
const modeType = ref(2);
const searchForm = reactive({
  pageSize: 100,
  pageNo: 1,
  scenePlatform: 0,
  sceneType: 0,
  sceneName: '',
});
const tableData: any = ref([]);
let isOperation = ref(1);
const isOpenShareCard = ref(false);
const isOpenSetTemp = ref(false);
const hoverCurrentScene = ref();
const setTemplateRef = ref();
const activeTempSceneId = ref(0);
const hoverCurrentTemplate = ref();
const isOpenQR = ref(false);
const codeUrl = ref('');
const qrName = ref('');
const showGroup = ref(true);

const groupId = ref(-1);
const expandGroup: any = ref(null);
const defaultGroupData: any = ref(null);
const flagVisible = ref(false);
let deleteGroupInfo: any = null;
const heightCur = ref(0);
const resultMap = ref({ latestUpdate: false });
const pageTotal = ref(0);

const _sceneType = computed(() => {
  return (item: any) => {
    switch (item.sceneType) {
      case 1:
        return '空间AR';
      case 2:
        return '平面AR';
      case 3:
        return '图像AR';
      case 5:
        return '身体AR';
      case 6:
        return '人脸AR';
      case 7:
        return '手势AR';
      case 8:
        return '单场景AR';
    }
  };
});

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  initData();
};

const updateGroup = (item: any) => {
  defaultGroupData.value = item;
  showProject.value = true;
};

const requestHandle = () => {
  deleteSceneGroup({ id: deleteGroupInfo.id }).then((res: any) => {
    if (res.code == 200) {
      tableData.value = tableData.value.filter(
        (e: any) => !e.children || e.id != deleteGroupInfo.id
      );
      sceneGroups.value = sceneGroups.value.filter((e: any) => e.id != deleteGroupInfo.id);
    }
    flagVisible.value = false;
    deleteGroupInfo = null;
  });
};

const deleteGroup = (item: any) => {
  if (item.children.length) {
    store.state.showTips = '当前项目组内存在项目，不支持删除。';
  } else {
    flagVisible.value = true;
    deleteGroupInfo = { ...item };
  }
};

const handleShare = (item: any) => {
  openShareCode(item);
};

const handleMoveTo = (val: string) => {
  const ids = val.split('_');
  addSceneGroupRelation({ scenId: ids[0], sceneGroupId: ids[1] }).then((res: any) => {
    if (res.code == 200) {
      initData(() => {
        expandChangeEvent({ id: ids[1] }, true);
      });
    }
  });
};

const handleSetTemplate = (item: any) => {
  openSetTemplate(item);
};

const deleteScene = (item: any) => {
  deleteDataEvent(item);
};

const routerEdit = (item: any) => {
  store.dispatch('updateCurrentData', null);
  const path =
    item.sceneType == 1
      ? '/scene_edit?sceneid=' + item.id
      : '/experience_edit?sceneid=' + item.id + '&sceneType=' + item.sceneType;
  router.push(path);
};

const handleRemove = (item: any) => {
  removeSceneGroup({ sceneId: item.id }).then((res: any) => {
    if (!expandGroup.value) {
      initData(() => {
        expandChangeEvent({ id: item.groupId }, true);
      });
    } else {
      sceneList.value = sceneList.value.filter((e: any) => e.id != item.id);
      tableData.value = tableData.value.filter((e: any) => e.groupId && e.id != item.id);
    }
  });
};

const changeGroup = (val: string) => {
  handleMoveTo(val);
};

const addNewProject = (val: number, type: number) => {
  if (
    (userInfo.value.userDto?.packageVersion == 'V2' &&
      homeStorage.value.packageSceneNum <= homeStorage.value.userdSceneNum) ||
    (userInfo.value.userDto?.packageVersion != 'V2' &&
      homeStorage.value.planeArScenePackageNum + homeStorage.value.spaceArScenePackageNum <=
        homeStorage.value.planeArSceneUsedNum + homeStorage.value.spaceArSceneUsedNum)
  ) {
    store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
    return;
  }
  store.state.addProjectFlag = !store.state.addProjectFlag;
  createScenePlatform.value = val;
  sceneType.value = type;
};

const hideQrCode = () => {
  isOpenQR.value = false;
};

const addSceneHome = () => {
  if (sceneList.value.length == sceneStorage.packageSceneNum) {
    return ElMessage({ type: 'warning', message: '项目数量已上限' });
  }
  modalShow.value = true;
};

const projectStyleCount = () => {
  if (window.innerWidth <= 1280) {
    columnLength.value = 3;
  } else if (window.innerWidth > 1280 && window.innerWidth <= 1680) {
    columnLength.value = 4;
  } else if (window.innerWidth > 1680) {
    columnLength.value = 5;
  }

  const dom = document.querySelector('.list-box>div');
  if (dom) {
    heightCur.value = (210 / 306) * dom.getBoundingClientRect().width;
  }
};

const handleHide = (renew?: boolean) => {
  showProject.value = false;
  modalShow.value = false;
  defaultGroupData.value = null;
  if (renew) {
    initData();
  }
};

const requestCancelTemplate = (item: any) => {
  const params = {
    sceneId: item.id,
  };
  return new Promise((resolve) => {
    cancelTemplate(params).then((res: any) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '取消成功！' });
        item.sceneStatus = 1;
      }
    });
  });
};

const deleteDataEvent = (data: any) => {
  ElMessageBox.confirm('是否删除此项目？', '确认', {
    type: 'warning',
    icon: markRaw(Delete),
  })
    .then(() => {
      deleteSceneMeta({ sceneId: data.id }).then((res: any) => {
        if (res.code == 200) {
          ElMessage({ type: 'success', message: '删除成功！' });
          initData();
        }
      });
    })
    .catch(() => {});
};

const confirm = (params: any) => {
  return new Promise((resolve) => {
    setSceneAsTemplate(params).then((res: any) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '操作成功！' });
        isOpenSetTemp.value = false;
        hoverCurrentScene.value.sceneStatus = 3;
      }
    });
  });
};

const update = (params: any) => {
  return new Promise((resolve) => {
    updateTemplateInfo(params).then((res: any) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '修改成功！' });
        isOpenSetTemp.value = false;
      }
    });
  });
};

const openSetTemplate = (item: any) => {
  if (item.sceneStatus == 3) return;
  isOpenSetTemp.value = true;
  hoverCurrentScene.value = item;
  nextTick(() => {
    setTemplateRef.value.setParmasEdit(hoverCurrentScene.value);
  });
};

const hideSetTemp = () => {
  isOpenSetTemp.value = false;
};

const expandChangeEvent = (row: any, expandedRows: any) => {
  if (expandedRows && showGroup.value) {
    getSceneInGroup({ sceneGroupId: row.id }).then((res: any) => {
      sceneGroups.value = sceneGroups.value.map((e: any) => {
        if (e.id == row.id) {
          e.children = res.data.map((item: any) => ({ ...item, groupId: e.id }));
        }
        return e;
      });
      tableData.value = tableData.value.filter((e: any) => !e.children);
      tableData.value.unshift(...sceneGroups.value);
    });
  }
};

const openShareCode = (item: any) => {
  if (item.scenePlatform == 3) {
    startLearn(item, 'id', 'scene');
  } else {
    hoverCurrentScene.value = item;
    isOpenShareCard.value = true;
  }
};

const designMakeRecursion = (scene_id: any) => {
  getShareInfo({ sceneId: scene_id }).then(async (res: any) => {
    if (res.code == '200' && res.data) {
      if (res.data.sceneLocationPicKey) {
        getRequestLocalOsskey(res.data.sceneLocationPicKey);
      }
    }
  });
};

const getRequestLocalOsskey = (ossKey: string) => {
  getOssAccessPath({ key: ossKey }).then((res: any) => {
    hoverCurrentTemplate.value.sceneLocationPicKey = res.data;
  });
};

const startLearn = (item: any, strId: any, cardType = 'template') => {
  sceneType.value = item.sceneType;
  sceneDataPlatform.value = item.scenePlatform;
  if (cardType == 'scene') {
    activeTempSceneId.value = 0;
  }
  isOpenQR.value = true;
  if (item.sceneId) {
    designMakeRecursion(item.sceneId);
  }
  getWXCode(item, strId);
};

const getWXCode = async (item: any, strId: any) => {
  qrName.value = !activeTempSceneId.value ? item.sceneName : item.templateName;
  const { data } = await getWxAppQrCode({ sceneId: item[strId], path: 'pages/njyjxr/scene' });
  if (data) {
    codeUrl.value = 'data:image/jpeg;base64,' + data;
  }
};

const hideQrSceneIdCode = () => {
  isOpenShareCard.value = false;
};

const initData = (callback?: any) => {
  let tableArr: any = [];
  getSceneMetaPageForWeb({ excludeGroupScene: 1, ...searchForm }).then((res: any) => {
    sceneList.value = [...res.data.records];
    tableArr = res.data ? [...res.data.records] : [];
    pageTotal.value = res.data.total;

    if (showGroup.value) {
      querySceneGroups().then((res: any) => {
        sceneGroups.value = [...res.data].map((e: any) => {
          e.scenePic = '/images/project-list-pic.png';
          e.sceneName = e.groupName;
          e.scenePlatform = '';
          e.sceneType = '';
          e.createTimeStr = getDayTime(e.createTime, true);
          e.updateTimeStr = getDayTime(e.updateTime, true);
          e.children = e.scenePicUrls || [];
          return e;
        });
        tableArr.unshift(...(res.data ? JSON.parse(JSON.stringify(sceneGroups.value)) : []));
        tableData.value = [...tableArr];
        callback && callback();
      });
    } else {
      tableData.value = [...tableArr];
      callback && callback();
    }
    resultMap.value.latestUpdate = true;
  });
};

onMounted(() => {
  homeStorage.value = store.state.storageData.home;

  getOrgnizationPackage({}).then((res: any) => {
    userInfo.value = { ...res.data };
  });

  getSceneStorage().then((res: any) => {
    sceneStorage.value = { ...res.data };
  });
  initData();
  projectStyleCount();
  window.addEventListener('resize', projectStyleCount, false);
  store.dispatch('updateCurrentData', {
    projectId: route.query.projectId,
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', projectStyleCount);
});

watch(
  () => expandGroup.value,
  (newState) => {
    if (newState) {
      getSceneInGroup({ sceneGroupId: newState.id }).then((res: any) => {
        sceneGroups.value = sceneGroups.value.map((e: any) => {
          if (e.id == newState.id) {
            e.children = res.data.map((item: any) => ({ ...item, groupId: e.id }));
          }
          tableData.value = [...e.children];
          return e;
        });
        sceneList.value = [...tableData.value];
      });
    } else {
      initData();
    }
  }
);

watch(
  () => store.state.isOperation,
  (nv) => {
    isOperation.value = nv;
  },
  { immediate: true }
);

watch(
  () => store.state.storageData,
  (newState) => {
    homeStorage.value = newState.home;
  },
  { deep: true }
);

watch(
  () => store.state.addProjectFlag,
  () => {
    addSceneHome();
  }
);

watch(searchForm, (newState) => {
  if (newState.sceneName || newState.scenePlatform || newState.sceneType) {
    showGroup.value = false;
  } else {
    showGroup.value = true;
  }
  initData();
});

watch(
  () => modeType.value,
  (newState) => {
    searchForm.pageNo = 1;
    searchForm.pageSize = newState == 1 ? 10 : 100;
    if (expandGroup.value) {
      getSceneInGroup({ sceneGroupId: expandGroup.value.id }).then((res: any) => {
        sceneGroups.value = sceneGroups.value.map((e: any) => {
          if (e.id == expandGroup.value.id) {
            e.children = res.data.map((item: any) => ({ ...item, groupId: e.id }));
          }
          tableData.value = [...e.children];
          return e;
        });
        sceneList.value = [...tableData.value];
      });
    } else {
      initData();
    }
  }
);

watch(
  () => sceneList.value,
  (newState) => {
    setTimeout(() => {
      const dom = document.querySelector('.list-box>div');
      if (dom) {
        heightCur.value = (210 / 306) * dom.getBoundingClientRect().width;
      }
    });
  }
);
</script>
<style scoped lang="less">
::-webkit-scrollbar {
  width: 5px;
  background-color: transparent;
}

.item-list {
  position: relative;
  height: 100%;
  padding-top: 51px;
  box-sizing: border-box;

  .image-empty {
    width: 24px;
    height: 24px;
    border-radius: 7px;
    position: relative;
    margin-right: 8px;
    background-color: #f7f7f9;
  }

  .image-empty2 {
    width: 149px;
    height: 41px;
    border-radius: 4px;
    background-color: #f7f7f9;
  }

  .empty-style {
    position: absolute;
    top: 200px;
    height: calc(100% - 200px);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 140px;
      height: 140px;
    }
  }

  .empty-data {
    .list-box > div {
      cursor: default;
    }

    &.scene-card .list-box > div {
      cursor: default;
      border: 1px solid #dadada !important;
    }

    .hover-mask {
      display: none !important;
    }

    .cursor-box {
      display: none !important;
    }
  }

  .page-title {
    width: 100%;
    position: relative;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    & > span {
      font-weight: bold;
      font-size: 16px;
      color: #1e1e1e;
      margin-right: 16px;
    }

    ::v-deep(.progress-box) {
      margin-right: 16px;
    }

    .filter-data-wrap {
      position: absolute;
      right: 0;
      top: 0;
      width: 576px;
      height: 31px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .switching-modes {
        width: 60px;
        height: 32px;
        background: #ffffff;
        border-radius: 4px;
        padding: 4px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;

        & > div {
          width: 24px;
          height: 24px;
          border-radius: 4px 4px 4px 4px;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }

          &:hover {
            background-color: #eeeeee;
          }

          &.active {
            background-color: #eeeeee;
          }
        }
      }

      .select-platform {
        width: 122px;
        height: 32px;
        background: #ffffff;
        border-radius: 4px 4px 4px 4px;

        ::v-deep(.el-select__wrapper) {
          box-shadow: none;
          height: 32px !important;
        }
      }

      .search-scene {
        position: relative;
        width: 224px;
        height: 32px;
        background: #ffffff;
        border-radius: 4px 4px 4px 4px;

        ::v-deep(.el-input__wrapper) {
          box-shadow: none !important;
          border: none !important;
        }

        .search-icon {
          position: absolute;
          left: 0;
          top: 0;
          width: 32px;
          height: 31px;
          cursor: pointer;
        }
      }
    }
  }

  .card-wrap,
  .list-wrap {
    position: relative;
    height: calc(100% - 108px);
    overflow: hidden;
    overflow-y: auto;

    ::v-deep(.el-select) {
      position: absolute !important;
      left: 0;
      top: 0;
      height: 20px;
      line-height: 20px;
      width: 45px !important;
      opacity: 0;

      .el-select__wrapper {
        min-height: 10px;
        height: 20px !important;
      }
    }
  }

  .expand-style {
    height: calc(100% - 148px);
  }

  .list-style-3 {
    width: calc(33.33% - 16px) !important;
  }

  .list-style-4 {
    width: calc(25% - 18px) !important;
  }

  .list-style-5 {
    width: calc(20% - 20px) !important;
  }

  .create-wechat:hover {
    background: linear-gradient(219deg, rgba(53, 255, 46, 0.17) -7.12%, rgba(53, 255, 46, 0) 48.06%),
      #fff;
  }

  .create-room:hover {
    background: linear-gradient(
        208deg,
        rgba(182, 149, 255, 0.17) -1.33%,
        rgba(255, 255, 255, 0) 70.9%
      ),
      #fff;
  }

  .create-glasses:hover {
    background: linear-gradient(
        208deg,
        rgba(46, 118, 255, 0.17) -1.33%,
        rgba(255, 255, 255, 0) 70.9%
      ),
      #fff;
  }

  .create-project:hover {
    background: linear-gradient(
        208deg,
        rgba(246, 191, 79, 0.2) -1.33%,
        rgba(255, 255, 255, 0) 70.9%
      ),
      #fff;
  }

  .style-color-1,
  .style-color-2,
  .style-color-3 {
    width: 55px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    background: rgba(129, 95, 204, 0.5);
    position: absolute;
    bottom: 53px;
    left: 0;
    z-index: 2;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    border-radius: 0px 10px 0px 0px;
  }

  .style-color-2 {
    background: rgba(46, 118, 255, 0.5);
  }

  .style-color-3 {
    background: rgba(6, 198, 97, 0.5);
  }

  .p_sceneName {
    font-weight: bold;
    font-size: 14px;
    color: #1e1e1e;
    margin: 0;
    text-align: left;
  }

  .p_updateTimeStr {
    font-weight: 400;
    font-size: 12px;
    color: #797979;
    margin: 0;
    text-align: left;
  }

  .no_handle {
    background: none !important;
    cursor: default !important;
  }

  .green {
    background: rgba(21, 142, 100, 0.5) !important;
  }

  .isDisabled {
    cursor: no-drop;
  }

  .scene-card {
    .con_father {
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 2;

      .con_sheet {
        position: relative;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0 16px;
        height: 55px;
        z-index: -1;
        font-weight: bold;
        font-size: 14px;
        color: #1e1e1e;
        // background: #fff;

        .con_sheet_img {
          position: relative;
          width: 24px;
          height: 24px;
          margin-right: 16px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .list-box {
      width: 100%;
      height: 265px;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-wrap: wrap;

      .last-empty {
        cursor: default;
        border: 1px solid #dadada !important;
      }

      & > div:hover {
        border-color: #2e76ff;
      }

      & > div {
        position: relative;
        width: calc(25% - 18px);
        height: 100%;
        cursor: pointer;
        border-radius: 10px;
        margin-right: 24px;
        margin-bottom: 24px;
        border: 1px solid #dadada;
        box-sizing: border-box;

        img {
          width: 100%;
          height: 210px;
          -o-object-fit: cover;
          object-fit: cover;
          vertical-align: middle;
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 10px;
          border-bottom-right-radius: 0;
          border-bottom-left-radius: 0;
        }

        &:hover {
          .hover-mask {
            display: block;
            background-color: transparent;
          }

          .cursor-box {
            display: block;
          }
        }

        .hover-mask {
          position: relative;
          width: 100%;
          height: 210px;
          background: rgba(0, 0, 0, 0.6);
          z-index: 1;
          position: absolute;
          top: 0;
          left: 0;
          box-sizing: border-box;
          border-radius: 10px;
          padding-top: 65px;
          padding-left: calc(50% - 47px);
          display: none;

          & > div {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            position: absolute;
            left: 50%;
            top: 55%;
            transform: translateX(-50%) translateY(-50%);
            z-index: 2;

            & > div {
              margin-bottom: 12px;
            }
          }

          &::before {
            content: '';
            width: 100%;
            height: calc(100% - 2px);
            position: absolute;
            left: 0;
            top: 0;
            z-index: 1;
            box-sizing: border-box;
            border: 2px solid #2e76ff;
            border-radius: 10px;
          }

          .learn-btn {
            position: relative;
            box-sizing: border-box;
            font-weight: bold;
            font-size: 14px;
            color: #ffffff;
            line-height: 32px;
            text-align: center;
            cursor: pointer;
            width: 93px;
            height: 34px;
            background: #2e76ff;
            border-radius: 4px 4px 4px 4px;
            z-index: 2;

            &:hover {
              background: #1251cb;
            }
          }

          .desc_temp {
            width: 152px;
            font-weight: bold;
            font-size: 12px;
            color: #ffffff;
            margin-top: 16px;
            overflow: hidden;
          }

          .create-btn {
            position: relative;
            font-weight: bold;
            line-height: 32px;
            text-align: center;
            cursor: pointer;
            width: 92px;
            height: 32px;
            background: #669aff;
            border-radius: 4px 4px 4px 4px;
            font-weight: 500;
            font-size: 14px;
            color: #fff;
            z-index: 2;

            &:hover {
              color: #fff;
              background: #2e76ff;
            }
          }
        }

        .list-bottom {
          width: 100%;
          height: 45px;
          position: absolute;
          left: 0;
          bottom: 0;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-left: 10px;
          box-sizing: border-box;
          padding-bottom: 4px;
          font-weight: bold;
          font-size: 14px;
          color: #1e1e1e;

          img {
            width: 24px;
            height: 24px;
            border-radius: 0;
            position: relative;
            margin-right: 8px;
          }

          .empty-style {
            width: 149px;
            height: 20px;
            background-color: #f7f7f9;
            border-radius: 4px;
          }
        }
      }
    }

    .cursor-box {
      position: relative;
      position: absolute;
      right: 12px;
      bottom: 14px;
      width: 16px;
      height: 16px;
      background-image: url(~@/assets/images/hengdian.png);
      background-size: 100% 100%;
      cursor: pointer;
      z-index: 2;
      border: 4px solid #fff;
      background-color: #fff;
      border-radius: 100%;
      display: none;

      .hover-card {
        display: none;

        .move-to-style {
          position: relative;

          & > div {
            width: 100%;
            position: absolute;
            left: -108px;
            top: 13px;
            transform: translateY(-50%);
            background-color: transparent;
            padding: 0 4px;
            display: none;

            & > div {
              background-color: #fff;
              padding: 8px 0;
              box-sizing: border-box;
              border-radius: 8px;

              & > div {
                height: 26px;
                line-height: 26px;
                text-align: center;

                &:hover {
                  background-color: #e8f1fd;
                }
              }
            }
          }

          &:hover {
            & > div {
              display: block;
            }
          }
        }
      }

      &:hover {
        border-color: #eaeaea;
        background-color: #eaeaea;

        .hover-card {
          display: block;
        }
      }

      & > div {
        position: relative;
        position: absolute;
        left: -43px;
        bottom: 36px;
        width: 100px;
        background: #ffffff;
        box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
        text-align: center;
        font-weight: 400;
        font-size: 12px;
        color: #1e1e1e;
        padding: 8px 0 8px;
        border-radius: 8px;

        &::before {
          content: '';
          width: 36px;
          height: 26px;
          position: absolute;
          left: 50%;
          bottom: -18px;
          margin-left: -18px;
          background: url(~@/assets/images/experience-icon/triangle-shadow.png);
          background-size: 100% 100%;
          transform: rotate(180deg);
        }

        &::after {
          content: '';
          width: 20px;
          height: 9px;
          background-color: #fff;
          position: absolute;
          left: 50%;
          bottom: -1px;
          margin-left: -10px;
        }

        & > div {
          line-height: 26px;
          height: 26px;
        }

        & > div:hover {
          background: #e8f1fd;
        }

        & > div.active {
          color: #3671fe;
        }
      }
    }
  }

  .scene-card {
    .list-box:hover .hover-mask::before {
      z-index: 3;
      border-radius: 8px;
      border-width: 1px;
    }

    .list-box:hover .hover-mask::after {
      content: none;
    }
  }

  .list-title-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 42px;

    & > div {
      width: 197px;
      height: 66px;
      background: #ffffff;
      border-radius: 10px 10px 10px 10px;
      border: 1px solid rgba(0, 0, 0, 0.06);
      margin-right: 8px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 0 24px;
      box-sizing: border-box;
      cursor: pointer;

      img {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }

      .right-icon {
        display: none;
        margin-right: 0 !important;
      }
    }

    & > div:hover {
      justify-content: space-between;

      .left-icon {
        display: none;
      }

      .right-icon {
        display: block;
      }
    }
  }

  .group-list {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    margin-bottom: 24px;

    & > div {
      width: calc(20% - 19px);
      margin-right: 24px;
      height: 264px;
      border-radius: 10px;
      padding: 16px 16px 1px 16px;
      box-sizing: border-box;
      border: 1px solid #dadada;
      cursor: pointer;

      &:hover {
        padding: 15px 15px 0px 15px;
        border: 2px solid #2e76ff;
      }

      .list-content {
        width: 100%;
        height: 192px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-wrap: wrap;

        & > div {
          width: calc(50% - 6px);
          height: 90px;
          background-color: #ebeef0;
          border-radius: 8px;
          margin-bottom: 12px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .group-title {
        height: 28px;
        box-sizing: border-box;
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        img {
          width: 24px;
          height: 24px;
          vertical-align: middle;
          margin-right: 8px;
        }

        span {
          vertical-align: middle;
          font-weight: bold;
          font-size: 14px;
          color: #1e1e1e;
        }

        .item-more {
          position: relative;
          width: 24px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background: #eceef0;
          border-radius: 28px;
          font-weight: bold;
          font-size: 14px;
          color: #1e1e1e;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          .hover-card {
            display: none;
            position: absolute;
            left: -76px;
            top: -78px;
            width: 102px;
            height: 68px;
            padding-bottom: 10px;

            & > div {
              width: 102px;
              height: 68px;
              box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
              border-radius: 8px;
              padding: 8px 0;
              background-color: #fff;
              box-sizing: border-box;

              & > div {
                height: 26px;
                line-height: 26px;
                font-weight: 400;
                font-size: 12px;
                color: #1e1e1e;

                &:hover {
                  background-color: #e8f1fd;
                }
              }
            }
          }

          &:hover {
            border-color: #eaeaea;
            background-color: #eaeaea;

            .hover-card {
              display: block;
            }
          }

          .more-icon {
            width: 14px;
            height: auto;
            margin-right: 0;
            display: none;
          }

          &:hover {
            .more-icon {
              display: block;
            }

            .num {
              display: none;
            }
          }
        }
      }
    }

    .marR0 {
      margin-right: 0;
    }
  }
}

.title-tips {
  position: relative;
  font-weight: bold;
  font-size: 14px;
  color: #1e1e1e;
  text-align: left;
  margin-bottom: 5px;
  height: 24px;
  line-height: 24px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;

  img {
    width: 24px;
    height: 24px;
    transform: rotate(180deg);
  }
}
</style>
