<template>
  <div>
    <search-form :form-keys-data="keysData" currentRoute="/system/role"></search-form>
  </div>
  <div>
    <table-list
      ref="table_l"
      :data="tableData"
      :column-list="columnList"
      create-list="新建角色"
      :change-page="changePage"
      :delete-content="deleteContent"
      :operation-items="operationItems"></table-list>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import type { OperationItems, TableRow } from '@/types/operation';

const table_l = ref();
const currentIndex = ref(1);
const deleteContent = {
  title: '删除角色',
  content: '是否删除此角色？',
};

const operationItems = reactive<OperationItems>([
  {
    label: '编辑',
    key: 'edit',
    onClick: (row: TableRow) => {
      editData(row);
    },
  },
]);

const keysData = [
  {
    key: 'roleName',
    type: 'input',
    label: '角色名称',
  },
  {
    key: 'roleStatus',
    type: 'select',
    label: '角色状态',
    dataList: [
      {
        name: '正常',
        value: 1,
      },
      {
        name: '异常',
        value: 2,
      },
    ],
  },
];

const tableData: any = [
  {
    roleId: 'R01',
    roleName: '超级管理员',
    roleDescription: '系统管理，所有权限',
    status: '正常',
    updateDate: '2023-8-09',
  },
];

const editData = (data: TableRow) => {
  // Add your edit logic here
};

const columnList = [
  {
    prop: 'roleId',
    label: '角色id',
  },
  {
    prop: 'roleName',
    label: '角色名称',
  },
  {
    prop: 'roleDescription',
    label: '角色描述',
  },
  {
    prop: 'status',
    label: '状态',
  },
  {
    prop: 'updateDate',
    label: '更新日期',
  },
  {
    prop: 'operate',
    label: '操作',
    width: 120,
    type: 'operation',
  },
];

const changePage = (cur: any) => {
  currentIndex.value = cur;
};
</script>
