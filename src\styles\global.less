::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(154, 156, 159, 0.5);
  border-radius: 3px;
}

.icon-close {
  font-size: 26px !important;
  cursor: pointer;
  font-weight: 400 !important;

  &:hover {
    color: #2e76ff !important;
  }
}

.ml-20 {
  margin-left: 20px;
}

.form-submit .el-form-item__content {
  justify-content: flex-end;
}

.customer-style .form-submit .el-form-item__content {
  width: calc(100% - 12px);
  box-sizing: border-box;
  right: 0;

  button {
    margin-left: 12px;
  }
}

.customer-style .form-submit1 .el-form-item__content {
  justify-content: flex-start;

  button {
    margin-left: 0;
    margin-right: 12px;
  }
}

.customer-style .el-form-item__error {
  color: transparent;
}

.el-form-item__error {
  padding-top: 2px !important;
}

.el-textarea__inner {
  border-radius: 4px !important;
  height: 100%;
  padding: 8px 12px;
}

.el-upload {
  width: 100%;
  height: 100%;
  flex-direction: column;

  .el-icon {
    font-size: 50px;
  }

  .el-upload__text {
    display: block;
    font-size: 10px;
    transform: scale(0.83333);
    transform-origin: 0 0;
    font-weight: 400;
    color: #0375ff;
  }
}

.el-input__wrapper .el-input__inner {
  color: #0f0f0f;
  font-size: 16px;
  margin-top: 1px;
}

.source-title {
  font-weight: 400;
  font-size: 10px;
  top: 40px;
  text-align: center;
  color: #414040;
  min-width: 28px;
  height: 20px;
  line-height: 20px;
  border-radius: 14px;
  padding: 0 10px;
}

.source-title.active {
  background: #2e76ff;
  border: 1px solid #ffffff;
  color: #ffffff;
}
