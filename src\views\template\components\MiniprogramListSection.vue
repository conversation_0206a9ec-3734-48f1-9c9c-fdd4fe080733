<template>
  <section class="template-section">
    <div class="template-section__header">
      <h2 class="template-section__title">列表模板</h2>
      <button class="add-list-template-btn" @click="showAddListTemplateDialog = true" type="button">
        添加列表模板
      </button>
    </div>

    <draggable
      v-model="listData"
      class="template-grid template-grid--miniprogram-list"
      :class="{ 'template-grid--dragging': isDragging }"
      item-key="id"
      :animation="200"
      :disabled="false"
      ghost-class="template-card--ghost"
      chosen-class="template-card--chosen"
      drag-class="template-card--drag"
      @start="onDragStart"
      @end="onDragEnd">
      <template #item="{ element: item, index: i }">
        <TemplateCard
          :key="`miniprogram-list-${item.id}`"
          :template-data="item"
          :card-index="i"
          :dom-mask-indexs="domMaskIndexs"
          :show-edit-button="showEditButton"
          :is-sort-mode="true"
          :disable-video-preview="isDragging"
          :is-recommend-area="true"
          :remove-button-text="'移除'"
          @mouseenter="handleMouseEnter"
          @mouseleave="handleMouseLeave"
          @edit="handleEdit"
          @experience="handleExperience"
          @create-similar="handleCreateSimilar"
          @remove-recommend="handleRemove" />
      </template>
    </draggable>

    <!-- 添加列表模板弹窗 -->
    <el-dialog
      v-model="showAddListTemplateDialog"
      title="添加列表模板"
      width="453px"
      :close-on-click-modal="false"
      class="add-list-template-dialog">
      <div class="list-template-form">
        <!-- 模板选择 -->
        <div class="form-item">
          <el-select
            v-model="selectedTemplate"
            placeholder="选择模板"
            class="template-select"
            :loading="isLoadingTemplates"
            clearable>
            <el-option
              v-for="template in availableTemplates"
              :key="template.id"
              :label="template.name"
              :value="template.value" />
          </el-select>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddListTemplateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmAdd">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </section>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import draggable from 'vuedraggable';
import TemplateCard from '@/components/TemplateCard.vue';
import { ElMessage, ElDialog, ElSelect, ElOption, ElButton } from 'element-plus';
import { queryTemplateScene, setWebRecommend, setWxTemplateRecommend } from '@/api';

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  domMaskIndexs: {
    type: Array,
    default: () => [],
  },
  showEditButton: {
    type: Boolean,
    default: true,
  },
});

// Emits
const emit = defineEmits([
  'update:modelValue',
  'mouseenter',
  'mouseleave',
  'edit',
  'experience',
  'create-similar',
  'remove',
  'drag-start',
  'drag-end',
  'refresh',
]);

// Computed
const listData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// State
const isDragging = ref(false);
const showAddListTemplateDialog = ref(false);
const selectedTemplate = ref('');
const availableTemplates = ref([]);
const isLoadingTemplates = ref(false);

// Methods
const onDragStart = (evt) => {
  console.log('🔄 开始拖拽小程序端列表模板:', evt);
  isDragging.value = true;

  // 清除所有模板的 hover 状态
  listData.value.forEach((item) => {
    item.isHover = false;
  });

  emit('drag-start', evt);
};

const onDragEnd = async (evt) => {
  console.log('✅ 小程序端列表模板拖拽结束:', evt);
  isDragging.value = false;

  if (evt.oldIndex !== evt.newIndex) {
    console.log(`小程序端列表模板从位置 ${evt.oldIndex} 移动到 ${evt.newIndex}`);

    // 构建排序参数
    const orderParams = listData.value.map((item, index) => ({
      sceneId: item.sceneId,
      sort: index + 1,
    }));

    emit('drag-end', { evt, orderParams });
  } else {
    emit('drag-end', { evt, orderParams: null });
  }
};

const handleMouseEnter = (templateData, cardIndex) => {
  emit('mouseenter', templateData, cardIndex);
};

const handleMouseLeave = (templateData) => {
  emit('mouseleave', templateData);
};

const handleEdit = (templateData) => {
  emit('edit', templateData);
};

const handleExperience = (templateData) => {
  emit('experience', templateData);
};

const handleCreateSimilar = (templateData) => {
  emit('create-similar', templateData);
};

const handleRemove = (templateData) => {
  emit('remove', templateData);
};

// 获取可选择的模板列表
const fetchAvailableTemplates = async () => {
  try {
    isLoadingTemplates.value = true;
    console.log('🔄 开始获取小程序端模板列表...');

    // 请求小程序端的模板数据
    const response = await queryTemplateScene({
      scenePlatform: 3, // 小程序端
    });

    console.log('📡 小程序模板列表API响应:', response);

    if (response.code === 200 || response.code === '200') {
      // 将API返回的数据转换为下拉选项格式
      availableTemplates.value = response.data.map((template) => ({
        id: template.id || template.sceneId,
        name: template.sceneName || template.name || `模板${template.id}`,
        value: template.id || template.sceneId,
      }));

      console.log('✅ 小程序模板列表获取成功:', availableTemplates.value);
    } else {
      console.error('❌ 小程序模板列表API响应码不匹配:', response.code);
      ElMessage.error('获取模板列表失败');
    }
  } catch (error) {
    console.error('❌ 获取小程序模板列表失败:', error);
    ElMessage.error('获取模板列表失败，请重试');
  } finally {
    isLoadingTemplates.value = false;
  }
};

// 确认添加列表模板
const handleConfirmAdd = async () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请选择模板');
    return;
  }
  // 调用推荐接口
  try {
    const res = await setWxTemplateRecommend(selectedTemplate.value);
    if (res.code === 200 || res.code === '200') {
      ElMessage.success('成功');
      // 关闭弹窗并重置表单
      showAddListTemplateDialog.value = false;
      selectedTemplate.value = '';
      // 触发父组件刷新列表
      emit('refresh');
    } else {
      ElMessage.error(res.message || '推荐失败');
    }
  } catch (e) {
    ElMessage.error('网络异常，推荐失败');
  }
};

// 组件挂载时获取模板列表
onMounted(() => {
  fetchAvailableTemplates();
});
</script>

<style lang="less" scoped>
// 模板网格布局
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  padding: 4px;
  overflow: visible; // 允许视频预览超出网格边界

  // 拖拽状态下的网格样式
  &--dragging {
    :deep(.template-card) {
      transition: transform 0.2s ease;
    }
  }
}

// 拖拽状态下禁用视频预览
.template-grid--dragging {
  :deep(.back_video) {
    display: none !important;
  }
}

// 模板区域样式
.template-section {
  margin-bottom: 40px;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  &__title {
    font-size: 20px;
    font-weight: 600;
    color: #1e1e1e;
    margin: 0;
  }
}

// 添加列表模板按钮样式
.add-list-template-btn {
  width: 120px;
  height: 32px;
  background: #2e76ff;
  border-radius: 4px;
  border: none;
  font-weight: bold;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 12px;
  color: #ffffff;

  &:hover {
    opacity: 0.8;
  }
}

// 添加列表模板弹窗样式
.add-list-template-dialog {
  .list-template-form {
    padding: 0px 0;
  }

  .form-item {
    margin-bottom: 24px;
  }

  .template-select {
    width: 100%;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 18px;
  }
}

@media (max-width: 992px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 14px;
    padding: 2px;
  }
}

@media (max-width: 576px) {
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 12px;
  }
}
</style>
