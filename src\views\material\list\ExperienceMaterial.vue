<template>
  <div class="source-box">
    <div>
      <search-form
        :form-keys-data="keysData"
        :create-event="updateSource"
        :searchDataEvent="searchDataEvent"
        currentRoute="/experience_material"
        :upload-buttons="experienceUploadButtons"></search-form>
    </div>
    <div>
      <table-list
        ref="tableRefs"
        :data="tableData"
        :column-list="columnList"
        :operation-items="operationItems"
        :change-page="changePage"
        create-list="上传素材"
        :delete-content="deleteContent"
        :handle-create="handleCreate"
        :data-total="pageTotal"
        :delete-dataEvent="deleteDataEvent"
        :handle-filter="handleFilter"
        :page-size="searchForm.pageSize"
        :page-no="searchForm.pageNo"
        :show-del-btn="showDelBtn">
        <div class="tips-list-box">
          <div v-for="(item, index) in userScenes" :key="index">{{ item.firstData }}</div>
        </div>
      </table-list>
    </div>
    <create-source-material
      v-if="modalShow"
      :material-affiliation="3"
      :handle-hide="handleHide"
      :default-value="defaultValue"></create-source-material>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import { useRouter } from 'vue-router';
import CreateSourceMaterial from '../create/CreateSourceMaterial.vue';
import {
  getMaterialPageByUser,
  deletePersonMaterial,
  getUserStorage,
  getMatUsedScene,
  getUserTypeByToken,
} from '@/api';
import { materialType, materialUrls } from '@/config';
import { useStore } from 'vuex';
import type { OperationItems, TableRow } from '@/types/operation';
import { formatDateOnly } from '@/utils';

const router = useRouter();
const store = useStore();
const modalShow = ref(false); // 显示新建
const tableData: any = ref([]); // 表格数据
const pageTotal = ref(0); // 总计数据条数
const defaultValue = ref({}); // 进入编辑页的初始值
const userType = ref(0); // 用户类型

const searchForm: any = reactive({
  // 查询对象
  pageSize: 10,
  pageNo: 1,
});

// 体验用户素材页面的上传按钮配置（包含AI生成按钮）
const experienceUploadButtons = [
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '上传',
    label: '模型素材',
    materialType: '4',
    action: 'upload',
    type: 'model',
  },
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '上传',
    label: '图片素材',
    materialType: '3',
    action: 'upload',
    type: 'image',
  },
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '上传',
    label: '视频素材',
    materialType: '1',
    action: 'upload',
    type: 'video',
  },
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '上传',
    label: '音频素材',
    materialType: '2',
    action: 'upload',
    type: 'audio',
  },
  {
    icon: require('@/assets/images/aistar.png'),
    label: 'AI生成 素材',
    isAI: true,
    action: 'ai-generate',
  },
];
const tableRefs = ref();
const deleteContent = ref({
  // 删除tips
  title: '删除素材',
  content: '是否删除此素材？',
});
const showDelBtn = ref(true); // 是否显示下方按钮
const userScenes: any = ref([]); // 哪些场景用到这个素材

// 素材类型选项数据
const materialTypeOptions = [
  { name: '所有类型', value: '' }, // 默认选项
  { name: '视频', value: 1 },
  { name: '音频', value: 2 },
  { name: '图片', value: 3 },
  { name: '模型', value: 4 },
];

// 搜索数据
const keysData = reactive([
  {
    key: 'materialType',
    type: 'select',
    label: '素材类型',
    dataList: materialTypeOptions,
    placeholder: '所有类型',
  },
  {
    key: 'materialName',
    type: 'input',
    label: '素材名称',
  },
]);

const editData = (data: any) => {
  modalShow.value = true;
  defaultValue.value = data;
};

const deleteDataEvent1 = (data: any) => {
  console.log('🔍 deleteDataEvent1 开始执行，当前 deleteContent:', deleteContent.value);

  // 关键：设置 TableList 的 currentData，这样 TipsView 才会显示
  if (tableRefs.value) {
    tableRefs.value.currentData = data;
    console.log('✅ 设置 TableList currentData:', data);
  }

  getMatUsedScene({ materialId: data.id, materialAffiliation: 1 }).then((res) => {
    if (res.data) {
      // 当素材被使用时，显示不同的标题和内容
      deleteContent.value.title = '无法删除素材';
      store.state.showTips = '当前素材已经在以下项目中使用，不支持删除';
      showDelBtn.value = false;
      userScenes.value = [...res.data];
    } else {
      // 当素材未被使用时，恢复原始的删除确认标题和内容
      store.state.showTips = deleteContent.value.content;
      showDelBtn.value = true;
    }
    console.log('🔍 deleteDataEvent1 执行完毕');
  });
};

const deleteDataEvent = (data: any) => {
  deletePersonMaterial({ materialId: data.id }).then((res: any) => {
    if (
      res.code == 200 &&
      searchForm.pageNo - 1 == (pageTotal.value - 1) / searchForm.pageSize &&
      searchForm.pageNo > 1
    ) {
      searchForm.pageNo -= 1;
    }
    getDataList();
  });
};

// 操作按钮配置 - 所有用户都可以编辑和删除
const operationItems = computed<OperationItems>(() => {
  const items: OperationItems = [
    {
      label: '编辑',
      key: 'edit',
      onClick: (row: TableRow) => {
        editData(row);
      },
    },
    {
      label: '删除',
      key: 'delete',
      onClick: (row: TableRow) => {
        deleteDataEvent1(row);
      },
    },
  ];

  return items;
});

// 表头列
const columnList = [
  {
    prop: 'thumbnailOssAccessUrl',
    label: '缩略图',
    type: 'image',
    url: [materialUrls, 'materialType'],
  },
  {
    prop: 'materialName',
    label: '素材名称',
  },
  {
    prop: 'materialType',
    label: '素材类型',
  },
  {
    prop: 'materialFormat',
    label: '上传格式',
  },
  {
    prop: 'storageSize',
    label: '素材大小',
  },
  {
    prop: 'equipType',
    label: '应用平台',
    customType: 'platform',
    segm: true, // 分段显示
  },
  {
    prop: 'updatetime',
    label: '更新时间',
    formatter: (value: string) => formatDateOnly(value),
  },
  {
    prop: 'materialGenStatus',
    label: '当前状态',
    list: {
      1: '正在处理',
      2: '正在处理',
      3: '生成错误',
      4: '已保存',
    },
  },
  {
    prop: 'operate',
    label: '操作',
    width: 120,
    type: 'operation',
  },
];

const handleFilter = (key: string, value: any) => {
  searchForm[key] = value ? value[0] : null;
  searchForm.pageNo = 1;
  getDataList();
  tableRefs.value.selectDataArr = null;
};

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getDataList();
};

const handleCreate = () => {
  modalShow.value = true;
};

const updateSource = () => {
  router.push('/upload_source');
};

const handleHide = (renew?: boolean) => {
  modalShow.value = false;
  defaultValue.value = {};

  if (renew) {
    // 判断是否需要重新渲染
    getDataList();
  }
};

const searchDataEvent = (data: any, type?: string) => {
  // 处理按钮点击事件
  if (data && typeof data === 'object' && data.action) {
    if (data.action === 'upload') {
      // 处理上传按钮点击，弹出上传素材弹框
      console.log('上传按钮点击:', data);
      if (data.materialType) {
        // 设置默认值，包含素材类型
        defaultValue.value = { materialType: data.materialType };
      }
      modalShow.value = true;
      return;
    }

    if (data.action === 'ai-generate') {
      // 处理AI生成按钮点击，跳转到AI大模型页面
      router.push('/ai_generate');
      return;
    }
  }

  // 处理搜索表单数据（兼容旧的调用方式）
  if (typeof data === 'string') {
    type = data;
  } else if (data && typeof data === 'object') {
    for (const key in data) {
      if (key !== 'action') {
        searchForm[key] = data[key];
      }
    }
  }

  if (type == 'reset') {
    searchForm.materialType = '';
    tableRefs.value.clearFilterEvent();
  }
  getDataList();
  tableRefs.value.selectDataArr = null;
};

const getDataList = () => {
  getMaterialPageByUser({ ...searchForm }).then((res: any) => {
    if (res.data) {
      // 为数据添加默认值处理
      tableData.value = res.data.records.map((item: any) => ({
        ...item,
        materialGenStatus: item.materialGenStatus || '-',
        materialFormat: item.materialFormat || '-',
        storageSize: item.storageSize || '-',
        materialDescribe: item.materialDescribe || '-',
        equipType: item.equipType || '-',
        updatetime: item.updatetime || '-',
      }));
      pageTotal.value = res.data.total;
    }
  });
};

// 获取用户存储容量
const getCurrentUserStorage = () => {
  getUserStorage()
    .then((res: any) => {
      console.log('体验用户素材页面 API Response:', res);
      if (res.code === '200' || res.code === '2000') {
        const storageData = JSON.parse(JSON.stringify(store.state.storageData));
        console.log('体验用户素材页面 Before update:', storageData);
        storageData.material = { ...res.data };
        console.log('体验用户素材页面 After update:', storageData);
        store.state.storageData = { ...storageData };
        console.log('体验用户素材页面 Store updated:', store.state.storageData);
      } else {
        console.warn('体验用户素材页面 Unexpected response code:', res.code);
      }
    })
    .catch((err) => {
      console.error('体验用户素材页面 API Error:', err);
    });
};

watch(
  () => store.state.storageData.material,
  (newVal) => {
    console.log('体验用户素材页面 Material storage changed:', newVal);
  },
  { deep: true }
);

onMounted(() => {
  console.log('体验用户素材页面 experienceUploadButtons:', experienceUploadButtons);

  // 获取用户类型
  getUserTypeByToken().then((res: any) => {
    userType.value = res.data;
    console.log('当前用户类型:', userType.value);
  });

  getDataList();
  getCurrentUserStorage();
});
</script>

<style scoped lang="less">
.source-box {
  height: 100%;
  .tips-list-box {
    margin-top: 10px;
    font-size: 12px;
    color: #666;
  }
}
</style>
