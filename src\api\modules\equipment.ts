import request from '../request';

// 添加设备
export function addEquipment(data: any) {
  return request({
    url: `equip/addEquipment?equipName=${data.equipName}&snCode=${data.snCode}`,
    method: 'post',
  });
}

// 更新设备
export function updateEquipment(data: any) {
  return request({
    url: `equip/updateEquipment?equipName=${data.equipName}&snCode=${data.snCode}`,
    method: 'post',
  });
}

// 获取设备列表
export function getEquipmentPage(data: any) {
  return request({
    url: `equip/getEquipmentPage?pageNo=${data.pageNo}&pageSize=${data.pageSize}&snCode=${
      data.snCode || ''
    }&equipmentType=${data.equipmentType || ''}&equipStatus=${
      data.equipStatus == undefined ? '' : data.equipStatus
    }&mail=${data.mail || ''}&equipmentName=${data.equipmentName || ''}`,
    method: 'get',
  });
}

// 删除设备
export function deleteEquip(data: any) {
  return request({
    url: `equip/deleteEquip?equipId=${data.equipId}`,
    method: 'post',
  });
}

// 获取设备统计信息
export function getEquipmentStatistic() {
  return request({
    url: '/equip/getEquipmentStatistic',
    method: 'get',
  });
}

// 获取设备访问统计
export function getEquipmentVistStatistic(data: any) {
  return request({
    url: `/equip/getEquipmentVistStatistic?sceneId=${data.sceneId || ''}&equipmentSncode=${
      data.equipmentSncode || ''
    }&pageNo=${data.pageNo}&pageSize=${data.pageSize}&equipmentType=${
      data.equipmentType || ''
    }&mail=${data.mail || ''}&startDate=${data.startDate || ''}&endDate=${data.endDate || ''}`,
    method: 'get',
  });
}

// 导出设备访问统计Excel
export function getEquipmentVistStatisticExcel(data: any) {
  return request({
    url: '/equip/getEquipmentVistStatisticExcel',
    method: 'post',
    data,
  });
}
