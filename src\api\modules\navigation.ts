import request from "../request";

// 添加导航图
export function addNjyjGraph(data: any) {
  return request({
    url: "/navi/addNjyjGraph",
    method: "post",
    data,
  });
}

// 获取导航图列表
export function getNjyjGraphPage(data: any) {
  return request({
    url: `/navi/getNjyjGraphPage?pageNo=${data.pageNo}&pageSize=${data.pageSize}&graphName=${data.graphName || ''}&spaceId=${data.spaceId || ''}`,
    method: "get",
  });
}

// 保存导航节点
export function saveNjyjNodes(data: any) {
  return request({
    url: "/navi/saveNjyjNodes",
    method: "post",
    data,
  });
}

// 获取导航节点
export function getNjyjNodes(data: any) {
  return request({
    url: `/navi/getNjyjNodes?graphId=${data.graphId}`,
    method: "get",
  });
}

// 获取导航路线
export function getNaviRoute(data: any) {
  return request({
    url: `/navi/getNaviRoute?graphId=${data.graphId}&startNodeId=${data.startNodeId}&endNodeId=${data.endNodeId}`,
    method: "get",
  });
}

// 删除导航节点
export function deleteNjyjNaviNode(data: any) {
  return request({
    url: `/navi/deleteNjyjNaviNode?esId=${data.esId}`,
    method: "post",
    data: data.deleteData
  });
}

// 删除导航图
export function deleteNjyjGraph(data: any) {
  return request({
    url: `/navi/deleteNjyjGraph?njyjGraphId=${data.njyjGraphId}`,
    method: "post",
  });
}

// 根据ID获取导航图
export function getNjyjGraphById(data: any) {
  return request({
    url: `/navi/getNjyjGraphById?graphId=${data.graphId}`,
    method: "get",
  });
}

// 更新导航图
export function updateNjyjGraph(data: any) {
  return request({
    url: '/navi/updateNjyjGraph',
    method: "post",
    data
  });
}

// 连接两个节点
export function joinTwoNode(data: any) {
  return request({
    url: '/navi/joinTwoNode',
    method: "post",
    data
  });
}

// 设置场景图
export function setSceneGraph(data: any) {
  return request({
    url: `scene/setSceneGraph?sceneId=${data.sceneId}&graphId=${data.graphId}`,
    method: "post",
  });
}

// 根据定位获取导航路线
export function getNaviRouteByLocalize(data: any) {
  return request({
    url: `navi/getNaviRouteByLocalize?localizeX=${data.localizeX}&localizeZ=${data.localizeZ}&graphId=${data.graphId}&endNodeId=${data.endNodeId}`,
    method: "get",
  });
}

// 获取图形使用的场景
export function getGraphUserdScene(data: any) {
  return request({
    url: `navi/getGraphUserdScene?njyjGraphId=${data.njyjGraphId}`,
    method: "get",
  });
} 