import request from '../request';

// 保存场景元数据
export function saveSceneMeta(data: any) {
  return request({
    url: '/scene/saveSceneMeta',
    method: 'post',
    data,
  });
}

// 保存场景元数据并返回ID
export function saveSceneMetaBackId(data: any) {
  return request({
    url: '/scene/saveSceneMetaBackId',
    method: 'post',
    data,
  });
}

// 获取场景元数据页面
export function getSceneMetaPageForWeb(data: any) {
  return request({
    url: `/scene/getSceneMetaPageForWeb?pageNo=${data.pageNo}&pageSize=${data.pageSize}&sceneName=${
      data.sceneName || ''
    }&sceneId=${data.sceneId || ''}&sceneStatus=${data.sceneStatus || ''}&sceneType=${
      data.sceneType || ''
    }&scenePlatform=${data.scenePlatform || ''}&excludeGroupScene=${data.excludeGroupScene || ''}`,
    method: 'get',
  });
}

// 保存场景
export function saveScene(data: any) {
  return request({
    url: '/scene/saveScene',
    method: 'post',
    data,
  });
}

// 获取Web场景
export function getWebScene(data: any) {
  return request({
    url: '/scene/getWebScene?id=' + data.id,
    method: 'get',
  });
}

// 修改场景开放状态
export function modifySceneOpenStatus(data: any) {
  return request({
    url: `scene/modifySceneOpenStatus?status=${data.status}&sceneId=${data.sceneId}`,
    method: 'post',
  });
}

// 更新场景元数据
export function updateSceneMeta(data: any) {
  return request({
    url: 'scene/updateSceneMeta',
    method: 'post',
    data,
  });
}

// 删除场景元数据
export function deleteSceneMeta(data: any) {
  return request({
    url: `scene/deleteSceneMeta?sceneId=${data.sceneId}`,
    method: 'post',
  });
}

// 获取场景类型
export function getSceneTypes() {
  return request({
    url: '/scene/getSceneTypes',
    method: 'get',
  });
}

// 获取素材场景
export function getMaterialScene(data: any) {
  return request({
    url: `/scene/queryMaterialMetaInScene?sceneId=${data.sceneId}&materialType=${data.materialType}&interactionId=${data.interactionId}`,
    method: 'get',
  });
}

// 获取场景元数据
export function getSceneMeta(data: any) {
  return request({
    url: `/scene/getSceneMeta?id=${data.id || ''}`,
    method: 'get',
  });
}

// 获取互动区域统计数据
export function getInteractionPage(data: any) {
  return request({
    url: `scene/getInteractionPage?pageNo=${data.pageNo}&pageSize=${data.pageSize}&sceneId=${
      data.sceneId || ''
    }&interactionName=${data.interactionName || ''}`,
    method: 'get',
  });
}

// cancel template
export function cancelTemplate(data: any) {
  return request({
    url: '/scene/cancelTemplate?sceneId=' + data.sceneId,
    method: 'post',
  });
}

// 获取路线统计数据
export function getGuideRoutePage(data: any) {
  return request({
    url: `scene/getGuideRoutePage?pageNo=${data.pageNo}&pageSize=${data.pageSize}&sceneId=${
      data.sceneId || ''
    }&guideRouteName=${data.guideRouteName || ''}`,
    method: 'get',
  });
}

// 获取场景存储信息
export function getSceneStorage() {
  return request({
    url: '/scene/getSceneStorage',
    method: 'get',
  });
}

// 设置网页端推荐模板
export function setWebRecommend(sceneId: number) {
  return request({
    url: '/scene/setWebRecommend',
    method: 'post',
    data: sceneId,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 网页端推荐模板排序
export function orderWebTemplate(orderData: Array<{ firstData: number; secondData: number }>) {
  return request({
    url: '/scene/orderWebTemplate',
    method: 'post',
    data: orderData,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 删除推荐模板
export function removeRecommend(templateId: number) {
  return request({
    url: '/scene/removeRecommend',
    method: 'post',
    data: templateId,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 查询模板场景
export function queryTemplateScene(data: any, flag = false) {
  let url = `/scene/queryTemplateScene`;
  let hasParams = false;

  // 添加 name 参数（只有当存在且不为空时）
  if (data.name && data.name.trim() !== '') {
    url += hasParams ? `&name=${data.name}` : `?name=${data.name}`;
    hasParams = true;
  }

  // 添加 sceneType 参数（当不是-1时）
  if (data.sceneType !== undefined && data.sceneType !== null && data.sceneType >= 0) {
    url += hasParams ? `&sceneType=${data.sceneType}` : `?sceneType=${data.sceneType}`;
    hasParams = true;
  }

  // 添加 scenePlatform 参数（除非flag为true，且只有当scenePlatform >= 0时）
  if (
    !flag &&
    data.scenePlatform !== undefined &&
    data.scenePlatform !== null &&
    data.scenePlatform >= 0
  ) {
    url += hasParams
      ? `&scenePlatform=${data.scenePlatform}`
      : `?scenePlatform=${data.scenePlatform}`;
    hasParams = true;
  }

  // 添加 recommend 参数（0或1）- 用于网页端
  if (data.recommend !== undefined && data.recommend !== null) {
    const recommendValue = data.recommend === true || data.recommend === 1 ? 1 : 0;
    url += hasParams ? `&recommend=${recommendValue}` : `?recommend=${recommendValue}`;
    hasParams = true;
  }

  // 添加 wxRecommend 参数（0或1）- 用于小程序端
  if (data.wxRecommend !== undefined && data.wxRecommend !== null) {
    const wxRecommendValue = data.wxRecommend === true || data.wxRecommend === 1 ? 1 : 0;
    url += hasParams ? `&wxRecommend=${wxRecommendValue}` : `?wxRecommend=${wxRecommendValue}`;
    hasParams = true;
  }

  // 添加 templateType 参数
  if (data.templateType !== undefined && data.templateType !== null) {
    url += hasParams ? `&templateType=${data.templateType}` : `?templateType=${data.templateType}`;
    hasParams = true;
  }

  // 添加 bannerRecommend 参数（0或1）- 用于Banner推荐
  if (data.bannerRecommend !== undefined && data.bannerRecommend !== null) {
    const bannerRecommendValue =
      data.bannerRecommend === true || data.bannerRecommend === 1 ? 1 : 0;
    url += hasParams
      ? `&bannerRecommend=${bannerRecommendValue}`
      : `?bannerRecommend=${bannerRecommendValue}`;
    hasParams = true;
  }

  console.log('queryTemplateScene API URL:', url);

  return request({
    url: url,
    method: 'get',
  });
}

// 获取模板场景集合
export function getTemplateSceneCollection(data: any) {
  return request({
    url: `/scene/getTemplateSceneCollection?scenePlatform=${data.scenePlatform}`,
    method: 'get',
  });
}

// 设置场景为模板
export function setSceneAsTemplate(data: any) {
  return request({
    url: `/scene/setSceneAsTemplate`,
    method: 'post',
    data,
  });
}

// 更新模板信息
export function updateTemplateInfo(data: any) {
  return request({
    url: `/scene/updateTemplateInfo`,
    method: 'post',
    data,
  });
}

// 获取模板列表
export function getTemplates(data: any) {
  return request({
    url: `/scene/getSceneMetaPageForWeb?pageNo=${data.pageNo}&pageSize=${data.pageSize}&sceneName=${data.sceneName}&sceneId=${data.sceneId}&sceneStatus=${data.sceneStatus}&sceneType=${data.sceneType}&scenePlatform=${data.scenePlatform}`,
    method: 'get',
  });
}

// 获取场景开始的行为列表
export function getSeceneStartBehaviorList(data: any) {
  return request({
    url: `/scene/getSceneStartBehaviorList?sceneId=${data.sceneId}`,
    method: 'get',
  });
}

// 获取场景中的某个素材的行为列表
export function getElmentBehavior(data: any) {
  return request({
    url: `/scene/getElmentBehavior?sceneId=${data.sceneId}&elementId=${data.elementId}`,
    method: 'get',
  });
}

// 上传地址照片
export function uploadLocationPic(data: any) {
  return request({
    url: `/scene/uploadLocationPic`,
    method: 'post',
    data,
  });
}

// 更新某个行为
export function updateBehavior(data: any) {
  return request({
    url: `/scene/updateBehavior`,
    method: 'post',
    data,
  });
}

// 删除某个行为
export function deleteBehavior(data: any) {
  return request({
    url: `/scene/deleteBehavior?id=${data.id}`,
    method: 'post',
  });
}

// 查询行为是否存在
export function queryBehaviorExist(data: any, sceneId: any) {
  return request({
    url: `/scene/queryBehaviorExist?elementId=${data.elementId}&elmentStatusData=${data.elmentStatusData}&effectElmentBehavior=${data.effectElmentBehavior}&effectElementId=${data.effectElementId}&effectElmentBehaviorData=${data.effectElmentBehaviorData}`,
    method: 'get',
  });
}

// 添加场景开始行为
export function addSceneStartBehavior(data: any, sceneId: any) {
  return request({
    url: `/scene/addSceneStartBehavior?sceneId=${sceneId}`,
    method: 'post',
    data,
  });
}

// 添加元素行为
export function addElementBehavior(data: any) {
  return request({
    url: `/scene/addElementBehavior`,
    method: 'post',
    data,
  });
}

// Banner相关API

/**
 * 上传Banner封面图
 * @param file 图片文件
 * @returns Promise
 */
export function uploadBannerPic(file: File) {
  const formData = new FormData();
  formData.append('pic', file);

  return request({
    url: '/scene/uploadBannerPic',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 上传Banner背景图
 * @param file 图片文件
 * @returns Promise
 */
export function uploadBannerBackGroundPic(file: File) {
  const formData = new FormData();
  formData.append('pic', file);

  return request({
    url: '/scene/uploadBannerBackGroundPic',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 设置Banner模板
 * @param params Banner模板参数
 * @returns Promise
 */
export function setBannerTemplate(params: {
  bannerCoverPic?: string;
  bannerBackGroundPic?: string;
  templateId: string;
}) {
  const { bannerCoverPic, bannerBackGroundPic, templateId } = params;

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (bannerCoverPic) {
    queryParams.append('bannerCoverPic', bannerCoverPic);
  }
  if (bannerBackGroundPic) {
    queryParams.append('bannerBackGroundPic', bannerBackGroundPic);
  }
  queryParams.append('templateId', templateId);

  return request({
    url: `/scene/setBannerTemplate?${queryParams.toString()}`,
    method: 'POST',
    data: '',
  });
}

/**
 * 设置小程序模板推荐
 * @param id 模板ID
 * @returns Promise
 */
export function setWxTemplateRecommend(id: string | number) {
  return request({
    url: `/scene/setWxTemplateRecommend?id=${id}`,
    method: 'POST',
    data: '',
  });
}

/**
 * 移除小程序模板推荐
 * @param id 模板ID
 * @returns Promise
 */
export function removeWxRecommend(id: string | number) {
  return request({
    url: '/scene/removeWxRecommend',
    method: 'POST',
    data: JSON.stringify(id),
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 移除Banner模板推荐
 * @param id 模板ID
 * @returns Promise
 */
export function removeBannerRecommend(id: string | number) {
  return request({
    url: '/scene/removeBannerRecommend',
    method: 'POST',
    data: JSON.stringify(id),
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// Banner排序
export function orderBannerTemplate(orderData: Array<{ firstData: number; secondData: number }>) {
  return request({
    url: '/scene/orderBannerTemplate',
    method: 'POST',
    data: orderData,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 修改Banner模板
export function updateBannerTemplate(params: {
  bannerCoverPic?: string;
  bannerBackGroundPic?: string;
  currentTemplateId: string | number;
  changeTemplateId: string | number;
}) {
  const queryParams = new URLSearchParams();
  if (params.bannerCoverPic) queryParams.append('bannerCoverPic', params.bannerCoverPic);
  if (params.bannerBackGroundPic)
    queryParams.append('bannerBackGroundPic', params.bannerBackGroundPic);
  queryParams.append('currentTemplateId', String(params.currentTemplateId));
  queryParams.append('changeTemplateId', String(params.changeTemplateId));

  return request({
    url: `/scene/updateBannerTemplate?${queryParams.toString()}`,
    method: 'POST',
  });
}
