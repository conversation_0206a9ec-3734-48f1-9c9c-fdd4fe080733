<template>
  <div class="customer-box">
    <div>
      <search-form
        :form-keys-data="keysData"
        :search-data-event="searchData"
        currentRoute="/customer"
        :upload-buttons="customerUploadButtons"></search-form>
    </div>
    <div>
      <table-list
        ref="tableRefs"
        :data="tableData"
        :column-list="columnList"
        :operation-items="operationItems"
        create-list="新增客户"
        :change-page="changePage"
        :delete-content="deleteContent"
        :handle-create="handleCreate"
        :data-total="pageTotal"
        :page-no="searchForm.pageNo"
        @againStart="againStart"
        :handle-filter="handleFilter"></table-list>
    </div>
    <create-customer
      :package-data="packageArray"
      v-if="
        (modalShow == 'add' || modalShow == 'reopen') &&
        showCard &&
        packageVersion != 'V2' &&
        defaultValue.id
      "
      :handle-show="handleShow"
      :default-value="defaultValue"
      @updateValue="updateValue"
      :modalShow="modalShow"></create-customer>
    <create-customer2
      :package-data="packageArray"
      v-if="
        (modalShow == 'add' || modalShow == 'reopen') &&
        showCard &&
        (packageVersion == 'V2' || !defaultValue.id)
      "
      :handle-show="handleShow"
      :default-value="defaultValue"
      @updateValue="updateValue"
      :modalShow="modalShow"></create-customer2>
    <expansion-package
      v-if="modalShow == 'exp' && packageVersion != 'V2'"
      :handle-show="handleShow"
      :default-value="defaultValue"></expansion-package>
    <expansion-package2
      v-if="modalShow == 'exp' && packageVersion == 'V2'"
      :handle-show="handleShow"
      :default-value="defaultValue"></expansion-package2>
    <Calendar
      v-if="isShowCalendar"
      :hideAddMask="hideAddMask"
      :default-value="defaultValue"
      @showCreateCustomer="showCreateCustomer"
      @updateValue="updateValue"></Calendar>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, reactive, computed } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import { userStatus, orgnizationType } from '@/config';
import { getOrgnizationInfos, getAllPackageInfo, reopenUser, closeUser } from '@/api';
import CreateCustomer from './components/CreateCustomer.vue';
import CreateCustomer2 from './components/CreateCustomer2.vue';
import ExpansionPackage from '../package/components/ExpansionPackage.vue';
import ExpansionPackage2 from '../package/components/ExpansionPackage2.vue';
import Calendar from '@/components/tool/Calendar.vue';
import { getDayTime } from '@/utils';
import type { OperationItems, TableRow } from '@/types/operation';

type CustomerRow = TableRow & {
  hideEdit?: boolean;
  userStatus: number;
  adminUserInfo: {
    id: string | number;
    packageVersion?: string;
  };
};

const dymaicHandleStatus = (row: TableRow): string => {
  const status = row.userStatus as number;
  if ([1, 3].includes(status)) {
    return '停用';
  } else if ([2].includes(status)) {
    return '启用';
  } else if ([4].includes(status)) {
    return '重新开启';
  } else if ([5].includes(status)) {
    return '';
  }
  return '';
};

const operationItems = computed(() => [
  {
    label: (row: TableRow) => {
      const status = row.userStatus as number;
      if ([1, 3].includes(status)) return '停用';
      if ([2].includes(status)) return '启用';
      if ([4].includes(status)) return '重新开启';
      return '';
    },
    key: 'status',
    show: (row: TableRow) => Boolean(row?.adminUserInfo),
    disabled: (row: TableRow) => [5].includes(row.userStatus as number),
    onClick: (row: TableRow) => editData(row, false),
  },
  {
    label: '编辑',
    key: 'edit',
    show: (row: TableRow) => !row.hideEdit,
    disabled: (row: TableRow) => [2, 4, 5].includes(row.userStatus as number),
    onClick: (row: TableRow) => editData(row, true),
  },
  // {
  //   label: '扩展服务包',
  //   key: 'extend',
  //   show: (row: TableRow) => row.adminUserInfo?.packageVersion !== 'V2',
  //   disabled: (row: TableRow) => [2, 4, 5].includes(row.userStatus as number),
  //   onClick: expansionEvent,
  // },
  {
    label: '扩展历史',
    key: 'history',
    show: (row: TableRow) => row.adminUserInfo?.packageVersion === 'V2',
    disabled: (row: TableRow) => [2, 4, 5].includes(row.userStatus as number),
    onClick: expansionEvent,
  },
]);

const isShowCalendar = ref(false);
const showCard = ref(true);
const pageTotal = ref(0); // 总计数据条数
const tableData: any = ref([]); // 表格数据
const deleteContent = {
  title: '删除客户',
  content: '是否删除该客户？',
};
const modalShow = ref(''); // 显示新建
const packageArray: any = ref([]);
const defaultValue: any = ref({}); // 进入编辑页的初始值
const tableRefs = ref();
const packageVersion = ref('');
const searchForm: any = reactive({
  // 查询对象
  pageSize: 20,
  pageNo: 1,
});

const customerUploadButtons = [
  {
    icon: require('@/assets/images/add2232.png'),
    prefix: '',
    label: '新增客户',
    action: 'create',
    type: 'customer',
  },
];

const handleFilter = (key: string, value: any) => {
  searchForm[key] = value ? value[0] : null;
  searchForm.pageNo = 1;
  getData();
  tableRefs.value.selectDataArr = null;
};

const updateValue = (date: Date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
  const newYearMonth = year + '-' + month;
  defaultValue.value.expireTimeStr = newYearMonth + defaultValue.value.expireTimeStr.slice(7);
};

const againStart = (data: TableRow) => {
  modalShow.value = 'reopen';
  defaultValue.value = data;
  packageVersion.value = data.adminUserInfo?.packageVersion || '';
};

const editData = (data: any, isEdit?: boolean) => {
  if (isEdit) {
    modalShow.value = 'add';
    defaultValue.value = data;
    packageVersion.value = data.adminUserInfo.packageVersion;
  } else {
    if ([1, 3].includes(data.userStatus)) {
      closeUser({ userId: data.adminUserInfo.id }).then((res: any) => {
        getData();
      });
    } else if (data.userStatus == 2) {
      reopenUser({ userId: data.adminUserInfo.id }).then((res: any) => {
        getData();
      });
    }
  }
};

const showCreateCustomer = () => {
  showCard.value = true;
};

const expansionEvent = (data: any) => {
  modalShow.value = 'exp';
  defaultValue.value = data;
  packageVersion.value = data.adminUserInfo.packageVersion;
};

const updateTime = () => {
  showCard.value = false;
  isShowCalendar.value = true;
};

const hideAddMask = () => {
  isShowCalendar.value = false;
};

const keysData = ref([
  {
    key: 'orgnizationName',
    type: 'input',
    label: '客户名称',
  },
]);

const columnList = ref([
  {
    prop: 'orgnizationName',
    label: '客户名称',
    customType: 'orgnizationName',
  },
  {
    prop: 'orgnizationType',
    label: '客户类型',
    customType: 'orgnizationType',
    filters: [
      { text: '文娱', value: 1 },
      { text: '工业', value: 2 },
      { text: '教育', value: 3 },
      { text: '其他', value: 4 },
    ],
  },
  {
    prop: 'packageName',
    label: '套餐类型',
    customType: 'packageName',
    resetKey: 'packageId',
    filters: [],
  },
  {
    prop: 'mail',
    label: '账号信息',
    customType: 'mail',
  },
  {
    prop: 'cycleTime',
    label: '有效时间',
    customType: 'cycleTime',
  },
  {
    prop: 'userStatus',
    label: '账号状态',
    customType: 'userStatus',
  },
  {
    prop: 'operate',
    label: '操作',
    width: 224,
  },
]);

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getData();
};

const handleCreate = () => {
  modalShow.value = 'add';
  defaultValue.value = {};
};

const handleShow = (value: string, renew?: boolean) => {
  modalShow.value = value;
  defaultValue.value = {};
  if (renew) {
    getData();
  }
};

const searchData = (data: any, type?: string) => {
  // 处理按钮点击事件
  if (data && typeof data === 'object' && data.action) {
    if (data.action === 'create' && data.type === 'customer') {
      handleCreate();
      return;
    }
  }

  // 处理搜索表单数据
  for (const key in data) {
    searchForm[key] = data[key];
  }
  if (type == 'reset') {
    searchForm.packageId = '';
    searchForm.orgnizationType = '';
    tableRefs.value.clearFilterEvent();
  }
  getData();
};

onMounted(() => {
  getData();
});

const getData = () => {
  getOrgnizationInfos({ ...searchForm }).then((res: any) => {
    pageTotal.value = res.data.total;
    res.data.records.forEach((item: Record<string, any>) => {
      if (!item.packageStartTimeStr) {
        item.packageStartTimeStr = '2024-11-13';
      }
    });
    tableData.value = res.data.records.map((d: any) => ({
      ...d,
      packageName: d.packageInfoDto?.packageName,
      // userStatus: userStatus[d.adminUserInfo.userStatus],
      orgnizationType: orgnizationType[d.orgnizationType],
      packageStartTimeStr: d.userBindPackageDto
        ? getDayTime(d.userBindPackageDto?.packageStartTime)
        : d.packageStartTimeStr,
      expireTimeStr:
        (d.userBindPackageDto
          ? getDayTime(d.userBindPackageDto?.packageEndTime)
          : d.expireTimeStr) || '',
    }));
  });

  getAllPackageInfo().then((res: any) => {
    columnList.value[2].filters = res.data.map((e: any) => ({ text: e.packageName, value: e.id }));
    packageArray.value = [...res.data];
  });
};
</script>
<style scoped lang="less">
.customer-box {
  height: 100%;
}
</style>
