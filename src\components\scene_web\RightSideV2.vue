<template>
  <div class="canvas-right" :class="hide_side ? 'hide-right-side' : ''">
    <div class="canvas-right-title">
      {{ showType ? (showType == 1 ? '素材属性' : '互动区域属性') : '公共区域属性' }}
    </div>
    <div class="scene-info" v-if="showType">
      <!-- 互动区域的属性 -->
      <div v-if="showType == 2">
        <div class="info-title">
          <img src="@/assets/images/icon/list.png" />
          <span>区域信息</span>
        </div>
        <div class="scene-info-list">
          <div>区域名称</div>
          <div class="info-item">
            <el-input
              v-model="interactionBackgroundMusic[activeInteraction].interactionName"
              @input="changeInteractionName"
              @blur="
                (e) => resetInteractionName(e, interactionBackgroundMusic[activeInteraction])
              " />
          </div>
        </div>
        <div class="scene-info-list">
          <div>背景音乐</div>
          <div class="info-item">
            <div class="select-box">
              <el-select
                clearable
                v-model="interactionBackgroundMusic[activeInteraction].backgroundMusic"
                popper-class="select-option"
                @change="changeInteractionValue">
                <el-option
                  v-for="op in interactionBackgroundMusic[activeInteraction].materialMetaDtoList"
                  :label="op.elementName || op.materialDto.materialName"
                  :value="op.materialDto.id"
                  :key="op.materialDto.id" />
              </el-select>
            </div>
            <div
              class="loop-btn"
              :class="interactionBackgroundMusic[activeInteraction].isMusicCycle ? 'active' : ''"
              @click="changeMusic">
              <span>循环</span>
              <img
                v-if="interactionBackgroundMusic[activeInteraction].isMusicCycle"
                src="@/assets/images/icon/loop-icon.png" />
              <img v-else src="@/assets/images/icon/no-loop-icon.png" />
            </div>
          </div>
        </div>
      </div>
      <!-- 素材的属性 -->
      <div v-if="showType == 1">
        <div class="info-title">
          <img src="@/assets/images/icon/list.png" />
          <span>
            素材信息 - {{ calcuType(materialAttributeInfo[activeMaterial].materialType) }}
          </span>
        </div>
        <div class="scene-info-list">
          <div>素材名称</div>
          <div class="info-item">
            <el-input
              v-model="materialAttributeInfo[activeMaterial].materialName"
              @input="changeMaterialName"
              @blur="(e) => resetInitName(e, materialAttributeInfo[activeMaterial])" />
          </div>
        </div>
        <!-- 视频 -->
        <div class="scene-info-list" v-if="materialAttributeInfo[activeMaterial]">
          <div>初始状态</div>
          <div>
            <div
              class="info-item"
              v-if="
                ['1', '3', '4', '5'].includes(materialAttributeInfo[activeMaterial].materialType)
              ">
              <span class="play-switch">
                <span>静态</span>
                <el-switch
                  style="--el-switch-on-color: #2e76ff; --el-switch-off-color: #c2d4fe"
                  v-model="materialAttributeInfo[activeMaterial].isStatic"
                  @change="changeStaticStatus" />
                <span class="box-item">
                  <span>
                    <span style="font-weight: bold">静态</span>
                    ：体验项目时，不能缩放或旋转项目内的素材，和交互行为无关
                  </span>
                </span>
              </span>
            </div>
            <div
              class="info-item"
              v-if="['1', '2', '4'].includes(materialAttributeInfo[activeMaterial].materialType)">
              <span class="play-switch">
                <span>播放</span>
                <el-switch
                  style="--el-switch-on-color: #2e76ff; --el-switch-off-color: #c2d4fe"
                  v-model="materialAttributeInfo[activeMaterial].autoPlay"
                  @change="changePlayStatus" />
              </span>
              <div
                v-show="materialAttributeInfo[activeMaterial].autoPlay"
                class="loop-btn"
                :class="materialAttributeInfo[activeMaterial].cyclePlay ? 'active' : ''"
                @click="changeCyclePlay">
                <span>循环</span>
                <img
                  v-if="materialAttributeInfo[activeMaterial].cyclePlay"
                  src="@/assets/images/icon/loop-icon.png" />
                <img v-else src="@/assets/images/icon/no-loop-icon.png" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="interactive-data" v-if="showType">
      <div class="info-title">
        <img src="@/assets/images/icon/list.png" />
        <span>交互参数</span>
      </div>
      <div
        v-for="(item, index) in spaceData"
        :key="index"
        v-show="!store.state.isPlanStyle || showType == 2 || !index">
        <div class="info-list">
          <span>{{ item.title }}</span>
          <div>
            <span class="axle">X</span>
            <el-input
              v-model="item.value.x"
              type="text"
              autocomplete="off"
              :formatter="(value: string) => checkNumber(value)"
              :class="'is' + item.key"
              @input="changeXYZ($event, 'x', item.key)"
              :disabled="store.state.isPlanStyle && showType == 2 && item.key == 'rotation'"
              @blur="(e) => resetZero(e, 'x', item)" />
          </div>
          <div>
            <span class="axle">Y</span>
            <el-input
              v-model="item.value.y"
              type="text"
              autocomplete="off"
              :formatter="(value: string) => checkNumber(value)"
              :class="'is' + item.key"
              @input="changeXYZ($event, 'y', item.key)"
              :disabled="
                store.state.isPlanStyle && showType == 2 && ['location', 'scale'].includes(item.key)
              "
              @blur="(e) => resetZero(e, 'y', item)" />
          </div>
          <div>
            <span class="axle">Z</span>
            <el-input
              v-model="item.value.z"
              type="text"
              autocomplete="off"
              :formatter="(value: string) => checkNumber(value)"
              :class="'is' + item.key"
              @input="changeXYZ($event, 'z', item.key)"
              :disabled="store.state.isPlanStyle && showType == 2 && item.key == 'rotation'"
              @blur="(e) => resetZero(e, 'z', item)" />
          </div>
        </div>
      </div>
    </div>
    <div class="scene-info" v-if="!showType">
      <div v-for="(item, index) in sceneInfoList" :key="index">
        <div
          v-if="item.type == 2 && sceneInfoList[index - 1]?.type == 1"
          class="scene-info-line"></div>
        <div
          v-if="index == 0 || (item.type == 2 && sceneInfoList[index - 1]?.type == 1)"
          class="info-title">
          <img src="@/assets/images/icon/list.png" />
          <span>{{ index == 0 ? '项目信息' : '空间信息' }}</span>
        </div>
        <div
          class="scene-info-list"
          v-if="item.type"
          v-show="
            (userType == 1 && ['项目编号', '空间编码'].includes(item.name)) ||
            !['项目编号', '空间编码'].includes(item.name)
          ">
          <div>{{ item.name }}</div>
          <div class="info-item">
            <span v-if="!item.key">{{ item.value }}</span>
            <el-input
              v-if="
                item.key &&
                item.key != 'backgroundMusic' &&
                item.key != 'takePhotoButtonVisible' &&
                item.key != 'pathNavigation'
              "
              :class="item.type === 'textarea' ? 'scene-info-dec' : ''"
              v-model="item.value"
              :type="item.type || 'text'"
              resize="none"
              @change="changeValue(item.value, item.key || '')"
              @blur="(e) => initSceneName(e, item)"
              :maxlength="item.name == '项目名称' ? 20 : 30" />
            <div class="select-box" v-if="item.key == 'backgroundMusic'">
              <el-select
                v-model="item.value"
                clearable
                popper-class="select-option"
                @change="changeValue(item.value, item.key || '')">
                <el-option
                  v-for="op in audioSelectList"
                  :label="op.elementName || op.materialDto.materialName"
                  :value="op.materialDto.id"
                  :key="op.materialDto.id" />
              </el-select>
            </div>
            <div
              v-if="item.key == 'backgroundMusic'"
              class="loop-btn"
              :class="isLoop ? 'active' : ''"
              @click="isLoop = !isLoop">
              <span>循环</span>
              <img v-if="isLoop" src="@/assets/images/icon/loop-icon.png" />
              <img v-else src="@/assets/images/icon/no-loop-icon.png" />
            </div>
            <span v-if="item.key == 'takePhotoButtonVisible'" class="play-switch">
              <el-switch
                style="--el-switch-on-color: #2e76ff; --el-switch-off-color: #c2d4fe"
                v-model="item.value"
                @change="changeValue(item.value, item.key || '')" />
            </span>
            <div class="select-box" v-if="item.key == 'pathNavigation'">
              <el-select
                v-model="item.value"
                clearable
                popper-class="select-option"
                @change="changeSceneGraph(item.value)">
                <el-option
                  v-for="op in graphInfo"
                  :label="op.graphName"
                  :value="op.id"
                  :key="op.id" />
              </el-select>
              <span
                class="box-item path-box-item"
                :class="isHideGraph ? 'hide-graph' : ''"
                v-if="item.value"
                @click="isHideGraph = !isHideGraph">
                <span>
                  <span style="font-weight: bold">显示/隐藏</span>
                  ：编辑状态中可见路径或隐藏路径，不影响终端体验
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <BehaviorTree v-if="showType != 2"></BehaviorTree>
    <div class="interaction-tips" v-if="showType == 2">
      <div>注意：</div>
      <div>1.互动区域下方管理的内容，只有用户进入互动区域后才能看见。</div>
      <div>2.同一个互动区域内的素材，才能创建互相影响的交互行为。</div>
    </div>
  </div>
  <div v-if="!hide_side" class="hide-side" @click="hide_side = true">
    <img src="@/assets/images/icon/hide-side.png" />
  </div>
  <div v-if="hide_side" class="show-side" @click="hide_side = false">
    <img src="@/assets/images/icon/show-side.png" />
  </div>
  <TipsView></TipsView>
</template>
<script lang="ts" setup>
import { onMounted, ref, reactive, watch, computed } from 'vue';
import { searchMaterial, radiansToAngle, searchMaterialIndex, desensitizte } from '@/utils';
import { useStore } from 'vuex';
import BehaviorTree from '../BehaviorTree.vue';
import TipsView from '@/components/TipsView.vue';
import { sceneTypeMap } from '@/config';
import { getNjyjGraphPage, setSceneGraph, getUserTypeByToken } from '@/api/index';

const store = useStore();

const props = defineProps({
  changeSceneValue: {
    default: null,
    type: Function,
  },
  changeActiveValue: {
    default: null,
    type: Function,
  },
});

const sceneInfoList: any = ref([]); // TODO 真实接数据时候需要加上key
const hide_side = ref(false);
const activeMaterial = ref('');
const activeInteraction = ref('');
const showType = ref(0); // 1是素材，2是互动区域
const audioSelectList: any = ref([]);
const isLoop = ref(false);
const interactionBackgroundMusic: any = reactive({});
const materialAttributeInfo: any = reactive({});
const materialIdexs: any = ref([]); // 存当前高亮素材位置
const graphInfo: any = ref(null);
const graphId: any = ref(null);
const userType = ref(0);
const isHideGraph = ref(false);
const scenePlatformMap: any = { 1: '眼镜端', 2: '移动端', 3: '小程序' };
const spaceData: any = ref([
  {
    title: '位置',
    key: 'location',
    value: {
      x: 0,
      y: 0,
      z: 0,
    },
  },
  {
    title: '旋转',
    key: 'rotation',
    value: {
      x: 0,
      y: 0,
      z: 0,
    },
  },
  {
    title: '缩放',
    key: 'scale',
    value: {
      x: 1,
      y: 1,
      z: 1,
    },
  },
]);

const calcuType = computed(() => {
  return (type) => {
    switch (type) {
      case '1':
        return '视频';
      case '2':
        return '音频';
      case '3':
        return '图片';
      case '4':
        return '模型';
      case '5':
        return '文本';
    }
  };
});

const resetZero = (e, key, item) => {
  if (e.target.value == '') {
    item.value[key] = 0;
    props.changeActiveValue(0, key, item.key, showType.value);
  }
};

const resetInteractionName = async (e, item) => {
  if (e.target.value == '') {
    item.interactionName = item.initInteractionName;
  } else {
    const hasSensitiveWords = await desensitizte(e.target.value, '不可包含敏感词汇！');
    if (hasSensitiveWords) {
      item.interactionName = item.initInteractionName;
    }
  }
  changeInteractionName(item.interactionName);
};

const resetInitName = async (e, item) => {
  // 素材名称
  if (e.target.value == '') {
    item.materialName = item.initMaterialName;
  } else {
    const hasSensitiveWords = await desensitizte(e.target.value, '不可包含敏感词汇！');
    if (hasSensitiveWords) {
      item.materialName = item.initMaterialName;
      changeMaterialName(item.materialName);
    }
  }
};

const initSceneName = async (e, item) => {
  if (e.target.value == '') {
    item.value = item.initValue;
  } else {
    const hasSensitiveWords = await desensitizte(e.target.value, '不可包含敏感词汇！');
    if (hasSensitiveWords) {
      item.value = item.initValue;
    }
  }
  changeValue(item.value, item.key || '');
};

const changeMaterialName = (val: string) => {
  val = val.trim();
  const editSceneData = { ...store.state.editSceneData, changeTime: new Date().getTime() };
  if (materialIdexs.value.length > 1) {
    editSceneData.interactionDtoList[materialIdexs.value[0]].materialMetaDtoList[
      materialIdexs.value[1]
    ].elementName = val;
    editSceneData.interactionDtoList[materialIdexs.value[0]].materialMetaDtoList[
      materialIdexs.value[1]
    ].flag = 'update';
  } else {
    editSceneData.outerMaterialMetaDtoList[materialIdexs.value[0]].elementName = val;
    editSceneData.outerMaterialMetaDtoList[materialIdexs.value[0]].flag = 'update';
  }
  const currentMaterial = (window as any).scene?.getObjectByName(store.state.activeMaterial);
  if (currentMaterial) {
    currentMaterial.children[0].element.innerHTML = val;
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
};

const checkNumber = (value: string) => {
  const newValue = value.trim();
  if (newValue == '') return '';
  if (((newValue[0] == '0' && newValue[1] !== '.') || newValue[0] == '.') && newValue.length > 1) {
    return newValue.slice(1);
  }
  if (
    !Number.isNaN(Number(newValue.slice(-1)[0])) ||
    (value[0] == '-' && value.length == 1) ||
    newValue.slice(-1)[0] == '.'
  ) {
    return value;
  } else {
    return newValue.slice(0, -1);
  }
};

const changeSceneGraph = (val: string) => {
  const sceneId = sceneInfoList.value[0].value;
  setSceneGraph({ sceneId, graphId: val || '' }).then((res) => {
    store.state.editSceneData.graphId = val;
    isHideGraph.value = false;
    graphId.value = val;
  });
};

const changeValue = (val: any, key: any) => {
  if (!['takePhotoButtonVisible', 'backgroundMusic', 'pathNavigation'].includes(key)) {
    val = val.trim();
  }
  if (key) {
    props.changeSceneValue(val, key);
  }
  if (!val && isLoop.value && key == 'backgroundMusic') {
    isLoop.value = false;
  }
};

const changeInteractionValue = (val: string) => {
  store.state.editSceneData.interactionDtoList = store.state.editSceneData.interactionDtoList.map(
    (e: any) => {
      if (e.uuid == activeInteraction.value) {
        e.backgroundMusic = val;
        e.flag = e.flag != 'add' ? 'update' : 'add';
      }
      return e;
    }
  );
  if (!val && interactionBackgroundMusic[activeInteraction.value].isMusicCycle) {
    changeMusic();
  }
};

const changeMusic = () => {
  interactionBackgroundMusic[activeInteraction.value].isMusicCycle =
    !interactionBackgroundMusic[activeInteraction.value].isMusicCycle;
  store.state.editSceneData.interactionDtoList = store.state.editSceneData.interactionDtoList.map(
    (e: any) => {
      if (e.uuid == activeInteraction.value) {
        e.isMusicCycle = interactionBackgroundMusic[activeInteraction.value].isMusicCycle ? 1 : 0;
        e.flag = e.flag != 'add' ? 'update' : 'add';
      }
      return e;
    }
  );
};

// 素材循环开关
const changeCyclePlay = () => {
  const editSceneData = { ...store.state.editSceneData };
  materialAttributeInfo[activeMaterial.value].cyclePlay =
    !materialAttributeInfo[activeMaterial.value].cyclePlay;
  if (materialIdexs.value.length > 1) {
    editSceneData.interactionDtoList[materialIdexs.value[0]].materialMetaDtoList[
      materialIdexs.value[1]
    ].cyclePlay = materialAttributeInfo[activeMaterial.value].cyclePlay ? 1 : 0;
    editSceneData.interactionDtoList[materialIdexs.value[0]].materialMetaDtoList[
      materialIdexs.value[1]
    ].flag = 'update';
  } else {
    editSceneData.outerMaterialMetaDtoList[materialIdexs.value[0]].cyclePlay =
      materialAttributeInfo[activeMaterial.value].cyclePlay ? 1 : 0;
    editSceneData.outerMaterialMetaDtoList[materialIdexs.value[0]].flag = 'update';
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
};

// 素材播放开关
const changePlayStatus = (val: string) => {
  const editSceneData = { ...store.state.editSceneData };
  if (materialIdexs.value.length > 1) {
    editSceneData.interactionDtoList[materialIdexs.value[0]].materialMetaDtoList[
      materialIdexs.value[1]
    ].autoPlay = val ? 1 : 0;
    editSceneData.interactionDtoList[materialIdexs.value[0]].materialMetaDtoList[
      materialIdexs.value[1]
    ].flag = 'update';
  } else {
    editSceneData.outerMaterialMetaDtoList[materialIdexs.value[0]].autoPlay = val ? 1 : 0;
    editSceneData.outerMaterialMetaDtoList[materialIdexs.value[0]].flag = 'update';
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
  if (!val && materialAttributeInfo[activeMaterial.value].cyclePlay) {
    changeCyclePlay();
  }
};

// 素材是否静态
const changeStaticStatus = (val: string) => {
  const editSceneData = { ...store.state.editSceneData };
  if (materialIdexs.value.length > 1) {
    editSceneData.interactionDtoList[materialIdexs.value[0]].materialMetaDtoList[
      materialIdexs.value[1]
    ].isStatic = val ? 1 : 0;
    editSceneData.interactionDtoList[materialIdexs.value[0]].materialMetaDtoList[
      materialIdexs.value[1]
    ].flag = 'update';
  } else {
    editSceneData.outerMaterialMetaDtoList[materialIdexs.value[0]].isStatic = val ? 1 : 0;
    editSceneData.outerMaterialMetaDtoList[materialIdexs.value[0]].flag = 'update';
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
};

const changeInteractionName = (val: string) => {
  store.state.editSceneData.interactionDtoList = store.state.editSceneData.interactionDtoList.map(
    (e: any) => {
      if (e.uuid == activeInteraction.value) {
        e.interactionName = val;
        e.flag = e.flag != 'add' ? 'update' : 'add';
      }
      return e;
    }
  );
};

const changeXYZ = (value: any, key: string, type: string) => {
  if (value == Number(value) && value != String(+value)) return;
  if ((Number(value) && value.slice(-1) != '.') || value === '0') {
    props.changeActiveValue(Number(value), key, type, showType.value);
  }
};

const selectInteractionMusic = (editSceneData: any) => {
  editSceneData.interactionDtoList.forEach((e: any) => {
    const materialMetaDtoList =
      e.materialMetaDtoList.filter((m: any) => m.materialDto.materialType == '2') || [];
    if (!interactionBackgroundMusic[e.uuid]) {
      interactionBackgroundMusic[e.uuid] = {
        isMusicCycle: !!e.isMusicCycle,
        backgroundMusic: e.backgroundMusic,
        materialMetaDtoList,
        interactionName: e.interactionName,
        initInteractionName: e.interactionName,
      };
    } else {
      interactionBackgroundMusic[e.uuid].materialMetaDtoList = [...materialMetaDtoList];
    }
    const hasInterMusic = materialMetaDtoList.find(
      (music) => music.materialDto.id == interactionBackgroundMusic[e.uuid].backgroundMusic
    );
    if (!hasInterMusic) {
      interactionBackgroundMusic[e.uuid].backgroundMusic = null;
    }
  });
};

const removeDecimals = (obj: any, dec?: number) => {
  return {
    x: +Number(obj.x).toFixed(dec || 2),
    y: +Number(obj.y).toFixed(dec || 2),
    z: +Number(obj.z).toFixed(dec || 2),
  };
};

const resetData = (data: any) => {
  spaceData.value[0].value = { ...removeDecimals(data.location) };
  spaceData.value[1].value = {
    x: +radiansToAngle(data.rotation.x).toFixed(2),
    y: +radiansToAngle(data.rotation.y).toFixed(2),
    z: +radiansToAngle(data.rotation.z).toFixed(2),
  };
  spaceData.value[2].value = { ...removeDecimals(data.scale) };
};

onMounted(() => {
  getUserTypeByToken().then((res: any) => {
    userType.value = res.data;
  });
});

watch(hide_side, (newState) => {
  if (!document.querySelector('.zoom-operation')) return;
  if (newState) {
    (document.querySelector('.zoom-operation') as any).style.right = '30px';
  } else {
    (document.querySelector('.zoom-operation') as any).style.right = '347px';
  }
});

watch(
  () => store.state.editSceneData,
  (newState) => {
    if (!newState.id) return;
    // 项目元信息
    sceneInfoList.value = [
      {
        name: '项目编号',
        value: newState?.id,
        type: 1,
      },
      {
        name: '项目名称',
        value: newState?.sceneName,
        initValue: newState?.sceneName,
        key: 'sceneName',
        type: 1,
      },
      {
        name: '背景音乐',
        value: newState.backgroundMusic || null,
        key: 'backgroundMusic',
        type: 1,
      },
      {
        name: '空间编码',
        value: newState.spaceDto?.id,
        type: 2,
      },
      {
        name: '空间名称',
        value: newState.spaceDto?.descriptionName,
        type: 2,
      },
      {
        name: '路径导航',
        value: newState.graphId || graphId.value,
        key: 'pathNavigation',
        type: graphInfo.value && graphInfo.value.length ? 2 : 0,
      },
    ];
    if (newState.scenePlatform == 3) {
      // 小程序空间AR需要拍照
      const takePhoto = {
        name: '拍照功能',
        value: !!newState.takePhotoButtonVisible,
        key: 'takePhotoButtonVisible',
        type: 1,
      };
      sceneInfoList.value.splice(3, 0, takePhoto);
      if (newState.takePhotoButtonVisible == null) {
        sceneInfoList.value[3].value = true;
        props.changeSceneValue(1, 'takePhotoButtonVisible');
      }
    }

    if (graphId.value && graphId.value != newState.graphId) {
      store.state.editSceneData.graphId = graphId.value;
    } else if (String(graphId.value) == 'undefined' && newState.graphId) {
      store.state.editSceneData.graphId = null;
    }

    if (showType.value == 1) {
      const data: any = searchMaterial();
      data && resetData(data);
    } else if (showType.value == 2) {
      const data: any = newState.interactionDtoList.filter(
        (e: any) => e.uuid == activeInteraction.value
      )[0];
      data && resetData(data);
      selectInteractionMusic(newState);
    } else {
      isLoop.value = !!newState.isMusicCycle;
      const audioList = newState.outerMaterialMetaDtoList.filter(
        (e: any) => e.materialDto.materialType == '2'
      );
      audioSelectList.value = [...audioList];
    }

    if (!graphInfo.value) {
      getNjyjGraphPage({ pageSize: 200, pageNo: 1, spaceId: newState.spaceDto?.id }).then(
        (res: any) => {
          graphInfo.value = [...res.data.records];
        }
      );
    }

    sceneInfoList.value.splice(3, 0, {
      name: '项目类型',
      value: `${scenePlatformMap[newState.scenePlatform]}/${sceneTypeMap[newState?.sceneType]}`,
      type: 1,
    });
  }
);

watch(
  () => showType.value,
  (nv) => {
    // showType为1，editSceneData的音乐有变化时，也需要更新audioList数组，此时showType为0，不会进到watch editSceneData
    if (!nv) {
      isLoop.value = !!store.state.editSceneData.isMusicCycle;
      const audioList = store.state.editSceneData.outerMaterialMetaDtoList.filter(
        (e: any) => e.materialDto.materialType == '2'
      );
      audioSelectList.value = [...audioList];
    }
  }
);

watch(
  () => store.state.activeMaterial,
  (newState) => {
    activeMaterial.value = newState;
    materialIdexs.value = (newState && searchMaterialIndex(newState).split(',')) || [];
    if (newState && !materialAttributeInfo[newState]) {
      let activeMaterialData: any = null;
      if (materialIdexs.value.length == 1) {
        activeMaterialData = {
          ...store.state.editSceneData.outerMaterialMetaDtoList[materialIdexs.value[0]],
        };
      } else if (materialIdexs.value.length == 2) {
        activeMaterialData = {
          ...store.state.editSceneData.interactionDtoList[materialIdexs.value[0]]
            .materialMetaDtoList[materialIdexs.value[1]],
        };
      }
      materialAttributeInfo[newState] = {
        materialName: activeMaterialData.elementName || activeMaterialData.materialDto.materialName,
        animationInfoDto: activeMaterialData.materialDto.animationInfoDto || {},
        cyclePlay: !!activeMaterialData.cyclePlay,
        autoPlay: !!activeMaterialData.autoPlay,
        materialType: activeMaterialData.materialDto.materialType,
        id: activeMaterialData.id,
        isStatic: !!activeMaterialData.isStatic,
      };
      materialAttributeInfo[newState]['initMaterialName'] =
        materialAttributeInfo[newState].materialName;
    }
    if (newState) {
      const data: any = searchMaterial();
      data && resetData(data);
      showType.value = 1;
    } else {
      if (activeInteraction.value) {
        showType.value = 2;
        store.state.editSceneData = JSON.parse(JSON.stringify({ ...store.state.editSceneData }));
      } else {
        showType.value = 0;
      }
    }
  }
);

watch(
  () => store.state.activeAreaUuid,
  (newState) => {
    activeInteraction.value = newState;
    if (newState && !interactionBackgroundMusic[newState]) {
      const interactionObject = store.state.editSceneData.interactionDtoList.filter(
        (e: any) => e.uuid == newState
      )[0];
      interactionBackgroundMusic[newState] = {
        isMusicCycle: !!interactionObject.isMusicCycle,
        backgroundMusic: interactionObject.backgroundMusic,
        materialMetaDtoList: [],
        interactionName: interactionObject.interactionName,
        id: interactionObject.id,
      };
      interactionBackgroundMusic[newState]['initInteractionName'] =
        interactionBackgroundMusic[newState].interactionName;
    }
    if (newState) {
      if (activeMaterial.value) {
        showType.value = 1;
        store.state.editSceneData = JSON.parse(JSON.stringify({ ...store.state.editSceneData }));
      } else {
        const data: any = store.state.editSceneData.interactionDtoList.filter(
          (e: any) => e.uuid == newState
        )[0];
        data && resetData(data);
        showType.value = 2;
      }
    } else {
      if (activeMaterial.value) {
        showType.value = 1;
        store.state.editSceneData = JSON.parse(JSON.stringify({ ...store.state.editSceneData }));
      } else {
        showType.value = 0;
      }
    }
  }
);

watch(showType, (newState) => {
  if (newState == 2) {
    selectInteractionMusic(store.state.editSceneData);
  }
});

watch(graphInfo, (newState) => {
  if (newState.length) {
    sceneInfoList.value = sceneInfoList.value.map((e: any) => {
      if (e.key == 'pathNavigation') {
        e.type = 2;
      }
      return e;
    });
  }
});

watch(isLoop, (newState) => {
  props.changeSceneValue(newState ? 1 : 0, 'isMusicCycle');
});

watch(
  () => isHideGraph.value,
  (newState) => {
    const graphLine: any = (window as any).scene.getObjectByName('graph-group');
    const graphLine2: any = (window as any).scene2.getObjectByName('graph-group');
    if (graphLine) {
      graphLine.visible = !newState;
    }
    if (graphLine2) {
      graphLine2.visible = !newState;
    }
  }
);
</script>
<style scoped lang="less">
#app .interactive-data .info-list {
  ::v-deep(.el-input__wrapper) {
    padding: 0 10px;
    width: 66px;
    height: 34px;
    line-height: 34px;
  }
}

#app .scene-info .scene-info-list {
  ::v-deep(.el-input__wrapper) {
    height: 34px;
  }

  ::v-deep(.el-input__wrapper .el-input__inner) {
    font-size: 14px;
  }
}

.box-item {
  position: relative;
  width: 16px;
  height: 16px;
  background-image: url(~@/assets/images/icon/help.png);
  background-size: 100% 100%;
  position: absolute;
  left: 170px;
  top: 9px;

  &:hover {
    background-image: url(~@/assets/images/icon/helpA.png);
    background-size: 100% 100%;
  }

  & > span {
    position: relative;
    position: absolute;
    left: -95px;
    top: -56px;
    width: 195px;
    height: 44px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px 4px 4px 4px;
    display: inline-block;
    font-size: 12px;
    color: #ffffff;
    line-height: 1.5;
    padding: 5px 5px 0;
    display: none;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      bottom: -6px;
      border: 3px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.7);
      margin-left: -3px;
    }
  }

  &:hover span {
    display: inline-block;
  }

  &.path-box-item {
    left: 215px;
    top: 5px;
    width: 24px;
    height: 24px;
    background-image: url(~@/assets/images/icon/view-icon.png);
    background-size: 100% 100%;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background-color: #eceef0;
    }

    & > span {
      left: -167px;
      top: -58px;

      &::before {
        left: 87%;
      }
    }

    &.hide-graph {
      background-image: url(~@/assets/images/icon/off-icon.png);
      background-size: 100% 100%;
    }
  }
}

.el-select {
  min-width: 130px;
}

.interactive-data {
  padding: 20px 16px 0;
  box-sizing: border-box;
  border-bottom: 1px solid #c8c8c8;

  .flex-double {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .img {
      width: 30px;
      height: 30px;
      transform: translateY(-5px);
      cursor: pointer;
    }
  }
}

.info-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  color: #6f6f6f;
  margin-bottom: 20px;

  .el-input {
    width: 60px;
    height: 34px;
    vertical-align: middle;
    margin-right: 5px;
    display: inline-block;
  }

  span {
    vertical-align: middle;
  }
}

.axle {
  font-size: 15px;
  font-weight: 400;
  color: #0375ff;
  margin-right: 3px;
  margin-left: 3px;
}

.is-location {
  position: relative;

  &::after {
    content: 'm';
    position: absolute;
    right: 2px;
    top: 0;
    color: rgba(113, 113, 120, 0.5);
  }
}

.is-rotate {
  position: relative;

  &::after {
    content: '°';
    position: absolute;
    right: 2px;
    top: 0;
    color: rgba(113, 113, 120, 0.5);
  }
}

.info-title {
  font-size: 12px;
  color: #000000;
  text-align: left;
  margin-bottom: 15px;
  height: 24px;
  line-height: 24px;
  position: relative;

  & > span {
    vertical-align: middle;
  }

  & > img {
    vertical-align: middle;
    margin-right: 6px;
    width: 24px;
    height: 24px;
  }
}

.canvas-right {
  position: fixed;
  right: 0;
  top: 63px;
  width: 317px;
  height: calc(100% - 59px);
  background: #dfe0e3;
  // backdrop-filter: blur(20px);
  z-index: 8;
  overflow-y: auto;

  ::v-deep(.el-input__inner) {
    color: #0f0f0f !important;
  }

  ::v-deep(.el-textarea__inner) {
    color: #0f0f0f !important;
  }

  .canvas-right-title {
    height: 54px;
    line-height: 54px;
    text-align: left;
    padding-left: 16px;
    font-weight: 500;
    font-size: 17px;
    color: #414141;
  }

  .interaction-tips {
    text-align: left;
    width: 100%;
    font-weight: 400;
    font-size: 14px;
    color: #6f6f6f;
    line-height: 20px;
    padding: 16px;
    box-sizing: border-box;
  }
}

.canvas-right.hide-right-side {
  right: -320px;
}

.hide-side,
.show-side {
  position: absolute;
  top: 50%;
  margin-top: -65px;
  right: 317px;
  width: 16px;
  height: 42px;
  transform: rotate(180deg);
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.show-side {
  right: 0;
}

.scene-info {
  width: 317px;
  // height: calc(100% - 226px);
  padding: 20px 16px 0;
  box-sizing: border-box;
  border-bottom: 1px solid #c8c8c8;
  border-top: 1px solid #c8c8c8;
  overflow: hidden;
  overflow-y: auto;

  .scene-info-list {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    font-size: 14px;
    font-weight: 400;
    color: #0f0f0f;
    line-height: 14px;
    margin-bottom: 16px;

    & > div:first-child {
      font-size: 14px;
      color: #6f6f6f;
      width: 60px;
      margin-top: 9px;
    }

    & > div:last-child {
      width: 208px;
      min-height: 32px;
      border-radius: 8px;
      line-height: 32px;
      box-sizing: border-box;
      text-align: left;

      & > span {
        padding-left: 10px;
      }

      .play-switch {
        padding: 0;

        & > span {
          margin-right: 16px;
        }
      }
    }

    .scene-info-dec {
      height: 79px;
      position: relative;

      &::after {
        content: '30\5b57\4ee5\5185';
        position: absolute;
        right: 8px;
        bottom: 5px;
        font-size: 9px;
        transform: scale(0.75);
        transform-origin: 100% 100%;
        font-weight: 400;
        color: #6f6f6f;
      }
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .loop-btn {
        font-weight: 400;
        font-size: 14px;
        color: #414141;
        margin-left: 10px;

        & > img {
          width: 16px;
          height: 16px;
          vertical-align: middle;
          cursor: pointer;
        }

        & > span {
          vertical-align: middle;
          display: inline-block;
          height: 20px;
          line-height: 20px;
          margin-right: 5px;
        }

        &.active {
          color: #2e76ff;
        }
      }
    }
  }

  .scene-info-line {
    border-bottom: 1px solid #c8c8c8;
    width: calc(100% + 32px);
    margin: 17px 0;
    margin-left: -16px;
  }

  .space-model {
    width: 212px;
    height: 185px;
    background-size: 100% 100%;
    margin: 30px 0 20px 32px;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

/* 媒体查询，适配ipad端和小电脑页面 */
@media screen and (max-width: 1200px) {
  .interactive-data {
    padding-right: 5px;

    .info-list .el-input {
      width: 50px;
    }
  }

  .axle {
    font-size: 14px;
  }

  .canvas-right {
    width: 267px;
  }

  .canvas-right.hide-right-side {
    right: -270px;
  }

  .hide-side {
    right: 267px;
  }

  .scene-info {
    padding-right: 10px;
    width: 265px;

    .scene-info-list > div:first-child {
      font-size: 13px;
    }

    .scene-info-list > div:last-child {
      width: 168px;
    }
  }
}
</style>
