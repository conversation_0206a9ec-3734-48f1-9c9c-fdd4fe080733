<template>
    <div>我是模板管理</div>
    <draggable v-model="arr1" handle=".mover" animation="300" @start="onStart" @end="onEnd">
        <div class="item" v-for="item in arr1" :key="item.id"><span class="mover">+</span>{{ item.name }}</div>
    </draggable>
</template>

<script lang="ts" setup>
import { getTemplateSceneCollection } from "@/api";
import { onMounted, ref } from "vue";
import draggable from 'vuedraggable'
const scenePlatform = ref(3)
const disabled = ref(true)
const drag = ref(false)
const arr1 = ref([
    { id: 1, name: 'www.itxst.com' },
    { id: 2, name: 'www.jd.com' },
    { id: 3, name: 'www.baidu.com' },
    { id: 4, name: 'www.taobao.com' }
])

// const requestGetTemplateSceneCollection = () => {
//     const params = {
//         scenePlatform
//     }
//     getTemplateSceneCollection(params).then(res => {
//         if (res.code == 200) {

//         }
//     })
// }


const onStart = () => {
    drag.value = true;
}
const onEnd = () => {
    drag.value = false;
}


onMounted(() => {
    requestGetTemplateSceneCollection()
})

</script>


<style lang="less">
.itxst {
    margin: 10px;
}

.col {
    width: 80%;
    flex: 1;
    padding: 10px;
    border: solid 1px #eee;
    border-radius: 5px;
    float: left;
}

.col+.col {
    margin-left: 10px;
}

.item {
    padding: 6px 12px;
    margin: 0px 10px 0px 10px;
    border: solid 1px #eee;
    background-color: #f1f1f1;
    text-align: left;
}

.item+.item {
    border-top: none;
    margin-top: 6px;
}

.mover {
    background-color: #fdfdfd;
    cursor: move;
    padding: 3px 6px;
}
</style>