<template>
  <div class="new-mask">
    <div>
      <div class="new-mask-title">
        <div>添加设备</div>
        <el-icon class="close-icon" @click="closeEvent"><Close /></el-icon>
      </div>
      <div class="new-mask-content">
        <div v-if="!encryptionError" class="qrcode-box">
          <qrcode-vue :value="qrContent" :size="148" />
        </div>
        <div v-if="!encryptionError">
          <div class="code-text">扫描左图二维码，</div>
          <div class="code-text">成功识别即可登录并配对</div>
          <div class="code-text-tips">*修改账号密码时，此二维码会自动刷新</div>
        </div>
        <div v-else class="error-message">
          <el-alert
            title="二维码生成失败"
            type="error"
            description="请关闭页面重试，如果问题持续存在请联系管理员"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CryptoJS from "crypto-js";
import qrcodeVue from 'qrcode.vue'
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { Close } from '@element-plus/icons-vue'

const userName = window.localStorage.getItem('userName') || ''
const password = window.localStorage.getItem('codePassword') || ''
const data = JSON.stringify({ userName, password })
const isUnmounted = ref(false)
const qrContent = ref<any>('')
const encryptionError = ref(false)

const props = defineProps({
  hideAddMask: {
    default: null,
    type: Function
  }
})

function encryptString(message: string, key: string) {
  try {
    // 确保 key 长度为 16 字节
    const keyUtf8 = CryptoJS.enc.Utf8.parse(key.substring(0, 16));
    const iv = CryptoJS.enc.Utf8.parse("1234567890123456"); // 固定 16 字节 IV

    // 使用 AES 加密
    const encrypted = CryptoJS.AES.encrypt(message, keyUtf8, { iv: iv, padding: CryptoJS.pad.Pkcs7 });
    // 将 IV 和密文一起返回（Base64 编码）
    return iv.toString(CryptoJS.enc.Base64) + ":" + encrypted.toString();
  } catch (error) {
    console.error('Encryption error:', error);
    encryptionError.value = true;
    return 'error'
  }
}

onMounted(async () => {
  try {
    if (isUnmounted.value) return
    const key = "h7K3s9jW5n2D1qXo";  // 必须 16 字节密钥
    const encryptedMessage = encryptString(data, key);
    if (!isUnmounted.value && encryptedMessage !== 'error') {
      qrContent.value = encryptedMessage
    }
  } catch (error) {
    console.error('QR code generation error:', error);
    encryptionError.value = true;
  }
})

onUnmounted(() => {
  isUnmounted.value = true
  qrContent.value = ''
})

const closeEvent = () => {
  if (props.hideAddMask) {
    props.hideAddMask()
  }
}
</script>

<style scoped lang="less">
.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  &>div {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    position: relative;
    width: 453px;
    height: 263px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 16px 24px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 600;
    font-size: 18px;
    color: #797979;

    .close-icon {
      font-size: 26px;
      cursor: pointer;
      font-weight: 400;
      position: absolute;
      right: 28px;
      top: 22px;
      color: #797979;

      &:hover {
        color: #2E76FF;
      }
    }

    .new-mask-title {
      width: 100%;
      height: 30px;
      font-weight: bold;
      font-size: 20px;
      color: #1E1E1E;
      margin-bottom: 16px;
      line-height: 30px;
    }

    .new-mask-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #797979;
      font-weight: 400;

      .qrcode-box {
        padding: 9px;
        font-size: 0;
        border: 1px solid #DADADA;
        margin-right: 16px;
      }

      .code-text {
        font-size: 14px;
        line-height: 21px;
      }

      .code-text-tips {
        font-size: 12px;
        margin-top: 20px;
      }
    }
  }
}

.error-message {
  width: 100%;
  padding: 20px;
  text-align: center;
}
</style>