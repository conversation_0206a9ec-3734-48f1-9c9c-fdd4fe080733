<template>
    <div class="new-mask">
        <div class="main_box" :class="{ plane_ar: (sceneType != 1 && activeTempSceneId) || !activeTempSceneId }">
            <div class="left_top">
                <div class="name">项目预览</div>
                <div class="closed" @click="closeEvent"></div>
            </div>
            <main>
                <div v-loading="isLoading" class="left"
                    :class="{ new_left: (sceneType != 1 && sceneDataPlatform == 3) || !activeTempSceneId, room_left: (sceneType == 1 && activeTempSceneId) }">
                    <div class="header">{{ name }}</div>
                    <img class="p" :src="codeUrl" alt="" @load="load" v-if="sceneDataPlatform == 3" id="capTexture">
                    <qrcode-vue :value="qrContent" :size="210" foreground="#000000" class="qrcode" v-else
                        id="capTexture" />
                    <div class="desc" v-if="sceneDataPlatform == 3 && sceneType == 1 && activeTempSceneId">使用微信扫描二维码
                        <br>启动混空小程序
                    </div>
                </div>
                <div class="right" v-if="sceneType == 1 && activeTempSceneId">
                    <img :src="hoverCurrentTemplate.sceneLocationPicKey || 'http://njyjxr.oss-cn-shanghai.aliyuncs.com/zhanghai/room_map.png'"
                        alt="">
                    <header>项目演示地址</header>
                    <div class="address">{{ hoverCurrentTemplate.sceneLocation || '上海市徐汇区钦州北路1001号光启园7号楼6层展厅' }}</div>
                </div>
            </main>
            <el-button color="#2E76FF" style="width: 134px; height: 32px;" class="footer_btn"
                :class="{ samll_left_btn: sceneType != 1 || !activeTempSceneId }" @click="downloadImg">下载二维码</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import html2canvas from 'html2canvas' // dom转canvas
import qrcodeVue from 'qrcode.vue' // https://gitcode.com/gh_mirrors/qr/qrcode.vue/overview?utm_source=artical_gitcode&index=top&type=card&webUrl&isLogin=1
import { onMounted, ref } from 'vue';
const isLoading = ref(true)
const props = defineProps({
    hoverCurrentTemplate: {
        require: true,
        type: Object
    },
    hideAddMask: {
        default: null,
        type: Function
    },
    codeUrl: {
        default: '',
        type: String
    },
    name: {
        default: '',
        type: String
    },
    sceneDataPlatform: {
        default: 3,
        type: Number
    },
    sceneType: {
        default: 1,
        type: Number
    },
    activeTempSceneId: {
        default: 0,
        type: Number
    }
})

const qrContent = ref<any>('')

function encryptString(message, key) {
    // 确保 key 长度为 16 字节
    const keyUtf8 = CryptoJS.enc.Utf8.parse(key.substring(0, 16));
    const iv = CryptoJS.enc.Utf8.parse("1234567890123456"); // 固定 16 字节 IV
    // 使用 AES 加密
    const encrypted = CryptoJS.AES.encrypt(message, keyUtf8, { iv: iv, padding: CryptoJS.pad.Pkcs7 });
    // 将 IV 和密文一起返回（Base64 编码）
    return iv.toString(CryptoJS.enc.Base64) + ":" + encrypted.toString();
}


const downloadImg = () => {
    const element = document.getElementById('capTexture') // 获取到要转为图片的dom
    html2canvas(element).then(canvas => {
        const dataURL = canvas.toDataURL('image/png');
        const a = document.createElement('a');
        a.href = dataURL;
        a.download = props.hoverCurrentTemplate.sceneName + '.png';
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    })
}

const load = () => {
    isLoading.value = false
}


const closeEvent = () => {
    props.hideAddMask()
}


onMounted(() => {
    // 示例
    const key = "h7K3s9jW5n2D1qXo";  // 必须 16 字节密钥
    const encryptedMessage = encryptString(props.activeTempSceneId + '', key);
    qrContent.value = encryptedMessage
    isLoading.value = false
    console.log(props.activeTempSceneId);

})

</script>
<style scoped lang="less">
.new-mask {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .samll_left_btn {
        left: 130px !important;
    }

    .new_left {
        border: 1px solid #DADADA;
        width: 385px;
        height: 302px;
        box-sizing: border-box;
    }

    .room_left {
        width: 255px;
        height: 351px;
        box-sizing: border-box;
        border: 1px solid #DADADA;
    }

    .main_box {
        position: relative;
        width: 850px !important;
        height: 480px;
        background: #fff;
        border-radius: 8px;

        .footer_btn {
            position: absolute;
            bottom: 16px;
            left: 50% !important;
            transform: translateX(-50%);
        }
    }

    main {
        background: #FFFFFF;
        display: flex;
        justify-content: center;
        position: relative;
        align-items: center;

        .right {
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #DADADA;
            margin-left: 16px;
            padding: 24px;
            min-height: 355px;
            box-sizing: border-box;

            img {
                width: 482px;
                height: 247px;
                margin-bottom: 14px;
            }

            header {
                margin-bottom: 3px;
                font-weight: 400;
                font-size: 14px;
                color: #1E1E1E;
                color: #1E1E1E;
            }

            .address {
                font-weight: bold;
                font-size: 14px;
                color: #1E1E1E;
            }
        }

        &>div {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
            font-weight: 600;
            font-size: 18px;
            color: #3D566C;
            padding: 0 24px;

            .header {
                margin-bottom: 14px;
                margin-top: 4px;
                font-weight: bold;
                font-size: 20px;
                color: #1E1E1E;
            }

            .p {
                width: 207px;
                height: 212px;
                margin-bottom: 14px;
            }



            .desc {
                font-weight: bold;
                font-size: 14px;
                color: #3D566C;
                margin-bottom: 24px;
            }
        }
    }

    .plane_ar {
        width: 433px !important;
        height: 430px !important;
        background: #FFFFFF;
        border-radius: 8px 8px 8px 8px;
    }

    .left_top {
        display: flex;
        align-items: center;
        display: flex;
        justify-content: space-between;
        padding: 17px 24px;

        img {
            width: 24px;
            height: 24px;
            cursor: pointer;
        }

        .name {
            font-weight: bold;
            transform: translateY(-1px);
            font-size: 18px;
            color: #1E1E1E;
        }
    }

}

.closed {
    position: absolute;
    right: 22px;
    top: 16px;
    width: 28px;
    height: 28px;
    background: url('http://njyj.oss-cn-shanghai.aliyuncs.com/close.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;

    &:hover {
        background: url('http://njyj.oss-cn-shanghai.aliyuncs.com/blue_icon.png') no-repeat;
        background-size: 100% 100%;
    }
}
</style>