import request from "../request";

// 添加场景组
export function addSceneGroup(data: any) {
  return request({
    url: 'scene/addSceneGroup',
    method: "post",
    data
  });
}

// 查询场景组列表
export function querySceneGroups() {
  return request({
    url: 'scene/querySceneGroups',
    method: "get",
  });
}

// 获取场景组中的场景
export function getSceneInGroup(data: any) {
  return request({
    url: 'scene/getSceneInGroup?sceneGroupId=' + data.sceneGroupId,
    method: "get",
  });
}

// 添加场景组关系
export function addSceneGroupRelation(data: any) {
  return request({
    url: `scene/addSceneGroupRelation?scenId=${data.scenId}&sceneGroupId=${data.sceneGroupId}`,
    method: "post",
    data,
  });
}

// 移除场景组
export function removeSceneGroup(data: any) {
  return request({
    url: `scene/removeSceneGroup?sceneId=${data.sceneId}`,
    method: "post",
    data,
  });
}

// 更新场景组名称
export function updateSceneGroupName(data: any) {
  return request({
    url: `scene/updateSceneGroupName?id=${data.id}&groupName=${data.groupName}`,
    method: "post",
    data,
  });
}

// 删除场景组
export function deleteSceneGroup(data: any) {
  return request({
    url: `scene/deleteSceneGroup?id=${data.id}`,
    method: "post",
    data,
  });
} 