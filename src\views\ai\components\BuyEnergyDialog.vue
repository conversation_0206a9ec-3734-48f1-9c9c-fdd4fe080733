<template>
  <el-dialog
    v-model="dialogVisible"
    width="985px"
    :close-on-click-modal="false"
    :show-close="false"
    class="buy-energy-dialog">
    <el-icon class="close-icon" @click="handleClose"><Close /></el-icon>

    <div class="dialog-header">
      <div class="user-info">
        <img :src="profilePic" class="avatar" />
        <span class="email">{{ userEmail }}</span>
      </div>
      <div class="energy-info">
        <img src="@/assets/images/star11.png" class="energy-icon" />
        <span>当前AI能量值: {{ formatNumber(aiEnergy) }}</span>
      </div>
    </div>

    <div class="dialog-content">
      <div class="main-section">
        <div class="energy-selection">
          <div class="section-header">
            <div class="title">选择购买能量值</div>
            <div class="rate-info">能量值换算比例: 10,000能量值=1元</div>
          </div>

          <div class="energy-options">
            <div
              v-for="option in energyOptions"
              :key="option.energy"
              class="energy-option"
              :class="{ active: selectedEnergy === option.energy }"
              @click="selectEnergy(option)">
              <img src="@/assets/images/star11.png" style="width: 24px; height: 24px" />
              {{ formatNumber(option.energy) }}
              <div class="energy-price">¥{{ option.price.toFixed(2) }}</div>
            </div>
          </div>

          <div class="custom-amount">
            <div class="input-group">
              <div class="label">
                自定义充值金额:
                <span class="price" style="margin: 0 10px">¥</span>
              </div>
              <el-input
                v-model="customAmount"
                placeholder="请输入充值金额"
                class="custom-input"
                @input="handleCustomInput"
                :min="0"
                type="number"></el-input>
              <div>(最小输入50)</div>
            </div>
            <div class="custom-energy">
              <img src="@/assets/images/star11.png" class="energy-icon" />
              {{ formatNumber(customEnergy) }}
            </div>
          </div>
        </div>

        <div class="payment-section">
          <div class="title">支付方式</div>
          <div class="payment-options">
            <div
              class="payment-option"
              :class="{ active: paymentMethod === 'wechat' }"
              @click="paymentMethod = 'wechat'">
              <img src="@/assets/images/WeChat12.png" class="payment-icon" />
              <span>微信支付</span>
            </div>
            <div
              class="payment-option"
              :class="{ active: paymentMethod === 'alipay' }"
              @click="paymentMethod = 'alipay'">
              <img src="@/assets/images/Alipay12.png" class="payment-icon" />
              <span>支付宝支付</span>
            </div>
          </div>
        </div>
      </div>

      <div class="order-section">
        <div class="order-info">
          <div class="title">订单信息</div>
          <div class="info-item">
            <span class="label">AI能量值:</span>
            <div class="value">
              {{ formatNumber(selectedEnergy || customEnergy) }}
            </div>
          </div>
          <div class="info-item">
            <span class="label">订单金额:</span>
            <span class="value price">
              ¥{{ ((selectedEnergy || customEnergy) / 10000).toFixed(2) }}
            </span>
          </div>
          <div
            class="confirm-btn"
            :class="{ disabled: !canPay }"
            @click="handlePay"
            @click.stop="console.log('按钮被点击了')">
            确认支付
          </div>
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- 支付弹框 -->
  <el-dialog
    v-model="showPaymentDialog"
    width="568px"
    top="200px"
    :show-close="true"
    :close-on-click-modal="false"
    class="payment-qr-dialog"
    :title="'付款'"
    center>
    <div class="payment-amount">
      <div class="amount-label">扫一扫付款（元）</div>
      <div class="amount">
        {{ ((selectedEnergy || customEnergy) / 10000).toFixed(2) }}
      </div>
    </div>
    <div class="qr-code">
      <div v-if="isLoadingQRCode" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在生成二维码...</div>
      </div>
      <img v-else-if="paymentQRCode" :src="paymentQRCode" class="qr-image" />
      <div v-else class="error-placeholder">
        <div class="error-text">二维码加载失败</div>
        <el-button type="primary" size="small" @click="handlePay" style="margin-top: 8px">
          重新获取
        </el-button>
      </div>
    </div>
    <div class="merchant-info">
      <span>卖家：上海凝聚元界信息科技有限公司</span>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useStore } from 'vuex';
import { Close } from '@element-plus/icons-vue';
import {
  getOrgnizationPackage,
  getOssAccessPath,
  getPaymentQrCode,
  getOrderStatusByOrderNo,
} from '@/api';
import { ElMessage } from 'element-plus';

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  userEmail: {
    type: String,
    default: '',
  },
  currentEnergy: {
    type: Number,
    required: true,
  },
});

const emit = defineEmits(['update:show', 'pay']);
const store = useStore();
const profilePic = ref('');

// 直接从 store 获取AI能量值
const aiEnergy = computed(() => {
  const userDto = store.state.userDto;
  if (userDto?.packageVersion === 'V2') {
    return store.state.userBindPackageDto?.aiEnergy || 0;
  }
  return store.state.packageInfoDto?.aiEnergy || 0;
});

const dialogVisible = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val),
});

// 监听store中的头像变化
watch(
  () => store.state.profilePic,
  () => {
    profilePic.value = store.state.profilePic;
  }
);

onMounted(async () => {
  // 获取用户信息（主要为了获取头像）
  try {
    const res = await getOrgnizationPackage({});
    if (res.data.userDto?.profilePicture) {
      const picRes = await getOssAccessPath({ key: res.data.userDto.profilePicture });
      profilePic.value = picRes.data;
    } else {
      profilePic.value = require('@/assets/images/ailongmask.png');
    }
  } catch (error) {
    console.error('获取用户头像失败:', error);
    profilePic.value = require('@/assets/images/ailongmask.png');
  }
});

// 先声明停止轮询函数
const stopPaymentPolling = () => {
  // 清除轮询定时器
  if (pollingTimer.value) {
    clearTimeout(pollingTimer.value);
    pollingTimer.value = null;
  }

  // 重置状态
  currentOrderNo.value = '';
  pollingCount.value = 0;
};

// 组件卸载时清理轮询
onUnmounted(() => {
  stopPaymentPolling();
});

const formatNumber = (num: number) => {
  return new Intl.NumberFormat().format(num);
};

// 能量选项
const energyOptions = [
  { energy: 500000, price: 50 },
  { energy: 1000000, price: 100 },
  { energy: 5000000, price: 500 },
];

const selectedEnergy = ref<number>(500000);
const customAmount = ref('');
const customEnergy = ref(0);
const paymentMethod = ref('wechat');

const selectEnergy = (option: { energy: number; price: number }) => {
  selectedEnergy.value = option.energy;
  customAmount.value = '';
  customEnergy.value = 0;
};

const handleCustomInput = (value: string) => {
  const amount = parseFloat(value);
  if (!isNaN(amount)) {
    if (amount >= 0) {
      // if (amount >= 50) {
      customEnergy.value = Math.floor(amount * 10000);
      selectedEnergy.value = 0;
    } else {
      customEnergy.value = 0;
      // ElMessage.warning('最小充值金额为50元');
    }
  } else {
    customEnergy.value = 0;
  }
};

const canPay = computed(() => {
  return (selectedEnergy.value > 0 || customEnergy.value > 0) && paymentMethod.value;
});

const showPaymentDialog = ref(false);
const paymentQRCode = ref('');
const isLoadingQRCode = ref(false);

// HTTP轮询相关状态
const pollingTimer = ref<ReturnType<typeof setTimeout> | null>(null);
const currentOrderNo = ref<string>('');
const pollingCount = ref(0);
const maxPollingCount = 30; // 最大轮询30次（约1.5分钟）
const pollingInterval = 3000; // 轮询间隔3秒

const handlePay = async () => {
  if (!canPay.value) {
    return;
  }

  // 计算支付金额 - 转换为分（整数）
  const amountInYuan = parseFloat(
    ((selectedEnergy.value || customEnergy.value) / 10000).toFixed(2)
  );
  const amount = Math.round(amountInYuan * 100); // 转换为分

  // 根据选择的支付方式确定payType
  const payType = paymentMethod.value === 'wechat' ? 'WXPay' : 'AliPay';

  try {
    // 设置加载状态
    isLoadingQRCode.value = true;

    // 显示支付弹窗（先显示加载状态）
    showPaymentDialog.value = true;
    paymentQRCode.value = ''; // 清空之前的二维码

    // 调用支付二维码API
    const response = await getPaymentQrCode({
      payType,
      amount,
    });

    const responseData = response as any;
    if (responseData.code === '2000' || responseData.code === '200') {
      const qrCodeData = responseData.data?.qrCode;
      const orderNo = responseData.data?.orderNo;

      if (qrCodeData && orderNo) {
        paymentQRCode.value = qrCodeData;
        ElMessage.success('二维码生成成功，请扫码支付');

        // 开始HTTP轮询监听支付状态
        startPaymentPolling(orderNo);
      } else {
        ElMessage.error('支付数据格式错误');
        showPaymentDialog.value = false;
      }
    } else {
      showPaymentDialog.value = false;
    }
  } catch (error) {
    ElMessage.error('获取支付二维码失败，请稍后重试');
    showPaymentDialog.value = false;
  } finally {
    // 清除加载状态
    isLoadingQRCode.value = false;
  }
};

// 查询支付状态的API函数
const checkPaymentStatus = async (orderNo: string) => {
  try {
    const response = await getOrderStatusByOrderNo({ orderNo });
    console.log('支付状态查询响应:', response);
    return response;
  } catch (error: any) {
    console.log('支付状态查询错误:', error);
    // 网络错误等情况返回null，让轮询继续
    return null;
  }
};

// 开始HTTP轮询监听支付状态
const startPaymentPolling = (orderNo: string) => {
  // 停止之前的轮询
  stopPaymentPolling();

  // 保存订单号并重置计数
  currentOrderNo.value = orderNo;
  pollingCount.value = 0;

  // 立即执行第一次查询
  pollPaymentStatus();
};

// 轮询支付状态
const pollPaymentStatus = async () => {
  try {
    // 检查是否超过最大轮询次数
    if (pollingCount.value >= maxPollingCount) {
      console.log('轮询超时，停止查询');
      // 静默停止轮询，不显示提示信息
      stopPaymentPolling();
      return;
    }

    // 检查支付弹窗是否还在显示
    if (!showPaymentDialog.value) {
      stopPaymentPolling();
      return;
    }

    pollingCount.value++;
    console.log(`第${pollingCount.value}次查询支付状态，订单号: ${currentOrderNo.value}`);

    const result = await checkPaymentStatus(currentOrderNo.value);

    // 如果没有返回结果，继续轮询
    if (!result) {
      pollingTimer.value = setTimeout(pollPaymentStatus, pollingInterval);
      return;
    }

    // 根据返回结果判断支付状态
    const resultData = result as any;
    if (resultData.code === '200' || resultData.code === '2000') {
      const orderState = resultData.data?.orderState;
      const status = resultData.data?.status;

      // 根据新的API响应格式判断支付状态
      if (
        orderState === 'SUCCESS' ||
        orderState === 'PAID' ||
        status === 'success' ||
        status === 'paid' ||
        status === 'completed'
      ) {
        // 支付成功
        console.log('支付成功，停止轮询');
        handlePaymentSuccess();
        return;
      } else if (
        orderState === 'FAILED' ||
        orderState === 'CANCELLED' ||
        status === 'failed' ||
        status === 'error' ||
        status === 'cancelled'
      ) {
        // 支付失败
        console.log('支付失败，停止轮询');
        handlePaymentFailed();
        return;
      } else if (orderState === 'NOTPAY' || status === 'pending') {
        // 订单未支付，继续轮询
        console.log('订单未支付，继续轮询...');
      } else {
        // 其他未知状态，继续轮询
        console.log('未知支付状态:', orderState || status, '继续轮询...');
      }
    }

    // 设置下次轮询
    pollingTimer.value = setTimeout(pollPaymentStatus, pollingInterval);
  } catch (error) {
    console.error('查询支付状态出错:', error);

    // 网络错误时继续轮询，但不显示错误提示，只在达到最大次数时提示
    if (pollingCount.value < maxPollingCount) {
      pollingTimer.value = setTimeout(pollPaymentStatus, pollingInterval);
    } else {
      // 只在轮询超时时显示一次提示
      console.log('支付状态查询超时，停止轮询');
      stopPaymentPolling();
    }
  }
};

// 监听支付弹窗关闭，确保停止轮询
watch(
  () => showPaymentDialog.value,
  (newValue) => {
    if (!newValue) {
      // 支付弹窗关闭时停止轮询
      stopPaymentPolling();
    }
  }
);

// 处理支付成功
const handlePaymentSuccess = async () => {
  // 停止轮询
  stopPaymentPolling();

  // 重新调用用户接口获取最新能量值
  try {
    const res = await getOrgnizationPackage({});
    if (res.data.userBindPackageDto) {
      store.dispatch('updateUserBindPackageDto', res.data.userBindPackageDto);
    }
    if (res.data.userDto) {
      store.dispatch('updateUserDto', res.data.userDto);
    }
  } catch (error) {
    console.error('更新用户信息失败:', error);
  }

  // 关闭支付弹窗
  resetPaymentState();

  // 显示成功提示
  ElMessage.success('支付成功！能量已充值');

  // 关闭整个购买弹窗
  emit('update:show', false);
};

// 处理支付失败
const handlePaymentFailed = () => {
  // 停止轮询
  stopPaymentPolling();

  // 显示失败提示
  ElMessage.error('支付失败，请重试');

  // 保持支付弹窗打开，让用户可以重新扫码
};

// 重置支付状态
const resetPaymentState = () => {
  // 停止轮询
  stopPaymentPolling();

  showPaymentDialog.value = false;
  paymentQRCode.value = '';
  isLoadingQRCode.value = false;
};

const handleClose = () => {
  emit('update:show', false);
  selectedEnergy.value = 500000;
  customAmount.value = '';
  customEnergy.value = 0;
  paymentMethod.value = 'wechat';
  // 重置支付状态
  resetPaymentState();
};
</script>

<style scoped lang="less">
.buy-energy-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;
    position: relative;

    .el-dialog__header {
      display: none;
    }

    .el-dialog__body {
      padding: 0;
    }
  }

  .close-icon {
    position: absolute;
    top: 0px;
    right: -40px;
    font-size: 20px;
    color: #909399;
    cursor: pointer;
    z-index: 10;
    transition: color 0.3s;

    &:hover {
      color: #606266;
    }
  }
}

.dialog-header {
  width: 100%;
  position: relative;
  padding: 0 15px;
  top: -10px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      object-fit: cover;
    }

    .email {
      font-weight: bold;
      font-size: 20px;
      color: #1e1e1e;
    }
  }

  .energy-info {
    display: flex;
    align-items: center;
    height: 32px;
    background: linear-gradient(
      44deg,
      rgba(246, 41, 123, 0.1) -0.9%,
      rgba(208, 41, 246, 0.1) 31.63%,
      rgba(123, 19, 251, 0.1) 59.12%,
      rgba(19, 204, 251, 0.1) 99.93%
    );
    border-radius: 4px;
    padding: 0 16px;

    .energy-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    span {
      color: #1e1e1e;
      font-weight: 500;
      font-size: 14px;
      line-height: 18px;
    }
  }
}

.dialog-content {
  display: flex;
  padding: 15px;
  gap: 15px;
}

.main-section {
  flex: 1;

  .energy-selection {
    background: #f5f6f7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 15px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .title {
        font-weight: bold;
        font-size: 18px;
        color: #1e1e1e;
      }

      .rate-info {
        font-size: 12px;
        color: #86909c;
      }
    }

    .energy-options {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      margin-bottom: 24px;

      .energy-option {
        width: 186px;
        height: 110px;
        background: #ffffff;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        cursor: pointer;
        transition: all 0.3s;
        font-weight: bold;
        font-size: 20px;
        color: #4916d3;
        line-height: 18px;

        &:hover,
        &.active {
          background: #f0f7ff;
          border-color: #409eff;
        }

        .energy-icon {
          width: 16px;
          height: 16px;
        }
      }

      .energy-price {
        font-weight: 500;
        font-size: 14px;
        margin-top: 5px;
        color: #000000;
      }
    }

    .custom-amount {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 16px;
      margin-bottom: 8px;

      .input-group {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
      }

      .label {
        white-space: nowrap;
        color: #1f2329;
        font-weight: 500;
        font-size: 14px;
        color: #000000;
      }

      .custom-input {
        width: 180px;
        height: 42px;
      }

      .custom-energy {
        display: flex;
        align-items: center;
        gap: 4px;
        white-space: nowrap;
        font-weight: bold;
        font-size: 20px;
        color: #4916d3;

        .energy-icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .payment-section {
    background: #f5f6f7;
    border-radius: 4px;
    padding: 20px;

    .title {
      font-size: 14px;
      color: #1f2329;
      margin-bottom: 16px;
    }

    .payment-options {
      display: flex;
      gap: 12px;

      .payment-option {
        width: 160px;
        height: 48px;
        background: #ffffff;
        border-radius: 4px 4px 4px 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16px;
        gap: 8px;
        cursor: pointer;
        transition: all 0.3s;
        border: 1px solid #ffffff;

        &:hover,
        &.active {
          border-color: #409eff;
          background: rgba(46, 118, 255, 0.1);
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #2e76ff;
        }

        .payment-icon {
          font-size: 24px;
          width: 24px;
        }

        span {
          font-size: 14px;
          color: #1f2329;
        }
      }
    }
  }
}

.order-section {
  width: 280px;

  .order-info {
    background: #f5f6f7;
    border-radius: 4px 4px 4px 4px;
    padding: 20px;
    margin-bottom: 16px;
    font-weight: 400;
    font-size: 14px;
    color: #1e1e1e;
    .title {
      margin-bottom: 16px;
      font-weight: bold;
      font-size: 18px;
      color: #000000;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 14px;
      }

      .value {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        font-weight: 500;

        .energy-icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .confirm-btn {
    width: 240px;
    height: 34px;
    background: #2e76ff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s;

    &:hover {
      background: #4288ff;
    }

    &:active {
      background: #1e66ff;
    }

    &.disabled {
      background: #a1a5ab;
      cursor: not-allowed;
    }
  }
}

.payment-qr-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
    height: 377px;
    background: #ffffff;

    .el-dialog__header {
      margin: 0;
      padding: 20px;
      border-bottom: 1px solid #e4e7ed;
      text-align: center;

      .el-dialog__title {
        font-size: 16px;
        font-weight: bold;
        color: #000000;
      }

      .el-dialog__close {
        font-size: 20px;
        color: #909399;
      }
    }

    .el-dialog__body {
      padding: 0 20px;
      text-align: center;
      height: calc(100% - 57px);
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }

  .payment-amount {
    text-align: center;
    width: 100%;

    .amount-label {
      font-size: 14px;
      color: #1e1e1e;
      margin-bottom: 12px;
      font-weight: 400;
      font-size: 14px;
      color: #797979;
    }

    .amount {
      font-weight: bold;
      font-size: 32px;
      color: #2e76ff;
      margin-bottom: 24px;
    }
  }

  .qr-code {
    width: 200px;
    height: 200px;
    margin: 0 auto;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 24px;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #dadada;
    display: flex;
    align-items: center;
    justify-content: center;

    .qr-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #2e76ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 12px;
      }

      .loading-text {
        font-size: 14px;
        color: #666;
      }
    }

    .error-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;

      .error-text {
        font-size: 14px;
        color: #999;
      }
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .merchant-info {
    text-align: center;
    width: 100%;
    font-weight: 400;
    font-size: 14px;
    color: #797979;
  }
}
</style>
