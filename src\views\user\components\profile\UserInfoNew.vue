<template>
  <div class="user-info-modal">
    <div class="modal-overlay" @click="handleClose"></div>
    <div class="modal-container">
      <!-- 左侧用户信息 -->
      <div class="user-info-panel">
        <div class="panel-header">
          <h3>用户信息</h3>
        </div>
        <div class="panel-content">
          <!-- 用户头像和基本信息 -->
          <div class="user-profile">
            <div class="avatar-section">
              <upload-template
                class-style="avatar-uploader"
                :baseURL="baseURL"
                :beforeUpload="beforeUpload"
                :handleAvatarSuccess="handleAvatarSuccess">
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <div class="avatar-placeholder" v-else>
                  <i class="el-icon-user"></i>
                </div>
                <div class="avatar-edit-icon"></div>
              </upload-template>
            </div>
            <div class="user-details">
              <div class="company-name">{{ getCompanyName() }}</div>
              <div class="package-validity" v-if="showPackageTime">
                <span>有效期：</span>
                <span>{{ formatDate(packageStartDate) }} ~ {{ formatDate(packageEndDate) }}</span>
              </div>
            </div>
          </div>

          <!-- 企业信息 -->
          <div class="enterprise-info" v-if="showEnterpriseInfo">
            <div class="enterprise-info-content">
              <h4>企业信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">企业名称</span>
                  <span class="value">{{ organizationInfo.orgnizationName }}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系人</span>
                  <span class="value">{{ organizationInfo.contactName }}</span>
                </div>
                <div class="info-item">
                  <span class="label">登录账号</span>
                  <span class="value">{{ accountName }}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系方式</span>
                  <span class="value">{{ organizationInfo.contactPhone }}</span>
                </div>
                <div class="info-item">
                  <span class="label">登录密码</span>
                  <span class="bold-text">
                    <span v-if="!isPasswordVisible">******</span>
                    <span v-if="isPasswordVisible">
                      {{ userPassword ? userPassword : '未设置' }}
                    </span>
                    <span class="password-view" @click="togglePasswordVisibility">
                      <img v-if="isPasswordVisible" src="@/assets/images/icon/view.png" />
                      <img v-if="!isPasswordVisible" src="@/assets/images/icon/view_off.png" />
                    </span>
                    <span class="update-passWord" @click="handleUpdatePassword">更新密码</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 退出登录按钮 -->
          <div class="logout-section">
            <div class="logout">
              <div @click="handleLogout">退出登录</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧产品服务信息 -->
      <div class="service-info-panel">
        <div class="panel-header">
          <h3>产品服务信息</h3>
          <div class="close-btn" @click="handleClose">
            <img src="@/assets/images/home/<USER>" alt="关闭" />
          </div>
        </div>
        <div class="panel-content">
          <!-- 服务信息双表格 -->
          <div class="package-box">
            <!-- 标准服务包表格（基础服务） -->
            <div class="service-table-wrapper">
              <div class="title">基础服务</div>
              <div v-for="item in baseServiceList" :key="item.label" class="table-row">
                <div class="service-name">{{ item.label }}</div>
                <div class="service-value">{{ item.value() }}</div>
              </div>
            </div>
            <!-- 支持项目类型表格 -->
            <div class="service-table-wrapper">
              <div class="title">支持项目类型</div>
              <div v-for="item in supportTypeList" :key="item.label" class="table-row">
                <div class="service-name">{{ item.label }}</div>
                <div class="service-value extended">{{ item.value() }}</div>
              </div>
            </div>
          </div>
          <div class="ai-service-box">
            <div class="ai-service-table">
              <div class="ai-service-title">AI服务</div>
              <div class="table-row" style="height: 36px; line-height: 36px">
                <div class="service-name">
                  <div style="display: flex; align-items: center">
                    <div>当前能量值</div>

                    <div class="energy-value">
                      <img
                        src="@/assets/images/star11.png"
                        style="width: 16px; height: 16px; margin-right: 8px"
                        alt="AI能量值" />
                      <span class="gradient-text">
                        {{
                          store.state.userBindPackageDto?.aiEnergy
                            ? store.state.userBindPackageDto.aiEnergy
                            : '0'
                        }}
                      </span>
                    </div>
                    <span>用于生成图片、语音、视频、模型功能</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import type { UploadProps } from 'element-plus';
import { getDayTime } from '@/utils';
import { getOssAccessPath, getOrgnizationPackage } from '@/api';
import UploadTemplate from '@/views/template/components/UploadTemplate.vue';

// Props
const props = defineProps({
  changeEvent: {
    type: Function,
    default: null,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  userType: {
    type: Number,
    default: 6,
  },
});

// Composables
const router = useRouter();
const store = useStore();

// Reactive data
const imageUrl = ref('');
const isPasswordVisible = ref(false);
const agreementType = ref(0);
const userPassword = ref('');
const packageData = ref<any>(null);

// Upload configuration
const baseURL =
  (process.env.NODE_ENV === 'production' ? '/api' : '/api1') + '/authority/addprofilePicture';

// Computed properties
const showPackageTime = computed(() => ![6, 7].includes(props.userType));
const showEnterpriseInfo = computed(() => ![6, 7].includes(props.userType));

const accountName = computed(() => {
  // 优先使用新接口返回的用户名
  const userName = packageData.value?.userDto?.username;
  if (userName) return userName;

  // 回退到原有逻辑
  return [6, 7].includes(props.userType)
    ? localStorage.getItem('phoneNo')
    : localStorage.getItem('userName');
});

const packageName = computed(() => {
  if (props.userType === 6) return '【个人版】';
  if (props.userType === 7) return '【定制版】';
  return props.data.packageInfoDto?.packageName || props.data.userBindPackageDto?.packageName || '';
});

const organizationInfo = computed(() => {
  return packageData.value?.orgnizationDto || props.data.orgnizationDto || {};
});

const packageStartDate = computed(() => {
  const startTime =
    packageData.value?.userBindPackageDto?.packageStartTime ||
    props.data.packageStartDate ||
    props.data.userBindPackageDto?.packageStartTime;
  return startTime ? getDayTime(startTime, false) : '';
});

const packageEndDate = computed(() => {
  const endTime =
    packageData.value?.userBindPackageDto?.packageEndTime ||
    props.data.packageEndDate ||
    props.data.userBindPackageDto?.packageEndTime;
  return endTime ? getDayTime(endTime, false) : '';
});

// 基础服务字段，与UserInfo2.vue一致
const baseServiceList = computed(() => {
  const userBindPackage = packageData.value?.userBindPackageDto || props.data.userBindPackageDto;
  const extensionDtos =
    packageData.value?.userBindPackageExtensionDtos || props.data.userBindPackageExtensionDtos;

  return [
    { label: '项目数量', value: () => `${userBindPackage?.sceneNum || 0}个` },
    {
      label: '素材总容量',
      value: () =>
        userBindPackage?.materialUploadSize
          ? `${Math.round(userBindPackage.materialUploadSize / 1024)}GB`
          : '0GB',
    },
    {
      label: '单个素材上限',
      value: () =>
        userBindPackage?.singleUploadSize ? `${userBindPackage.singleUploadSize}MB` : '0MB',
    },
    {
      label: props.userType == 6 ? '访问次数' : '扩展访问次数',
      value: () =>
        props.userType == 6
          ? '每天100次'
          : extensionDtos?.reduce((sum: any, e: any) => sum + (e.shareExtensionVisitCount || 0), 0)
          ? `${extensionDtos?.reduce(
              (sum: any, e: any) => sum + (e.shareExtensionVisitCount || 0),
              0
            )}次`
          : '-',
    },
    {
      label: '空间容量',
      value: () => (userBindPackage?.spaceAr ? `${userBindPackage.spacePicTotalNum || 0}张` : '-'),
    },
    {
      label: '默认并发数量',
      value: () => (userBindPackage?.spaceAr ? `${userBindPackage.vpsQps || 5}个` : '-'),
    },
    {
      label: '扩展并发数量',
      value: () =>
        extensionDtos?.reduce((sum: any, e: any) => sum + (e.vpsQps || 0), 0)
          ? `${extensionDtos?.reduce((sum: any, e: any) => sum + (e.vpsQps || 0), 0)}个`
          : '-',
    },
  ];
});

// 支持项目类型字段，与UserInfo2.vue一致
const supportTypeList = computed(() => {
  const userBindPackage = packageData.value?.userBindPackageDto || props.data.userBindPackageDto;

  return [
    {
      label: '【眼镜端】空间AR',
      value: () => (userBindPackage?.spaceAr ? '支持' : '-'),
    },
    {
      label: '【移动端】空间AR',
      value: () => (userBindPackage?.spaceAr ? '支持' : '-'),
    },
    {
      label: '【小程序】空间AR',
      value: () => (userBindPackage?.spaceAr ? '支持' : '-'),
    },
    {
      label: '【小程序】平面AR',
      value: () => (userBindPackage?.wxPlaneAr ? '支持' : '-'),
    },
    {
      label: '【小程序】图像AR',
      value: () => (userBindPackage?.wxPicAr ? '支持' : '-'),
    },
    {
      label: '【小程序】单场景AR',
      value: () => (userBindPackage?.wxSingleSceneAr ? '支持' : '-'),
    },
    {
      label: '【小程序】人脸AR',
      value: () => (userBindPackage?.wxFaceAr ? '支持' : '-'),
    },
    {
      label: '【小程序】身体AR',
      value: () => (userBindPackage?.wxBodyAr ? '支持' : '-'),
    },
    {
      label: '【小程序】手势AR',
      value: () => (userBindPackage?.wxHandAr ? '支持' : '-'),
    },
  ];
});

// Methods
const getCompanyName = () => {
  console.log('props.userType', props.userType, props.data, packageData.value);
  if (props.userType === 6) return '个人体验版';
  if (props.userType === 7) return '定制版';

  // 优先使用新接口返回的数据
  const orgName = packageData.value?.orgnizationDto?.orgnizationName;
  if (orgName) return orgName;

  // 回退到原有数据
  return props.data.packageInfoDto?.packageName || props.data.userBindPackageDto?.packageName || '';
};

const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  return getDayTime(dateStr).split('-').join('.');
};

const beforeUpload = (uploadFile: any) => {
  if (uploadFile.size / 1024 / 1024 > 5) {
    store.state.showTips = '大小不超过5MB';
    return false;
  }
  return true;
};

const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  imageUrl.value = URL.createObjectURL(uploadFile.raw!);
  store.state.profilePic = imageUrl.value;
  store.state.profilePicture = response.data;
};

const togglePasswordVisibility = () => {
  isPasswordVisible.value = !isPasswordVisible.value;
};

const handleUpdatePassword = () => {
  props.changeEvent?.('password');
};

const handleLogout = () => {
  router.push('/login');
};

const handleClose = () => {
  props.changeEvent?.('');
};

const showAgreement = (type: number) => {
  agreementType.value = type;
};

// Lifecycle
onMounted(async () => {
  // Initialize user avatar
  const profilePicture = props.data.userDto?.profilePicture;
  if (profilePicture) {
    getOssAccessPath({ key: profilePicture }).then((res: any) => {
      imageUrl.value = res.data;
    });
  } else {
    imageUrl.value = require('@/assets/images/ailongmask.png');
  }

  // 调用 getOrgnizationPackage 接口并打印结果
  try {
    console.log('🔄 开始调用 getOrgnizationPackage 接口...');
    const response = await getOrgnizationPackage({});
    console.log('📦 getOrgnizationPackage 接口返回结果:', response);
    console.log('📦 接口返回的完整数据结构:', JSON.stringify(response, null, 2));

    // 保存接口返回的数据
    if (response && response.data) {
      packageData.value = response.data;
      console.log('✅ 用户信息数据已更新:', packageData.value);

      // 使用新接口返回的密码数据
      userPassword.value =
        packageData.value.userDto?.password || props.data.userDto?.password || '';
    }
  } catch (error) {
    console.error('❌ getOrgnizationPackage 接口调用失败:', error);
    // 接口调用失败时使用原有数据
    userPassword.value = props.data.userDto?.password || '';
  }
});
</script>

<style lang="less" scoped>
.energy-value {
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  margin: 0 16px;
  background: linear-gradient(#f7f9fc, #f7f9fc) padding-box,
    linear-gradient(
        90deg,
        rgba(246, 41, 123, 1),
        rgba(208, 41, 246, 1),
        rgba(123, 19, 251, 1),
        rgba(19, 204, 251, 1)
      )
      border-box;
  border: 1px solid transparent;
  border-radius: 20px;
}

.gradient-text {
  background: linear-gradient(
    90deg,
    rgba(246, 41, 123, 1),
    rgba(208, 41, 246, 1),
    rgba(123, 19, 251, 1),
    rgba(19, 204, 251, 1)
  );
  -webkit-background-clip: text; /* 关键：背景裁剪为文字形状 */
  background-clip: text;
  color: transparent; /* 关键：让文字颜色透明，显示背景渐变 */
  -webkit-text-fill-color: transparent; /* 兼容 Safari */
}
.ai-service-box {
  width: 100%;
  border-radius: 4px 0px 0px 0px;
  margin: 20px auto 0 auto;
  display: flex;
  align-items: start;
}
.ai-service-table {
  flex: 1;
  border: 1px solid #e6edf7;
  border-radius: 4px;
  overflow: hidden;
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;

  .ai-service-title {
    height: 32px;
    line-height: 32px;
    background: rgba(230, 237, 247, 0.3);
    text-align: left;
    padding-left: 12px;
    font-weight: 400;
    font-size: 12px;
    color: #797979;
    border-bottom: 1px solid #e6edf7;
  }
  .table-row {
    height: 32px;
    line-height: 32px;
    text-align: left;
    padding-left: 12px;
    font-weight: 400;
    font-size: 12px;
    color: #1e1e1e;
  }
}

.user-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
  }

  .modal-container {
    position: relative;
    display: flex;
    width: 1000px;
    height: 531px;
    gap: 15px;
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    overflow: visible;
  }

  .user-info-panel {
    width: 321px;
    height: 531px;
    background: #ffffff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .service-info-panel {
    width: 664px;
    height: 531px;
    background: #ffffff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .panel-header {
    padding: 16px 20px 0px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .close-btn {
      width: 20px;
      height: 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .panel-content {
    flex: 1;
    padding: 0px 20px 0px 20px;
    overflow-y: auto;
  }

  // 用户信息面板样式
  .user-profile {
    display: flex;
    align-items: center;
    margin-bottom: 34px;
    margin-top: 20px;
    flex-direction: column;
    justify-content: center;

    .avatar-section {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-top: 20px;

      .avatar-uploader {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid #e4e7ed;
        cursor: pointer;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409eff;
        }

        .avatar {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .avatar-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f7fa;
          color: #c0c4cc;
          font-size: 32px;
        }
      }

      .avatar-edit-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 22px;
        height: 22px;
        background: #409eff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 12px;

        &::before {
          content: '✎';
        }
      }
    }

    .user-details {
      flex: 1;
      overflow: hidden;

      .company-name {
        margin-top: 15px;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 6px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .package-validity {
        font-size: 13px;
        color: #606266;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .enterprise-info {
    margin-bottom: 24px;

    .enterprise-info-content {
      background: #f7f9fc;
      border-radius: 4px 4px 4px 4px;
      padding: 15px;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;
    }

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
  }

  .account-info {
    margin-bottom: 24px;
  }

  .info-grid {
    width: 100%;
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        text-align: start;
        width: 70px;
        font-size: 13px;
        color: #606266;
        flex-shrink: 0;
      }

      .value {
        text-align: start;
        font-size: 13px;
        color: #303133;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .password-section {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;

        .password-display {
          font-size: 13px;
          color: #303133;
          margin-right: 8px;
          flex-shrink: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .password-toggle {
          padding: 0;
          margin-right: 8px;
          color: #909399;
          flex-shrink: 0;

          &:hover {
            color: #409eff;
          }
        }

        .update-password-btn {
          font-size: 12px;
          padding: 3px 6px;
          flex-shrink: 0;
        }
      }
      .bold-text {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #303133;
        .password-view {
          margin-left: 5px;
          cursor: pointer;
          height: 16px;
          display: inline-block;
          img {
            width: 16px;
            height: 16px;
          }
        }
        .update-passWord {
          position: relative;
          width: 96px;
          height: 24px;
          line-height: 24px;
          background: rgba(46, 118, 255, 0.1);
          border-radius: 12px;
          display: inline-block;
          font-size: 14px;
          font-weight: 400;
          color: #1e84ff;
          text-align: center;
          margin-left: 10px;
          padding-left: 19px;
          box-sizing: border-box;
          cursor: pointer;
          &::before {
            content: '';
            position: absolute;
            left: 11px;
            top: 5px;
            width: 14px;
            height: 14px;
            background-image: url(~@/assets/images/icon/modify.png);
            background-size: 100% 100%;
          }
          &:hover {
            background-color: rgba(46, 118, 255, 0.27);
          }
        }
      }
    }
  }

  .logout-section {
    margin-top: auto;
    padding-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    .logout {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      div {
        position: relative;
        padding-left: 26px;
        height: 32px;
        line-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 14px;
        color: #2e76ff;
        cursor: pointer;
        &::before {
          content: '';
          width: 16px;
          height: 16px;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          background-image: url(~@/assets/images/icon/logout-1.png);
          background-size: 100% 100%;
        }
        &:hover {
          color: rgba(18, 81, 200, 1);
          &::before {
            background-image: url(~@/assets/images/icon/logout-1A.png);
          }
        }
      }
    }
  }

  // 服务信息面板样式
  .package-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-top: 12px;
    gap: 20px;

    .service-table-wrapper {
      flex: 1;
      border: 1px solid #e6edf7;
      border-radius: 4px;
      overflow: hidden;
      font-weight: 400;
      font-size: 14px;
      color: #1e1e1e;

      .title {
        height: 32px;
        line-height: 32px;
        background: rgba(230, 237, 247, 0.3);
        text-align: left;
        padding-left: 12px;
        font-weight: 400;
        font-size: 12px;
        color: #797979;
      }

      .table-row {
        border-top: 1px solid #e6edf7;
        height: 36px;
        box-sizing: border-box;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        text-align: left;

        .service-name {
          width: 60%;
          height: 35px;
          line-height: 35px;
          padding-left: 12px;
          color: #1e1e1e;
          font-size: 14px;
        }

        .service-value {
          width: 40%;
          height: 35px;
          line-height: 35px;
          padding-left: 12px;
          color: #1e1e1e;
          font-size: 14px;

          &.extended {
            font-weight: 500;
          }
        }
      }
    }
  }

  .service-notes {
    margin-top: 15px;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #909399;
    padding-left: 5px;

    i {
      margin-right: 5px;
      color: #409eff;
    }
  }

  .agreement-links {
    position: absolute;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    color: #909399;
    white-space: nowrap;

    .el-button--text {
      font-size: 12px;
      color: #409eff;
      padding: 0;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}
</style>
