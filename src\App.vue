<template>
  <div
    :style="{
      minWidth:
        currentRoute?.value?.includes('home') || currentRoute?.value?.includes('item_list')
          ? 0
          : '1360px',
      boxSizing: 'border-box',
    }">
    <base-header v-if="!hideHeader" :current-route="currentRoute"></base-header>
    <div
      class="layout"
      :class="!hideSide ? 'pl216' : 'noPadding'"
      :style="{
        height: hideHeader
          ? store.state.pageH == 69
            ? '100vh'
            : 'auto'
          : `calc(100vh - ${store.state.pageH}px)`,
      }">
      <div class="base_side" v-if="!hideSide">
        <BaseSide :hideSide="hideSide" :current-route="currentRoute"></BaseSide>
      </div>
      <div class="content">
        <div
          class="content-box"
          :style="{
            borderRadius: currentRoute?.value?.includes('item_list') ? '0px' : 'auto',
          }"
          :class="{
            paddingStyle: hasPadding,
            tableLayoutStyle: hasSpeicalColor,
            isHome: !hasPadding,
          }">
          <router-view />
        </div>
      </div>
    </div>
  </div>
  <tips-view
    v-show="showTips"
    :show-btn="getShowBtnText()"
    :is-remove-button="isRemoveButton"
    :sure-event="handleTipsConfirm"
    :cancle-event="handleTipsCancel"></tips-view>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import BaseSide from '@/components/layouts/BaseSide.vue';
import BaseHeader from '@/components/layouts/BaseHeader.vue';
import TipsView from '@/components/TipsView.vue';
import { useStore } from 'vuex';
import { ref, watch, reactive, nextTick, onMounted } from 'vue';

const router = useRouter();
const store = useStore();
const hideSide = ref(true);
const hideHeader = ref(true);
const currentRoute: any = reactive({});
const showTips = ref(false);
const hasPadding = ref(true);
const hasSpeicalColor = ref(false);
const isRemoveButton = ref(false);
let timer: any = null;
let ws: any = null;

// 处理socket返回数据
const handleMessage = (event: any) => {
  const data = JSON.parse(event.data);
  if (data.messageType == 2) {
    window.localStorage.setItem('clientid', data.data);
  } else if (data.messageType == 1) {
    // 处理进度更新消息和gene57任务完成消息
    const taskData = JSON.parse(data.data);
    const taskProccessMap = taskData.taskProccessMap;

    // 更新任务进度映射到store中
    if (store.state.spaceUploadTask.currentUploadMethod) {
      store.state.spaceUploadTask.taskProccessMap = taskProccessMap;
    }

    // 如果是gene57的任务完成消息
    if (
      store.state.spaceUploadTask.currentUploadMethod === 'e57' &&
      taskData.taskStatus !== undefined
    ) {
      handleTaskCompletion(taskData, data.messageType);
    } else {
      // 处理进度更新
      if (taskProccessMap.uploadToNjyjServer) {
        store.state.uploadStep = 2;
      }
      if (taskProccessMap.unzipAndTreat) {
        store.state.uploadStep = 3;
      }
      if (taskProccessMap.uploadToImmersal) {
        store.state.uploadStep = 4;
      }
    }
  } else if (data.messageType == 4) {
    // 处理genB2g任务完成消息
    if (store.state.spaceUploadTask.currentUploadMethod === 'b2g') {
      try {
        const taskData = JSON.parse(data.data);
        handleTaskCompletion(taskData, data.messageType);
      } catch (error) {
        console.error('解析B2g任务完成消息失败:', error);
        handleTaskError('解析B2g任务消息失败');
      }
    }
  } else if (data.messageType == 5) {
    // 处理genPolycam任务完成消息
    if (store.state.spaceUploadTask.currentUploadMethod === 'polycam') {
      try {
        const taskData = JSON.parse(data.data);
        handleTaskCompletion(taskData, data.messageType);
      } catch (error) {
        console.error('解析Polycam任务完成消息失败:', error);
        handleTaskError('解析Polycam任务消息失败');
      }
    }
  } else if (data.messageType == 6) {
    // 处理genQiyu任务完成消息
    if (store.state.spaceUploadTask.currentUploadMethod === 'qiyu') {
      try {
        const taskData = JSON.parse(data.data);
        handleTaskCompletion(taskData, data.messageType);
      } catch (error) {
        console.error('解析Qiyu任务完成消息失败:', error);
        handleTaskError('解析Qiyu任务消息失败');
      }
    }
  }
};

// 处理任务完成的通用函数
const handleTaskCompletion = (taskData: any, messageType: number) => {
  const { taskProccessMap, taskStatus, errorMessage, taskId, taskName } = taskData;

  // 检查所有任务是否完成
  const allTasksCompleted =
    taskProccessMap.uploadToNjyjServer &&
    taskProccessMap.unzipAndTreat &&
    taskProccessMap.uploadToImmersal;

  // 更新任务状态
  store.state.spaceUploadTask = {
    isCompleted: allTasksCompleted,
    isSuccess: taskStatus && allTasksCompleted && !errorMessage,
    errorMessage: errorMessage,
    taskId: taskId,
    taskName: taskName,
    currentUploadMethod: store.state.spaceUploadTask.currentUploadMethod, // 保持当前上传方式
    taskProccessMap: taskProccessMap, // 保存任务进度映射
  };

  // 如果任务完成，设置uploadStep为完成状态
  if (allTasksCompleted) {
    store.state.uploadStep = taskStatus && !errorMessage ? 5 : -1; // 5表示成功完成，-1表示失败
  }
};

// 处理任务错误的通用函数
const handleTaskError = (errorMessage: string) => {
  store.state.spaceUploadTask = {
    isCompleted: true,
    isSuccess: false,
    errorMessage: errorMessage,
    taskId: null,
    taskName: null,
    currentUploadMethod: store.state.spaceUploadTask.currentUploadMethod, // 保持当前上传方式
  };
  store.state.uploadStep = -1;
};

// WebSocket 相关配置与实现方式同步 master 版
const wsUrl: any =
  window.location.host == 'mixedspace.njyjxr.com'
    ? process.env.VUE_APP_WS_API || 'wss://mixedspace.njyjxr.com/api/ws' // 生产环境
    : // : `ws://${window.location.host}/ws`; // 开发环境使用当前主机
      `ws://*************:8080/ws`; // 开发环境使用当前主机

console.log(`当前环境: ${window.location.host}, 使用 WebSocket URL: ${wsUrl}`);

const reconnect = () => {
  setTimeout(function () {
    console.log('Attempting to reconnect...');
    const newSocket = new WebSocket(wsUrl); // 重新创建WebSocket实例
    newSocket.binaryType = 'arraybuffer'; // 设置二进制类型为arraybuffer
    newSocket.onopen = function (event) {
      console.log('Reconnected');
      ws = newSocket; // 更新全局的socket引用，以便可以调用send等方法
    };
    newSocket.onerror = function (event) {
      console.error('Reconnection error:', event);
    };
    newSocket.onmessage = function (event) {
      console.log('Received message during reconnection:', event.data);
      handleMessage(event);
    };
    newSocket.onclose = function (event) {
      console.log('Reconnection closed:', event);
      reconnect(); // 如果再次关闭，则再次尝试重连
    };
  }, 500); // 500毫秒后重试，可以根据需要调整时间间隔和重试策略
};

// 路由拦截，这里主要根据路由确定一些组件的显隐性
router.beforeEach((to, from, next) => {
  hideSide.value = Boolean(to.meta && to.meta.hideSide);
  // 登录时候自动刷新缓存
  if (
    (currentRoute.value == '/' || currentRoute.value == '/login') &&
    (to.path == '/home' || to.path == '/experience_home')
  ) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      location.reload();
    }, 200);
  }
  if (to.path[0] === '/') {
    window.sessionStorage.setItem('path', to.path);
    currentRoute.value = to.path;
  }

  if (
    [
      '/login',
      '/',
      '/login2',
      '/experience_edit',
      '/space_edit',
      '/scene_edit',
      '/space_edit_v2',
      '/path_navigation_edit',
      '/ai_generate',
    ].includes(to.path)
  ) {
    hideHeader.value = true;
  } else {
    hideHeader.value = false;
  }
  if (
    [
      '/',
      '/login',
      '/home',
      '/experience_home',
      '/square',
      '/package',
      '/path_navigation_edit',
      '/ai_generate',
    ].includes(to.path)
  ) {
    hasPadding.value = false;
  } else {
    hasPadding.value = true;
  }

  if (
    [
      '/source_material',
      '/spacelist',
      '/customer',
      '/devicelist',
      '/default_material',
      '/experience_material',
      '/path_navigation',
    ].includes(to.path)
  ) {
    hasSpeicalColor.value = true;
  } else {
    hasSpeicalColor.value = false;
  }
  next();
});

const changeWindowSize = () => {
  if (1360 > window.innerWidth) {
    store.state.pageH = 80;
  } else {
    store.state.pageH = 72;
  }
};

onMounted(() => {
  changeWindowSize();
  window.addEventListener('resize', changeWindowSize, false);
  setTimeout(() => {
    if (currentRoute.value != '/' && currentRoute.value != '/login') {
      ws = new WebSocket(wsUrl);
      ws.binaryType = 'arraybuffer'; // 设置二进制类型为arraybuffer
      ws.onmessage = (event: any) => {
        handleMessage(event);
      };
      ws.onerror = (error: any) => {
        console.error('WebSocket Error: ', error);
      };
      ws.onclose = () => {
        console.log('WebSocket connection closed');
        reconnect(); // 调用重连函数
      };
    }
  }, 1000); // 延迟一秒，确保Vue CLI的WebSocket已经初始化
});

watch(
  () => store.state.showTips,
  (newState) => {
    if (newState) {
      nextTick(() => {
        showTips.value = !!(document.querySelectorAll('.tips-model').length == 1);
      });
    }
  }
);

// TipsView 相关方法
const getShowBtnText = () => {
  // 检查是否是移除模板的提示
  if (
    store.state.showTips &&
    (store.state.showTips.includes('确认将当前模版从推荐列表中移除') ||
      store.state.showTips.includes('确认将当前模版从小程序端模版中移除') ||
      store.state.showTips.includes('确认将当前模版从Banner推荐中移除'))
  ) {
    isRemoveButton.value = true;
    return '移除';
  }
  isRemoveButton.value = false;
  return '';
};

const handleTipsConfirm = () => {
  console.log('🔍 handleTipsConfirm 被调用');
  console.log('🔍 store.state.confirmDeleteTemplate:', store.state.confirmDeleteTemplate);
  // 调用 store 中的确认事件
  if (store.state.confirmDeleteTemplate) {
    console.log('✅ 执行确认删除事件');
    store.state.confirmDeleteTemplate();
    store.state.confirmDeleteTemplate = null;
  } else {
    console.log('❌ 确认删除事件未找到');
  }
};

const handleTipsCancel = () => {
  store.state.showTips = '';
  isRemoveButton.value = false;
};
</script>

<style lang="less">
@import url('./styles/global.less');
@import url('./styles/el_reset_style.less');

html,
body {
  margin: 0;
  padding: 0;
}
#app,
div,
span,
input {
  font-family: 'PingFang SC', 'Lantinghei SC', 'Microsoft YaHei', 'HanHei SC', 'Helvetica Neue',
    'Open Sans', 'Hiragino Sans GB', '微软雅黑', STHeiti, 'WenQuanYi Micro Hei', Arial, SimSun,
    sans-serif !important;
}
#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background: linear-gradient(180deg, rgba(46, 118, 255, 0.1) 0%, rgba(255, 255, 255, 0) 19.42%),
    #fff;
}

.el-dialog__title {
  font-weight: bold;
  font-size: 18px;
  color: #1e1e1e;
}

.item-list .el-dialog {
  padding-bottom: 24px;
  padding-right: 24px;
}

.paddingStyle {
  // padding: 0 32px;
  margin-right: 24px;
  padding-left: 0;
}

.tableLayoutStyle {
  // background: #fff !important;
  // box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.04) !important;
  // padding-top: 0px;
  // padding-left: 32px;
  // padding-right: 32px;
}

.layout {
  position: relative;
  // height: calc(100vh - 64px);
}

.pl216 {
  padding-left: 263px;
}

.base_side {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  // background-color: #F5F6F7;
  overflow: hidden;
}

.content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  // background-color: #F5F6F7;
}

.content .content-box {
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
  // background: #FFFFFF;
  // box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.04);
  border-radius: 10px 10px 10px 10px;

  &.isHome {
    height: 100%;
    background-color: transparent;
  }
}

canvas {
  outline: none !important;
}

.home .font20 {
  font-size: 20px;
}

.filter-style {
  width: 120px;
  text-align: center;
  margin-left: -78px;
  border-radius: 10px !important;
  border: 1px solid #ebf0fc !important;
  box-shadow: none !important;

  * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* 整个滚动条 */
  ::-webkit-scrollbar {
    height: 5px;
    /* 水平滚动条的高度 */
  }

  li {
    height: 29px;
    line-height: 29px;
    font-size: 14px;
    color: #1e1e1e;
    white-space: nowrap;
    overflow: hidden;
    overflow-x: auto;

    &.is-active {
      background-color: transparent;
      color: #2e76ff !important;
    }
  }

  li:hover {
    background-color: #f7fafd;
    color: #1e1e1e;
  }

  .el-table-filter__list {
    padding: 8px 0 !important;
  }
}

.el-table th.el-table__cell > .cell.highlight {
  color: #2e76ff !important;
}
</style>
