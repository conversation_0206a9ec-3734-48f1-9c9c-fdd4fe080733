import request from "../request";

// 获取所有套餐信息
export function getAllPackageInfo() {
  return request({
    url: "/authority/package/getAllPackageInfo",
    method: "get",
  });
}

// 更新用户套餐
export function updateUserPackage(data: any) {
  return request({
    url: "/authority/package/updateUserPackage?packageId=" + data.packageId + '&updateUserId=' + data.updateUserId,
    method: "post",
    data,
  });
}

// 更新用户套餐时间
export function updateUserPackageTime(data: any) {
  const hasStartTime = data.startTime
  return request({
    url: '/authority/package/updateUserPackageTime?updateUserId=' + data.updateUserId + '&endTime=' + data.endTime + (hasStartTime ? '&startTime=' + data.startTime : ''),
    method: "post",
  })
}

// 添加套餐扩展
export function addPackageExtension(data: any) {
  return request({
    url: "/authority/package/addPackageExtension",
    method: "post",
    data,
  });
}

// 获取套餐列表
export function getPackageList(data: any) {
  return request({
    url: "/authority/package/getPackageList",
    method: "get",
  });
}

// 添加套餐
export function addPackage(data: any) {
  return request({
    url: "/authority/package/addPackage",
    method: "post",
    data,
  });
}

// 更新套餐
export function updatePackage(data: any) {
  return request({
    url: "/authority/package/updatePackage",
    method: "post",
    data,
  });
}

// 添加用户绑定套餐
export function addUserBindPackage(data: any) {
  return request({
    url: `authority/package/addUserBindPackage?userId=${data.userId}`,
    method: "post",
    data
  });
}

// 获取用户绑定套餐
export function getUserBindPackage(data: any) {
  return request({
    url: `authority/package/getUserBindPackage?userId=${data.userId}`,
    method: "get",
  });
}

// 添加用户绑定套餐扩展
export function addUserBindPackageExtension(data: any) {
  return request({
    url: `authority/package/addUserBindPackageExtension?userId=${data.userId}`,
    method: "post",
    data
  });
}

// 获取组织的套餐和软件包
export function getOrgnizationPackage(data: any) {
  return request({
    url: "/authority/package/getOrgnizationPackage" + (data.orgnizationId ? `?orgnizationId=${data.orgnizationId}` : ""),
    method: "get",
  });
}