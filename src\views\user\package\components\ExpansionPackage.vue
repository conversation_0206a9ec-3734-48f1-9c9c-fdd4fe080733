<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div class="header">{{ defaultValue.orgnizationName }}扩展服务包</div>
        <!-- <div class="icon iconfont icon-close" @click="changeState"></div> -->
      </div>
      <div class="modal-form">
        <div class="exp-box">
          <div class="title">
            <div>服务项</div>
            <div>{{ `【${packageInfo.base.packageName}】标准服务包` }}</div>
            <div v-for="(e, i) in packageInfo.expands" :key="i">
              扩展服务包
            </div>
            <div>新增服务包</div>
            <div>更新后服务包</div>
          </div>
          <div class="exp-content">
            <div>
              <div>小程序平面识别AR项目数量</div>
              <div>大空间定位AR项目数量</div>
              <div>素材空间</div>
              <div>空间上限</div>

            </div>
            <div>
              <div>{{ packageInfo.base.planeSceneNum }}个</div>
              <div>{{ packageInfo.base.arSceneNum }}个</div>
              <div v-html="calculateSize(packageInfo.base.materialUploadSize)"></div>
              <div>
                {{ packageInfo.base.spacePicTotalNum }}张
              </div>
            </div>
            <div v-for="(e, i) in packageInfo.expands" :key="i">
              <div>{{ e.planeSceneNum }}个</div>
              <div>{{ e.arSceneNum }}个</div>
              <div v-html="calculateSize(e.materialUploadSize)"></div>
              <div>
                {{ e.spacePicTotalNum }}张
              </div>
            </div>
            <div>
              <div v-for="(e, i) in Object.keys(packageInfo.expand)" :key="i">
                <span class="minus" @click="minusEvent(e, packageInfo.expand[e], i)"></span>
                <input class="number_unit" type="number" :value="packageInfo.expand[e]"
                  @input="(ev) => handleInput(ev, e)">
                <span class="plus" @click="plusEvent(e, packageInfo.expand[e], i)"></span>
                {{ unitList[i] }}
              </div>
            </div>
            <div>
              <div>{{ totalPackage.planeSceneNum + packageInfo.expand.planeSceneNum }}个</div>
              <div>{{ totalPackage.arSceneNum + packageInfo.expand.arSceneNum }}个</div>
              <div v-html="calculateSize(totalPackage.materialUploadSize + packageInfo.expand.materialUploadSize)">
              </div>
              <div>{{ totalPackage.spacePicTotalNum + packageInfo.expand.spacePicTotalNum }}张</div>
            </div>
          </div>
        </div>
        <div class="btn-box">
          <div class="btn-default el-size3">
            <el-button @click="changeState">取消</el-button>
          </div>
          <div class="btn-primary el-size3">
            <el-button @click="submitForm()">确认</el-button>
          </div>
        </div>
      </div>
    </div>
    <ExtendedPackage v-if="confirmAlert" @hiddenMask="hiddenMask" :info="packageInfo.expand"></ExtendedPackage>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { addPackageExtension, getOrgnizationPackage } from '@/api'
import { calculateSize } from '@/utils'
import ExtendedPackage from '@/components/experience/create/ExtendedPackage.vue';

const props = defineProps({
  handleShow: {
    default: null,
    type: Function
  },
  defaultValue: {
    default: null,
    type: Object
  }
})
const baseExpand = [1, 1, 512, 100]
const packageInfo: any = ref({ base: {}, expand: { planeSceneNum: 0, arSceneNum: 0, materialUploadSize: 0, spacePicTotalNum: 0 }, expands: [] })
const totalPackage: any = ref({})
const unitList = ref(['个', '个', 'MB', '张'])
const confirmAlert = ref(false)


const handleInput = (e, name) => {
  packageInfo.value.expand[name] = Number(e.target.value)
}

const requestAddPackageExtension = () => {
  return new Promise(resolve => {
    addPackageExtension({ ...packageInfo.value.expand, userId: props.defaultValue.adminUserInfo.id }).then((res: any) => {
      resolve(1)
    })
  })
}

const hiddenMask = async (flag) => {
  if (flag) {
    await requestAddPackageExtension()
    props.handleShow('', true)
  }
  confirmAlert.value = false
}


const submitForm = async () => {
  const sum = Object.values(packageInfo.value.expand).reduce((acc: any, cur: any) => acc + cur, 0)
  if (!sum) {
    props.handleShow('')
    return
  }
  confirmAlert.value = true
}

const changeState = () => {
  props.handleShow('')
}

const minusEvent = (key: string, value: number, index: number) => {
  const expand = { ...packageInfo.value.expand }
  expand[key] = Math.max(value - baseExpand[index], 0)
  packageInfo.value.expand = { ...expand }
}

const plusEvent = (key: string, value: number, index: number) => {
  const expand = { ...packageInfo.value.expand }
  expand[key] = value + baseExpand[index]
  packageInfo.value.expand = { ...expand }
}

onMounted(() => {
  // 编辑页初始数据

  getOrgnizationPackage({ orgnizationId: props.defaultValue.id }).then((res: any) => {
    packageInfo.value.base = res.data.packageInfoDto
    packageInfo.value.expands = res.data.packageExtensionDtos

    const { arSceneNum, materialUploadSize, planeSceneNum, spacePicTotalNum } = res.data.packageInfoDto;
    totalPackage.value = { planeSceneNum, arSceneNum, materialUploadSize, spacePicTotalNum }
    packageInfo.value.expands.forEach((item: any) => {
      Object.keys(totalPackage.value).forEach((e: any) => {
        totalPackage.value[e] += (item[e] || 0)
      })
    });

    (document.querySelector('.exp-box .title') as any).style.width = 777 + packageInfo.value.expands.length * 194 + 'px'
  })
})
</script>
<style scoped lang="less">
/* 取消上下箭头 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

input[type='number'] {
  -moz-appearance: textfield;
}

.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .number_unit {
    width: 90px;
    height: 24px;
    outline: none;
    border: none;
    padding-left: 10px;
    border: 1px solid #efefef;
  }

  .modal-content {
    width: 724px;
    height: 447px;
    max-height: 90%;
    background: #fff;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    border: 1px solid #EDEFF2;
    overflow: hidden;

    .modal-content-title {
      height: 68px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px 0 22px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;

      .header {
        font-weight: bold;
        font-size: 18px;
        color: #1E1E1E;
      }

      .icon-close {
        font-size: 26px;
        cursor: pointer;
        font-weight: 400;

        &:hover {
          color: #2E76FF;
        }
      }
    }

    .modal-form {
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
      overflow-y: auto;

      .exp-box {
        overflow: hidden;
        overflow-x: auto;
        font-size: 0;
        text-align: center;
        white-space: nowrap;
        border-radius: 20px 20px 0px 0px;

        &>div {
          font-size: 16px;
          font-weight: 400;
          color: #333333;

          &>div {
            width: 194px;
            display: inline-block;
          }
        }

        &>.title {
          font-weight: bold;
          height: 32px;
          background: rgba(230, 237, 247, 0.3);
          font-weight: 400;
          font-size: 12px;
          color: #797979;
          line-height: 32px;
        }

        &>.exp-content {
          box-sizing: border-box;

          &>div>div {
            line-height: 69px;
            height: 69px;
            border: 1px solid #E6EDF7;
            font-weight: 400;
            font-size: 14px;
            color: #1E1E1E;
            border-right: none;
            border-left: none;
            border-top: none;
          }

          &>div:first-child>div {
            padding-left: 20px;
          }

          .minus,
          .plus {
            display: inline-block;
            width: 15px;
            background-size: 100% 100%;
            vertical-align: middle;
            cursor: pointer;
            background-repeat: no-repeat;
            padding: 10px;
            background-position: center;
          }

          .minus {
            height: 4px;
            background-image: url(~@/assets/images/icon/minus-exp.png);
            background-size: 15px 4px;
          }

          .plus {
            height: 15px;
            background-image: url(~@/assets/images/icon/plus-exp.png);
            background-size: 15px 15px;
          }

          .unit {
            display: inline-block;
            vertical-align: middle;
            margin: 0 9px;
            width: 55px;
            height: 25px;
            line-height: 25px;
            background: rgba(30, 132, 255, 0.04);
            border-radius: 2px;
            user-select: none;
          }
        }
      }
    }
  }
}

.btn-box {
  width: 261px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  float: right;
  margin-top: 8px;
  margin-right: 19px;

  .el-size3 {
    width: 114px;
    height: 38px;
    margin-left: 12px;
  }
}
</style>