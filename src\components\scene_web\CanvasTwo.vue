<template>
  <div id="canvas2d" class="canvas-view"></div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, onBeforeUnmount, ref, watch } from 'vue';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import {
  Vector3,
  Scene,
  OrthographicCamera,
  WebGLRenderer,
  Vector2,
  Group,
  AmbientLight,
  PlaneGeometry,
  MeshBasicMaterial,
  Mesh,
  TextureLoader,
  BackSide,
} from 'three';
import { CPoint, CLine } from '@/core/primitives';
import { CPointAttrs } from '@/core/primitives/CPoint';
import { CLineAttrs } from '@/core/primitives/CLine';
import { screenToWorldVector, createGroundLine } from '@/utils';
import { CSS2DRenderer } from '@/core/libs/CSS2DRenderer';
import { useStore } from 'vuex';
import { getNjyjNodes } from '@/api/index';
import { loader } from '@/config/threeJs';

const props = defineProps({
  handleMouseMove: {
    default: null,
    type: Function,
  },
  handleMouseDown: {
    default: null,
    type: Function,
  },
  handleMouseUp: {
    default: null,
    type: Function,
  },
});

const store = useStore();

let camera: OrthographicCamera;
let width: number, height: number;
let scene: Scene;
let renderer: any;
let labelRenderer: any;
const graphPoints: any = ref([]);
const childNodeArray: any = ref([]); // 线中间的点位集合
const controlsScale = ref(1);
const currentGraphId = ref(0);
let animationFrameId: any = null;

onMounted(() => {
  scene = new Scene();
  (window as any).scene = scene;
  let canvas = document.getElementById('canvas2d');
  width = canvas?.getBoundingClientRect().width || 0;
  height = canvas?.getBoundingClientRect().height || 0;
  let k = width / height;
  let s = 15;

  const markGroup = new Group(); // 用来存放标注
  markGroup.name = 'markGroup-init';
  scene.add(markGroup);

  camera = new OrthographicCamera(-s * k, s * k, s, -s, 1, 1000);
  camera.position.set(0, 750, 0);
  camera.lookAt(new Vector3(0, 0, 0));
  (window as any).camera = camera;

  window.sessionStorage.setItem('width', width + '');
  window.sessionStorage.setItem('height', height + '');

  renderer = new WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  renderer.setClearColor(0xc3c7cb, 1);
  canvas?.appendChild(renderer.domElement);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.render(scene, camera);
  (window as any).renderer = renderer;

  // 添加控制器
  const controls = new OrbitControls(camera, renderer.domElement);
  controls.maxZoom = 2.5;
  controls.minZoom = 0.05;
  // 使用控制器
  // controls.enableDamping = true;
  // controls.enabled = false; // 页面禁止转动
  controls.enableRotate = false;
  // controls.enableZoom = false;

  // 环境光
  const ambient = new AmbientLight(0xffffff, 1);
  ambient.name = 'ambient-init';
  scene.add(ambient);

  (window as any).controls = controls;

  labelRenderer = new CSS2DRenderer();
  labelRenderer.setSize(width, height);
  labelRenderer.domElement.style.position = 'absolute';
  // 相对鼠标的相对偏移
  labelRenderer.domElement.style.top = '0px';
  labelRenderer.domElement.style.left = '0px';
  // //设置.pointerEvents=none，以免模型标签HTML元素遮挡鼠标选择场景模型
  labelRenderer.domElement.style.pointerEvents = 'none';
  canvas?.appendChild(labelRenderer.domElement);
  labelRenderer.render(scene, camera);

  animate();
  function animate() {
    if (store.state.isPlanStyle) {
      renderer.render(scene, camera);
      labelRenderer.render(scene, camera);
      if (renderer.domElement.style.width == '0px') {
        resizeCanvas();
      }
    }
    // 使用 requestAnimationFrame 执行动画
    animationFrameId = requestAnimationFrame(animate);
  }

  // 鼠标落下
  canvas?.addEventListener('mousedown', mousedownCanvas);

  // 鼠标抬起
  canvas?.addEventListener('mouseup', mouseupCanvas);

  // 滑动悬浮部分逻辑
  canvas?.addEventListener('mousemove', mousemoveCanvas, false);

  window.addEventListener('resize', resizeCanvas, false);

  const geometry = new PlaneGeometry(100, 100);
  const material = new MeshBasicMaterial({ side: 1, transparent: true, opacity: 1 });
  const t = new TextureLoader().load('sourceType/ground.png');
  material.map = t;
  const plane = new Mesh(geometry, material);
  plane.name = 'ground-init';
  plane.lookAt(0, 1, 0);
  createGroundLine(plane, 100, true);
  scene.add(plane);
});

onUnmounted(() => {
  let canvas = document.getElementById('canvas2d');
  canvas?.removeEventListener('mousemove', mousemoveCanvas, false);
  canvas?.removeEventListener('mousedown', mousedownCanvas, false);
  canvas?.removeEventListener('mouseup', mouseupCanvas, false);
  window.removeEventListener('resize', resizeCanvas);
  cancelAnimationFrame(animationFrameId);
});

const resizeCanvas = () => {
  if (renderer) {
    let canvas = document.getElementById('canvas2d');
    width = canvas?.getBoundingClientRect().width || 0;
    height = canvas?.getBoundingClientRect().height || 0;

    renderer.setSize(width, height);
    labelRenderer.setSize(width, height);
    // 重置相机投影的相关参数
    const k = width / height; //窗口宽高比
    const s = 15;
    camera.left = -s * k;
    camera.right = s * k;
    camera.top = s;
    camera.bottom = -s;
    camera.updateProjectionMatrix();
  }
};

// 鼠标移动事件
const mousemoveCanvas = (e: any) => {
  const worldVector = screenToWorldVector(e.clientX, e.clientY);
  props.handleMouseMove && props.handleMouseMove(worldVector, new Vector2(e.clientX, e.clientY));
};

// 鼠标点下事件
const mousedownCanvas = (e: any) => {
  if (store.state.isPlanStyle) {
    const worldVector = screenToWorldVector(e.clientX, e.clientY);
    props.handleMouseDown && props.handleMouseDown(worldVector);
  }
};

// 鼠标抬起事件
const mouseupCanvas = (e: any) => {
  const worldVector = screenToWorldVector(e.clientX, e.clientY);
  props.handleMouseUp && props.handleMouseUp(worldVector);
};

onBeforeUnmount(() => {
  for (let i = 0; i < scene.children.length; i++) {
    // 删除文字标记
    scene.children[i].traverse((e: any) => {
      if (e.element) {
        e.parent?.remove(e);
      }
    });
    scene.remove(scene.children[i]);
    i--;
  }
});

// 画线
const drawDashLine = (graphGroup: any, groundY: number, data: any, nodeIds: any) => {
  const currentLine = scene.getObjectByName(nodeIds[1] + nodeIds[0]);
  if (currentLine) return;
  const cline = new CLine({
    ...data,
    color: 0x333333,
    lineWidth: 4,
    depthTest: false,
    transparent: true,
  });
  cline.userData.points = [new Vector3(...data.vertexs[0]), new Vector3(...data.vertexs[1])];
  cline.name = nodeIds.join('');
  cline.position.y = groundY;
  graphGroup.add(cline);
};

// 画点
const drawPoint = (
  graphGroup: any,
  groundY: number,
  data: any,
  nodeId: string,
  nodeType: number,
  isValid?: boolean
) => {
  const cpoint = new CPoint({
    ...data,
    size: 46 * controlsScale.value,
    depthTest: false,
    transparent: true,
    url: isValid
      ? nodeType == 1
        ? '/images/valid-default.png'
        : '/images/valid-default2.png'
      : nodeType == 1
      ? '/images/invalid-default.png'
      : '/images/invalid-default2.png',
  });
  cpoint.userData.valid = !!isValid;
  cpoint.name = nodeId;
  cpoint.userData.nodeId = nodeId;
  cpoint.position.y = groundY;
  graphGroup.add(cpoint);
};

watch(
  () => store.state.editSceneData.graphId,
  (newState) => {
    const groundObj: any = scene.getObjectByName('ground-init');
    const groundY = groundObj.position.y;
    const graphLine: any = scene.getObjectByName('graph-group');
    graphLine && scene.remove(graphLine);

    if (currentGraphId.value != newState && groundY && newState) {
      const graphGroup = new Group();
      graphGroup.name = 'graph-group';
      scene.add(graphGroup);
      getNjyjNodes({ graphId: newState }).then((res) => {
        graphPoints.value = res.data.filter((e: any) => e.nodeType == 1 || e.nodeType == 3);
        childNodeArray.value = res.data.filter((e: any) => e.nodeType == 2);
        initPoint(groundY, graphGroup);
      });
    }
    currentGraphId.value = newState;
  }
);

const initPoint = (groundY: number, graphGroup: any) => {
  const sceneChild = scene.children.filter((e) => e.userData.nodeId);
  sceneChild.forEach((e) => {
    scene.remove(e);
  });
  const pointObj: any = {};
  graphPoints.value.forEach((e: any) => {
    pointObj[e.nodeId] = e;
  });
  graphPoints.value.forEach((e: any) => {
    // 这里判断是有效点位还是无效点位
    if (e.relatedNodeIdList.length) {
      drawPoint(
        graphGroup,
        groundY,
        { vertex: new Vector3(e.x, 0, e.z) },
        e.nodeId,
        e.nodeType,
        true
      );
      e.relatedNodeIdList.forEach((k: any) => {
        const oldPath = scene.getObjectByName(pointObj[k].nodeId + e.nodeId);
        if (!oldPath) {
          drawDashLine(
            graphGroup,
            groundY,
            {
              vertexs: [
                [e.x, 0, e.z],
                [pointObj[k].x, 0, pointObj[k].z],
              ],
            },
            [e.nodeId, pointObj[k].nodeId]
          );
        }
      });
    } else {
      drawPoint(graphGroup, groundY, { vertex: new Vector3(e.x, 0, e.z) }, e.nodeId, e.nodeType);
    }
  });
};

defineExpose({
  //
});
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
h3 {
  margin: 40px 0 0;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  display: inline-block;
  margin: 0 10px;
}

a {
  color: #42b983;
}

.canvas-view {
  width: 100%;
  height: 100%;
}
</style>
