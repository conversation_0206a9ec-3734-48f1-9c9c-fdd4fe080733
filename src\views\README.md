# Views Directory Structure

This directory contains all the view components of the application.

## Directory Organization

- `auth/` - Authentication related views
- `device/` - Device management views
- `material/` - Material management views
- `space/` - Basic space management views
  - `SpaceList.vue` - Space list page
  - `components/` - Shared components for space management
  - `create/` 
    - `CreateSpace.vue` - Create new space
    - `UpdateSpace.vue` - Update space info
    - `RemoveSpace.vue` - Delete space
- `scene_editor/` - 3D/2D scene editing related
  - `SpaceEdit.vue` - Main editor page
  - `components/` 
    - `SpaceEdit2D.vue` - 2D scene editor
    - `SpaceEdit3D.vue` - 3D scene editor
    - `LeftSide.vue` - Editor left panel
    - `RightSide.vue` - Editor right panel
    - `HeaderView.vue` - Editor header
    - `EditTools.vue` - Editor tools
- `user/` - User management views

## Directory Structure

```
views/
├── auth/                  # Authentication related views
│   ├── components/       # Auth specific components
│   ├── LoginView.vue
│   └── ChangePassword.vue
├── user/                  # User management
│   ├── components/       # User specific components
│   ├── UserList.vue
│   ├── UserInfo.vue
│   └── RoleList.vue
├── scene/                 # Scene management
│   ├── components/       # Scene specific components
│   ├── SceneList.vue
│   └── SceneEdit.vue
├── space/                 # Space management
│   ├── components/       # Space specific components
│   ├── SpaceList.vue
│   └── SpaceEdit.vue
├── experience/            # Experience management
│   ├── components/       # Experience specific components
│   ├── ExperienceHome.vue
│   ├── ExperienceEdit.vue
│   └── ExperienceMaterial.vue
├── template/              # Template management
│   ├── components/       # Template specific components
│   ├── TemplateManage.vue
│   └── TemplateSquare.vue
├── device/               # Device management
│   ├── components/       # Device specific components
│   ├── DeviceList.vue
│   └── details/
│       └── DeviceDetails.vue
└── material/             # Material management
    ├── components/       # Material specific components
    ├── SourceMaterial.vue
    ├── DefaultMaterial.vue
    └── UploadSource.vue
```

## Guidelines

1. Each feature module should have its own directory
2. Module-specific components should be placed in the `components` subdirectory of that module
3. Complex views can have their own subdirectories (e.g. `device/details/`)
4. Shared components that are used across multiple modules should remain in the root `components` directory 