<template>
  <div class="progress-box-container">
    <span class="progress-box">
      <span class="progress">
        <span>{{ titleText }}</span>
        <span class="progress-num">
          {{ sceneStorage[0] }}{{ showSingle ? '' : '/' }}{{ showSingle ? '' : sceneStorage[1] }}
        </span>
        <span>{{ unitText }}</span>
      </span>
      <span class="progress-style">
        <span
          :style="{
            width: Math.min(sceneStorage[0] / (sceneStorage[1] || 1), 1) * 100 + '%',
            background: color,
          }"></span>
      </span>
    </span>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';
const props = defineProps({
  sceneStorage: {
    default: [],
    type: Object,
  },
  color: {
    default: '#639EFF',
    type: String,
  },
  titleText: {
    default: '',
    type: String,
  },
  unitText: {
    default: '',
    type: String,
  },
  showSingle: {
    default: false,
    type: Boolean,
  },
});
</script>

<style scoped lang="less">
.progress-box-container {
  display: flex;
  align-items: center;
}

.progress-box {
  position: relative;
  display: inline-block;
  width: auto;
  line-height: 0.4;

  & > span {
    vertical-align: middle;
  }

  .progress {
    font-size: 12px;
    color: #1e1e1e;
    line-height: 18px;

    .progress-num {
      font-weight: bold;
      font-size: 12px;
      color: #2e76ff;
      line-height: 18px;
      margin: 0 4px 0 8px;
    }
  }

  .progress-style {
    position: relative;
    width: 100%;
    height: 4px;
    border-radius: 11px;
    background-color: #d9d9d9;
    display: inline-block;
    overflow: hidden;

    & > span {
      position: absolute;
      left: 0;
      top: 0;
      width: 0;
      height: 100%;
      display: inline-block;
    }
  }
}
</style>
