<template>
    <div class="behavior-tree" v-if="isShow && !isTempMask">
        <div class="interactive-data temp">
            <div class="flex-double">
                <div class="info-title">
                    <img src="@/assets/images/icon/list2.png" />
                    <span>交互行为</span>
                </div>
                <img src="@/assets/images/icon/add-event.png" class="img" @click="addBehavior" alt="">
            </div>
        </div>
        <header v-if="showTitle" :class="{ newHead: !behaviors.length }">
            <div class="trigger" v-if="behaviors.length">
                <div class="title">{{ currentData ? '当前对象' : '进入' }}</div>
                <div class="main" style="font-weight: 700;">{{ eventTip }}</div>
            </div>
        </header>
        <main class="item" v-for="(item, i) in behaviors" :key="i">
            <div class="item__ active-type" v-if="item.elementId">
                <div class="lt">
                    <div class="title">触发状态</div>
                    <div class="main">
                        <el-select v-model="item.elmentStatusData" class="select-default" popper-class="select-option"
                            :suffix-icon="DropDown">
                            <el-option v-for="el in item.triggerCondites" :key="el.value" :label="el.label"
                                :value="el.value" />
                        </el-select>
                    </div>
                </div>
                <div class="rt" v-if="item.elementId">
                    <el-button size="small" class="save_" type="primary" :disabled="!item.effectElementId"
                        style="margin-right: 6px;" @click="saveBehavior(item, i)">存</el-button>
                    <el-button size="small" class="save_" type="danger" @click="removeBehavior(item, i)">删</el-button>
                </div>
            </div>
            <div class="item__">
                <div class="lt">
                    <div class="title">影响对象</div>
                    <div class="main">
                        <el-select v-if="!item.effectType" v-model="item.effectType" clearable class="select-default"
                            @change="(val) => selectImpactType(val, item)" popper-class="select-option"
                            :suffix-icon="DropDown">
                            <el-option v-for="el in materialType" :key="el.value" :label="el.name" :value="el.value" />
                        </el-select>
                        <span v-else style="margin-left: 2px;">{{ materialType[item.effectType - 1]['name']
                        }}</span>
                    </div>
                </div>
                <div class="rt" v-if="!item.elementId">
                    <el-button size="small" class="save_" type="primary" :disabled="!item.effectElementId"
                        style="margin-right: 6px;" @click="saveBehavior(item, i)">存</el-button>
                    <el-button size="small" class="save_" type="danger" @click="removeBehavior(item, i)">删</el-button>
                </div>
            </div>
            <div class="assets_list">
                <div style="min-width: 56px; margin-right: 7px;"></div>
                <div style="width: 80%;">
                    <el-select v-model="item.effectElementId" v-if="item.effectType && item.assetses.length" clearable
                        class="select-default" popper-class="select-option" :suffix-icon="DropDown"
                        @visible-change="(val) => visibleChange(val, item)">
                        <el-option v-for="el in item.assetses" :key="el.id"
                            :label="el.elementName || el.materialDto.materialName" :value="el.id" />
                    </el-select>
                    <button type="danger" disabled class="button"
                        v-if="item.effectType && isTempLoading && !item.assetses.length"
                        style="font-size: 13px; width: 100% !important; height: 100%;">暂无素材哦！</button>
                </div>
            </div>
            <div class="item__">
                <div class="lt">
                    <div class="title">对象状态</div>
                    <div class="main">
                        <el-select ref="selectRef" :disabled="!item.assetses.length"
                            v-model="item.effectElmentBehaviorData" class="select-default" popper-class="select-option"
                            :suffix-icon="DropDown">
                            <el-option v-for="el in item.effectCondites" :key="el.value" :label="el.label"
                                :value="el.value" />
                        </el-select>
                    </div>
                </div>
                <div class="rt"></div>
            </div>
        </main>
    </div>
    <tips-view-v2 show-btn="确定" tips-title="删除交互行为" :sure-event="deleteBev" :cancleEvent="cancleEvent"
        :isOpenTip="isDeleteTips"></tips-view-v2>
</template>


<script lang="ts" setup>
// @ts-nocheck
import { onMounted, ref, onUnmounted, watch, reactive, computed, defineProps, defineEmits, nextTick, defineExpose } from 'vue'
import { getMaterialScene, addElementBehavior, addSceneStartBehavior, getSeceneStartBehaviorList, getElmentBehavior, updateBehavior, deleteBehavior, queryBehaviorExist } from '@/api/index'
import { ElMessage, ElMessageBox, ElCard } from "element-plus";
import DropDown from '@/components/DropDown.vue'
import { materialType, conditeMap } from '@/config'
import { useRouter } from 'vue-router'
import TipsViewV2 from './TipsViewV2.vue';
import { useStore } from "vuex";
const props = defineProps({
    isShow: {
        default: true,
        type: Boolean
    }
})
const store = useStore();
const currentData = store.state.editSceneData;
const showTitle = ref(true)
const behaviors: any = ref([])
const isTempLoading = ref(false)
const router = useRouter()
const sceneName = ref('')
const isDeleteTips = ref(false)
const selectRef = ref()
const removeItem = ref()
const removeIndex = ref(0)
const isTempMask = ref(false) // temp..


const selectImpactType = async (val, item) => {
    let assetsTemp = []
    const effectType = materialType[val - 1]['desc']
    item.effectCondites = conditeMap[effectType]['effectCondites']
    if (!currentData.value) {
        item.effectCondites = item.effectCondites.filter(effect => !effect.isMutex)
    }

    item.effectElmentBehaviorData = '0'

    item.newUpdateEffectTip = val

    assetsTemp = await requestGetMaterialScene(val)
    item.assetses = assetsTemp.filter(asset => asset.materialDto.materialName != '遮罩')
    nextTick(() => {
        isTempLoading.value = true
    })
    if (item.assetses.length) item.effectElementId = item.assetses[0].id
}



watch(() => store.state.editSceneData, (newState) => {
    if (newState) {
        sceneName.value = newState.sceneName
    }
}, { deep: true })


const visibleChange = async (val, item) => {
    if (val) {
        const tmepType = item.newUpdateEffectTip || item.effectType
        item.assetses = await requestGetMaterialScene(tmepType)
        item.assetses = item.assetses.filter(asset => {
            return asset.materialDto && asset.materialDto.materialName != '遮罩'
        })
    }
}

// 获取素材列表
const requestGetMaterialScene = (val) => {
    let interactionId = ''
    const pageQuery: any = router.currentRoute.value.query;
    const sceneId = pageQuery.sceneid || '';
    const activeAreaUuid = store.state.activeAreaUuid
    if (activeAreaUuid && store.state.editSceneData.interactionDtoList) {
        const activeInter = store.state.editSceneData.interactionDtoList.find(inter => inter.uuid == activeAreaUuid)
        if (activeInter) {
            interactionId = activeInter.id
        }
    }
    return new Promise(resolve => {
        const params = {
            sceneId: sceneId,
            materialType: val,
            interactionId
        }
        getMaterialScene(params).then(res => {
            if (res.code == '200' && res.data) {
                resolve(res.data)
            }
        })
    })
}



const addBehavior = () => {
    showTitle.value = true
    const triggerType = currentData.value ? materialType[currentData.value.materialType - 1]['desc'] : 'model'
    behaviors.value.unshift({
        effectType: '',
        elementId: currentData.value ? currentData.value.id : '',
        elmentStatusData: currentData.value ? conditeMap[triggerType]['triggerCondites'][0]['value'] : '',
        triggerCondites: conditeMap[triggerType]['triggerCondites'],
        assetses: [],
        effectElementId: '',
        effectCondites: [],
        behaviorType: currentData.value ? 1 : 2
    })
}

const eventTip = computed(() => {
    return currentData.value ? currentData.value.materialName : sceneName.value
})

const openAssetsCard = () => {
    isTempLoading.value = !isTempLoading.value
}

const retrievalRepeat = (item, i) => {
    const configFilterType = {
        1: [['0', '1', '2', '3'], ['4', '5', '6']],
        2: [['0', '1', '2', '3']],
        3: [['0', '1', '2']],
        4: [['0', '1', '2'], ['3', '4', '5', '6']],
        5: [['0', '1', '2']]
    }
    const _behavs = behaviors.value.filter((o, x) => i != x)
    const oldEffectElement = _behavs.find(behav => behav.effectElementId == item.effectElementId)
    if (oldEffectElement) {
        const oldEffectTypeGroup = configFilterType[item.effectType].find(effectGroup => effectGroup.includes(oldEffectElement.effectElmentBehaviorData))
        return oldEffectElement.elmentStatusData == item.elmentStatusData && oldEffectTypeGroup.includes(item.effectElmentBehaviorData)
    }
    return oldEffectElement
}

const saveBehavior = async (item, i) => {
    if (!item.effectElementId) return // 无素材，保存按钮禁用
    // const haveBehav = await requestQueryBehaviorExist(item)
    if (retrievalRepeat(item, i)) return ElMessage({ type: 'warning', message: '已存在相同行为！' })
    if (!item.id) { // 添加
        if (item.elementId) { // 素材
            item['id'] = await requestSaveAssetBehavior(item)
        } else { // 场景
            item['id'] = await requestSaveSceneBehavior(item)
        }
    } else { // 更新
        await requestUpdateBehavior(item)
    }
    ElMessage({ type: 'success', message: '保存成功！' })
}


const requestQueryBehaviorExist = (item) => {
    return new Promise(resolve => {
        const params = {
            elementId: item['elementId'],
            elmentStatusData: item['elmentStatusData'],
            effectElementId: item['effectElementId'],
            effectElmentBehaviorData: item['effectElmentBehaviorData'],
            effectElmentBehavior: item['effectType']
        }
        queryBehaviorExist(params).then(res => {
            if (res.code == 200) {
                resolve(res.data) // false可以添加，true则重复
            }
        })
    })
}

const deleteBev = async () => {
    if (removeItem.value.id) {
        await requestRemoveBehavior(removeItem.value.id)
    }
    behaviors.value.splice(removeIndex.value, 1)
    store.state.showTipsV2 = '';
    isDeleteTips.value = false;
}

// 删除某个行为
const removeBehavior = async (item, i) => {
    removeIndex.value = i
    removeItem.value = item
    store.state.showTipsV2 = '确定要删除当前交互行为，删除后不可恢复';
    isDeleteTips.value = true
}

const cancleEvent = () => {
    isDeleteTips.value = false
    store.state.showTipsV2 = ''
}


// 给场景添加行为数组
const requestSaveSceneBehavior = (item) => {
    const pageQuery: any = router.currentRoute.value.query;
    const sceneId = pageQuery.sceneid || '';
    return new Promise(resolve => {
        const params = {
            elementId: item['elementId'],
            behaviorType: item['behaviorType'],
            elmentStatusData: item['elmentStatusData'],
            effectElementId: item['effectElementId'],
            effectElmentBehaviorData: item['effectElmentBehaviorData'],
            effectElmentBehavior: item['effectType']
        }
        addSceneStartBehavior(params, sceneId).then(res => {
            if (res.code == '200') {
                resolve(res.data)
            }
        })
    })
}

// 给素材添加行为数组
const requestSaveAssetBehavior = (item) => {
    const pageQuery: any = router.currentRoute.value.query;
    const sceneId = pageQuery.sceneid || '';
    return new Promise(resolve => {
        const params = {
            sceneId: sceneId,
            behaviorType: item['behaviorType'],
            elementId: item['elementId'],
            elmentStatusData: item['elmentStatusData'],
            effectElementId: item['effectElementId'],
            effectElmentBehaviorData: item['effectElmentBehaviorData'],
            effectElmentBehavior: item['effectType']
        }
        addElementBehavior(params).then(res => {
            if (res.code == '200') {
                resolve(res.data)
            }
        })
    })
}


// 获取行为数组
const requestGetBehavior = (nv) => {
    const pageQuery: any = router.currentRoute.value.query;
    const sceneId = pageQuery.sceneid || '';
    return new Promise(resolve => {
        const params = {
            elementId: currentData.value ? currentData.value.id : '',
            sceneId: sceneId
        }
        behaviors.value = []
        const requestGet = nv ? getElmentBehavior : getSeceneStartBehaviorList
        requestGet(params).then(async res => {
            if (res.code == 200) {
                const promiseResults = res.data.map(async (item) => {
                    const assetses = await requestGetMaterialScene(item.effectElmentBehavior);
                    return {
                        id: item.id,
                        behaviorType: item.behaviorType,
                        effectType: item.effectElmentBehavior,
                        elementId: item.elementId,
                        elmentStatusData: item.elmentStatusData,
                        triggerCondites: item.elmentStatusData ? conditeMap[materialType[item.effectElementMaterialInfo.materialType - 1]['desc']]['triggerCondites'] : [],
                        assetses,
                        effectElementId: item.effectElementId,
                        effectCondites: conditeMap[materialType[item.effectElmentBehavior - 1]['desc']]['effectCondites'],
                        effectElmentBehaviorData: item.effectElmentBehaviorData
                    };
                });
                behaviors.value = await Promise.all(promiseResults);
                behaviors.value.reverse()
            }
        })
    })
}

// 更新某个行为
const requestUpdateBehavior = (item) => {
    const pageQuery: any = router.currentRoute.value.query;
    const sceneId = pageQuery.sceneid || '';
    return new Promise(resolve => {
        const params = {
            id: item.id,
            behaviorType: item.behaviorType, // 后端给的type，只在更新接口用
            elementId: item['elementId'],
            elmentStatusData: item['elmentStatusData'],
            effectElementId: item['effectElementId'],
            effectElmentBehaviorData: item['effectElmentBehaviorData'],
            effectElmentBehavior: item['effectType'],
            sceneId: sceneId
        }
        updateBehavior(params).then(res => {
            if (res.code == '200') {
                resolve()
            }
        })
    })
}

// 删除某个行为
const requestRemoveBehavior = (id) => {
    return new Promise(resolve => {
        const params = {
            id
        }
        deleteBehavior(params).then(res => {
            if (res.code == '200') {
                resolve()
            }
        })
    })
}

watch(() => currentData.value, (nv) => {
    if (nv && nv.materialName == '遮罩') {
        isTempMask.value = true
        return
    }
    isTempMask.value = false
    requestGetBehavior(nv)
}, { immediate: true })


onMounted(() => {

})


</script>
<style scoped lang="less">
.behavior-tree {
    :deep(.el-input__inner) {
        font-size: 14px !important;
    }

    :deep(.el-input--suffix) {
        height: 32px !important;
    }

    :deep(.el-input__wrapper) {
        border-radius: 0 !important;
    }
}

.newHead {
    padding-bottom: 0 !important;
}

.rt {
    display: flex;
}

.card {
    width: 200px;
    height: 300px;
}

.save_ {
    font-size: 12px;
    padding: 2px 6px;
    margin-left: 0 !important;
}

.button {
    width: 100%;
    border-radius: 4px;
    background: #fab6b6;
    color: #fff;
    border-color: #fab6b6;
    border: none;
}

.assets_list {
    display: flex;
    margin-bottom: 10px;
    transform: translateY(-5px);
    height: 32px;
    width: 71%;
}

.interactive-data {
    padding: 20px 16px;
    box-sizing: border-box;
    padding-bottom: 6px;

    .flex-double {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .img {
            width: 30px;
            height: 30px;
            transform: translateY(-5px);
            cursor: pointer;
        }
    }
}

.info-title {
    font-size: 12px;
    color: #000000;
    text-align: left;
    margin-bottom: 15px;
    height: 24px;
    line-height: 24px;
    position: relative;

    &>span {
        vertical-align: middle;
    }

    &>img {
        vertical-align: middle;
        margin-right: 6px;
        width: 24px;
        height: 24px;
    }
}

.button {
    width: 150px;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 12px;
    padding-bottom: 14px;
    border-bottom: 1px solid #c8c8c8;

    .trigger {
        display: flex;
        align-items: center;
        font-size: 14px;

        .title {
            margin-right: 12px;
        }
    }
}

main {
    display: flex;
    flex-direction: column;
    padding: 0 12px;
    padding-top: 14px;
    border-bottom: 1px solid #c8c8c8;

    .item__ {
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 14px;
        justify-content: space-between;
    }

    .lt {
        display: flex;
        align-items: center;
        font-size: 14px;
        width: 71%;

        .main {
            width: 80%;
        }
    }

    .change-image {
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    .title {
        margin-right: 12px;
        margin-right: 7px;
        min-width: 56px;
    }
}

.select-default {
    height: 34px;
}
</style>