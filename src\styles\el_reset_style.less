/* 给全局按钮设置样式颜色 */
.btn-primary {
  background: transparent !important;
  border-radius: 4px;
}

.btn-primary .el-button {
  background: #2e76ff !important;
  border-radius: 4px !important;
  color: #ffffff !important;
  border: none;
  width: 100%;

  &:hover {
    box-shadow: none;
    background-color: #1251c8 !important;
  }

  // &:active {
  //   background: #2c66db;
  //   box-shadow: 0px 5px 10px 0px rgba(35, 108, 255, 0.5);
  // }
}

.btn-danger .el-button {
  background: #e84a4b;
  box-shadow: 0px 5px 10px 0px rgba(232, 74, 75, 0.5);
  border-radius: 4px !important;
  color: #ffffff !important;
  border: none;
  width: 100%;
  height: 100%;

  &:hover {
    background: #e84a4b;
    box-shadow: 0px 0px 4px 4px rgba(255, 35, 35, 0.3);
  }

  &:active {
    background: #cf4243;
    box-shadow: 0px 5px 10px 0px rgba(255, 35, 35, 0.5);
  }
}

.btn-default .el-button {
  background: transparent !important;
  border-radius: 4px !important;
  border: 1px solid #dadada !important;
  color: #797979 !important;
  width: 100%;
  height: 100%;

  &:hover {
    box-shadow: none;
    background: rgba(0, 96, 255, 0.06) !important;
    border-color: rgba(218, 218, 218, 1) !important;
  }
  // &:hover {
  //   background: #f7f8fa;
  //   box-shadow: 0px 0px 4px 4px rgba(185, 203, 225, 0.3);
  // }

  // &:active {
  //   background: #edeff2;
  //   box-shadow: 0px 5px 10px 0px rgba(185, 203, 225, 0.5);
  // }
}

.btn-reset {
  background: transparent !important;
  border-radius: 4px;
  box-sizing: border-box;

  .el-button {
    background: transparent !important;
    border-radius: 4px !important;
    border: 1px solid #dadada !important;
    color: #797979 !important;
    width: 100%;
    height: 100%;

    &:hover {
      box-shadow: none;
      background: rgba(0, 96, 255, 0.06) !important;
      border-color: rgba(218, 218, 218, 1) !important;
    }
  }
}

/* 单选 */
.el-radio.el-radio--large {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 10px;
  margin-bottom: 20px;
}

/* 分页器 */
.pagination .el-pagination {
  .el-pager > li {
    width: 30px;
    min-width: 30px;
    height: 30px;
    border-radius: 8px;
    background: transparent !important;
    color: #1e1e1e !important;

    &.is-active {
      font-weight: 400;
      background-color: #2e76ff !important;
      color: #fff !important;
    }
  }

  .el-pager > li:not(.is-active):hover {
    background: #f5f5f5 !important;
  }

  .btn-prev,
  .btn-next {
    position: relative;
    width: 90px;
    height: 30px;
    background: #f5f5f5;
    border-radius: 8px;
    color: #1e1e1e !important;
    padding-left: 24px;
    box-sizing: border-box;
    font-family: 'iconfont' !important;
    font-size: 14px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    &:disabled {
      background-color: transparent !important;
      color: #757575 !important;
    }
  }

  .btn-next {
    padding-left: 0;
    padding-right: 24px;

    &::after {
      content: '\e600';
      color: #1e1e1e;
      font-size: 14px;
      position: absolute;
      right: 10px;
      top: 8px;
      line-height: 1;
    }

    &:disabled::after {
      color: #757575;
    }
  }

  .btn-prev {
    &::before {
      content: '\e75e';
      color: #1e1e1e;
      font-size: 14px;
      position: absolute;
      left: 10px;
      top: 8px;
      line-height: 1;
    }

    &:disabled::before {
      color: #757575;
    }
  }
}

.home .table-module {
  margin: 0 !important;
}

/* 自制删除弹出框 */
.popover-style {
  position: relative;
  padding: 8px;
  width: 208px;
  background: #fff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid #ebf0fc;
  position: absolute;
  z-index: 10;

  & > div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 16px;
    margin: 4px 0;
    font-weight: 400;
    font-size: 14px;
    color: #1e1e1e;
    text-align: left;
  }
}

/* 下拉框 */
.select-default {
  position: relative;
  font-size: 16px;
  font-weight: 400;
  color: #0f0f0f;
  // height: 42px;
}

.select-default .select-trigger.el-tooltip__trigger.el-tooltip__trigger {
  height: 100%;
  & > .el-input.el-input--suffix {
    height: 100%;
  }
}

.select-option {
  background: #ffffff;
  border-radius: 4px !important;

  .el-select-dropdown__item.selected {
    color: #3370ff;
    font-weight: 400;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background: #f5f7fc;
  }
}
.search-source .el-input__wrapper {
  border-radius: 4px !important;
  width: 232px;
  max-width: 232px;
  height: 32px;
  box-sizing: border-box;
  font-size: 14px;
}

#app .el-button {
  border-radius: 4px !important;
  font-family: 'PingFang SC', 'Lantinghei SC', 'Microsoft YaHei', 'HanHei SC', 'Helvetica Neue',
    'Open Sans', 'Hiragino Sans GB', '微软雅黑', STHeiti, 'WenQuanYi Micro Hei', Arial, SimSun,
    sans-serif;
}

#app .el-input__wrapper {
  // height: 42px;
  border-radius: 4px;
  border: 1px solid #dadada;
  box-shadow: none !important;
  box-sizing: border-box;
  padding: 0 12px;
}

#app input {
  font-family: 'PingFang SC', 'Lantinghei SC', 'Microsoft YaHei', 'HanHei SC', 'Helvetica Neue',
    'Open Sans', 'Hiragino Sans GB', '微软雅黑', STHeiti, 'WenQuanYi Micro Hei', Arial, SimSun,
    sans-serif;
}

#app .el-select .el-input__wrapper {
  width: 226px;
  // height: 42px;
  border-radius: 4px;
  border: 1px solid #dadada;
  box-shadow: none !important;
  box-sizing: border-box;
  padding: 0 12px;
}

#app .scene-info-list .el-select .el-input__wrapper {
  width: 212px;
}

#app .scene-info-list .select-box .el-select .el-input__wrapper {
  width: 148px;
}

#app .customer-style .modal-form .el-form-item {
  margin: 0 0 12px;
}

#app .modal-form {
  .el-form-item {
    margin-top: 12px;
    margin-bottom: 15px;
  }

  .el-input__wrapper {
    height: 34px;
  }

  input {
    font-size: 14px;
  }
}

#app .is-error .el-input__wrapper {
  border: 1px solid #dd5a5a;
}

#app .el-select__wrapper {
  border-radius: 4px;
  height: 34px;

  .el-select__placeholder {
    color: #0f0f0f !important;
  }

  .el-select__placeholder.is-transparent {
    // color: #a8abb2 !important;
  }
}

.el-form-item.is-error .el-input__wrapper {
  border: 1px solid #dd5a5a;
}

#app .object-flex-box .el-input__wrapper {
  padding: 0;
  height: 24px;
  border: none;

  .el-input__inner {
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 1200px) {
  .search-source .el-input__wrapper {
    width: 182px;
    max-width: 182px;
    height: 32px;
    font-size: 13px;
  }
  .info-list .el-input__wrapper {
    padding: 1px 5px;
  }
  .select-default {
    width: 160px;
  }
}
