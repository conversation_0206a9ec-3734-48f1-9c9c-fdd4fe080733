<template>
  <el-dialog
    v-model="dialogVisible"
    width="1200px"
    :close-on-click-modal="false"
    class="apply-invoice-dialog"
    title="发票管理">
    <div class="dialog-content">
      <!-- 左侧表格区域 -->
      <div class="left-section">
        <span style="position: relative; top: -8px; color: #1e1e1e">选择开具金额</span>

        <CommonTable
          :data="orderTableData"
          :columns="orderColumns"
          :loading="tableLoading"
          :total="tableTotal"
          :current-page="currentPage"
          :page-size="pageSize"
          :min-height="500"
          @current-change="handlePageChange"
          @size-change="handleSizeChange" />
      </div>

      <!-- 右侧表单区域 -->
      <div class="right-section">
        <span style="position: relative; top: -8px; color: #1e1e1e">申请发票</span>
        <div class="form-container">
          <!-- Tab切换 -->
          <TabsBar v-model="activeTab" :tabs="tabsList" :bottom-margin="0" />

          <!-- 申请发票表单 -->
          <el-form
            ref="formRef"
            :model="invoiceForm"
            :rules="formRules"
            label-width="120px"
            class="invoice-form">
            <!-- 个人发票类型显示 -->
            <el-form-item v-if="invoiceForm.type === 'personal'" label="发票类型:">
              <div class="content-display">电子普票</div>
            </el-form-item>

            <!-- 企业发票种类选择 -->
            <el-form-item v-if="invoiceForm.type === 'company'" label="发票类型:">
              <el-radio-group v-model="invoiceForm.invoiceType">
                <el-radio value="electronic">电子普票</el-radio>
                <el-radio value="special">电子专票</el-radio>
              </el-radio-group>
            </el-form-item>
            <div
              v-if="
                !userMajorInfo?.billTitle &&
                invoiceForm.type === 'company' &&
                invoiceForm.invoiceType === 'special'
              "
              class="box-yellow">
              电子专票：公司开票信息一经录入，后续不可修改， 请确认相关信息
            </div>
            <!-- 发票抬头 -->
            <el-form-item v-if="invoiceForm.type === 'company'" label="发票抬头:" prop="title">
              <div v-if="shouldShowMajorInfo" class="content-display">
                {{ userMajorInfo.billTitle }}
              </div>
              <el-input
                v-else
                v-model="invoiceForm.title"
                placeholder="请输入企业抬头"
                :maxlength="100"
                clearable
                @blur="handleInputTrim('title')" />
            </el-form-item>

            <!-- 纳税人识别号 -->
            <el-form-item v-if="invoiceForm.type === 'company'" label="纳税人识别号:" prop="taxId">
              <div v-if="shouldShowMajorInfo" class="content-display">
                {{ userMajorInfo.taxpayerNum }}
              </div>
              <el-input
                v-else
                v-model="invoiceForm.taxId"
                placeholder="请输入纳税人识别号"
                :maxlength="50"
                clearable
                @blur="handleInputTrim('taxId')" />
            </el-form-item>

            <!-- 开票金额 -->
            <el-form-item label="开票金额:">
              <div class="amount-display">¥{{ invoiceForm.amount.toFixed(2) }}</div>
            </el-form-item>

            <!-- 发票内容 -->
            <el-form-item label="发票内容:">
              <div class="content-display">信息技术服务</div>
            </el-form-item>

            <!-- 注册地址 -->
            <el-form-item v-if="invoiceForm.type === 'company'" label="注册地址:" prop="address">
              <div v-if="shouldShowMajorInfo" class="content-display">
                {{ userMajorInfo.registeredAddress }}
              </div>
              <el-input
                v-else
                v-model="invoiceForm.address"
                placeholder="请输入注册地址"
                :maxlength="200"
                clearable
                @blur="handleInputTrim('address')" />
            </el-form-item>

            <!-- 注册电话 -->
            <el-form-item v-if="invoiceForm.type === 'company'" label="注册电话:" prop="phone">
              <div v-if="shouldShowMajorInfo" class="content-display">
                {{ userMajorInfo.registeredPhone }}
              </div>
              <el-input
                v-else
                v-model="invoiceForm.phone"
                placeholder="请输入注册电话"
                :maxlength="20"
                clearable
                @blur="handleInputTrim('phone')" />
            </el-form-item>

            <!-- 开户银行 -->
            <el-form-item v-if="invoiceForm.type === 'company'" label="开户银行:" prop="bank">
              <div v-if="shouldShowMajorInfo" class="content-display">
                {{ userMajorInfo.billBank }}
              </div>
              <el-input
                v-else
                v-model="invoiceForm.bank"
                placeholder="请输入开户银行"
                :maxlength="100"
                clearable
                @blur="handleInputTrim('bank')" />
            </el-form-item>

            <!-- 银行账号 -->
            <el-form-item
              v-if="invoiceForm.type === 'company'"
              label="银行账号:"
              prop="bankAccount">
              <div v-if="shouldShowMajorInfo" class="content-display">
                {{ userMajorInfo.bankNum }}
              </div>
              <el-input
                v-else
                v-model="invoiceForm.bankAccount"
                placeholder="请输入银行账号"
                :maxlength="30"
                clearable
                @blur="handleInputTrim('bankAccount')" />
            </el-form-item>

            <!-- 备注信息 -->
            <el-form-item label="备注信息:" prop="remark">
              <el-input
                v-model="invoiceForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
                :maxlength="200"
                show-word-limit
                @blur="handleInputTrim('remark')" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">提交</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 确认开具对话框 -->
  <el-dialog
    v-model="showConfirmDialog"
    title="确认开具"
    width="480px"
    :close-on-click-modal="false"
    :show-close="false"
    class="confirm-dialog">
    <div class="confirm-content">
      专票信息公司开票信息一径录入，后续不可修改，请确认相关信息
      <div style="color: #909399">（提交后预计于3-7个工作日内出具发票）</div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showConfirmDialog = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmSubmit" :loading="submitting">
          确认提交
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue';
import { ElMessage, ElCheckbox, ElDialog, ElButton } from 'element-plus';
import { getUserMajor, getNotOpenBill, applyBill } from '@/api';
import CommonTable from '@/components/CommonTable.vue';
import TabsBar from '@/components/TabsBar.vue';
import dayjs from 'dayjs';

// Props
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  amount: {
    type: Number,
    default: 0,
  },
});

// Emits
const emit = defineEmits(['update:show', 'success']);

// 用户专票信息类型定义
interface UserMajorInfo {
  billTitle?: string;
  taxpayerNum?: string;
  registeredAddress?: string;
  registeredPhone?: string;
  billBank?: string;
  bankNum?: string;
}

// 响应式数据
const dialogVisible = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val),
});

const submitting = ref(false);
const formRef = ref();
const userMajorInfo = ref<UserMajorInfo | null>(null); // 存储用户专票信息
const showConfirmDialog = ref(false); // 确认对话框显示状态

// 判断是否显示专票信息回显
const shouldShowMajorInfo = computed(() => {
  return (
    userMajorInfo.value?.billTitle &&
    invoiceForm.value.type === 'company' &&
    invoiceForm.value.invoiceType === 'special'
  );
});

// 自定义验证器：检查空白字符
const validateNotEmpty = (rule: any, value: any, callback: any) => {
  if (!value || typeof value !== 'string') {
    callback(new Error(rule.message || '此字段不能为空'));
    return;
  }

  // 检查是否只包含空白字符（空格、制表符、换行符等）
  if (value.trim() === '') {
    callback(new Error(rule.emptyMessage || '此字段不能只包含空格'));
    return;
  }

  callback();
};

// 自定义验证器：检查长度和空白字符
const validateWithLength = (rule: any, value: any, callback: any) => {
  // 先检查空白字符
  if (!value || typeof value !== 'string') {
    callback(new Error(rule.message || '此字段不能为空'));
    return;
  }

  if (value.trim() === '') {
    callback(new Error(rule.emptyMessage || '此字段不能只包含空格'));
    return;
  }

  // 检查长度（使用trim后的值）
  const trimmedValue = value.trim();
  if (rule.max && trimmedValue.length > rule.max) {
    callback(new Error(rule.maxMessage || `此字段不能超过${rule.max}个字符`));
    return;
  }

  callback();
};

// 处理输入框失焦时去除前后空格
const handleInputTrim = (fieldName: keyof typeof invoiceForm.value) => {
  const value = invoiceForm.value[fieldName];
  if (value && typeof value === 'string') {
    (invoiceForm.value[fieldName] as string) = value.trim();
  }
};

// 动态表单验证规则
const formRules = computed(() => {
  // 个人发票不需要验证
  if (invoiceForm.value.type === 'personal') {
    return {};
  }

  // 企业发票
  if (invoiceForm.value.type === 'company') {
    // 如果显示专票信息回显，则不需要验证相关字段
    if (shouldShowMajorInfo.value) {
      return {
        remark: [
          { required: true, message: '请输入备注信息', trigger: 'blur' },
          {
            validator: validateWithLength,
            message: '请输入备注信息',
            emptyMessage: '备注信息不能只包含空格',
            max: 500,
            maxMessage: '备注信息不能超过500个字符',
            trigger: 'blur',
          },
        ],
      };
    }

    const baseRules = {
      title: [
        { required: true, message: '请输入发票抬头', trigger: 'blur' },
        {
          validator: validateWithLength,
          message: '请输入发票抬头',
          emptyMessage: '发票抬头不能只包含空格',
          max: 100,
          maxMessage: '发票抬头不能超过100个字符',
          trigger: 'blur',
        },
      ],
      taxId: [
        { required: true, message: '请输入纳税人识别号', trigger: 'blur' },
        {
          validator: validateWithLength,
          message: '请输入纳税人识别号',
          emptyMessage: '纳税人识别号不能只包含空格',
          max: 50,
          maxMessage: '纳税人识别号不能超过50个字符',
          trigger: 'blur',
        },
      ],
    };

    // 企业电子专票需要验证7个项目
    if (invoiceForm.value.invoiceType === 'special') {
      return {
        ...baseRules,
        address: [
          { required: true, message: '请输入注册地址', trigger: 'blur' },
          {
            validator: validateWithLength,
            message: '请输入注册地址',
            emptyMessage: '注册地址不能只包含空格',
            max: 200,
            maxMessage: '注册地址不能超过200个字符',
            trigger: 'blur',
          },
        ],
        phone: [
          { required: true, message: '请输入注册电话', trigger: 'blur' },
          {
            validator: validateWithLength,
            message: '请输入注册电话',
            emptyMessage: '注册电话不能只包含空格',
            max: 20,
            maxMessage: '注册电话不能超过20个字符',
            trigger: 'blur',
          },
        ],
        bank: [
          { required: true, message: '请输入开户银行', trigger: 'blur' },
          {
            validator: validateWithLength,
            message: '请输入开户银行',
            emptyMessage: '开户银行不能只包含空格',
            max: 100,
            maxMessage: '开户银行不能超过100个字符',
            trigger: 'blur',
          },
        ],
        bankAccount: [
          { required: true, message: '请输入银行账号', trigger: 'blur' },
          {
            validator: validateWithLength,
            message: '请输入银行账号',
            emptyMessage: '银行账号不能只包含空格',
            max: 30,
            maxMessage: '银行账号不能超过30个字符',
            trigger: 'blur',
          },
        ],
        remark: [
          { required: true, message: '请输入备注信息', trigger: 'blur' },
          {
            validator: validateWithLength,
            message: '请输入备注信息',
            emptyMessage: '备注信息不能只包含空格',
            max: 500,
            maxMessage: '备注信息不能超过500个字符',
            trigger: 'blur',
          },
        ],
      };
    }

    // 企业电子普票只验证2个项目
    return baseRules;
  }

  return {};
});

// Tab相关
const activeTab = ref('personal');
const tabsList = ref([
  { label: '个人发票', value: 'personal' },
  { label: '企业发票', value: 'company' },
]);

// 表格相关
const orderTableData = ref<any[]>([]);
const tableLoading = ref(false);
const tableTotal = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const selectedOrders = ref<string[]>([]); // 选中的订单ID

// 表格列配置
const orderColumns = ref([
  {
    prop: 'selection',
    label: '',
    width: 80,
    formatter: (props: any) => {
      return h(ElCheckbox, {
        modelValue: selectedOrders.value.includes(props.row.id),
        onChange: (checked: boolean) => handleOrderSelect(props.row, checked),
      });
    },
    headerFormatter: () => {
      const allSelected = computed(
        () =>
          orderTableData.value.length > 0 &&
          orderTableData.value.every((row) => selectedOrders.value.includes(row.id))
      );
      const someSelected = computed(() => selectedOrders.value.length > 0 && !allSelected.value);

      return h('div', { class: 'select-header' }, [
        h(ElCheckbox, {
          modelValue: allSelected.value,
          indeterminate: someSelected.value,
          onChange: (checked: boolean) => handleSelectAll(checked),
        }),
        h('span', { class: 'select-text' }, '全选'),
      ]);
    },
  },
  {
    prop: 'orderNo',
    label: '订单ID(编号)',
    minWidth: 160,
    formatter: (props: any) => {
      return props.row.orderNo || '-';
    },
  },
  // {
  //   prop: 'energyValue',
  //   label: '能量值',
  //   minWidth: 100,
  //   formatter: (props: any) => {
  //     const energy = props.row.energyValue || 0;
  //     return h('span', {}, new Intl.NumberFormat().format(energy));
  //   },
  // },
  {
    prop: 'amount',
    label: '金额 (¥)',
    minWidth: 120,
    formatter: (props: any) => {
      const amount = props.row.amount || 0;
      return h('span', {}, `¥${(amount / 100).toFixed(2)}`);
    },
  },
  // {
  //   prop: 'payType',
  //   label: '支付方式',
  //   minWidth: 100,
  //   formatter: (props: any) => {
  //     const payTypeMap: Record<string, string> = {
  //       '1': '微信支付',
  //       '2': '支付宝',
  //     };
  //     return payTypeMap[props.row.payType] || props.row.payType || '未知';
  //   },
  // },
  {
    prop: 'orderDate',
    label: '订单日期',
    minWidth: 140,
    formatter: (props: any) => {
      const time = props.row.orderDate || props.row.createTime;
      if (!time) return '-';
      return dayjs(time).format('YYYY-MM-DD HH:mm');
    },
  },
]);

// 发票表单数据
const invoiceForm = ref({
  type: 'personal', // personal | company
  invoiceType: 'electronic', // electronic | special
  title: '',
  taxId: '',
  amount: 0,
  content: '信息技术服务',
  address: '',
  phone: '',
  bank: '',
  bankAccount: '',
  remark: '',
});

// 监听金额变化
watch(
  () => props.amount,
  (newAmount) => {
    invoiceForm.value.amount = newAmount;
  },
  { immediate: true }
);

// 获取用户专票信息
const fetchUserMajor = async () => {
  try {
    const response = await getUserMajor();
    console.log('getUserMajor response:', response);

    if (response.code === '200' || response.code === '2000') {
      const majorData = response.data;
      if (majorData) {
        console.log('用户专票信息:', majorData);
        userMajorInfo.value = majorData;
      }
    }
  } catch (error) {
    console.error('获取用户专票信息失败:', error);
  }
};

// 监听弹窗显示状态，获取订单列表
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      // 重置选择状态和分页
      selectedOrders.value = [];
      currentPage.value = 1;
      // 重置tab状态
      activeTab.value = 'personal';
      fetchOrderList();
      // 获取用户专票信息
      fetchUserMajor();
    }
  }
);

// 监听发票类型切换，清除验证状态
watch(
  () => [invoiceForm.value.type, invoiceForm.value.invoiceType],
  () => {
    // 清除表单验证状态
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  }
);

// 监听Tab切换，同步发票类型
watch(
  () => activeTab.value,
  (newTab) => {
    invoiceForm.value.type = newTab;
  }
);

// 获取订单列表
const fetchOrderList = async () => {
  tableLoading.value = true;
  try {
    const response = await getNotOpenBill();

    if (response.code === '200' || response.code === '2000') {
      const data = response.data;
      orderTableData.value = data;
      tableTotal.value = data.total || 0;
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
  } finally {
    tableLoading.value = false;
  }
};

// 处理订单选择
const handleOrderSelect = (row: any, checked: boolean) => {
  const orderId = row.id;
  if (checked) {
    if (!selectedOrders.value.includes(orderId)) {
      selectedOrders.value.push(orderId);
    }
  } else {
    const index = selectedOrders.value.indexOf(orderId);
    if (index > -1) {
      selectedOrders.value.splice(index, 1);
    }
  }

  // 计算选中订单的总金额
  updateSelectedAmount();
};

// 处理全选
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    // 全选当前页面的所有订单
    selectedOrders.value = orderTableData.value.map((row) => row.id);
  } else {
    // 清空选择
    selectedOrders.value = [];
  }

  // 计算选中订单的总金额
  updateSelectedAmount();
};

// 更新选中订单的总金额
const updateSelectedAmount = () => {
  const selectedOrdersData = orderTableData.value.filter((order) =>
    selectedOrders.value.includes(order.id)
  );

  const totalAmount = selectedOrdersData.reduce((sum, order) => {
    return sum + (order.amount || 0);
  }, 0);

  invoiceForm.value.amount = totalAmount / 100; // 转换为元
};

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchOrderList();
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchOrderList();
};

// 表单验证
const validateForm = async () => {
  // if (selectedOrders.value.length === 0) {
  //   ElMessage.error('请先选择要开票的订单');
  //   return false;
  // }

  // if (invoiceForm.value.amount <= 0) {
  //   ElMessage.error('开票金额必须大于0');
  //   return false;
  // }

  try {
    await formRef.value.validate();
    return true;
  } catch (error) {
    return false;
  }
};

// 提交申请
const handleSubmit = async () => {
  if (!(await validateForm())) return;

  // 检查是否选择了订单
  if (selectedOrders.value.length === 0) {
    ElMessage.error('请选择开具金额');
    return;
  }

  // 判断是否为第一次提交电子专票
  const isFirstTimeSpecialInvoice =
    !userMajorInfo.value?.billTitle &&
    invoiceForm.value.type === 'company' &&
    invoiceForm.value.invoiceType === 'special';

  if (isFirstTimeSpecialInvoice) {
    showConfirmDialog.value = true;
    return;
  }

  // 直接提交
  await submitInvoiceApplication();
};

// 确认提交
const handleConfirmSubmit = async () => {
  showConfirmDialog.value = false;
  await submitInvoiceApplication();
};

// 提交发票申请
const submitInvoiceApplication = async () => {
  submitting.value = true;
  try {
    // 获取选中订单的ID数组 (转换为数字)
    const orderEnergyIds = selectedOrders.value.map((id) => parseInt(id.toString()));

    // 清理表单数据，去除前后空格
    // 如果是电子专票且有专票信息，使用专票信息；否则使用表单输入的信息
    const shouldUseMajorInfo = shouldShowMajorInfo.value;

    const requestData = {
      billTitle: shouldUseMajorInfo
        ? (userMajorInfo.value?.billTitle || '').trim()
        : (invoiceForm.value.title || '').trim(),
      taxpayerNum: shouldUseMajorInfo
        ? (userMajorInfo.value?.taxpayerNum || '').trim()
        : (invoiceForm.value.taxId || '').trim(),
      billContent: (invoiceForm.value.content || '').trim(),
      registeredAddress: shouldUseMajorInfo
        ? (userMajorInfo.value?.registeredAddress || '').trim()
        : (invoiceForm.value.address || '').trim(),
      registeredPhone: shouldUseMajorInfo
        ? (userMajorInfo.value?.registeredPhone || '').trim()
        : (invoiceForm.value.phone || '').trim(),
      billBank: shouldUseMajorInfo
        ? (userMajorInfo.value?.billBank || '').trim()
        : (invoiceForm.value.bank || '').trim(),
      bankNum: shouldUseMajorInfo
        ? (userMajorInfo.value?.bankNum || '').trim()
        : (invoiceForm.value.bankAccount || '').trim(),
      billPersonFirm: invoiceForm.value.type === 'personal' ? 1 : 2, // 1:个人 2:企业
      billType: invoiceForm.value.invoiceType === 'electronic' ? 2 : 1, // 1:电子专票 2:电子普票
      remark: (invoiceForm.value.remark || '').trim(),
      orderEnergyIds: orderEnergyIds,
      operationType: 1, // 操作类型：1-申请
    };

    console.log('提交发票申请数据:', requestData);
    console.log('是否使用专票信息:', shouldUseMajorInfo);
    console.log('用户专票信息:', userMajorInfo.value);

    const response = await applyBill(requestData);

    if (response.code === '200' || response.code === '2000') {
      ElMessage.success('发票申请提交成功');
      emit('success');
      handleCancel();
    } else {
      ElMessage.error(response.message || '申请失败，请稍后重试');
    }
  } catch (error) {
    console.error('申请发票失败:', error);
    ElMessage.error('申请失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false;
  // 重置表单和选择状态
  selectedOrders.value = [];
  invoiceForm.value = {
    type: 'personal',
    invoiceType: 'electronic',
    title: '',
    taxId: '',
    amount: 0,
    content: '信息技术服务',
    address: '',
    phone: '',
    bank: '',
    bankAccount: '',
    remark: '',
  };
};
</script>

<style scoped lang="less">
.apply-invoice-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;

    .el-dialog__header {
      padding: 20px 24px 16px;
      border-bottom: 1px solid #e4e7ed;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #1e1e1e;
      }
    }

    .el-dialog__body {
      padding: 24px;
      max-height: 70vh;
      overflow-y: auto;
    }
  }
}

.dialog-content {
  display: flex;
  gap: 24px;

  .left-section {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;

    // 确保表格撑满高度
    :deep(.common-table) {
      flex: 1;
      display: flex;
      flex-direction: column;

      .el-table {
        flex: 1;
      }
    }

    // 表格样式
    :deep(.amount-text) {
      color: #e74c3c;
      font-weight: 500;
    }

    :deep(.energy-text) {
      color: #2e76ff;
      font-weight: 500;
    }

    // 全选表头样式
    :deep(.select-header) {
      display: flex;
      align-items: center;
      gap: 6px;

      .select-text {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        user-select: none;
      }
    }
  }

  .right-section {
    width: 350px;
    flex-shrink: 0;

    .form-container {
      border-radius: 4px 4px 0px 0px;
      background-color: #f5f7fa;
      overflow: hidden;
    }

    .invoice-form {
      padding: 18px;
      .box-yellow {
        width: 100%;
        font-weight: 400;
        font-size: 12px;
        color: #ba1e1e;
        background: #eab308;
        border-radius: 4px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        padding: 8px 15px;
        box-sizing: border-box;
      }
      :deep(.el-form-item) {
        margin-bottom: 16px;

        .el-form-item__label {
          font-size: 13px;
          color: #1e1e1e;
          line-height: 32px;
          text-align: left;
          justify-content: flex-start;
        }

        .el-form-item__content {
          .el-input,
          .el-textarea {
            .el-input__inner,
            .el-textarea__inner {
              font-size: 13px;
            }
          }

          .el-input .el-input__inner {
            height: 32px;
          }

          .amount-display {
            font-size: 13px;
            color: #e74c3c;
            font-weight: 600;
            line-height: 32px;
          }

          .content-display {
            font-size: 13px;
            color: #1e1e1e;
            line-height: 32px;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-radio-group) {
  .el-radio {
    margin-right: 16px;

    .el-radio__label {
      font-size: 13px;
    }
  }
}

:deep(.el-textarea) {
  .el-textarea__inner {
    resize: none;
    font-size: 13px;
    min-height: 60px !important;
  }
}

.confirm-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;

    .el-dialog__header {
      padding: 20px 24px 16px;
      border-bottom: 1px solid #e4e7ed;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #1e1e1e;
      }
    }

    .el-dialog__body {
      padding: 24px;
    }
  }

  .confirm-content {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    text-align: start;
  }
}

// 通用按钮样式 - 应用于所有弹窗的底部按钮
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 4px;

  .el-button {
    width: 92px;
    height: 32px;
    border-radius: 4px;
    font-size: 14px;

    // 取消按钮样式
    &:not(.el-button--primary) {
      background: #ffffff;
      border: 1px solid #d9d9d9;
      color: #666666;

      &:hover {
        border-color: #2e76ff;
        color: #2e76ff;
      }
    }

    // 提交按钮样式
    &.el-button--primary {
      background: #2e76ff;
      border: 1px solid #2e76ff;
      color: #ffffff;

      &:hover {
        background: #1c5dd6;
        border-color: #1c5dd6;
      }

      // 加载状态样式
      &.is-loading {
        background: #2e76ff;
        border-color: #2e76ff;
      }
    }
  }
}
</style>
