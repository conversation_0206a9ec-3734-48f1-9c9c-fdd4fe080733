import { Vector3, Plane, Group } from "three";
import { CPoint, CLine } from "@/core/primitives";
import { CSS2DObject } from "../libs/CSS2DRenderer";
import { getOffsetPoint } from "@/utils";

const D = 10; // 端点离标注线的距离

// 直线标注
export const lineMark = (
  v1: Vector3,
  v2: Vector3,
  normal = new Vector3(0, 1, 0),
  clockwise = true
) => {
  if (!v1 || !v2) return;
  let sign = -1;
  if (!clockwise) sign = 1;
  const [cp1, cp2] = getOffsetPoint(v1, v2, normal, D * sign);

  const pp1 = new CPoint({
    vertex: cp1,
    size: 10,
  });
  const pp2 = new CPoint({
    vertex: cp2,
    size: 10,
  });

  const scene = (window as any).scene;
  const markGroup = scene.getObjectByName("markGroup");

  const lineMarkGroup = new Group();
  lineMarkGroup.name = "markline";
  lineMarkGroup.add(pp1, pp2);

  const cline = new CLine({
    vertexs: [cp1, cp2],
    color: 0x0d5ca7,
    dashed: true,
    dashSize: 1,
    gapSize: 1,
  });
  lineMarkGroup.add(cline);

  const dashCenter =
    cline.geometry.boundingSphere?.center.clone() || new Vector3(0, 0, 0);
  const distance = v1.distanceTo(v2).toFixed(0);

  const div = document.createElement("div");
  div.innerHTML = distance + "cm";
  div.style.padding = "3px 10px";
  div.style.color = "#fff";
  div.style.fontSize = "16px";
  div.style.position = "absolute";
  div.style.backgroundColor = "rgba(25,25,25,0.5)";
  div.style.borderRadius = "5px";
  div.style.left = (-40 - distance.length * 9) / 2 + "px";
  div.style.top = "-12px";
  div.style.pointerEvents = "none"; //避免HTML标签遮挡三维场景的鼠标事件
  const tag = new CSS2DObject(div);
  tag.position.set(dashCenter?.x, dashCenter?.y, dashCenter?.z);
  lineMarkGroup.add(tag);
  markGroup.add(lineMarkGroup);
};
