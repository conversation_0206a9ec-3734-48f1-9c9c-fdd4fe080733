export interface TableRow {
  userStatus?: number;
  id?: string | number;
  adminUserInfo?: {
    packageVersion?: string;
    isOperation?: number;
    id?: string | number;
  };
  userBindPackageDto?: {
    isOperate?: number;
  };
  mail?: string;
  packageStartTimeStr?: string;
  expireTimeStr?: string;
  createTimeStr?: string;
  materialType?: number;
  [key: string]: any;
}

export interface ColumnItem {
  prop: string;
  label: string;
  customType?: string;
  width?: number | string;
  filters?: Array<{ text: string; value: any }>;
  resetKey?: string;
  type?: string;
  url?: [Record<string, any>, string];
  list?: Record<string, string>;
  segm?: boolean;
  spaceType?: string;
  isDeviceTime?: boolean;
}

export interface AsyncConfirmData {
  event?: (data: TableRow, isEdit?: boolean) => void;
  data?: TableRow;
  isEdit?: boolean;
  dymaicStatus?: string;
}

export interface OperationItem {
  label: string | ((row: TableRow) => string);
  key: string;
  show?: (row: TableRow) => boolean;
  disabled?: (row: TableRow) => boolean;
  onClick: (row: TableRow) => void;
}
