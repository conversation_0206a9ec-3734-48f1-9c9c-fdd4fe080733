# Vuex Store 使用指南

## 基本使用

### 在组件中使用

```javascript
import { useStore } from 'vuex';

export default {
  setup() {
    const store = useStore();

    // 获取状态
    const userInfo = store.state.userInfo;
    const currentData = store.state.currentData;

    // 修改状态
    store.dispatch('updateUserInfo', newUserInfo);
    store.dispatch('updateCurrentData', newData);

    // 监听状态变化
    watch(
      () => store.state.userInfo,
      (newVal) => {
        console.log('用户信息更新:', newVal);
      }
    );
  },
};
```

## 主要状态

### 用户相关

- `userInfo` - 用户信息
- `userType` - 用户类型
- `userDto` - 用户 DTO
- `profilePic` - 用户头像

### 编辑相关

- `currentData` - 当前编辑数据
- `sceneId` - 场景 ID
- `interactionId` - 交互 ID
- `editSceneData` - 编辑场景数据

### 权限状态

- `isWorkAuth` - 工作权限
- `saveBehavior` - 保存行为状态

## 常用 Actions

### 用户操作

```javascript
store.dispatch('updateUserInfo', userInfo);
store.dispatch('updateUserType', userType);
store.dispatch('updateProfilePic', picUrl);
```

### 编辑操作

```javascript
store.dispatch('updateCurrentData', data);
store.dispatch('updateSceneId', sceneId);
store.dispatch('updateInteractionId', interactionId);
store.dispatch('toggleSaveBehavior');
```

### 重置操作

```javascript
store.dispatch('resetEditData'); // 重置所有编辑状态
```

## 状态分类

### 基础 UI 状态

- `homeListType` - 首页列表类型
- `activeAreaIndex` - 激活区域索引
- `showTips` - 提示信息

### 功能状态

- `storageData` - 存储数据
- `isOperation` - 操作状态
- `addProjectFlag` - 添加项目标志

### 加载状态

- `isDragLoading` - 拖拽加载
- `isFinishModel` - 模型完成
- `isRequesting` - 请求状态

## 注意事项

1. **命名规范**: 使用 `updateXxx` 形式的 actions
2. **状态访问**: 直接通过 `store.state.xxx` 访问
3. **状态修改**: 必须通过 `store.dispatch('actionName', value)` 修改
4. **响应式**: 使用 `watch` 监听状态变化
