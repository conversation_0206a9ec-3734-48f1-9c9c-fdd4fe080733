<template>
  <div class="canvas-right" :class="hide_side ? 'hide-right-side' : ''">
    <div class="canvas-right-title">{{ showType ? (showType == 1 ? '素材属性' : '互动区域属性') : '公共区域属性' }}</div>
    <div class="scene-info" v-if="showType">
      <!-- 素材的属性 -->
      <div v-if="showType == 1">
        <div class="info-title">
          <img src="@/assets/images/icon/list.png" />
          <span>素材信息 - {{ calcuType(materialAttributeInfo[activeMaterial].materialType) }}</span>
        </div>
        <div class="scene-info-list">
          <div>素材名称</div>
          <div class="info-item">
            <el-input v-model="materialAttributeInfo[activeMaterial].materialName" @input="changeMaterialName"
              @blur="(e) => resetInitName(e, materialAttributeInfo[activeMaterial])" />
          </div>
        </div>
        <!-- 视频 -->
        <div class="scene-info-list" v-if="materialAttributeInfo[activeMaterial]">
          <div>初始状态</div>
          <div>
            <div class="info-item"
              v-show="['1', '3', '4', '5'].includes(materialAttributeInfo[activeMaterial].materialType)">
              <span class="play-switch">
                <span>静态</span>
                <el-switch style="--el-switch-on-color: #2E76FF;--el-switch-off-color: #C2D4FE"
                  v-model="materialAttributeInfo[activeMaterial].isStatic" @change="changeStaticStatus" />
                <span class="box-item">
                  <span><span style="font-weight: bold;">静态</span>：体验项目时，不能缩放或旋转项目内的素材，和交互行为无关</span>
                </span>
              </span>
            </div>
            <div class="info-item" v-if="['1', '2', '4'].includes(materialAttributeInfo[activeMaterial].materialType)">
              <span class="play-switch">
                <span>播放</span>
                <el-switch style="--el-switch-on-color: #2E76FF;--el-switch-off-color: #C2D4FE"
                  v-model="materialAttributeInfo[activeMaterial].autoPlay" @change="changePlayStatus" />
              </span>
              <div v-show="materialAttributeInfo[activeMaterial].autoPlay" class="loop-btn"
                :class="materialAttributeInfo[activeMaterial].cyclePlay ? 'active' : ''" @click="changeCyclePlay">
                <span>循环</span>
                <img v-if="materialAttributeInfo[activeMaterial].cyclePlay" src="@/assets/images/icon/loop-icon.png" />
                <img v-else src="@/assets/images/icon/no-loop-icon.png" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="interactive-data" v-if="showType">
      <div class="info-title">
        <img src="@/assets/images/icon/list.png" />
        <span>交互参数</span>
      </div>
      <div v-for="(item, index) in spaceData" :key="index">
        <div class="info-list">
          <span>{{ item.title }}</span>
          <div>
            <span class="axle">X</span>
            <el-input v-model="item.value.x" type="text" autocomplete="off"
              :formatter="(value: string) => checkNumber(value)" :class="'is' + item.key"
              @input="changeXYZ($event, 'x', item.key)" @blur="(e) => resetZero(e, 'x', item)" />
          </div>
          <div>
            <span class="axle">Y</span>
            <el-input v-model="item.value.y" type="text" autocomplete="off"
              :formatter="(value: string) => checkNumber(value)" :class="'is' + item.key"
              @input="changeXYZ($event, 'y', item.key)" @blur="(e) => resetZero(e, 'y', item)" />
          </div>
          <div>
            <span class="axle">Z</span>
            <el-input v-model="item.value.z" type="text" autocomplete="off"
              :formatter="(value: string) => checkNumber(value)" :class="'is' + item.key"
              @input="changeXYZ($event, 'z', item.key)" @blur="(e) => resetZero(e, 'z', item)" />
          </div>
        </div>
      </div>
    </div>
    <div class="scene-info" v-show="!showType">
      <div v-for="(item, index) in sceneInfoList" :key="index">
        <div v-if="item.type == 2 && sceneInfoList[index - 1]?.type == 1" class="scene-info-line"></div>
        <div v-if="index == 0 || (item.type == 2 && sceneInfoList[index - 1]?.type == 1)" class="info-title">
          <img src="@/assets/images/icon/list.png" />
          <span>{{ index == 0 ? '项目信息' : '空间信息' }}</span>
        </div>
        <div class="scene-info-list"
          v-show="(userType == 1 && ['项目编号'].includes(item.name)) || !['项目编号'].includes(item.name)">
          <div>{{ item.name }}</div>
          <div class="info-item">
            <span v-show="!item.key && item.name != '识别图片'">{{ item.value }}</span>
            <div v-show="item.name == '识别图片'" class="identify-images">
              <img :src="loadedURL || identifyPath" v-if="identifyPath" />
              <span @mousemove="showUploadTips = true" @mouseleave="showUploadTips = false">（上传图片建议）</span>
              <div class="upload-tips">
                <div class="tips-mask"></div>
                <img src="@/assets/images/home/<USER>" @click="showUploadBtn = true" />
              </div>
            </div>
            <el-input v-if="item.key && item.key != 'backgroundMusic' && item.key != 'takePhotoButtonVisible'"
              :class="item.type === 'textarea' ? 'scene-info-dec' : ''" v-model="item.value" :type="item.type || 'text'"
              resize="none" @change="changeValue(item.value, item.key || '')"
              @blur="(e) => initSceneName(e, item)" :maxlength="item.name == '项目名称' ? 20 : 30"/>
            <div class="select-box">
              <el-select v-if="item.key == 'backgroundMusic'" v-model="item.value" clearable
                popper-class="select-option" @change="changeValue(item.value, item.key || '')">
                <el-option v-for="op in audioSelectList" :label="op.elementName || op.materialDto.materialName"
                  :value="op.materialDto.id" :key="op.materialDto.id" />
              </el-select>
            </div>
            <div v-if="item.key == 'backgroundMusic'" class="loop-btn" :class="isLoop ? 'active' : ''"
              @click="isLoop = !isLoop">
              <span>循环</span>
              <img v-if="isLoop" src="@/assets/images/icon/loop-icon.png" />
              <img v-else src="@/assets/images/icon/no-loop-icon.png" />
            </div>
            <span v-if="item.key == 'takePhotoButtonVisible'" class="play-switch">
              <el-switch style="--el-switch-on-color: #2E76FF;--el-switch-off-color: #C2D4FE" v-model="item.value"
                @change="changeValue(item.value, item.key || '')" />
            </span>
          </div>
        </div>
      </div>
    </div>
    <BehaviorTree
      v-if="['1', '2', '8'].includes(sceneType) || (isTriggerAsset && !['1', '2', '8'].includes(sceneType))">
    </BehaviorTree>
  </div>
  <div v-if="!hide_side" class="hide-side" @click="hide_side = true">
    <img src="@/assets/images/icon/hide-side.png" />
  </div>
  <div v-if="hide_side" class="show-side" @click="hide_side = false">
    <img src="@/assets/images/icon/show-side.png" />
  </div>
  <div class="upload-tips-list" v-if="showUploadTips">
    <div class="title">上传图片建议</div>
    <div>1、识别图格式：png/jpg</div>
    <div>2、识别图宽高比：9:16 ~ 16:9之间，尺寸240px ~ 2048px，建议为800px</div>
    <div>3、识别图文件大小：5MB</div>
    <div>4、识别图具有丰富的细节与高对比度，避免大面积纯色与空白，避免重复图案</div>
  </div>
  <TipsView></TipsView>
  <div class="select-upload-method" v-if="showUploadBtn">
    <div class="method-content" v-if="!showMaterialLibrary">
      <div class="close-icon" @click="showUploadBtn = false"></div>
      <div class="tips-title">选择图片</div>
      <div class="tips-content">从素材库中选择图片，或者从本地直接上传图片</div>
      <div class="tips-btn">
        <div @click="showMaterialLibrary = true">我的素材库</div>
        <div>上传本地文件</div>
        <upload-template file-name="item" class-style="upload-btn" :loading="loading" :baseURL="uploadURL" :beforeUpload="beforeUpload" :handleAvatarSuccess="uploadedThumbnail" :handle-change="handleChange"></upload-template>
      </div>
    </div>
    <div class="image-material-library" v-if="showMaterialLibrary"
      :style="{ paddingRight: imgMaterial.length > 10 ? '10px' : '24px' }">
      <div class="library-title">我的素材库 - 图片</div>
      <div class="image-box">
        <div :class="activeMaterialId == item.id ? 'active' : ''" v-for="item in imgMaterial" :key="item.id"
          @click="activeMaterialId = item.id">
          <img :src="item.ossPath" />
          <i v-if="activeMaterialId == item.id"></i>
        </div>
      </div>
      <div class="submit-btns">
        <div @click="handleCancle">取消</div>
        <div @click="handleSure">确定</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, reactive, watch, computed, nextTick } from 'vue';
import { radiansToAngle, desensitizte } from '@/utils';
import { useStore } from "vuex";
import BehaviorTree from '../BehaviorTree.vue';
import TipsView from '@/components/TipsView.vue';
import { sceneTypeMap } from '@/config';
import { useRouter } from 'vue-router';
import { getOssAccessPath, updateWxIdentifyAttachInfo, getMaterialPageByUser, getUserTypeByToken } from '@/api/index';
import { TextureLoader } from "three";
import UploadTemplate from '@/views/template/components/UploadTemplate.vue';

const store = useStore();

const props = defineProps({
  changeSceneValue: {
    default: null,
    type: Function
  },
  changeActiveValue: {
    default: null,
    type: Function
  }
})

const activeMaterialId = ref(-1)
const imgMaterial: any = ref([])
const sceneId = ref(-1)
const showUploadBtn = ref(false)
const baseURL = process.env.NODE_ENV === 'production' ? '/api' : '/api1' // 基础url
const showMaterialLibrary = ref(false)
const picUrl = ref('')
const loading = ref(false)
const uploadURL = ref('') // 上传素材接口地址
const loadedURL = ref('') // 预览素材照片地址
const showUploadTips = ref(false)
const sceneInfoList: any = ref([]) // TODO 真实接数据时候需要加上key
const hide_side = ref(false)
const activeMaterial = ref('')
const showType = ref(0) // 1是素材，2是互动区域
const audioSelectList: any = ref([])
const isLoop = ref(false);
const materialAttributeInfo: any = reactive({})
const materialIdex: any = ref(-1) // 存当前高亮素材位置
const router = useRouter()
const sceneType = ref('');
const identifyPath = ref('');
const userType = ref(0)
const spaceData: any = ref([
  {
    title: '位置',
    key: 'location',
    value: {
      x: 0,
      y: 0,
      z: 0
    }
  },
  {
    title: '旋转',
    key: 'rotation',
    value: {
      x: 0,
      y: 0,
      z: 0
    }
  },
  {
    title: '缩放',
    key: 'scale',
    value: {
      x: 1,
      y: 1,
      z: 1
    }
  }
])

const calcuType = computed(() => {
  return (type) => {
    switch (type) {
      case '1':
        return '视频'
      case '2':
        return '音频'
      case '3':
        return '图片'
      case '4':
        return '模型'
      case '5':
        return '文本'
    }
  }
})
const scenePlatformMap: any = {1: '眼镜端', 2: '移动端', 3: '小程序'};

const checkImageSize = (url: string) => {
  return new Promise(resolve => {
    const img = new Image();
    img.onload = function () {
      console.log(`图片宽度: ${img.width}, 图片高度: ${img.height}`);
      resolve({ width: img.width, height: img.height })
    };
    img.onerror = function () {
      console.error('图片加载失败');
    };
    img.src = url; // 设置图片 URL
  })
}

const handleSure = () => {
  const activeData = imgMaterial.value.filter((e: any) => e.id == activeMaterialId.value)[0];
  if (activeData) {
    changeIdentifyKey(activeData.ossKey, activeData.ossPath);
    loadedURL.value = activeData.ossPath;
    showMaterialLibrary.value = false;
  }
}

const handleCancle = () => {
  showMaterialLibrary.value = false;
}

const handleChange = (file: any) => {
  let sourceFormat = file.name.split('.')[1]
  if (sourceFormat) {
    sourceFormat = sourceFormat.toUpperCase().toLowerCase();
  }
  if (!['png', 'jpg'].includes(sourceFormat)) {
    return
  }
  loadedURL.value = URL.createObjectURL(file.raw)
  loading.value = false;
}

const uploadedThumbnail = (res: any) => {
  let sourceFormat = res.data.split('.')[1]
  if (sourceFormat) {
    sourceFormat = sourceFormat.toUpperCase().toLowerCase();
  }
  if (!['png', 'jpg'].includes(sourceFormat)) {
    return
  }
  picUrl.value = res.data;
  changeIdentifyKey(picUrl.value, loadedURL.value);
}

const changeIdentifyKey = (picUrl: string, loadURL?: string) => {
  const parame: any = {
    sceneId: sceneId.value
  }

  if (sceneType.value == '3') {
    parame.identifyPicKey = picUrl;
  } else if (sceneType.value == '8') {
    parame.identificationKey = picUrl;
  }
  updateWxIdentifyAttachInfo({ ...parame }).then(async (res) => {
    const scene = (window as any).scene2;
    showUploadBtn.value = false;
    // 更新替换底图
    const t = new TextureLoader().load(loadURL || loadedURL.value);
    scene.getObjectByName('renti_mark_img').material.map = t;
    const imageInfo: any = await checkImageSize(loadURL || loadedURL.value);
    const ratio = 6 / imageInfo.width;
    scene.getObjectByName('renti_mark_img').scale.set(6, imageInfo.height * ratio, 1);
    if (sceneType.value == '8') {
      (scene.getObjectByName('ground-init') as any).position.y = -imageInfo.height * ratio / 2;
    }
  })
}

const beforeUpload = async (rawFile: any) => {
  let sourceFormat = rawFile.name.split('.')[1]
  if (sourceFormat) {
    sourceFormat = sourceFormat.toUpperCase().toLowerCase();
  }
  if(!['png', 'jpg'].includes(sourceFormat)) {
    store.state.showTips = '您上传的素材格式不正确，请上传与要求相符的素材。'
    return
  }
  uploadURL.value = `${baseURL}/scene/uploadIdentiyElement?sceneType=${sceneType.value}`;
  loading.value = true;
}

const resetInitName = async (e, item) => { // 素材名称
  if (e.target.value == '') {
    item.materialName = item.initMaterialName
  } else {
    const hasSensitiveWords = await desensitizte(e.target.value, '不可包含敏感词汇！')
    if (hasSensitiveWords) {
      item.materialName = item.initMaterialName
      changeMaterialName(item.materialName)
    }
  }
}


const changeMaterialName = (val: string) => {
  val = val.trim()
  store.state.editSceneData.outerMaterialMetaDtoList[materialIdex.value].elementName = val;
  if (store.state.editSceneData.outerMaterialMetaDtoList[materialIdex.value].flag != 'add') {
    store.state.editSceneData.outerMaterialMetaDtoList[materialIdex.value].flag = 'update';
  }
}

const checkNumber = (value: string) => {
  const newValue = value.trim();
  if (newValue == '') return ''
  if (((newValue[0] == '0' && newValue[1] !== '.') || newValue[0] == '.') && newValue.length > 1) {
    return newValue.slice(1)
  }
  if (!Number.isNaN(Number(newValue.slice(-1)[0])) || (value[0] == '-' && value.length == 1) || newValue.slice(-1)[0] == '.') {
    return value
  } else {
    return newValue.slice(0, -1)
  }
}

const initSceneName = async (e, item) => {
  if (e.target.value == '') {
    item.value = item.initValue
  } else {
    const hasSensitiveWords = await desensitizte(e.target.value, '不可包含敏感词汇！')
    if (hasSensitiveWords) {
      item.value = item.initValue
    }
  }
  changeValue(item.value, item.key || '')
}

const changeValue = (val: any, key: any) => {
  if (!['takePhotoButtonVisible', 'backgroundMusic'].includes(key)) {
    val = val.trim()
  }
  if (key) {
    props.changeSceneValue(val, key)
  }
  if (!val && isLoop.value && key == 'backgroundMusic') {
    isLoop.value = false;
  }
}

// 素材循环开关
const changeCyclePlay = () => {
  const editSceneData = { ...store.state.editSceneData };
  materialAttributeInfo[activeMaterial.value].cyclePlay = !materialAttributeInfo[activeMaterial.value].cyclePlay;
  editSceneData.outerMaterialMetaDtoList[materialIdex.value].cyclePlay = materialAttributeInfo[activeMaterial.value].cyclePlay ? 1 : 0;
  if (editSceneData.outerMaterialMetaDtoList[materialIdex.value].flag != 'add') {
    editSceneData.outerMaterialMetaDtoList[materialIdex.value].flag = 'update';
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
}

// 素材播放开关
const changePlayStatus = (val: string) => {
  const editSceneData = { ...store.state.editSceneData };
  editSceneData.outerMaterialMetaDtoList[materialIdex.value].autoPlay = val ? 1 : 0;
  if (editSceneData.outerMaterialMetaDtoList[materialIdex.value].flag != 'add') {
    editSceneData.outerMaterialMetaDtoList[materialIdex.value].flag = 'update';
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));

  if (!val && materialAttributeInfo[activeMaterial.value].cyclePlay) {
    changeCyclePlay();
  }
}

// 素材是否静态
const changeStaticStatus = (val: string) => {
  const editSceneData = { ...store.state.editSceneData };
  editSceneData.outerMaterialMetaDtoList[materialIdex.value].isStatic = val ? 1 : 0;
  if (editSceneData.outerMaterialMetaDtoList[materialIdex.value].flag != 'add') {
    editSceneData.outerMaterialMetaDtoList[materialIdex.value].flag = 'update';
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
}

const resetZero = (e, key, item) => {
  if (e.target.value == '') {
    item.value[key] = 0
    props.changeActiveValue(0, key, item.key, showType.value)
  }
}


const changeXYZ = (value: any, key: string, type: string) => {
  if (value == Number(value) && value != String(+value)) return
  if ((Number(value) && value.slice(-1) != '.') || value === '0') {
    props.changeActiveValue(Number(value), key, type)
  }
}

const removeDecimals = (obj: any, dec?: number) => {
  return { x: +Number(obj.x).toFixed(dec || 2), y: +Number(obj.y).toFixed(dec || 2), z: +Number(obj.z).toFixed(dec || 2) }
}

const resetData = (data: any) => {
  spaceData.value[0].value = { ...removeDecimals(data.location) }
  spaceData.value[1].value = { x: +radiansToAngle(data.rotation.x).toFixed(2), y: +radiansToAngle(data.rotation.y).toFixed(2), z: +radiansToAngle(data.rotation.z).toFixed(2) }
  spaceData.value[2].value = { ...removeDecimals(data.scale) }
}

onMounted(() => {
  const pageQuery: any = router.currentRoute.value.query;
  sceneType.value = pageQuery.sceneType || 0;
  getUserTypeByToken().then((res: any) => {
    userType.value = res.data;
  })
})

watch(hide_side, (newState) => {
  if (!document.querySelector('.zoom-operation')) return
  if (newState) {
    (document.querySelector('.zoom-operation') as any).style.right = '30px';
  } else {
    (document.querySelector('.zoom-operation') as any).style.right = '347px';
  }
})

watch(() => store.state.editSceneData, (newState) => {
  if(!newState.id) return
  // 场景元信息
  sceneInfoList.value = [
    {
      name: '项目编号',
      value: newState?.id,
      type: 1
    },
    {
      name: '项目名称',
      value: newState?.sceneName,
      initValue: newState?.sceneName,
      key: 'sceneName',
      type: 1
    },
    {
      name: '背景音乐',
      value: newState.backgroundMusic || null,
      key: 'backgroundMusic',
      type: 1
    },
    {
      name: '拍照功能',
      value: !!newState.takePhotoButtonVisible,
      key: 'takePhotoButtonVisible',
      type: 1
    },
  ]
  if (newState.takePhotoButtonVisible == null) {
    sceneInfoList.value[3].value = true
    props.changeSceneValue(1, 'takePhotoButtonVisible')
  }
  if (showType.value == 1) {
    const data: any = store.state.editSceneData.outerMaterialMetaDtoList.filter((e: any) => e.uuid == activeMaterial.value)[0]
    data && resetData(data)
  } else {
    isLoop.value = !!newState.isMusicCycle
    const audioList = newState.outerMaterialMetaDtoList?.filter((e: any) => e.materialDto.materialType == '2') || [];
    audioSelectList.value = [...audioList];
  }
  if ([3, 4, 5, 6, 7, 8].includes(+sceneType.value)) {
    if ([3, 8].includes(+sceneType.value)) {
      sceneInfoList.value.splice(2, 0, {
        name: '识别图片',
        value: '',
        type: 1
      })
    }
  }
  sceneInfoList.value.splice(2, 0, {
    name: '项目类型',
    value: `${scenePlatformMap[newState.scenePlatform]}/${sceneTypeMap[newState?.sceneType]}`,
    type: 1
  })

  if (!identifyPath.value) {
    if (store.state.editSceneData.identifyPicKey || store.state.editSceneData.identificationKey) {
      getOssAccessPath({ key: store.state.editSceneData.identifyPicKey || store.state.editSceneData.identificationKey }).then((res) => {
        identifyPath.value = res.data;
      })
    }
  }
  sceneId.value = newState?.id;
})

watch(() => showType.value, (nv) => { // showType为1，editSceneData的音乐有变化时，也需要更新audioList数组，此时showType为0，不会进到watch editSceneData
  if (!nv) {
    isLoop.value = !!store.state.editSceneData.isMusicCycle
    const audioList = store.state.editSceneData.outerMaterialMetaDtoList?.filter((e: any) => e.materialDto.materialType == '2') || [];
    audioSelectList.value = [...audioList];
  }
})

watch(() => store.state.activeMaterial, (newState) => {
  const materialIdex1 = store.state.editSceneData.outerMaterialMetaDtoList.findIndex((e: any) => e.uuid == newState)
  materialIdex.value = materialIdex1
  if (newState && !materialAttributeInfo[newState]) {
    let activeMaterialData: any = null;
    activeMaterialData = { ...store.state.editSceneData.outerMaterialMetaDtoList[materialIdex1] }
    materialAttributeInfo[newState] = {
      materialName: activeMaterialData.elementName || activeMaterialData.materialDto.materialName,
      animationInfoDto: activeMaterialData.materialDto.animationInfoDto || {},
      cyclePlay: !!activeMaterialData.cyclePlay,
      autoPlay: !!activeMaterialData.autoPlay,
      materialType: activeMaterialData.materialDto.materialType,
      id: activeMaterialData.id,
      isStatic: !!activeMaterialData.isStatic
    }
    materialAttributeInfo[newState]['initMaterialName'] = materialAttributeInfo[newState].materialName
  }
  if (newState) {
    const data: any = { ...store.state.editSceneData.outerMaterialMetaDtoList[materialIdex1] }
    data && resetData(data)
    showType.value = 1;
  } else {
    delete materialAttributeInfo[activeMaterial.value]
    showType.value = 0
  }
  activeMaterial.value = newState;
})

watch(() => isLoop, (newState) => {
  props.changeSceneValue(newState ? 1 : 0, 'isMusicCycle')
})
watch(() => showMaterialLibrary.value, (newState) => {
  if (newState) {
    getMaterialPageByUser({ pageNo: 1, pageSize: 999, materialType: 3, getTotal: false }).then((res: any) => {
      imgMaterial.value = [...res.data.records]
    })
  }
})
</script>
<style scoped lang="less">
.box-item {
  position: relative;
  width: 16px;
  height: 16px;
  background-image: url(~@/assets/images/icon/help.png);
  background-size: 100% 100%;
  position: absolute;
  left: 170px;
  top: 9px;

  &:hover {
    background-image: url(~@/assets/images/icon/helpA.png);
    background-size: 100% 100%;
  }

  &>span {
    position: relative;
    position: absolute;
    left: -95px;
    top: -56px;
    width: 195px;
    height: 44px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px 4px 4px 4px;
    display: inline-block;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 1.5;
    padding: 5px 5px 0;
    display: none;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      bottom: -6px;
      border: 3px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.7);
      margin-left: -3px;
    }
  }

  &:hover span {
    display: inline-block;
  }
}

.image-material-library {
  position: relative;
  padding: 24px;
  box-sizing: border-box;
  background: #FFFFFF;
  border-radius: 8px;
  width: 598px;

  .submit-btns {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: absolute;
    right: 24px;
    bottom: 24px;

    &>div {
      width: 92px;
      height: 32px;
      border-radius: 4px;
      line-height: 30px;
      text-align: center;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      border: 1px solid #DADADA;
      margin-left: 12px;
      box-sizing: border-box;
      color: #797979;
    }

    &>div:last-child {
      background: #2E76FF;
      border-color: #2E76FF;
      color: #FFFFFF;
    }
  }

  .library-title {
    font-weight: bold;
    font-size: 18px;
    color: #1E1E1E;
    text-align: left;
  }

  .image-box {
    width: 100%;
    max-height: 324px;
    margin: 12px 0 56px;
    overflow: hidden;
    overflow-y: auto;

    &>div {
      position: relative;
      width: 100px;
      height: 100px;
      background: #FFFFFF;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #DADADA;
      overflow: hidden;
      float: left;
      margin: 0 12px 12px 0;
      box-sizing: border-box;

      img {
        width: 100%;
        height: 100%;
        cursor: pointer;
      }

      i {
        position: absolute;
        right: 0;
        top: 1px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-image: url(~@/assets/images/experience-icon/check-one.png);
        background-size: 100% 100%;
      }
    }

    &>div:nth-child(5n) {
      margin-right: 0; // 去除第3n个的margin-right
    }

    .active {
      border-color: #2E76FF;
    }
  }
}

.select-upload-method {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  .method-content {
    position: relative;
    width: 453px;
    height: 169px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 16px 24px 0;
    box-sizing: border-box;
    text-align: left;
    font-weight: 400;
    font-size: 14px;
    color: #797979;

    .close-icon {
      width: 24px;
      height: 24px;
      background: url(~@/assets/images/icon/close.png);
      background-size: 100% 100%;
      position: absolute;
      right: 16px;
      top: 16px;
      cursor: pointer;
    }

    .tips-title {
      font-weight: bold;
      font-size: 18px;
      color: #1E1E1E;
      text-align: left;
    }

    .tips-content {
      margin: 14px 0;
    }

    .tips-btn {
      position: relative;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      &>div {
        width: 197px;
        height: 64px;
        line-height: 64px;
        background: #2E76FF;
        border-radius: 10px 10px 10px 10px;
        text-align: center;
        font-weight: bold;
        font-size: 14px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .upload-btn {
        position: absolute;
        right: 0;
        top: 0;
        background-color: transparent;
      }
    }
  }
}

.upload-tips-list {
  width: 439px;
  height: 147px;
  background: #E5E7E9;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #2E76FF;
  position: absolute;
  right: 325px;
  top: 264px;
  font-weight: 400;
  font-size: 12px;
  color: #797979;
  line-height: 18px;
  padding: 6px 10px;
  box-sizing: border-box;
  text-align: left;

  &>div {
    margin-top: 6px;
  }

  .title {
    font-weight: bold;
    font-size: 14px;
    color: #1E1E1E;
    padding-left: 4px;
    border-left: 2px solid #2E76FF;
    height: 21px;
    line-height: 20px;
  }

  .title-tips {
    font-weight: 400;
    font-size: 12px;
    color: #797979;
    margin: 10px 0 8px;
  }
}

.identify-images {
  position: relative;
  width: 148px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;

  &>img {
    width: 148px;
    height: 101px;
    border-radius: 4px;
  }

  &>span {
    font-weight: 400;
    font-size: 12px;
    color: #2E76FF;
    text-decoration-line: underline;
  }

  &>div {
    position: relative;
    position: absolute;
    width: 100%;
    height: 101px;
    z-index: 1;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    // visibility: hidden;

    img {
      position: relative;
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: none;
      z-index: 1;
    }

    .tips-mask {
      display: none;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      position: absolute;
      left: 0;
      top: 0;
    }

    &:hover img,
    &:hover .tips-mask {
      display: inline-block;
    }
  }

  // &:hover div{
  //   visibility: visible;
  // }
}

#app .interactive-data .info-list {
  ::v-deep(.el-input__wrapper) {
    padding: 0 10px;
    width: 66px;
    height: 34px;
    line-height: 34px;
  }
}

#app .scene-info .scene-info-list {
  ::v-deep(.el-input__wrapper) {
    height: 34px;
  }

  ::v-deep(.el-input__wrapper .el-input__inner) {
    font-size: 14px;
  }
}

.el-select {
  min-width: 130px;
}

.interactive-data {
  padding: 20px 16px 0;
  box-sizing: border-box;
  border-bottom: 1px solid #C8C8C8;

  .flex-double {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .img {
      width: 30px;
      height: 30px;
      transform: translateY(-5px);
      cursor: pointer;
    }
  }
}


.info-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  color: #6F6F6F;
  margin-bottom: 20px;

  .el-input {
    width: 60px;
    height: 34px;
    vertical-align: middle;
    margin-right: 5px;
    display: inline-block;
  }

  span {
    vertical-align: middle;
  }
}

.axle {
  font-size: 15px;
  font-weight: 400;
  color: #0375FF;
  margin-right: 3px;
  margin-left: 3px;
}

.is-location {
  position: relative;

  &::after {
    content: 'm';
    position: absolute;
    right: 2px;
    top: 0;
    color: rgba(113, 113, 120, 0.5);
  }
}

.is-rotate {
  position: relative;

  &::after {
    content: '°';
    position: absolute;
    right: 2px;
    top: 0;
    color: rgba(113, 113, 120, 0.5);
  }
}

.info-title {
  font-size: 12px;
  color: #000000;
  text-align: left;
  margin-bottom: 15px;
  height: 24px;
  line-height: 24px;
  position: relative;

  &>span {
    vertical-align: middle;
  }

  &>img {
    vertical-align: middle;
    margin-right: 6px;
    width: 24px;
    height: 24px;
  }
}

.canvas-right {
  position: fixed;
  right: 0;
  top: 63px;
  width: 317px;
  height: calc(100% - 59px);
  background: #DFE0E3;
  // backdrop-filter: blur(20px);
  z-index: 8;
  overflow-y: auto;

  ::v-deep(.el-input__inner) {
    color: #0F0F0F !important;
  }

  ::v-deep(.el-textarea__inner) {
    color: #0F0F0F !important;
  }

  .canvas-right-title {
    height: 54px;
    line-height: 54px;
    text-align: left;
    padding-left: 16px;
    font-weight: 500;
    font-size: 17px;
    color: #414141;
  }
}

.canvas-right.hide-right-side {
  right: -320px;
}

.hide-side,
.show-side {
  position: absolute;
  top: 50%;
  margin-top: -65px;
  right: 317px;
  width: 16px;
  height: 42px;
  transform: rotate(180deg);
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.show-side {
  right: 0;
}

.scene-info {
  width: 317px;
  // height: calc(100% - 226px);
  padding: 20px 16px 0;
  box-sizing: border-box;
  border-bottom: 1px solid #C8C8C8;
  border-top: 1px solid #C8C8C8;
  overflow: hidden;
  overflow-y: auto;

  .scene-info-list {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    font-size: 14px;
    font-weight: 400;
    color: #0F0F0F;
    line-height: 14px;
    margin-bottom: 16px;

    &>div:first-child {
      font-size: 14px;
      color: #6F6F6F;
      width: 60px;
      margin-top: 9px;
    }

    &>div:last-child {
      width: 208px;
      min-height: 32px;
      border-radius: 8px;
      line-height: 32px;
      box-sizing: border-box;
      text-align: left;

      &>span {
        padding-left: 10px;
      }

      .play-switch {
        padding: 0;

        &>span {
          margin-right: 16px;
        }
      }
    }

    .scene-info-dec {
      height: 79px;
      position: relative;

      &::after {
        content: '30\5b57\4ee5\5185';
        position: absolute;
        right: 8px;
        bottom: 5px;
        font-size: 9px;
        transform: scale(0.75);
        transform-origin: 100% 100%;
        font-weight: 400;
        color: #6F6F6F;
      }
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;


      .loop-btn {
        font-weight: 400;
        font-size: 14px;
        color: #414141;
        margin-left: 10px;

        &>img {
          width: 16px;
          height: 16px;
          vertical-align: middle;
          cursor: pointer;
        }

        &>span {
          vertical-align: middle;
          display: inline-block;
          height: 20px;
          line-height: 20px;
          margin-right: 5px;
        }

        &.active {
          color: #2E76FF;
        }
      }
    }
  }

  .scene-info-line {
    border-bottom: 1px solid #C8C8C8;
    width: calc(100% + 32px);
    margin: 17px 0;
    margin-left: -16px;
  }

  .space-model {
    width: 212px;
    height: 185px;
    background-size: 100% 100%;
    margin: 30px 0 20px 32px;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

/* 媒体查询，适配ipad端和小电脑页面 */
@media screen and (max-width: 1200px) {
  .interactive-data {
    padding-right: 5px;

    .info-list .el-input {
      width: 50px;
    }
  }

  .axle {
    font-size: 14px;
  }

  .canvas-right {
    width: 267px;
  }

  .canvas-right.hide-right-side {
    right: -270px;
  }

  .hide-side {
    right: 267px;
  }

  .scene-info {
    padding-right: 10px;
    width: 265px;

    .scene-info-list>div:first-child {
      font-size: 13px;
    }

    .scene-info-list>div:last-child {
      width: 168px;
    }
  }

  .upload-tips-list {
    right: 273px;
  }
}
</style>