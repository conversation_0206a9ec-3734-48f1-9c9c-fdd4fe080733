<template>
  <div class="image-params">
    <div class="param-title">参数设置</div>
    <el-input
      v-model="desc"
      type="textarea"
      :maxlength="150"
      show-word-limit
      placeholder="输入想要生成的图片描述"
      class="desc-input"
      @input="onDescChange" />
    <div class="ratio-title">选择比例</div>
    <div class="custom-ratio-list">
      <div
        v-for="item in ratios"
        :key="item.value"
        :class="['custom-ratio-btn', { active: ratio === item.value }]"
        @click="selectRatio(item.value)">
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { COMMON_RATIOS } from '@/views/ai/config/ratios';

const desc = ref('');
const ratio = ref('1:1');
const ratios = COMMON_RATIOS;

const onDescChange = (value: string) => {
  desc.value = value;
};

const selectRatio = (value: string) => {
  ratio.value = value;
};

// 暴露方法给父组件
defineExpose({
  validateParams: () => !!desc.value?.trim(),
  getParams: () => ({
    desc: desc.value,
    ratio: ratio.value,
    ratios,
  }),
  desc,
});
</script>

<style scoped lang="less">
.image-params {
  .param-title {
    font-weight: bold;
    font-size: 24px;
    color: #1e1e1e;
    text-align: left;
    font-style: normal;
    margin-bottom: 12px;
  }

  .desc-input {
    width: 100%;
    height: 220px;
    margin-bottom: 24px;

    :deep(.el-textarea__inner) {
      background: rgba(218, 218, 218, 0.27) !important;
      border-radius: 8px !important;
      border: none !important;
      box-shadow: none !important;
      min-height: 220px;
      height: 220px;
      resize: none;
      padding-top: 10px !important;
    }

    :deep(.el-input__count) {
      right: 12px !important;
      left: auto !important;
      bottom: 8px !important;
      background: #f5f5f5 !important;
      border-radius: 8px !important;
      color: #797979 !important;
      padding: 2px 8px !important;
      font-size: 12px !important;
      font-weight: 400 !important;
      box-shadow: none !important;
    }
  }

  .ratio-title {
    font-weight: 400;
    font-size: 14px;
    color: #797979;
    margin-bottom: 8px;
    text-align: left;
  }

  .custom-ratio-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 11px;
    margin-bottom: 24px;

    .custom-ratio-btn {
      width: 100%;
      height: 33px;
      background: #f5f5f5;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #797979;
      cursor: pointer;
      border: none;
      transition: background 0.2s, color 0.2s, font-weight 0.2s;
      font-weight: 400;
      white-space: nowrap;

      &.active {
        background: rgba(46, 118, 255, 0.1);
        color: #2e76ff;
        font-weight: 400;
        border-radius: 4px;
      }
    }
  }
}
</style>
