import {
  InterleavedBuffer<PERSON>ttribute,
  Matrix4,
  Vector2,
  Vector3,
  Raycaster,
  Intersection,
  Group,
  Quaternion,
} from 'three';
import {
  COLOR_HELPER_GEOMETRY,
  COLOR_MOVE_GEOMETRY,
  LINE_DASH_SIZE,
  LINE_GAP_SIZE,
  canvasIds,
} from '../config';
import { Line2 } from 'three/examples/jsm/lines/Line2';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial';
import { CPoint } from './CPoint';
import { pointToLineDistance } from '@/utils';

const ADSORPTION_RADIUS = 0.2; // 点吸附半径

/**
 *   多个顶点组成的连续直线
 */

export type CLineAttrs = {
  vertexs?: Array<Vector3> | Array<Array<number>> | Array<number>; // 顶点
  dashed?: boolean; // 虚线
  color?: number; // 颜色
  lineWidth?: number; // 线宽
  transparent?: boolean; // 开启透明 和opacity配合使用
  opacity?: number; // 透明度 和transparent配合使用
  dashSize?: number; // 虚线长度
  gapSize?: number; // 虚线间隔
  name?: string; // 命名区分功能
  createPoints?: boolean; // 是否自动创建顶点
  constraint?: any; // 约束缓存
  marks?: any; // 标注缓存
  type?: string;
  repeatCenter?: Vector3;
  areaIndex?: number;
  reversalDepthTest?: boolean;
};

export class CLine extends Line2 {
  geometry: LineGeometry;
  material: LineMaterial;
  name: string;
  userData: { [k: string]: any };
  constructor(attrs?: CLineAttrs) {
    super();

    this.name = attrs && attrs.name !== undefined ? attrs.name : 'cline';
    this.geometry = new LineGeometry();
    this.material = new LineMaterial();
    this.userData = {};
    // init
    this.initialize(attrs);
  }

  getType() {
    return 'Line';
  }

  initData(attrs?: CLineAttrs) {
    // 约束/标注/uuid/控制点缓存
    this.userData = {
      constraint: {
        links: [],
      },
      marks: [],
      points: [],
    };
    this.setCreatePoints(attrs && attrs.createPoints !== undefined ? attrs.createPoints : false);
    if (attrs && attrs.constraint !== undefined) this.userData.constraint = attrs.constraint; // constraint data cache
    if (attrs && attrs.marks !== undefined) this.userData.marks = attrs.marks; // marks data cache
  }

  initUI(attrs?: CLineAttrs) {
    // 初始化顶点
    if (this.getCreatePoints()) {
      const group = new Group();
      this.add(group);
    }
    this.userData.areaIndex = attrs && attrs.areaIndex !== undefined ? attrs.areaIndex : -1;
    if (attrs && attrs.repeatCenter !== undefined) this.userData.repeatCenter = attrs.repeatCenter;
    if (attrs && attrs.color !== undefined) this.setColor(attrs.color);
    if (attrs && attrs.vertexs !== undefined) this.setVertexs(attrs.vertexs);
    if (attrs && attrs.lineWidth !== undefined) this.setLineWidth(attrs.lineWidth);
    if (attrs && attrs.opacity !== undefined) this.setOpacity(attrs.opacity);

    if (attrs && attrs.transparent !== undefined) this.setTransparent(attrs.transparent);

    if (attrs && attrs.dashed !== undefined) {
      this.setDashed(attrs.dashed);
      // dashed line 深度衰减
      this.setDashSize(attrs && attrs.dashSize !== undefined ? attrs.dashSize : LINE_DASH_SIZE);
      this.setGapSize(attrs && attrs.gapSize !== undefined ? attrs.gapSize : LINE_GAP_SIZE);

      const onChange = () => {
        this.setDashSize(this.getDashSize());
        this.setGapSize(this.getGapSize());
      };
      const controls = (window as any).controls;
      controls.addEventListener('change', onChange);
      // 被销毁后 移除事件
      this.geometry.addEventListener('dispose', (e: any) => {
        controls.removeEventListener('change', onChange);
      });
    }
    if (attrs && attrs.reversalDepthTest) {
      this.material.depthTest = attrs.reversalDepthTest;
    } else {
      this.material.depthTest = false;
    }
    this.renderOrder = 10;
    const pathname = window.sessionStorage.getItem('path')?.slice(1) || 'home';
    const canvasSize: any = document.getElementById(canvasIds[pathname])?.getBoundingClientRect();
    const width = canvasSize?.width || window.innerWidth;
    const height = canvasSize?.height || window.innerHeight;
    this.material.resolution.set(width, height);
    this.computeLineDistances();
  }

  // @override 射线拾取点
  raycast(raycaster: Raycaster, intersects: Intersection[]) {
    const p = (raycaster as any).ray.origin;
    const minDistanc = { d: 999, index: -1 };

    let pointData = [...this.userData.points];
    if (this.parent?.userData.rotate) {
      const linePoints = [...pointData].map((e) => e.clone().sub(this.userData.repeatCenter));
      pointData = linePoints.map((e) => {
        const quaternion = new Quaternion();
        quaternion.setFromAxisAngle(new Vector3(0, 1, 0), -this.parent?.userData.rotate);
        const f = e.clone().applyQuaternion(quaternion);
        return f;
      });
      pointData = pointData.map((e) => e.clone().add(this.userData.repeatCenter));
    }
    // 过滤出距离最小的线段
    for (let index = 0; index < pointData.length - 1; index++) {
      const v1 = pointData[index];
      const v2 = pointData[index + 1];
      const d = pointToLineDistance(v1, v2, raycaster);
      if (minDistanc.d > d) {
        minDistanc.d = d;
        minDistanc.index = index;
      }
    }
    if (minDistanc.index != -1) {
      const vv1 = pointData[minDistanc.index];
      const vv2 = pointData[minDistanc.index + 1];
      if (
        minDistanc.d < ADSORPTION_RADIUS &&
        vv1.distanceTo(vv2) > p.distanceTo(vv1) &&
        vv1.distanceTo(vv2) > p.distanceTo(vv2)
      ) {
        intersects.push({
          distance: minDistanc.d,
          object: this,
          points: [vv1, vv2],
          pointIndex: [minDistanc.index, minDistanc.index + 1],
        } as any);
      }
    }
  }

  initialize(attrs?: CLineAttrs) {
    this.initData(attrs);
    this.initUI(attrs);
  }

  getPoint() {
    return this.userData.points;
  }

  setHeightStyle() {
    this.children[0].visible = true;
    this.material.color.setHex(0xf89e01);
    return this;
  }

  setBaseStyle() {
    this.children[0].visible = false;
    this.material.color.setHex(0x04b138);
    return this;
  }

  /**
   * 设置顶点
   * @param vertexs  [vector3 , vector3 ...]  |  [ [a1,b1,c1 ] ,[ a2,b2,c2] ...]  |  [a,b,c,d...]
   */
  setVertexs(vertexs: Array<Vector3> | Array<Array<number>> | Array<number>, type?: string) {
    let positions: Array<number> = [];
    if (!vertexs || vertexs.length == 0) {
      throw new Error('vertexs is not defined or vertexs array is empty.');
    }

    if ((vertexs[0] as any).isVector3) {
      vertexs.forEach((v: any) => {
        positions.push(v.x, v.y, v.z);
      });
    } else if (vertexs[0] instanceof Array) {
      vertexs.forEach((v: any) => {
        positions.push(v[0], v[1], v[2]);
      });
    } else {
      positions = vertexs.slice() as Array<number>;
    }

    // update line
    this.geometry = new LineGeometry();
    this.geometry.setPositions(positions);

    this.computeLineDistances();

    this.userData.points = vertexs;

    const repeatCenter = this.userData.repeatCenter?.clone();

    // 生成新的组，并塞入点位
    if (this.userData.points.length && this.userData.createPoints) {
      if (type == 'update') {
        this.userData.points.forEach((point: Vector3, index: number) => {
          (this.children[0].children[index] as any).setVertex(point);
          if (repeatCenter) {
            (this.children[0].children[index] as any).position.set(
              -repeatCenter.x,
              -repeatCenter.y,
              -repeatCenter.z
            );
          }
        });
      } else {
        // 移除原先的组
        this.remove(this.children[0]);
        const group = new Group();
        this.add(group);
        this.userData.points.forEach((point: Vector3, index: number) => {
          const cpoint = new CPoint({
            vertex: point,
            color: 0xffffff,
            size: 6,
            areaIndex: this.userData.areaIndex,
          });
          cpoint.userData.index = index;
          if (repeatCenter) {
            cpoint.position.set(-repeatCenter.x, -repeatCenter.y, -repeatCenter.z);
          }
          group.add(cpoint);
        });
      }
    }
    return this;
  }

  getVertexs() {
    return this.userData.points;
  }

  // 材质: 虚线
  setDashed(dashed: boolean) {
    this.material.dashed = dashed;
    if (dashed) {
      this.material.defines.USE_DASH = '';
    } else {
      delete this.material.defines.USE_DASH;
    }
    this.material.needsUpdate = true;
    return this;
  }

  getDashed() {
    return this.material.dashed;
  }

  // 材质: 虚线长度
  setDashSize(size: number) {
    this.userData.dashSize = size;
    this.material.dashSize = size * (1 / (window as any).controls.object.zoom);
    return this;
  }

  getDashSize() {
    return this.userData.dashSize;
  }

  // 材质: 虚线间隔
  setGapSize(size: number) {
    this.userData.gapSize = size;
    this.material.gapSize = size * (1 / (window as any).controls.object.zoom);
    return this;
  }

  getGapSize() {
    return this.userData.gapSize;
  }

  // 线宽
  setLineWidth(linewidth = 2) {
    this.material.linewidth = linewidth;
    return this;
  }

  getLineWidth() {
    return this.material.linewidth;
  }

  // 开启透明
  setTransparent(transparent: boolean) {
    this.material.transparent = transparent;
    return this;
  }

  getTransparent() {
    return this.material.transparent;
  }

  // 透明度
  setOpacity(opacity = 1) {
    this.material.opacity = opacity;
    return this;
  }

  getOpacity() {
    return this.material.opacity;
  }

  // 颜色
  setColor(color = 0x97bfe0) {
    this.userData.color = color;
    this.material.color.set(color);
    return this;
  }

  getColor() {
    return this.userData.color;
  }

  // 是否自动生成顶点
  setCreatePoints(createPoints?: boolean) {
    this.userData.createPoints = createPoints;
    return this;
  }

  getCreatePoints() {
    return this.userData.createPoints;
  }

  toJSON() {
    const json: { [k: string]: any } = {
      name: this.name,
      color: this.getColor(),
      opacity: this.getOpacity(),
      lineWidth: this.getLineWidth(),
      vertexs: this.getVertexs(),
      transparent: this.getTransparent(),
      dashed: this.getDashed(),
      dashSize: this.getDashSize(),
      gapSize: this.getGapSize(),
      createPoints: this.getCreatePoints(),
    };

    for (const key in this.userData) {
      if (key === 'constraint') {
        json[key] = {};
        json[key].links = [];
        json[key].links.push(...this.userData[key].links);
      } else {
        json[key] = this.userData[key];
      }
    }
    return json;
  }
}
