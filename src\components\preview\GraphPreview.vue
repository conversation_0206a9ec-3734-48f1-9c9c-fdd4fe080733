<template>
  <div class="preview-scene-box">
    <div>
      <div class="title">
        <div>{{ titleName }}</div>
        <div class="icon iconfont icon-close" @click="closeEvent"></div>
      </div>
      <div class="preview-canvas">
        <graph-canvas ref="canvasRef" :resData="resData"></graph-canvas>
      </div>
      <div class="canvas-tips">可滑动鼠标查看路径导航信息</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import GraphCanvas from '@/components/GraphCanvas.vue'
import { Vector3 } from 'three'
import { loadZipFileForJSZip, base64ToBlob } from "@/utils";
import { getOssAccessPath } from '@/api/modules/storage'
import { updateSpaceName } from '@/api/modules/space'
import axios from "axios";

const props = defineProps({
  spaceInfo: {
    default: null,
    type: Object
  },
  hideModel: {
    default: null,
    type: Function
  },
  titleName: {
    default: '空间预览',
    type: String
  },
  resData: {
    default: null,
    type: Object
  }
})

const canvasRef = ref()
const baseURL = process.env.NODE_ENV === 'production' ? 'https://vps.njyjxr.com:18443/vps' : 'http://*************/vps' // 基础url
const token = window.localStorage.getItem('token');
const headers: any = {
  'Content-Type': 'text/plain',
  token: token
}

const closeEvent = () => {
  props.hideModel()
}

onMounted(() => {
  // 加载地图
  getOssAccessPath({ key: props.spaceInfo.roomStructureKey || props.spaceInfo.roomStructurePath }).then((res: any) => {
    const camera = canvasRef.value.getCamera();
    camera.position.set(0, 30, 0);
    camera.lookAt(new Vector3(0, 0, 0));
    loadZipFileForJSZip(res.data, async (glb: any) => {
      if (glb.scene) {
        canvasRef.value.addMesh(glb.scene)
      } else {
        canvasRef.value.addMesh(glb)
      }
    })
  })
})
</script>

<style scoped lang="less">
.preview-scene-box {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  &>div {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 16px 24px 6px;
    // width: 624px;
    box-sizing: border-box;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 18px;
      color: #1E1E1E;
      margin-bottom: 16px;

      .icon-close {
        font-size: 24px;
        cursor: pointer;
        font-weight: 400;
        color: #797979;
        
        &:hover {
          color: #2E76FF;
        }
      }
    }

    .preview-canvas {
      width: 1210px;
      height: 680px;
      background: #D9D9D9;
      border-radius: 10px;
    }

    .canvas-tips {
      font-weight: 400;
      font-size: 12px;
      color: #1e1e1e;
      height: 32px;
      line-height: 32px;
      margin-top: 2px;
    }
  }
}
</style> 