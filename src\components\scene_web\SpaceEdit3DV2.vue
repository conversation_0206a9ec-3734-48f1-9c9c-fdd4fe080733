<template>
  <div class="edit-box" v-show="!store.state.isPlanStyle">
    <canvas-three
      ref="canvas3dRef"
      :handle-mouse-move="handleMouseMoveCanvas"
      :set-location="setLocation"
      :set-rotation="setRotation"
      :set-scale="setScale"
      :change-object="changeObject"
      :isPlanStyle="isPlanStyle"
      :edit-scene-data="editSceneData"></canvas-three>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, reactive } from 'vue';
import { Vector3, Quaternion, Scene } from 'three';
import CanvasThree from '@/components/scene_web/CanvasThree.vue';
import { useStore } from 'vuex';
import { getOssAccessPath, saveScene } from '@/api';
import { useRouter } from 'vue-router';

const props = defineProps({
  sceneData: {
    default: null,
    type: Object,
  },
});

const store = useStore();
const isPlanStyle = ref(false);
const canvas3dRef = ref();
const editSceneData: any = ref({});
let scene = new Scene();
let cube: any = null;
const router = useRouter();

const exitEdit = () => {
  // 更新场景之前先截图保存
  const pageQuery: any = router.currentRoute.value.query;
  const sceneId = pageQuery.sceneid || '';
  const renderer = (window as any).renderer2;
  const camera = (window as any).camera2;
  const token = window.localStorage.getItem('token');
  if (!token) return;
  renderer.render(scene, camera);
  let imgData = renderer.domElement.toDataURL('image/jpeg');
  saveScene({ sceneId: sceneId, base64Pic: imgData }).then((res: any) => {
    if (res.code == 200) {
      router.push('home');
    }
  });
};

const handleMouseMoveCanvas = () => {};

const setLocation = () => {};

const setRotation = () => {};

const setScale = () => {};

const changeObject = () => {};

const createModel = (ele: any, key: string, index: number, i?: number, data?: any) => {
  let path = ele.materialDto.modelStorageMap
    ? ele.materialDto.modelStorageMap.web.ossKey
    : ele.materialDto.ossKey;
  if (ele.materialDto.materialFormat.includes('video') && ele.materialDto.thumbnail) {
    path = ele.materialDto.thumbnail;
  }
  if (path) {
    if (!ele.id) {
      getOssAccessPath({ key: path }).then((res1: any) => {
        addModel(ele, key, index, i, data, res1.data);
      });
    } else {
      path = ele.materialDto.modelStorageMap
        ? ele.materialDto.modelStorageMap.web?.ossPath
        : ele.materialDto.ossPath;
      if (
        ele.materialDto.materialFormat.includes('video') &&
        ele.materialDto.thumbnailOssAccessUrl
      ) {
        path = ele.materialDto.thumbnailOssAccessUrl;
      }
      addModel(ele, key, index, i, data, path);
    }
  }
};

const addModel = (ele: any, key: string, index: number, i: any, data: any, url: string) => {
  canvas3dRef.value.addModel(
    url,
    ele,
    (obj: any) => {
      obj.position.set(ele.location.x, ele.location.y, ele.location.z);
      obj.scale.set(
        ele.scale.x * obj.userData.initScale,
        ele.scale.y * obj.userData.initScale,
        ele.scale.z * obj.userData.initScale
      );
      obj.userData.boxInfos.scale = new Vector3(ele.scale.x, ele.scale.y, ele.scale.z);
      const metaInfo = ele.metaInfo?.split(',');
      if (metaInfo) {
        const quaternion = new Quaternion(+metaInfo[0], +metaInfo[1], +metaInfo[2], +metaInfo[3]);
        obj.applyQuaternion(quaternion);
      } else {
        ele.metaInfo = '0,0,0,1,' + obj.userData.initScale;
      }
      if (i == undefined) {
        editSceneData.value[key][index] = { ...ele };
        store.state.editSceneData[key][index].metaInfo = ele.metaInfo;
      } else {
        if (!editSceneData.value[key][index]) {
          editSceneData.value[key][index] = { ...data };
        }
        editSceneData.value[key][index].materialMetaDtoList[i] = { ...ele };
        store.state.editSceneData[key][index].materialMetaDtoList[i].metaInfo = ele.metaInfo;
      }
    },
    !!ele.materialDto.thumbnail
  );
};

onMounted(() => {
  scene = (window as any).scene2;
  cube = scene.getObjectByName('cube-init');
});

watch(
  () => store.state.editSceneData,
  (newState: any) => {
    if (newState.changeTime && newState.changeTime != editSceneData.value.changeTime) {
      if (newState.changeType == 'deleteInteraction') {
        // 删除互动区域
        const currentDeleteData: any = { ...store.state.currentDeleteData };
        editSceneData.value.interactionDtoList.splice(currentDeleteData.deleteIndex, 1);
        store.state.randerTotal += 1;

        const modelGroup: any = scene.getObjectByName('init-group');
        currentDeleteData.materialMetaDtoList.forEach((e: any) => {
          const oldObj: any = scene.getObjectByName(e.uuid);
          modelGroup.remove(oldObj);
        });
        return;
      } else if (newState.changeType == 'deleteSource') {
        const currentDeleteData: any = { ...store.state.currentDeleteData };
        let deleteIndex = currentDeleteData.deleteIndex;
        const deleteIndexs = deleteIndex.split(',');
        if (deleteIndexs.length == 2) {
          editSceneData.value.interactionDtoList[deleteIndexs[0]].materialMetaDtoList.splice(
            deleteIndexs[1],
            1
          )[0];
        } else {
          editSceneData.value.outerMaterialMetaDtoList.splice(deleteIndexs[0], 1)[0];
        }
        const group: any = scene.getObjectByName(currentDeleteData.uuid);
        // 删除互动区域元素
        group.parent?.remove(group);
        store.state.randerTotal += 1;
        return;
      }

      const oldOuterMaterialMetaDtoList = editSceneData.value.outerMaterialMetaDtoList || [];
      const currentOuterMaterialMetaDtoList = newState.outerMaterialMetaDtoList || [];
      const oldInteractionDtoList = [...(editSceneData.value.interactionDtoList || [])];
      const currentInteractionDtoList = [...(newState.interactionDtoList || [])];
      currentOuterMaterialMetaDtoList.forEach((ele: any, index: number) => {
        const oldEle = oldOuterMaterialMetaDtoList[index];
        if (oldEle) {
          oldOuterMaterialMetaDtoList[index].elementName = ele.elementName;
        }
        if (!oldEle) {
          createModel(ele, 'outerMaterialMetaDtoList', index);
        } else if (JSON.stringify(oldEle.location) !== JSON.stringify(ele.location)) {
          // diff location 移动
          (scene.getObjectByName(ele.uuid) as any).position.set(
            ele.location.x,
            ele.location.y,
            ele.location.z
          );
          oldOuterMaterialMetaDtoList[index].location = { ...ele.location };
          cube.position.set(ele.location.x, ele.location.y, ele.location.z);
        } else if (JSON.stringify(oldEle.rotation) !== JSON.stringify(ele.rotation)) {
          // diff rotation 旋转
          const curObj: any = scene.getObjectByName(ele.uuid);
          oldOuterMaterialMetaDtoList[index].rotation = { ...ele.rotation };
          if (ele.metaInfo !== '') {
            curObj.rotation.set(-ele.rotation.x, -ele.rotation.y, -ele.rotation.z);
          }
          const quaternion = curObj.quaternion.clone();
          let metaInfo = `${quaternion.x},${quaternion.y},${quaternion.z},${quaternion.w},${curObj.userData.initScale}`;
          oldOuterMaterialMetaDtoList[index].metaInfo = metaInfo;
          store.state.editSceneData.outerMaterialMetaDtoList[index].metaInfo = metaInfo;
        } else if (JSON.stringify(oldEle.scale) !== JSON.stringify(ele.scale)) {
          // diff scale 缩放
          oldOuterMaterialMetaDtoList[index].scale = { ...ele.scale };
          const curObj: any = scene.getObjectByName(ele.uuid);
          const initScale = curObj.userData.initScale;
          curObj.scale.set(
            ele.scale.x * initScale,
            ele.scale.y * initScale,
            ele.scale.z * initScale
          );
        }
      });

      currentInteractionDtoList.forEach((ele: any, index: number) => {
        if (ele.materialMetaDtoList && ele.materialMetaDtoList.length) {
          ele.materialMetaDtoList.forEach((e: any, i: number) => {
            const oldEle = oldInteractionDtoList[index]?.materialMetaDtoList
              ? oldInteractionDtoList[index].materialMetaDtoList[i]
              : null;
            if (oldEle) {
              editSceneData.value.interactionDtoList[index].materialMetaDtoList[i].elementName =
                e.elementName;
            }
            if (!oldEle) {
              createModel(e, 'interactionDtoList', index, i, ele);
            } else if (JSON.stringify(oldEle.location) !== JSON.stringify(e.location)) {
              // diff location 移动
              if (e.uuid) {
                (scene.getObjectByName(e.uuid) as any).position.set(
                  e.location.x,
                  e.location.y,
                  e.location.z
                );
              }

              editSceneData.value.interactionDtoList[index].materialMetaDtoList[i].location = {
                ...e.location,
              };
              cube.position.set(e.location.x, e.location.y, e.location.z);
            } else if (JSON.stringify(oldEle.rotation) !== JSON.stringify(e.rotation)) {
              // diff rotation 旋转
              const curObj: any = scene.getObjectByName(e.uuid);
              editSceneData.value.interactionDtoList[index].materialMetaDtoList[i].rotation = {
                ...e.rotation,
              };
              if (e.metaInfo !== '') {
                curObj.rotation.set(-e.rotation.x, -e.rotation.y, -e.rotation.z);
              }
              const quaternion = curObj.quaternion.clone();
              let metaInfo = `${quaternion.x},${quaternion.y},${quaternion.z},${quaternion.w},${curObj.userData.initScale}`;
              editSceneData.value.interactionDtoList[index].materialMetaDtoList[i].metaInfo =
                metaInfo;
              store.state.editSceneData.interactionDtoList[index].materialMetaDtoList[i].metaInfo =
                metaInfo;
            } else if (JSON.stringify(oldEle.scale) !== JSON.stringify(e.scale)) {
              // diff scale 缩放
              editSceneData.value.interactionDtoList[index].materialMetaDtoList[i].scale = {
                ...e.scale,
              };
              const curObj: any = scene.getObjectByName(e.uuid);
              const initScale = curObj.userData.initScale;
              curObj.scale.set(e.scale.x * initScale, e.scale.y * initScale, e.scale.z * initScale);
            }
          });
        } else if (!editSceneData.value.interactionDtoList) {
          editSceneData.value.interactionDtoList[0] = { ...ele, materialMetaDtoList: [] };
        }
      });
    }
  }
);

watch(
  () => props.sceneData,
  (newState: any) => {
    // 初始化数据
    editSceneData.value = { ...newState, outerMaterialMetaDtoList: [], interactionDtoList: [] };
    const currentOuterMaterialMetaDtoList = newState.outerMaterialMetaDtoList || [];
    const currentInteractionDtoList = newState.interactionDtoList || [];
    currentOuterMaterialMetaDtoList.forEach((ele: any, index: number) => {
      createModel(ele, 'outerMaterialMetaDtoList', index);
    });
    currentInteractionDtoList.forEach((ele: any, index: number) => {
      if (ele.materialMetaDtoList && ele.materialMetaDtoList.length) {
        ele.materialMetaDtoList.forEach((e: any, i: number) => {
          createModel(e, 'interactionDtoList', index, i, ele);
        });
      } else {
        editSceneData.value.interactionDtoList[index] = { ...ele };
      }
    });
  }
);

defineExpose({
  exitEdit,
  canvas3dRef, // 暴露canvas3dRef引用
});
</script>
<style scoped lang="less">
.edit-box {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
}
</style>
