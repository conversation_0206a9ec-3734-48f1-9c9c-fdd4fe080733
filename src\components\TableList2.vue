<template>
  <div class="table-module">
    <el-table
      :data="tableData"
      style="width: 100%; max-height: 100%"
      row-key="id"
      class="styleTable"
      @expand-change="expandChange"
      :class="expandGroup ? 'expand-style' : ''"
    >
      <el-table-column
        v-for="item in columnList"
        :prop="item.prop"
        :key="item.prop"
        :label="item.label"
        :show-overflow-tooltip="true"
        :width="item.width"
        :fixed="item.prop === 'operate' ? 'right' : null"
        :column-key="item.prop"
      >
        <template #default="scope">
          <img
            v-if="item.type === 'image' && scope.row[item.prop]"
            :src="scope.row[item.prop]"
            width="131"
            height="90"
            style="border-radius: 4px; vertical-align: middle"
            :class="!scope.row.children && hasGroupChild ? 'imageML' : ''"
          />
          <span v-if="item.list">{{ item.list[scope.row[item.prop]] }}</span>
          <div
            v-if="item.prop == 'operate' && !scope.row.children"
            class="operate-style"
          >
            <span
              @click="
                () => {
                  props.handleOpenScene(scope.row);
                }
              "
              >打开</span
            >
            <span
              @click="
                () => {
                  props.handleShare(scope.row);
                }
              "
              >分享</span
            >
            <span class="move-to-style" v-if="!scope.row.groupId">
              移动至
              <el-select
                v-model="groupId"
                placeholder="Select"
                @change="changeGroup"
                v-if="sceneGroups.length"
              >
                <el-option
                  v-for="item in sceneGroups"
                  :key="item.id"
                  :label="item.groupName"
                  :value="`${scope.row.id}_${item.id}`"
                  :disabled="item.disabled"
                />
              </el-select>
            </span>
            <span
              class="move-to-style"
              v-else
              @click="
                () => {
                  props.handleRemove(scope.row);
                }
              "
              >移出</span
            >
            <span
              @click="
                () => {
                  props.handleSetTemplate(scope.row);
                }
              "
              >设为模板</span
            >
            <span
              @click="
                () => {
                  props.deleteScene(scope.row);
                }
              "
              style="color: #eb5757"
              >删除</span
            >
          </div>
          <div
            v-if="item.prop == 'operate' && scope.row.children"
            class="group-operate"
          >
            <span
              @click="
                () => {
                  props.updateGroup(scope.row);
                }
              "
              >修改名称</span
            >
            <span
              @click="
                () => {
                  props.deleteGroup(scope.row);
                }
              "
              style="color: #eb5757"
              >删除</span
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length" class="pagination">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="dataTotal"
        :current-page="pageNo"
        prev-text="上一页"
        next-text="下一页"
        @current-change="changePageEvent"
        :pager-count="5"
        :page-size="pageSize"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { addSceneGroupRelation } from "@/api";

const props = defineProps({
  tableData: {
    default: null,
    type: Object,
  },
  expandChangeEvent: {
    default: null,
    type: Function,
  },
  handleOpenScene: {
    default: null,
    type: Function,
  },
  handleShare: {
    default: null,
    type: Function,
  },
  handleMoveTo: {
    default: null,
    type: Function,
  },
  handleSetTemplate: {
    default: null,
    type: Function,
  },
  handleRemove: {
    default: null,
    type: Function,
  },
  deleteScene: {
    default: null,
    type: Function,
  },
  sceneGroups: {
    default: null,
    type: Object,
  },
  expandGroup: {
    default: null,
    type: Object,
  },
  deleteGroup: {
    default: null,
    type: Function,
  },
  updateGroup: {
    default: null,
    type: Function,
  },
  dataTotal: {
    default: 0,
    type: Number,
  },
  pageSize: {
    default: 20,
    type: Number,
  },
  pageNo: {
    default: 1,
    type: Number,
  },
  changePage: {
    default: null,
    type: Function,
  },
});

const columnList: any = ref([
  {
    prop: "scenePic",
    label: "缩略图",
    type: "image",
    width: 200,
  },
  {
    prop: "sceneName",
    label: "名称",
  },
  {
    prop: "scenePlatform",
    label: "所属平台",
    list: {
      0: "所有平台",
      null: "所有平台",
      1: "眼镜端",
      2: "移动端",
      3: "微信小程序",
    },
  },
  {
    prop: "sceneType",
    label: "所属类型",
    list: {
      0: "所有平台",
      null: "所有平台",
      1: "空间AR",
      2: "平面AR",
      3: "图像AR",
      5: "身体AR",
      6: "人脸AR",
      7: "手势AR",
      8: "单场景AR",
    },
  },
  {
    prop: "createTimeStr",
    label: "创建时间",
  },
  {
    prop: "updateTimeStr",
    label: "创建时间",
  },
  {
    prop: "operate",
    label: "操作",
    width: 290,
  },
]);

const hasGroupChild = ref(false);
const groupId = ref(-1);

const changePageEvent = (e: any) => {
  props.changePage && props.changePage(e);
};

const expandChange = (row: any, expandedRows: any) => {
  props.expandChangeEvent(row, expandedRows);
};

const changeGroup = (val: string) => {
  props.handleMoveTo(val);
};

onMounted(() => {
  //
});

watch(
  () => props.tableData,
  (newState) => {
    hasGroupChild.value = false;
    newState.forEach((e: any) => {
      if (e.children?.length) {
        hasGroupChild.value = true;
      }
    });
  }
);
</script>
<style lang="less">
::-webkit-scrollbar {
  width: 5px;
  background-color: transparent;
}
.pagination {
  position: fixed;
  bottom: 15px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
.table-module .styleTable {
  background-color: transparent !important;
  border-radius: 4px;
  overflow: hidden;
  overflow-y: auto;
  height: calc(100vh - 290px);

  .el-table__header-wrapper th {
    background-color: rgba(230, 237, 247, 0.3) !important;
    font-weight: 400;
    font-size: 12px;
    color: #797979;
  }

  .el-table__inner-wrapper:before {
    background-color: transparent;
  }

  .imageML {
    position: relative;
    left: -20px;
  }

  .operate-style {
    & > span {
      display: inline-block;
      margin-left: 20px;
      cursor: pointer;
    }

    & > span:first-child {
      margin-left: 0;
    }

    .move-to-style {
      position: relative;
    }
  }

  .group-operate {
    & > span {
      margin-left: 20px;
      cursor: pointer;
    }
  }

  &.expand-style {
    height: calc(100vh - 330px);
  }
}

.el-table__empty-block {
  border-left: 1px solid #e6edf7;
  border-right: 1px solid #e6edf7;
  box-sizing: border-box;
}

.el-table th.el-table__cell,
.el-table tr {
  height: 42px;
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;

  & > td {
    border-bottom: 1px solid #e6edf7;
    box-sizing: border-box;
  }

  & > th {
    border-top: 1px solid #e6edf7 !important;
  }

  & > th:first-child {
    border-left: 1px solid #e6edf7 !important;
  }

  & > th:last-child {
    border-right: 1px solid #e6edf7 !important;
  }
}

.el-table tr td.el-table__cell:last-child {
  border-right: 1px solid #e6edf7 !important;
}

.el-table tr td.el-table__cell:first-child {
  border-left: 1px solid #e6edf7 !important;
}

.el-table__header-wrapper th {
  background-color: rgba(230, 237, 247, 0.3) !important;
  font-weight: 400;
  font-size: 12px;
  color: #797979;
}

.el-table__header-wrapper tr {
  height: 32px;
}

.el-table th {
  text-align: center;
}

.el-table th:first-child {
  text-align: left;
}

.el-table th:last-child {
  text-align: right;
}

.el-table td {
  text-align: center;
}

.el-table td:first-child {
  text-align: left;
}

.el-table td:last-child {
  text-align: right;
}
</style>
