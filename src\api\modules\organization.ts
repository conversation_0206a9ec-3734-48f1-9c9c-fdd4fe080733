import request from '../request';

// 创建企业并生成管理员账号
export function createOrgnizaiton(data: any) {
  const param = { ...data };
  const requestParam: any = {};
  delete param.packageId;
  for (const key in param) {
    if (!['isOperation', 'sceneDuration'].includes(key)) {
      requestParam[key] = param[key];
    }
  }
  return request({
    url:
      '/authority/orgnization/createOrgnizaiton?packageId=' +
      data.packageId +
      '&isOperation=' +
      param.isOperation +
      '&sceneDuration=' +
      param.sceneDuration,
    method: 'post',
    data: requestParam,
  });
}

// 更新企业账号信息
export function updateOrgnizaiton(data: any) {
  const param = { ...data };
  delete param.packageId;

  return request({
    url:
      '/authority/orgnization/updateOrgnizaiton?' +
      'isOperation=' +
      (param.isOperation || '') +
      '&sceneDuration=' +
      (param.sceneDuration || ''),
    method: 'post',
    data: param,
  });
}

// 获取企业信息
export function getOrgnizationInfos(data: any) {
  // 构建查询参数，过滤掉空值
  const params = new URLSearchParams();

  // 必需参数
  params.append('pageNo', data.pageNo);
  params.append('pageSize', data.pageSize);

  // 可选参数，只有非空时才添加
  if (data.orgnizationName && data.orgnizationName.trim()) {
    params.append('orgnizationName', data.orgnizationName.trim());
  }
  if (data.orgnizationType && data.orgnizationType.trim()) {
    params.append('orgnizationType', data.orgnizationType.trim());
  }
  if (data.packageId && data.packageId.trim()) {
    params.append('packageId', data.packageId.trim());
  }

  return request({
    url: `/authority/orgnization/getOrgnizationInfos?${params.toString()}`,
    method: 'get',
  });
}

// 获取企业统计信息
export function getOrgnizationStatistic() {
  return request({
    url: 'statistic/getOrgnizationStatistic',
    method: 'get',
  });
}

// 关闭用户
export function closeUser(data: any) {
  return request({
    url: `/authority/closeUser?userId=${data.userId}`,
    method: 'post',
  });
}

// 重新开启用户
export function reopenUser(data: any) {
  return request({
    url: `/authority/reopenUser?userId=${data.userId}`,
    method: 'post',
  });
}

// 获取企业用户列表
export function getOrgnizationUsers(data: any) {
  return request({
    url: `/authority/getOrgnizationUsers?pageNo=${data.pageNo}&pageSize=${data.pageSize}&userName=${
      data.userName || ''
    }&userStatus=${data.userStatus || ''}`,
    method: 'get',
  });
}

// 创建企业V2
export function createOrgnizationV2(data: any) {
  return request({
    url: 'authority/orgnization/createOrgnizationV2',
    method: 'post',
    data,
  });
}
