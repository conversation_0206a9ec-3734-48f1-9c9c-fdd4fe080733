<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div>{{ props.defaultValue.id ? '修改' : '新增' }}客户</div>
      </div>
      <div class="modal-form">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="120px"
          class="demo-ruleForm">
          <el-form-item label="客户名称" prop="orgnizationName">
            <el-input
              class="form-input"
              v-model="ruleForm.orgnizationName"
              placeholder="请输入客户名称"
              :disabled="modalShow == 'reopen'" />
          </el-form-item>
          <el-form-item label="客户类型" prop="orgnizationType">
            <el-select
              v-model="ruleForm.orgnizationType"
              clearable
              placeholder="请选择客户类型"
              class="select-default"
              v-if="!props.defaultValue.id"
              popper-class="select-option"
              :suffix-icon="DropDown"
              style="width: 432px; height: 36px">
              <el-option
                v-for="(item, index) in orgnizationTypes"
                :key="index"
                :label="item.name"
                :value="item.value" />
            </el-select>
            <span v-else>{{ calcuOrgnType() }}</span>
          </el-form-item>
          <el-form-item label="套餐类型" prop="packageId">
            <el-select
              v-if="!props.defaultValue.id"
              v-model="ruleForm.packageId"
              clearable
              placeholder="请选择套餐类型"
              class="select-default"
              :class="packageInfo.id ? 'select-package-show' : ''"
              popper-class="select-option"
              :suffix-icon="DropDown"
              style="width: 432px; height: 36px"
              @change="selectPackage">
              <el-option
                v-for="(item, index) in packageData"
                :key="index"
                :label="item.packageName"
                :value="item.id" />
            </el-select>
            <div class="flex_box" v-else>
              <span>{{ calcuPackage() }}</span>
              <el-button
                class="btn"
                @click="dialogUpdateVisible = true"
                color="#2e76ff"
                style="margin-left: 15px"
                :disabled="modalShow == 'reopen'">
                更新套餐
              </el-button>
            </div>
          </el-form-item>

          <div
            class="btn_look"
            v-if="!props.defaultValue.id && ruleForm.packageId"
            @mouseenter="handleHover(true)"
            @mouseleave="handleHover(false)">
            查看详情
          </div>
          <div class="foreVorign" :class="{ foreVcolor: props.defaultValue.id }">
            <el-form-item label="账号" prop="contactEmail">
              <el-input
                v-if="!props.defaultValue.id"
                class="form-input"
                v-model="ruleForm.contactEmail"
                placeholder="请输入客户邮箱" />
              <span v-else>{{ ruleForm.contactEmail }}</span>
            </el-form-item>
            <el-form-item label="类型" prop="isOperation">
              <el-select
                v-model="ruleForm.isOperation"
                v-if="!props.defaultValue.id"
                :disabled="props.defaultValue.id ? true : false"
                class="select-default"
                popper-class="select-option"
                :suffix-icon="DropDown"
                style="width: 432px; height: 36px">
                <el-option label="运营账号" :value="1" />
                <el-option label="非运营账号" :value="0" />
              </el-select>
              <div style="width: 100%; text-align: left" v-else>
                {{ props.defaultValue.adminUserInfo.isOperation == 1 ? '运营账号' : '非运营账号' }}
              </div>
              <div
                class="time_box"
                :class="{ hasTop: !props.defaultValue.id }"
                v-if="
                  (props.defaultValue.id && props.defaultValue.adminUserInfo.isOperation == 1) ||
                  (!props.defaultValue.id && ruleForm.isOperation)
                ">
                每个项目体验时长上限
                <input
                  type="text"
                  v-model="ruleForm.sceneDuration"
                  :disabled="modalShow == 'reopen'" />
                分钟
              </div>
            </el-form-item>
            <el-form-item label="有效期" prop="timeStr">
              <div class="line">
                <div class="startTime">{{ packageStart }}</div>
                <div>-</div>
                <!-- 添加 -->
                <div class="endTime" v-if="!props.defaultValue.id">
                  {{ props.defaultValue.id ? props.defaultValue.expireTimeStr : packageEnd }}
                </div>
                <!-- 修改 -->
                <div v-else class="con">
                  <div class="plo">
                    <el-icon>
                      <Calendar size="10" />
                    </el-icon>
                    <span>{{ formatMonth(monthValue) }}</span>
                  </div>
                  <el-date-picker
                    v-model="monthValue"
                    type="month"
                    popper-class="date-picker-popper"
                    @change="changeMonth"
                    :clearable="false"
                    :disabled-date="disabledDate" />
                </div>
                <el-button
                  style="margin-left: 20px"
                  type="primary"
                  @click="requestConfirm"
                  v-if="props.defaultValue.id"
                  color="#2e76ff">
                  更新时间
                </el-button>
              </div>
            </el-form-item>
          </div>

          <el-form-item label="联系人" prop="contactName">
            <el-input
              class="form-input"
              v-model="ruleForm.contactName"
              placeholder="请输入联系人姓名"
              :disabled="modalShow == 'reopen'" />
          </el-form-item>
          <el-form-item label="电话" prop="contactPhone">
            <el-input
              class="form-input"
              v-model="ruleForm.contactPhone"
              placeholder="请输入联系电话"
              :disabled="modalShow == 'reopen'" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              class="form-textarea"
              v-model="ruleForm.remark"
              type="textarea"
              placeholder="请输入2-10字备注"
              resize="none"
              :disabled="modalShow == 'reopen'" />
          </el-form-item>
          <el-form-item class="form-submit">
            <div>
              <el-button class="el-size3" @click="changeState">取消</el-button>
            </div>
            <div>
              <el-button class="el-size3" @click="submitForm(ruleFormRef)" color="#2e76ff">
                确认
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div
        class="package_info_box"
        v-if="packageInfo.id && isHover"
        :class="{ dialogPackage: dialogUpdateVisible }">
        <header>
          <div class="icon"></div>
          <div class="name">{{ dialogUpdateVisible ? diaHandleName : handleName }}</div>
        </header>
        <div>
          <span class="label">项目数量：</span>
          <main class="content_text">
            <div>小程序平面识别AR: {{ packageInfo.planeSceneNum || '--' }}</div>
            <div>大空间定位AR: {{ packageInfo.arSceneNum || '--' }}</div>
          </main>
        </div>
        <div>
          <span class="label">套餐时长：</span>
          <span style="margin-right: 40px" class="content_text">
            {{ packageInfo.expireDay == 99999 ? '不限' : packageInfo.expireDay + '天' }}
          </span>
        </div>
        <div v-if="packageInfo.spacePicTotalNum">
          <span class="label">空间上限：</span>
          <main
            v-if="packageInfo.spacePicSingleNum || packageInfo.spacePicTotalNum"
            class="content_text">
            <div class="sing" v-if="packageInfo.spacePicSingleNum">
              单次上传限制 {{ packageInfo.spacePicSingleNum }} 张
            </div>
            <div class="sing" v-if="packageInfo.spacePicTotalNum">
              总数上传限制 {{ packageInfo.spacePicTotalNum }}
              张
            </div>
          </main>
          <span v-else>--</span>
        </div>
        <div>
          <span class="label">大空间定位支持平台：</span>
          <main v-if="packageInfo.packagePlatformList" class="content_text">
            <div v-for="line in packageInfo.packagePlatformList" :key="line">
              {{ line == 1 ? '眼镜端' : line == 2 ? '移动端' : '微信小程序' }}
            </div>
          </main>
          <main v-else>--</main>
        </div>
        <div>
          <span class="label">空间数量：</span>
          <template v-if="packageInfo.spaceNum == null">
            <span style="margin-right: 40px" class="content_text">--</span>
          </template>
          <template v-else>
            <span v-if="packageInfo.spaceNum" style="margin-right: 40px" class="content_text">
              限制 {{ packageInfo.spaceNum }} 个
            </span>
            <span v-else style="margin-right: 40px" class="content_text">不限-照片用完为止</span>
          </template>
        </div>
        <div>
          <span class="label">素材空间总容量：</span>
          <span
            v-html="calculateSize(packageInfo.materialUploadSize)"
            style="margin-right: 40px"
            class="content_text"></span>
        </div>
        <div>
          <span class="label">单个素材上传上限：</span>
          <span style="margin-right: 40px" class="content_text">
            {{ packageInfo.singleUploadSize }}MB
          </span>
        </div>
      </div>
    </div>

    <el-dialog v-model="dialogUpdateVisible" title="更新套餐" width="500" align-center>
      <main style="margin: 20px 0" class="dialog_main">
        <el-form-item label="当前套餐类型：">
          <span>{{ currentPack.packageName }}</span>
        </el-form-item>
        <el-form-item label="套餐类型：">
          <el-select
            v-model="updatePackId"
            clearable
            placeholder="请选择套餐类型"
            class="select-default"
            :class="packageInfo.id ? 'select-package-show' : ''"
            popper-class="select-option"
            :suffix-icon="DropDown"
            style="width: 280px; height: 36px"
            @change="chooseUpdateId">
            <el-option
              v-for="(item, index) in packageData"
              :key="index"
              :label="item.packageName"
              :value="item.id" />
          </el-select>
        </el-form-item>

        <div
          class="btn_look dialogBtnLook"
          v-if="updatePackId"
          @mouseenter="handleHover(true, true)"
          @mouseleave="handleHover(false, true)">
          查看详情
        </div>
      </main>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogUpdateVisible = false" style="width: 110px">取消</el-button>
          <el-button type="primary" style="width: 110px" @click="updatePackageName" color="#2e76ff">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules, type UploadUserFile } from 'element-plus';
import DropDown from '@/components/DropDown.vue';
import { orgnizationTypes } from '@/config';
import {
  createOrgnizaiton,
  updateOrgnizaiton,
  updateUserPackage,
  updateUserPackageTime,
} from '@/api';
import { calculateSize } from '@/utils';
import dayjs from 'dayjs';

const emits = defineEmits(['updateValue']);
const props = defineProps({
  handleShow: {
    default: null,
    type: Function,
  },
  packageData: {
    default: null,
    type: Object,
  },
  defaultValue: {
    default: null,
    type: Object,
  },
  modalShow: {
    default: 'add',
    type: String,
  },
});

const monthValue = ref('');
const packageEnd = ref('');
const packageInfo: any = ref({});
const dialogUpdateVisible = ref(false);
const isHover = ref(false);
const lastDate = ref('');

const formatMonth = computed(() => {
  return (value) => {
    // 输入的日期字符串
    const dateString = value;

    // 创建 Date 对象
    const date = new Date(dateString);

    // 获取年、月、日
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
    const day = String(date.getDate()).padStart(2, '0');

    const formattedDate = `${year}-${month}-${lastDate.value}`;
    return formattedDate;
  };
});

interface RuleForm {
  orgnizationName: string;
  orgnizationType: string;
  packageId: string;
  maxSceneNum: string;
  maxMaterialUplodSize: string;
  contactEmail: string;
  contactName: string;
  contactPhone: string;
  remark: string;
  packageName: string;
  isOperation: number;
  timeStr: string;
  sceneDuration: number;
}

let currentPack = {};
const updatePackId = ref('');
const ruleFormRef = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  orgnizationName: '',
  orgnizationType: '',
  packageId: '',
  maxSceneNum: '0',
  maxMaterialUplodSize: '0',
  contactEmail: '',
  contactName: '',
  contactPhone: '',
  remark: '',
  packageName: '',
  isOperation: '',
  sceneDuration: 30,
});

const disabledDate = (date) => {
  if (!monthValue.value) {
    return false;
  }
  const after30Days = dayjs(date).isAfter(
    dayjs(props.defaultValue.packageStartTimeStr).add(0, 'day')
  );
  return !after30Days;
};

const requestConfirm = () => {
  const params = {
    updateUserId: props.defaultValue.adminUserInfo.id,
    startTime: props.defaultValue.packageStartTimeStr,
    endTime: props.defaultValue.expireTimeStr,
  };
  updateUserPackageTime(params).then((res) => {
    if (res.code == 200) {
      ElMessage({ type: 'success', message: '更新时间成功!' });
    }
  });
};

const handleHover = (flag) => {
  isHover.value = flag;
};

const changeMonth = (val) => {
  const date = new Date(val);
  emits('updateValue', date);
};

const handleName = computed(() => {
  const package_ = props.packageData.find((item) => item.id == ruleForm.packageId);
  return package_.packageName;
});

const diaHandleName = computed(() => {
  const package_ = props.packageData.find((item) => item.id == updatePackId.value);
  return package_.packageName;
});

const changeState = () => {
  props.handleShow('');
};

const packageStart = computed(() => {
  if (props.defaultValue.id) {
    return props.defaultValue.packageStartTimeStr;
  } else {
    return getCurrentDate();
  }
});

function getCurrentDate() {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
  const day = String(date.getDate()).padStart(2, '0'); // 获取当天的日期
  return `${year}-${month}-${day}`;
}

function addDaysToCurrentDate(days) {
  const date = new Date(); // 获取当前日期
  date.setDate(date.getDate() + days); // 将天数累加到当前日期
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
  const day = String(date.getDate()).padStart(2, '0'); // 获取当天的日期
  return days ? `${year}-${month}-${day}` : '';
}

const chooseUpdateId = (i) => {
  currentPack = props.packageData.find((item) => item.id == updatePackId.value);
  packageInfo.value = currentPack;
};

const calcuOrgnType = computed(() => {
  return () => {
    const orgnization = orgnizationTypes.find((item) => item.value == ruleForm.orgnizationType);
    return orgnization ? orgnization.name : '';
  };
});

const calcuPackage = computed(() => {
  return () => {
    currentPack = props.packageData.find((item) => item.id == ruleForm.packageId);
    return currentPack ? currentPack.packageName : '';
  };
});

const updatePackageName = () => {
  if (!currentPack) return ElMessage({ type: 'warning', message: '请选择套餐类型' });
  const params = {
    updateUserId: props.defaultValue.adminUserInfo.id,
    packageId: currentPack.id,
  };
  updateUserPackage(params).then((res) => {
    if (res.code == 200) {
      ElMessage({ type: 'success', message: '修改成功！' });
      ruleForm.packageId = updatePackId.value;
    }
  });
  dialogUpdateVisible.value = false;
};

const rules = reactive<FormRules<RuleForm>>({
  orgnizationName: [{ required: true, message: '请输入2-8字客户名称', trigger: 'blur' }],
  orgnizationType: [{ required: true, message: '请选择客户类型', trigger: 'blur' }],
  isOperation: [{ required: true, message: '请选择账号类型', trigger: 'blur' }],
  packageId: [{ required: true, message: '请选择套餐类型', trigger: 'blur' }],
  maxSceneNum: [{ required: true, message: '-', trigger: 'blur' }],
  maxMaterialUplodSize: [{ required: true, message: '-', trigger: 'blur' }],
  contactEmail: [{ required: true, message: '请输入客户邮箱', trigger: 'blur' }],
  contactName: [{ required: true, message: '请输入2-8字联系人姓名', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    const param = { ...ruleForm };
    delete param.maxSceneNum;
    delete param.maxMaterialUplodSize;
    param.orgnizationType = +param.orgnizationType || null;
    param.contactEmail = param.contactEmail.trim();
    if (valid) {
      if (props.defaultValue.id) {
        updateOrgnizaiton({
          ...ruleForm,
          id: props.defaultValue.id,
          packageName: currentPack.packageName,
        }).then((res: any) => {
          props.handleShow('', true);
        });
      } else {
        createOrgnizaiton(param).then((res: any) => {
          props.handleShow('', true);
        });
      }
    } else {
    }
  });
};

const selectPackage = (value: any) => {
  packageInfo.value = props.packageData.filter((e: any) => e.id == value)[0] || {};
  packageEnd.value = addDaysToCurrentDate(packageInfo.value.expireDay);
};

onMounted(() => {
  // 编辑页初始数据
  if (props.defaultValue.id) {
    ruleForm.orgnizationName = props.defaultValue.orgnizationName || '';
    ruleForm.orgnizationType =
      orgnizationTypes.filter((e: any) => e.name == props.defaultValue.orgnizationType)[0]?.value ||
      '';
    ruleForm.packageId = props.defaultValue.packageInfoDto.id || '';
    ruleForm.contactEmail = props.defaultValue.mail || '';
    ruleForm.contactName = props.defaultValue.contactName || '';
    ruleForm.contactPhone = props.defaultValue.contactPhone || '';
    ruleForm.remark = props.defaultValue.remark || '';
    ruleForm.isOperation = props.defaultValue.adminUserInfo.isOperation || 1;
    ruleForm.sceneDuration = props.defaultValue.adminUserInfo.sceneDuration || 30;

    monthValue.value = props.defaultValue.expireTimeStr;
  }
});

watch(
  () => props.defaultValue.expireTimeStr,
  (nv) => {
    if (nv) {
      lastDate.value = nv.split('-')[nv.split('-').length - 1];
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
.con {
  width: 200px;
  position: relative;
  margin-left: 10px;

  .plo {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 0;
    position: absolute;
    top: 0;
    left: 0;
    box-sizing: border-box;
    padding-left: 16px;
    border-radius: 10px;
    text-align: left;

    span {
      margin-left: 10px;
    }
  }

  :deep(.el-input) {
    opacity: 0;
    width: 200px !important;
  }
}

.dialog_main {
  :deep(.el-input__wrapper) {
    width: 290px !important;
  }

  :deep(.el-input__inner) {
    font-size: 14px;
  }
}

:deep(.el-dialog__header) {
  padding-bottom: 0px !important;
}

.line {
  position: relative;

  :deep(.el-input__wrapper) {
    padding: 0 14px !important;
    border: none !important;
  }

  .last-date {
    position: absolute;
    top: 3px;
    right: 184px;
    z-index: 999;
  }
}

:deep(.el-input__inner) {
  color: #1e1e1e;
}

:deep(.el-dialog__body) {
  padding-top: 3px !important;
  padding-bottom: 10px !important;
}

.time_box {
  font-weight: 400;
  font-size: 12px;
  color: #797979;

  input {
    width: 75px;
    margin: 0 4px;
    padding-left: 10px;
    box-sizing: border-box;
    border-radius: 10px;
    outline: none;
    border: none;
    border: 1px solid #ccc;
  }
}

.hasTop {
  margin-top: 10px !important;
}

.foreVorign {
  background: transparent;
}

.foreVcolor {
  background-color: #f7f7f7;
  padding-top: 16px;
  overflow: hidden;
  margin-bottom: 16px;
}

.btn_look {
  width: 432px;
  height: 24px;
  background: #f7f7f7;
  margin: 12px 0;
  font-size: 12px;
  color: #2e76ff;
  line-height: 24px;
  text-align: center;
  cursor: pointer;
  margin-left: 78px;
  border-radius: 10px;

  &:hover {
    background: #2e76ff;
    color: #fff;
  }
}

.dialogBtnLook {
  width: 293px;
}

:deep(.el-form-item) {
  margin-bottom: 16px !important;
  margin-top: 0 !important;
}

:deep(.el-form-item__label) {
  // width: 100px !important;
}

.line {
  display: flex;
  align-items: center;

  .startTime {
    margin-right: 10px;
  }

  .endTime {
    margin-left: 10px;
    margin-right: 20px;
  }
}

:deep(.el-dialog__header) {
  font-weight: bold;
  font-size: 18px;
  text-align: center;
}

:deep(.el-dialog__title) {
  color: #0675ff !important;
}

:deep(.el-form-item__label) {
  width: 120px;
  text-align: right;
}

:deep(.el-select__wrapper) {
  width: 432px;
}

.dialog_main {
  :deep(.el-select__wrapper) {
    width: 280px !important;
  }
}

:deep(.el-dialog.is-align-center) {
  padding: 25px;
  border-radius: 6px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .flex_box {
    width: 85%;
    display: flex;
    justify-content: flex-start;
  }

  main {
    width: calc(100% - 8px);
  }

  .modal-content {
    width: 555px;
    height: 710px;
    max-height: 90%;
    background: #fff;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    border: 1px solid #edeff2;
    position: relative;
    // overflow: hidden;

    .modal-content-title {
      height: 76px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px 0 30px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;

      .icon-close {
        font-size: 26px;
        cursor: pointer;
        font-weight: 400;

        &:hover {
          color: #2e76ff;
        }
      }
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      padding: 15px 15px;
      box-sizing: border-box;
      overflow-y: auto;
      padding-top: 0;
      display: flex;
      justify-content: flex-end;
      padding-right: 20px;

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-textarea {
        width: 432px;
        height: 91px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-submit {
        margin-top: 82px;
        position: absolute;
        bottom: 5px;
        right: 20px;
      }

      .select-package-show {
        ::v-deep(.el-input__wrapper) {
          border-radius: 8px 8px 0px 0px !important;
        }
      }
    }
  }
}

.el-size3 {
  width: 114px;
  height: 38px;
  margin-left: 12px;
}

.package_info_box {
  position: absolute;
  top: 150px;
  right: -465px;
  padding: 10px;
  text-align: left;
  padding-left: 20px;
  box-sizing: border-box;
  font-size: 14px;
  color: #606266;
  line-height: 34px;
  margin: -19px 74px 18px 120px;
  z-index: 10;
  background: #fff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #2e76ff;
  width: 380px;

  .label {
    font-weight: 400;
    font-size: 12px;
    color: #797979;
  }

  .content_text {
    font-weight: 400;
    font-size: 12px;
    color: #1e1e1e;
  }

  header {
    display: flex;
    align-items: center;

    .icon {
      width: 2px;
      height: 21px;
      background: #2e76ff;
      margin-right: 8px;
      border-radius: 0px 0px 0px 0px;
    }

    .name {
      font-weight: bold;
      font-size: 14px;
      color: #1e1e1e;
    }
  }

  & > div {
    display: flex;
  }

  & > div > span:first-child {
    display: inline-block;
    min-width: 150px;
    max-width: 150px;
  }

  & > div > span:last-child {
    width: calc(100% - 220px);
  }
}

.dialogPackage {
  z-index: 9999 !important;
  top: 191px;
  right: -438px;
}
</style>
