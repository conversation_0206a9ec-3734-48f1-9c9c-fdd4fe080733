<template>
  <div class="left-panel">
    <div class="params-content">
      <ImageParams v-if="props.type === AIModelType.IMAGE" ref="imageParamsRef" />
      <VoiceParams v-else-if="props.type === AIModelType.AUDIO" ref="voiceParamsRef" />
      <VideoParams v-else-if="props.type === AIModelType.VIDEO" ref="videoParamsRef" />
      <ModelParams v-else-if="props.type === AIModelType.MODEL" ref="modelParamsRef" />
    </div>
    <button
      class="generate-btn"
      :class="{ disabled: !isFormValid }"
      :disabled="!isFormValid"
      @click="handleGenerate"
      @mousedown="handleMouseDown">
      立即生成
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';
import {
  genAiVoice,
  genAiPic as genAiImage,
  genAiVideo,
  genAiModel,
  type GenAiVideoParams,
} from '@/api/modules/ai';
import { ApiResponse } from '@/types/api';
import ImageParams from './ImageParams.vue';
import VoiceParams from './VoiceParams.vue';
import VideoParams from './VideoParams.vue';
import ModelParams from './ModelParams.vue';
import { AIModelType } from '@/config';
import { startTaskPolling } from './utils';

// 定义 props
interface Props {
  type: number;
}

const props = defineProps<Props>();
const store = useStore();

const imageParamsRef = ref();
const voiceParamsRef = ref();
const videoParamsRef = ref();
const modelParamsRef = ref();

const emit = defineEmits([
  'show-progress',
  'update-progress',
  'generation-complete',
  'add-close-handler',
]);

// 添加任务状态ref
const taskStatus = ref(0);
const taskRank = ref(0);

// 表单验证计算属性
const isFormValid = computed(() => {
  try {
    if (props.type === AIModelType.AUDIO) {
      // 语音参数验证 - 使用validateParams方法
      const isValid = voiceParamsRef.value?.validateParams?.() || false;
      console.log('=== isFormValid 调试 ===');
      console.log('voiceParamsRef.value:', voiceParamsRef.value);
      console.log('validateParams result:', isValid);
      console.log('=======================');
      return isValid;
    } else if (props.type === AIModelType.IMAGE) {
      // 图片参数验证 - 使用validateParams方法
      return imageParamsRef.value?.validateParams?.() || false;
    } else if (props.type === AIModelType.VIDEO) {
      // 视频参数验证 - 使用validateParams方法避免弹出错误消息
      try {
        return videoParamsRef.value?.validateParams?.() || false;
      } catch (error) {
        return false;
      }
    } else if (props.type === AIModelType.MODEL) {
      // 3D模型参数验证 - 使用validateParams方法避免弹出错误消息
      try {
        return modelParamsRef.value?.validateParams?.() || false;
      } catch (error) {
        return false;
      }
    }
    return false;
  } catch (error) {
    return false;
  }
});

// 移除定时器，改用响应式更新

const handleMouseDown = () => {
  // 按钮被按下的处理逻辑
};

// 处理语音生成
const handleVoiceGenerate = async (params: any) => {
  console.log('=== handleVoiceGenerate 调试 ===');
  console.log('params:', params);
  console.log('params.desc:', params?.desc);
  console.log('params.desc.value:', params?.desc?.value);
  console.log('params.ratio:', params?.ratio);
  console.log('params.ratio.value:', params?.ratio?.value);
  console.log('params.selectedVoice:', params?.selectedVoice);
  console.log('params.selectedVoice.value:', params?.selectedVoice?.value);
  console.log('params.selectedVoice?.voiceType:', params?.selectedVoice?.voiceType);
  console.log('================================');

  const apiParams = {
    voiceType: params.selectedVoice?.voiceType || 'zh_female_daimengchuanmei_moon_bigtts',
    speedRatio: params.ratio, // 修复：直接使用ratio而不是ratio.value
    text: params.desc, // 修复：直接使用desc而不是desc.value
  };

  try {
    // 立即显示进度弹窗
    taskStatus.value = 0;
    taskRank.value = 0;
    emit('show-progress', { taskId: 0 });

    const response: ApiResponse<number> = await genAiVoice(apiParams);
    if (response.code === '200' && !response.error) {
      const taskId = response.data;

      // 使用通用轮询函数
      const clearPolling = startTaskPolling({
        taskId,
        taskType: '语音',
        onProgress: (data) => {
          taskStatus.value = data.status;
          taskRank.value = data.rank;
          emit('update-progress', data);
        },
        onComplete: () => {
          emit('generation-complete');
        },
      });

      // 添加关闭事件监听
      emit('add-close-handler', clearPolling);
    }
  } catch (error: any) {
    console.error('语音生成失败:', error);

    // 清除可能被全局拦截器设置的错误状态
    if (store.state.showTips) {
      store.state.showTips = '';
    }
    if (store.state.loginShowInfo) {
      store.state.loginShowInfo = null;
    }

    // 显示本地错误消息
    ElMessage.error('语音生成失败，请重试');
    emit('generation-complete'); // 关闭进度弹窗
  }
};

// 处理图片生成
const handleImageGenerate = async (params: any) => {
  // 检查参数
  if (!params.ratios || !Array.isArray(params.ratios)) {
    console.error('params.ratios不存在或不是数组');
    return;
  }

  // 检查描述文本是否为空
  if (!params.desc || params.desc.trim() === '') {
    console.error('描述文本为空:', params.desc);
    ElMessage.warning('请输入图片描述');
    return;
  }

  const selectedRatio = params.ratios.find((r: any) => r.value === params.ratio);

  if (!selectedRatio) {
    console.error('未找到选中的比例:', params.ratio);
    ElMessage.warning('请选择图片尺寸');
    return;
  }

  const apiParams = {
    prompt: params.desc.trim(),
    size: `${selectedRatio.width}x${selectedRatio.height}`,
  };

  try {
    // 立即显示进度弹窗
    taskStatus.value = 0;
    taskRank.value = 0;
    emit('show-progress', { taskId: 0 });

    const response: ApiResponse<number> = await genAiImage(apiParams);
    if (response.code === '200' && !response.error) {
      const taskId = response.data;

      // 使用通用轮询函数
      const clearPolling = startTaskPolling({
        taskId,
        taskType: '图片',
        onProgress: (data) => {
          taskStatus.value = data.status;
          taskRank.value = data.rank;
          emit('update-progress', data);
        },
        onComplete: () => {
          emit('generation-complete');
        },
      });

      // 添加关闭事件监听
      emit('add-close-handler', clearPolling);
    }
  } catch (error: any) {
    console.error('图片生成失败:', error);

    // 清除可能被全局拦截器设置的错误状态
    if (store.state.showTips) {
      store.state.showTips = '';
    }
    if (store.state.loginShowInfo) {
      store.state.loginShowInfo = null;
    }

    // 显示本地错误消息
    ElMessage.error('图片生成失败，请重试');
    emit('generation-complete'); // 关闭进度弹窗
  }
};

// 处理视频生成
const handleVideoGenerate = async (params: any) => {
  if (!params) return;

  // 将分辨率字符串转换为数字
  const getVideoQualityNumber = (resolution: string): number => {
    switch (resolution) {
      case '480p':
        return 480;
      case '720p':
        return 720;
      case '1080p':
        return 1080;
      default:
        return 720; // 默认值
    }
  };

  // 将比例字符串转换为数字
  const getRatioNumber = (ratio: string): number => {
    switch (ratio) {
      case '1:1':
        return 1;
      case '16:9':
        return 16 / 9;
      case '9:16':
        return 9 / 16;
      case '4:3':
        return 4 / 3;
      case '3:4':
        return 3 / 4;
      case 'keep_ratio':
        return 0; // 特殊值表示保持原比例
      default:
        return 1; // 默认1:1
    }
  };

  // 处理图片参数 - 将File对象转换为base64字符串或URL
  const getPicString = async (pic: File | null): Promise<string> => {
    if (!pic) return '';

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(pic);
    });
  };

  try {
    // 立即显示进度弹窗
    taskStatus.value = 0;
    taskRank.value = 0;
    emit('show-progress', { taskId: 0 });

    // 处理图片参数
    const picString = await getPicString(params.pic);

    // 组装新的API参数结构
    const apiParams: GenAiVideoParams = {
      queryParams: {
        VideoQuality: getVideoQualityNumber(params.resolution),
        duration: params.duration,
        ratio: getRatioNumber(params.tab === 'img2video' ? 'keep_ratio' : params.ratio),
      },
      bodyParams: {
        prompt: params.desc,
        pic: picString,
      },
    };

    const response: ApiResponse<number> = await genAiVideo(apiParams);
    if (response.code === '200' && !response.error) {
      const taskId = response.data;

      // 使用通用轮询函数
      const clearPolling = startTaskPolling({
        taskId,
        taskType: '视频',
        onProgress: (data) => {
          taskStatus.value = data.status;
          taskRank.value = data.rank;
          emit('update-progress', data);
        },
        onComplete: () => {
          emit('generation-complete');
        },
      });

      // 添加关闭事件监听
      emit('add-close-handler', clearPolling);
    }
  } catch (error: any) {
    console.error('视频生成失败:', error);

    // 清除可能被全局拦截器设置的错误状态
    if (store.state.showTips) {
      store.state.showTips = '';
    }
    if (store.state.loginShowInfo) {
      store.state.loginShowInfo = null;
    }

    // 显示本地错误消息
    ElMessage.error('视频生成失败，请重试');
    emit('generation-complete'); // 关闭进度弹窗
  }
};

// 处理3D模型生成
const handle3DModelGenerate = async (params: any) => {
  if (!params) return;

  try {
    // 立即显示进度弹窗
    taskStatus.value = 0;
    taskRank.value = 0;
    emit('show-progress', { taskId: 0 });

    let response: ApiResponse<number>;

    if (params.type === 'text') {
      // 文本生成3D模型
      response = await genAiModel({
        pic: 'string',
        prompt: params.desc,
        modelStyle: params.style,
      });
    } else if (params.type === 'image') {
      // 图片生成3D模型
      response = await genAiModel({
        pic: params.pic,
        prompt: '', // 图片生成模式不需要prompt
        modelStyle: 'general', // 默认风格
      });
    } else {
      ElMessage.error('未知的3D模型生成类型');
      emit('generation-complete');
      return;
    }

    if (response.code === '200' && !response.error) {
      const taskId = response.data;

      // 使用通用轮询函数
      const clearPolling = startTaskPolling({
        taskId,
        taskType: '3D模型',
        onProgress: (data) => {
          taskStatus.value = data.status;
          taskRank.value = data.rank;
          emit('update-progress', data);
        },
        onComplete: () => {
          emit('generation-complete');
        },
      });

      // 添加关闭事件监听
      emit('add-close-handler', clearPolling);
    }
  } catch (error: any) {
    console.error('3D模型生成失败:', error);

    // 清除可能被全局拦截器设置的错误状态
    if (store.state.showTips) {
      store.state.showTips = '';
    }
    if (store.state.loginShowInfo) {
      store.state.loginShowInfo = null;
    }

    // 显示本地错误消息
    ElMessage.error('3D模型生成失败，请重试');
    emit('generation-complete'); // 关闭进度弹窗
  }
};

const handleGenerate = async () => {
  // 如果表单无效，直接返回
  if (!isFormValid.value) {
    return;
  }

  let params: any = null;

  // 获取参数
  if (props.type === AIModelType.AUDIO) {
    params = voiceParamsRef.value;

    // 调试信息
    console.log('=== 语音参数调试 ===');
    console.log('params:', params);
    console.log('params.desc:', params?.desc);
    console.log('params.desc.value:', params?.desc?.value);
    console.log('params.desc.trim():', params?.desc?.trim?.());
    console.log('==================');

    // 检查语音参数 - 修复：直接访问desc而不是desc.value
    if (!params?.desc?.trim?.()) {
      ElMessage.warning('请输入要生成的文本');
      return;
    }
  } else if (props.type === AIModelType.IMAGE) {
    params = imageParamsRef.value?.getParams?.();
    // 检查图片参数
    if (!params?.desc?.trim()) {
      ElMessage.warning('请输入图片描述');
      return;
    }
  } else if (props.type === AIModelType.VIDEO) {
    params = videoParamsRef.value?.getParams?.();
    // 检查视频参数 - 文字必填，图片可选
    if (!params?.desc?.trim()) {
      ElMessage.warning('请输入视频描述');
      return;
    }
    // 注意：图片在img2video模式下是可选的，不需要强制检查
  } else if (props.type === AIModelType.MODEL) {
    params = modelParamsRef.value?.getParams?.();
    // 检查3D模型参数
    if (params?.type === 'text' && !params?.desc?.trim()) {
      ElMessage.warning('请输入模型描述');
      return;
    }
  }

  if (!params) {
    console.error('获取参数失败');
    return;
  }

  // 先显示进度弹窗，初始状态为0
  emit('show-progress', { status: 0, rank: 0 });

  try {
    switch (props.type) {
      case AIModelType.AUDIO:
        await handleVoiceGenerate(params);
        break;
      case AIModelType.IMAGE:
        await handleImageGenerate(params);
        break;
      case AIModelType.VIDEO:
        await handleVideoGenerate(params);
        break;
      case AIModelType.MODEL:
        await handle3DModelGenerate(params);
        break;
      default:
        console.error('未知的AI类型:', props.type);
        return;
    }
  } catch (error) {
    console.error('AI生成失败:', error);
    ElMessage.error('生成失败，请重试');
  }
};

// 移除生命周期钩子中的定时器
</script>

<style scoped lang="less">
.left-panel {
  width: 370px;
  min-width: 370px;
  min-height: 600px; // 设置最小高度
  background: #fff;
  border-radius: 10px;
  padding: 24px 20px 100px 20px; // 底部预留按钮空间
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow-y: auto;
  max-height: 100%;
  box-sizing: border-box;

  .params-content {
    width: 100%;
    flex-shrink: 0;
  }

  .generate-btn {
    width: 320px;
    height: 60px;
    background: #2e76ff !important;
    color: #fff !important;
    font-size: 18px;
    font-weight: 500;
    border-radius: 8px;
    border: none;
    box-shadow: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: absolute;
    bottom: 20px;
    left: 50%;
    margin-left: -160px; /* 宽度的一半，确保居中 */

    &:hover:not(.disabled) {
      background: #1e66ef !important;
    }

    &.disabled {
      background: #1e66ef !important;
      color: #fff !important;
      cursor: not-allowed;
      opacity: 0.6;

      &:hover {
        background: #2e76ff !important;
        opacity: 0.6;
      }
    }
  }
}
</style>
