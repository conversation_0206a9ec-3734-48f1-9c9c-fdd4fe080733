<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div class="header">{{ defaultValue.orgnizationName }}扩展历史</div>
        <div class="close" @click.stop="hideModal"></div>
      </div>
      <div class="exp-box">
        <table-list
          ref="tableRefs"
          :data="packageData"
          :column-list="columnList"
          :change-page="changePage"
          :data-total="pageTotal"
          :page-size="searchForm.pageSize"
          :page-no="searchForm.pageNo"
          :search-form="searchForm"
          :heightSize="'420px'"></table-list>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { getUserBindPackage } from '@/api';
import { getDayTime } from '@/utils';
import TableList from '@/components/TableList.vue';

const packageData: any = ref([]);
const pageTotal = ref(0);
const searchForm: any = reactive({
  // 查询对象
  pageSize: 100,
  pageNo: 1,
});

const props = defineProps({
  handleShow: {
    default: null,
    type: Function,
  },
  defaultValue: {
    default: null,
    type: Object,
  },
});

const columnList: any = ref([
  {
    prop: 'packageTime',
    label: '账号有效期',
    width: 195,
  },
  {
    prop: 'sceneNum',
    label: '项目数量',
  },
  {
    prop: 'materialUploadSize',
    label: '素材总容量',
  },
  {
    prop: 'singleUploadSize',
    label: '单个素材上限',
  },
  {
    prop: 'shareExtensionVisitCount',
    label: '项目分享次数',
  },
  {
    prop: 'spacePicTotalNum',
    label: '空间容量',
  },
  {
    prop: 'vpsQps',
    label: '并发数量',
  },
  {
    prop: 'qpsUseTime',
    label: '并发数量有效期',
    width: 195,
  },
  {
    prop: 'others',
    label: '其他更新',
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    width: 110,
  },
]);

const hideModal = () => {
  props.handleShow();
};

const changePage = () => {
  //
};

onMounted(() => {
  getUserBindPackage({ userId: props.defaultValue.userBindPackageDto.userId }).then((res) => {
    res.data.secondData.forEach((e: any) => {
      const obj = {
        packageTime: `${getDayTime(
          props.defaultValue.userBindPackageDto.packageStartTime
        )}~${getDayTime(props.defaultValue.userBindPackageDto.packageEndTime)}`,
        sceneNum: e.sceneNum + '个',
        materialUploadSize: Math.round(e.materialUploadSize / 1024) + 'GB',
        singleUploadSize: e.singleUploadSize + 'MB',
        shareExtensionVisitCount: (e.shareExtensionVisitCount || 0) + '次',
        spacePicTotalNum: e.spacePicTotalNum + '张',
        vpsQps: e.vpsQps + '个',
        qpsUseTime: e.qpsUseEndTime
          ? `${getDayTime(e.qpsUseStartTime)}~${getDayTime(e.qpsUseEndTime)}`
          : '-',
        others: e.functionAddDescription || '-',
        updateTime: e.updateTime ? getDayTime(e.updateTime) : '-',
      };
      packageData.value.push(obj);
    });
    pageTotal.value = res.data.secondData.length;
  });
});
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .modal-content {
    width: 1234px;
    height: 588px;
    max-height: 90%;
    background: #fff;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    border: 1px solid #edeff2;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .modal-content-title {
      position: relative;
      height: 48px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px 0 22px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;

      .close {
        width: 24px;
        height: 24px;
        position: absolute;
        right: 21px;
        top: 12px;
        background: url(~@/assets/images/icon/close.png);
        background-size: 100% 100%;
        cursor: pointer;
      }
    }
    .exp-box {
      width: 1186px;
      height: 340px;
      margin-left: 24px;
      margin-bottom: 30px;
    }
  }
}
</style>
