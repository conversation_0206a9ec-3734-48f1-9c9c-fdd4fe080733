import {
  BufferGeometry,
  Float32BufferAttribute,
  Intersection,
  Points,
  PointsMaterial,
  Raycaster,
  RepeatWrapping,
  TextureLoader,
  Vector2,
  Vector3,
  Quaternion,
} from "three";
import {
  POINT_BASIC_GEOMETRY,
  POINT_CLICK_GEOMETRY,
  POINT_DEFAULT_SIZE,
  POINT_DRAW_GEOMETRY,
  POINT_HOVER_GEOMETRY,
} from "../config";
import { CSS2DObject } from "../libs/CSS2DRenderer";

export const ADSORPTION_RADIUS = 5; // 点吸附半径

export type CPointAttrs = {
  vertex?: Vector3 | Array<number>;
  vertexs?: Array<Vector3> | Array<Array<number>> | Array<number>;
  color?: number;
  size?: number;
  index?: number;
  transparent?: boolean;
  url?: string;
  depthTest?: boolean;
  constraint?: any; // 约束缓存
  marks?: any; // 标注内容
  type?: string;
  areaIndex?: number;
};
export class CPoint extends Points {
  geometry: BufferGeometry;
  material: PointsMaterial;
  name: string;
  userData: { [k: string]: any };
  constructor(attrs?: CPointAttrs) {
    super();

    this.name = "cpoint";
    this.geometry = new BufferGeometry();
    this.material = new PointsMaterial();
    this.userData = {};
    this.initialize(attrs);
  }
  getType() {
    // 获取类型
    return "Point";
  }

  initData(attrs?: CPointAttrs) {
    // 约束/标注/索引缓存
    this.userData = {
      constraint: {
        links: [],
      },
      marks: '',
      index: -1,
    };

    if (attrs && attrs.constraint !== undefined)
      this.userData.constraint = attrs.constraint; // constraint data cache

    if (attrs?.type == "source") {
      this.userData.type = 'source'

      // 生成文字标注并隐藏
      const div = document.createElement("div");
      div.innerHTML = attrs?.marks || '';
      div.classList.add('source-title')
      div.style.pointerEvents = "none"; //避免HTML标签遮挡三维场景的鼠标事件
      const tag = new CSS2DObject(div);
      this.add(tag);
    }
  }

  initUI(attrs?: CPointAttrs) {
    this.userData.areaIndex =
      attrs && attrs.areaIndex !== undefined ? attrs.areaIndex : -1;
    if (attrs && attrs.size !== undefined) {
      this.setSize(attrs.size);
      this.userData.initSize = this.material.size;
    } else {
      this.setSize(POINT_DEFAULT_SIZE);
      this.userData.initSize = this.material.size;
    }
    // 默认坐标原点
    if (attrs) {
      if (attrs.vertexs !== undefined) {
        this.setVertexs(attrs.vertexs);
      } else if (attrs.vertex !== undefined) {
        this.setVertex(attrs.vertex);
      } else {
        this.setVertexs([0, 0, 0]);
      }
    } else {
      this.setVertexs([0, 0, 0]);
    }
    attrs && attrs.transparent !== undefined
      ? this.setTransparent(attrs.transparent)
      : this.setTransparent(true); // 透明

    if (attrs && attrs.url !== undefined) this.loadTexture(attrs.url); // 默认纹理
    if (attrs && attrs.color !== undefined) this.setColor(attrs.color); // 颜色
    if (attrs && !isNaN(Number(attrs.index))) this.setIndex(attrs.index || 0); // 设置点位相关信息
    // - 所有情况下点都是 depthtest 为false
    this.setDepthTest(attrs?.depthTest || false);
    this.renderOrder = 11;
  }

  initialize(attrs?: CPointAttrs) {
    this.initData(attrs);
    this.initUI(attrs);
  }

  // @override 射线拾取点
  raycast(raycaster: Raycaster, intersects: Intersection[]) {
    const r = Math.max(this.material.size / 2, ADSORPTION_RADIUS);
    const ori = (raycaster as any).ray.origin;
    const oriCurrent = ori.clone().project((window as any).camera);
    const v1 = new Vector2(
      (0.5 + oriCurrent.x / 2) * window.innerWidth,
      (0.5 - oriCurrent.y / 2) * window.innerHeight
    );
    let position = new Vector3(
      this.geometry.attributes.position.array[0],
      this.geometry.attributes.position.array[1],
      this.geometry.attributes.position.array[2]
    );

    // 外包围线上的点
    if (this.userData.index) {
      let centerPoint = new Vector3();
      let rotate = 0;
      if (this.parent?.parent?.userData) {
        centerPoint = this.parent?.parent.userData.repeatCenter?.clone();
      }
      if (this.parent?.parent?.parent) {
        rotate = this.parent?.parent?.parent.userData.rotate;
      }

      if (centerPoint && rotate) {
        const currentPoint = position.clone().sub(centerPoint);
        const quaternion = new Quaternion();
        quaternion.setFromAxisAngle(new Vector3(0, 1, 0), -rotate);
        const p = currentPoint.clone().applyQuaternion(quaternion);
        position = p.add(centerPoint);
      }
    }

    // 旋转图标
    if (this.userData.type == "rotatePoint") {
      position = this.userData.point.clone();
    }
    const current = position.clone().project((window as any).camera);
    const v2 = new Vector2(
      (0.5 + current.x / 2) * window.innerWidth,
      (0.5 - current.y / 2) * window.innerHeight
    );

    if (v1.distanceToSquared(v2) <= r * r) {
      intersects.push({
        distance: current.clone().distanceTo((window as any).camera.position),
        object: this,
        point: position,
      } as any);
    }
  }

  loadTexture(url: string) {
    const t = new TextureLoader().load(url);
    t.wrapS = t.wrapT = RepeatWrapping;
    this.material.map = t;
    return this;
  }
  //
  setDepthTest(depthTest: boolean) {
    this.material.depthTest = depthTest;
    return this;
  }

  setTransparent(transparent: boolean) {
    this.material.transparent = transparent;
    return this;
  }

  // 顶点位置： 多段数据
  setVertexs(vertexs: Array<Vector3> | Array<Array<number>> | Array<number>) {
    let positions: Array<number> = [];
    if (!vertexs || vertexs.length == 0) {
      throw new Error("vertexs is not defined or vertexs array is empty.");
    }

    if ((vertexs[0] as any).isVector3) {
      vertexs.forEach((v: any) => {
        positions.push(v.x, v.y, v.z);
      });
    } else if (vertexs[0] instanceof Array) {
      vertexs.forEach((v: any) => {
        positions.push(v[0], v[1], v[2]);
      });
    } else {
      positions = vertexs.slice() as Array<number>;
    }

    if (
      !this.geometry.attributes.position ||
      (this.geometry.attributes.position as any).array.length < positions.length
    ) {
      this.geometry.setAttribute(
        "position",
        new Float32BufferAttribute(positions, 3)
      );
    } else {
      (this.geometry.attributes.position as any).array.set(positions);
      (this.geometry.attributes.position as any).needsUpdate = true;
    }

    // 位置改变，需要重新计算包围球属性
    this.geometry.computeBoundingSphere();
    return this;
  }

  getVertexs() {
    const vertexs = [];
    if (!this.geometry.attributes.position) return [];
    const bufferData = this.geometry.attributes.position.array;

    for (let i = 0; i < bufferData.length; i += 3) {
      vertexs.push(
        new Vector3(bufferData[i], bufferData[i + 1], bufferData[i + 2])
      );
    }
    return vertexs;
  }

  // 顶点位置： 单个数据
  setVertex(vertex: Vector3 | Array<number>) {
    let vertices = [];

    if (!vertex || vertex.length == 0) {
      throw new Error("vertex is not defined or vertex array is empty.");
    }

    if ((vertex as any).isVector3) {
      vertices.push((vertex as any).x, (vertex as any).y, (vertex as any).z);
    } else if (vertex instanceof Array) {
      vertices = vertex.slice();
    }

    if (!this.geometry.attributes.position) {
      this.geometry.setAttribute(
        "position",
        new Float32BufferAttribute(vertices, 3)
      );
    } else {
      (this.geometry.attributes.position as any).array.set(vertices);
      (this.geometry.attributes.position as any).needsUpdate = true;
    }
    // 位置改变，需要重新计算包围球属性
    this.geometry.computeBoundingSphere();
   
    // 资源文字编注的路径重置和当前点位保存
    if(this.userData.type == 'source') {
      this.children[0].position.set(vertices[0], vertices[1], vertices[2]);
      this.userData.point = new Vector3(...vertices)
    }
    return this;
  }

  getVertex() {
    const bufferData = this.geometry.attributes.position.array;
    return new Vector3(bufferData[0], bufferData[1], bufferData[2]);
  }

  // 点大小
  setSize(size: number) {
    this.material.size = size;
    return this;
  }

  getSize() {
    return this.material.size;
  }

  // 颜色
  setColor(color: number) {
    this.material.color.set(color);
    return this;
  }

  getColor() {
    return this.material.color;
  }

  // 获取当前点位信息相关: index = -1为独立点位 , index != -1代表在线上
  getIndex() {
    return this.userData.index;
  }

  setIndex(index: number) {
    this.userData.index = index;
    return this;
  }

  toJSON() {
    const json: { [k: string]: any } = {
      name: this.name,
      color: this.getColor(),
      size: this.getSize(),
      index: this.getIndex(),
      vertex: this.getVertex(),
      vertexs: this.getVertexs(),
    };

    for (const key in this.userData) {
      json[key] = this.userData[key];
    }
    return json;
  }
}
