<template>
  <div>
    <search-form
      :form-keys-data="keysData"
      :search-data-event="searchData"
      currentRoute="/interactive/list"></search-form>
  </div>
  <div>
    <table-list
      :data="tableData"
      :column-list="columnList"
      :change-page="changePage"
      :delete-content="deleteContent"
      :delete-dataEvent="deleteDataEvent"
      :data-total="pageTotal"
      :page-size="searchForm.pageSize"
      :page-no="searchForm.pageNo"
      :operation-items="operationItems"></table-list>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive, onMounted } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import { useRouter } from 'vue-router';
import { getInteractionPage, getSceneMetaPageForWeb } from '@/api/index';
import type { OperationItems, TableRow } from '@/types/operation';

const router = useRouter();

const pageTotal = ref(0); // 总计数据条数
const tableData: any = ref([]); // 表格数据
const deleteContent = {
  title: '删除互动区域',
  content: '是否删除此互动区域？',
};
const searchForm: any = reactive({
  // 查询对象
  pageSize: 20,
  pageNo: 1,
});

// Operation items configuration
const operationItems = reactive<OperationItems>([
  {
    label: '编辑',
    key: 'edit',
    onClick: (row: TableRow) => {
      editData(row);
    },
  },
]);

const keysData: any = reactive([
  {
    key: 'sceneId',
    type: 'select',
    label: '项目名称',
    dataList: [],
  },
  {
    key: 'interactionName',
    type: 'input',
    label: '互动区域名称',
  },
]);

const editData = (data: any) => {
  router.push(`/scene_edit?sceneid=${data.sceneId}&interactionid=${data.id}`);
};

const deleteDataEvent = (data: any) => {};

const columnList = [
  {
    prop: 'id',
    label: '互动区域编号',
  },
  {
    prop: 'interactionName',
    label: '互动区域名称',
  },
  {
    prop: 'sceneName',
    label: '项目名称',
  },
  {
    prop: 'visitCount',
    label: '访问次数',
  },
  {
    prop: 'hasMaterial',
    label: '是否绑定素材',
  },
  {
    prop: 'updateTimeStr',
    label: '更新日期',
  },
  {
    prop: 'operate',
    label: '操作',
    width: 120,
    type: 'operation',
  },
];

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getData();
};

const searchData = (data: any) => {
  for (const key in data) {
    searchForm[key] = data[key];
  }
  getData();
};

const getData = () => {
  getInteractionPage({ ...searchForm }).then((res: any) => {
    pageTotal.value = res.data.total;
    tableData.value = [...res.data.records].map((e: any) => ({
      ...e,
      hasMaterial: e.hasMaterial ? '是' : '否',
    }));
  });
};

onMounted(() => {
  getSceneMetaPageForWeb({ pageNo: 1, pageSize: 999 }).then((res: any) => {
    keysData[0].dataList = [...res.data.records].map((e: any) => ({
      name: e.sceneName,
      value: e.id,
    }));
  });
  getData();
});
</script>
