<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div>新建路径导航</div>
        <img
          class="close-btn"
          src="@/assets/images/icon/close.png"
          @click="changeState"
          alt="关闭" />
      </div>
      <div class="modal-form">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm">
          <el-form-item label="路径名称" prop="graphName">
            <el-input
              class="form-input"
              v-model="ruleForm.graphName"
              placeholder="请输入路径名称"
              maxlength="20"
              show-word-limit />
          </el-form-item>
          <el-form-item label="选择空间" prop="spaceId">
            <el-select
              v-model="ruleForm.spaceId"
              clearable
              placeholder="请选择空间数据"
              class="select-default"
              popper-class="select-option"
              style="height: 36px">
              <el-option
                v-for="(item, index) in spaceList"
                :key="index"
                :label="item.descriptionName"
                :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="描述" prop="graphInfo" class="upload-box-style">
            <el-input
              type="textarea"
              resize="none"
              v-model="ruleForm.graphInfo"
              placeholder="请输入描述信息"
              style="height: 141px; margin-bottom: 6px"
              maxlength="60"
              show-word-limit />
          </el-form-item>
          <el-form-item class="form-submit">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef)">确认</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { getSpacePage, addNjyjGraph } from '@/api';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';

const props = defineProps({
  handleHide: {
    default: null,
    type: Function,
  },
});

interface RuleForm {
  graphName: string;
  spaceId: string;
  graphInfo: string;
}

const ruleFormRef = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  graphName: '',
  spaceId: '',
  graphInfo: '',
});
const spaceList: any = ref([]);
const router = useRouter();
const store = useStore();

const rules = reactive<FormRules<RuleForm>>({
  graphName: [{ required: true, message: '请输入路径名称', trigger: 'blur' }],
  spaceId: [{ required: true, message: '请选择空间信息', trigger: 'blur' }],
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      addNjyjGraph({ ...ruleForm }).then((res: any) => {
        if (res.code == 200) {
          props.handleHide(true);
          if (res.data) {
            router.push('/path_navigation_edit?graphId=' + res.data);
          }
        }
      });
    } else {
      console.log('error submit!', fields);
    }
  });
};

const changeState = () => {
  props.handleHide();
};

onMounted(() => {
  getSpacePage({ pageNo: 1, pageSize: 999, status: 2 }).then((res: any) => {
    spaceList.value = [...res.data.records];
  });
});
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;

  .modal-content {
    width: 430px;
    height: 397px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -215px;
    margin-top: -192px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;

    .form-textarea {
      width: 432px;
      height: 91px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 8px;
    }

    .modal-content-title {
      padding: 16px 24px 12px 24px;
      font-weight: bold;
      font-size: 20px;
      color: #1e1e1e;
      text-align: left;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .close-btn {
        width: 24px;
        height: 24px;
        cursor: pointer;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 0.7;
        }
      }
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      padding: 0 30px 0 0;
      box-sizing: border-box;

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-submit {
        margin-top: 92px;
      }

      .tips-text {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        margin-top: -19px;
        text-align: left;
        padding-left: 100px;
      }
    }
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;

  & > button {
    box-shadow: none;
    border-radius: 4px;
  }
}

.btn-primary .el-size3 > button {
  background: #2e76ff;
}
</style>
