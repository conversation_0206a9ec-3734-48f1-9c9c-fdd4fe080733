<template>
  <div class="device-box">
    <div>
      <search-form
        :form-keys-data="keysData"
        :search-data-event="searchData"
        currentRoute="/devicelist"
        :upload-buttons="deviceUploadButtons"
        :device-data="deviceListData"></search-form>
    </div>
    <div>
      <table-list
        ref="tableRefs"
        :delete-dataEvent="deleteDataEvent"
        :data="tableData"
        :column-list="columnList"
        :operation-items="operationItems"
        :handle-create="handleCreate"
        :change-page="changePage"
        :handleQrCode="handleQrCode"
        :delete-content="deleteContent"
        :data-total="pageTotal"
        :qr-code="true"
        :page-size="searchForm.pageSize"
        :page-no="searchForm.pageNo"
        :show-details="showDetails"
        :handle-filter="handleFilter"
        :empty-image="emptyImage"></table-list>
    </div>
    <create-device
      v-if="modalShow"
      :handle-hide="handleHide"
      :default-value="defaultValue"></create-device>
    <QrFormCode v-if="qrCode" :hideAddMask="hideQrCode"></QrFormCode>
    <device-details v-if="modalShow2" :hide-details="hideDetails"></device-details>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, onUnmounted, computed } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import DeviceDetails from './details/DeviceDetails.vue';
import CreateDevice from './components/CreateDevice.vue';
import { getEquipmentPage, deleteEquip, getEquipmentStatistic } from '@/api';
import QrFormCode from '@/components/experience/create/QrFormCode.vue';
import type { OperationItems } from '@/types/operation';
import { formatDateOnly } from '@/utils';
const qrCode = ref(false);
const modalShow = ref(false); // 显示新建
const modalShow2 = ref(false); // 显示新建
const tableData: any = ref([]); // 表格数据
const pageTotal = ref(0); // 总计数据条数
const isUnmounted = ref(false);
const deleteContent = {
  title: '删除设备',
  content: '确认删除当前设备？删除后将同步删除该设备所有数据信息。',
};
const searchForm: any = reactive({
  // 查询对象
  pageSize: 20,
  pageNo: 1,
  equipmentType: '', // 设备类型筛选
});

const deviceUploadButtons = [
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '',
    label: '添加设备',
    action: 'create',
    type: 'device',
  },
];

const defaultValue = ref({}); // 进入编辑页的初始值
const deviceListData: any = ref({});
const tableRefs = ref();
// 设备类型选项数据
const equipmentTypeOptions = [
  { name: '所有类型', value: '' }, // 默认选项
  { name: 'Android', value: 2 },
  { name: 'IOS', value: 3 },
  { name: 'Rokid', value: 4 },
  { name: 'Pico 4E', value: 5 },
  { name: 'Pico 4UE', value: 6 },
];

const keysData = reactive([
  {
    key: 'equipmentType',
    type: 'select',
    label: '设备类型',
    dataList: equipmentTypeOptions,
    placeholder: '所有类型',
  },
  {
    key: 'snCode',
    type: 'input',
    label: '设备序列号/设备名称',
  },
]);
const emptyImage = require('@/assets/images/nolistimg.png');
const hideQrCode = () => {
  qrCode.value = false;
};

const editData = (data: any) => {
  modalShow.value = true;
  defaultValue.value = data;
};

const handleQrCode = () => {
  qrCode.value = true;
};

const operationItems = computed<OperationItems>(() => [
  {
    label: '编辑',
    key: 'edit',
    show: () => true,
    onClick: editData,
  },
  {
    label: '删除',
    key: 'delete',
    show: () => true,
    onClick: deleteDataEvent,
  },
]);

const columnList: any = ref([
  {
    prop: 'snCode',
    label: '设备序列号',
  },
  {
    prop: 'equipmentName',
    label: '设备名称',
  },
  {
    prop: 'equipmentType',
    label: '设备类型',
    list: {
      2: 'Android',
      3: 'IOS',
      4: 'Rokid',
      5: 'Pico 4E',
      6: 'Pico 4UE',
    },
  },
  {
    prop: 'location',
    label: '位置信息',
  },
  {
    prop: 'batteryCapacity',
    label: '剩余电量',
    formatter: (value: number) => `${value}%`,
  },
  // {
  //   prop: 'equipStatus',
  //   label: '设备状态',
  //   list: {
  //     0: '等待上报数据',
  //     1: '设备正常',
  //     2: '设备下线',
  //     null: '设备不存在',
  //   },
  //   filters: [
  //     { text: '等待上报数据', value: 0 },
  //     { text: '设备正常', value: 1 },
  //     { text: '设备下线', value: 2 },
  //   ],
  // },
  {
    prop: 'lastActivityTime',
    label: '最近活动时间',
    formatter: (value: string) => formatDateOnly(value),
  },
  {
    prop: 'operate',
    label: '操作',
    width: 136,
  },
]);

const handleFilter = (key: string, value: any) => {
  searchForm[key] = value ? value[0] : null;
  searchForm.pageNo = 1;
  getDataList();
  tableRefs.value.selectDataArr = null;
};

const showDetails = () => {
  modalShow2.value = true;
};

const hideDetails = () => {
  modalShow2.value = false;
};

const handleHide = (renew?: boolean) => {
  modalShow.value = false;
  defaultValue.value = {};

  if (renew) {
    // 判断是否需要重新渲染
    getDataList();
  }
};

const searchData = (data: any, type?: string) => {
  // 处理按钮点击事件
  if (data.action === 'create' && data.type === 'device') {
    handleQrCode();
    return;
  }

  // 处理搜索表单数据
  for (const key in data) {
    searchForm[key] = data[key];
  }
  if (type == 'reset') {
    searchForm.equipStatus = '';
    searchForm.equipmentType = '';
    tableRefs.value.clearFilterEvent();
  }
  getDataList();
  tableRefs.value.selectDataArr = null;
};

const handleCreate = () => {
  modalShow.value = true;
};

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getDataList();
};

const deleteDataEvent = async (data: any) => {
  try {
    if (isUnmounted.value) return;
    await deleteEquip({ equipId: data.id });
    if (!isUnmounted.value) {
      await getDataList();
    }
  } catch (error) {
    console.error('Failed to delete equipment:', error);
  }
};

const getDataList = async () => {
  try {
    if (isUnmounted.value) return;
    const res = await getEquipmentPage({ ...searchForm });
    if (res.data && !isUnmounted.value) {
      tableData.value = [...res.data.records];
      pageTotal.value = res.data.total;
    }
  } catch (error) {
    console.error('Failed to fetch equipment list:', error);
  }
};

onMounted(async () => {
  const userDto = JSON.parse(window.sessionStorage.getItem('userDto') || '{}');
  const userBindPackageDto = JSON.parse(
    window.sessionStorage.getItem('userBindPackageDto') || '{}'
  );

  try {
    await getDataList();
    if (isUnmounted.value) return;

    const res = await getEquipmentStatistic();
    if (isUnmounted.value) return;

    deviceListData.value = {
      totalEquipment: {
        title: '已绑定设备数',
        value: res.data.totalEquipment,
      },
      arGlassEquipment: {
        title: '已绑定眼镜端',
        value: res.data.arGlassEquipment,
      },
      arMobileEquipment: {
        title: '已绑定移动端',
        value: res.data.arMobileEquipment,
      },
      activityEquipment: {
        title: '活跃设备数',
        value: res.data.activityEquipment,
      },
      sceneVisitCount: {
        title: '浏览项目总次数',
        value: res.data.sceneVisitCount,
      },
    };

    if (userDto.userType != 1 && !userDto.isOperation) {
      delete deviceListData.value.activityEquipment;
      delete deviceListData.value.sceneVisitCount;
    }
  } catch (error) {
    console.error('Failed to initialize device list:', error);
  }

  const visitObj: any = {
    prop: 'visitCount',
    label: '浏览项目次数',
    width: 180,
  };

  if (userDto.userType == 1) {
    // 超管账号
    // columnList.value.unshift({
    //   prop: 'ownerMail',
    //   label: '账号名称',
    // });
    columnList.value.splice(8, 0, visitObj);
    columnList.value[columnList.value.length - 1].hideDelete = true;
  } else {
    if (userDto.isOperation || (userBindPackageDto && userBindPackageDto.isOperate)) {
      // 运营账号
      columnList.value.splice(7, 0, visitObj);
      columnList.value[columnList.value.length - 1].hideDelete = true;
    }
  }
});

onUnmounted(() => {
  isUnmounted.value = true;
});
</script>
<style scoped lang="less">
.device-box {
  // min-width: 1289px;
  // overflow: hidden;
  // overflow-x: auto;

  ::v-deep(.styleTable) {
    height: calc(100vh - 458px);

    &.noData {
      height: auto;
    }
  }
}

.device-list {
  padding: 24px 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 32px;
  border-radius: 10px;
  border: 1px solid #dadada;

  & > div {
    width: 257px;
    height: 77px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    border-right: 1px solid #d9d9d9;
    box-sizing: border-box;
    padding-left: 24px;

    .list-title {
      font-weight: 500;
      font-size: 16px;
      color: #797979;
      line-height: 19px;
    }

    .list-content {
      font-weight: bold;
      font-size: 32px;
      color: #000000;
      line-height: 38px;
    }
  }

  & > div:last-child {
    border-right: none;
  }
}
</style>
