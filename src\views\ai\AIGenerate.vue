<template>
  <div class="ai-page">
    <AIHeader @tab-change="handleTabChange" />
    <div class="ai-content">
      <AILeftPanel
        :type="currentTab"
        @show-progress="handleShowProgress"
        @update-progress="handleUpdateProgress"
        @generation-complete="handleGenerationComplete"
        @add-close-handler="handleAddCloseHandler" />
      <AIRightPanel ref="rightPanelRef" :type="currentTab" />
    </div>
    <ProgressModal
      v-if="showProgressModal"
      :icon="progressIcon"
      :progress="progress"
      :text="progressText"
      :status="taskStatus"
      :rank="taskRank"
      @close="handleCloseProgress" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import AIHeader from './components/AIHeader.vue';
import AILeftPanel from './components/AILeftPanel.vue';
import AIRightPanel from './components/AIRightPanel.vue';
import ProgressModal from './components/ProgressModal.vue';
import { AIModelType } from '@/config';
import { getUserAiTask } from '@/api';

const showProgressModal = ref(false);
const progress = ref(0);
const progressText = ref('努力生成中...');
const progressIcon = require('@/assets/images/starGif.gif');
const taskStatus = ref(0);
const taskRank = ref(0);
const closeHandler = ref<(() => void) | null>(null);

const currentTab = ref<number>(AIModelType.IMAGE);

const rightPanelRef = ref();

const handleTabChange = (tab: number) => {
  currentTab.value = tab;
};

// 供 AILeftPanel 调用，点击立即生成时弹出弹框
const handleShowProgress = (
  data: { taskId: number; status?: number; rank?: number } = { taskId: 0, status: 0, rank: 0 }
) => {
  showProgressModal.value = true;
  taskStatus.value = data.status || 0;
  taskRank.value = data.rank || 0;
  progress.value = 0;
};

// 处理进度更新
const handleUpdateProgress = (data: { status: number; rank: number; progress: number }) => {
  if (!data) return;
  taskStatus.value = data.status;
  taskRank.value = data.rank;
  progress.value = data.progress;
};

// 处理生成完成
const handleGenerationComplete = () => {
  // 延迟一小段时间后关闭弹窗，让用户看到100%的进度
  setTimeout(() => {
    handleCloseProgress(); // 使用handleCloseProgress来关闭弹窗，确保清理函数被调用
    // 刷新右侧历史列表
    rightPanelRef.value?.fetchAiTasks?.();
  }, 1000);
};

const handleCloseProgress = () => {
  showProgressModal.value = false;
  // 执行清理函数
  if (closeHandler.value) {
    closeHandler.value();
    closeHandler.value = null;
  }
  // 刷新右侧历史列表
  rightPanelRef.value?.fetchAiTasks?.();
};

// 添加关闭处理函数
const handleAddCloseHandler = (handler: () => void) => {
  closeHandler.value = handler;
};

// 页面初始化时获取默认类型的数据
onMounted(async () => {
  try {
    const response = await getUserAiTask({
      pageNo: 1,
      pageSize: 10,
      taskType: currentTab.value,
    });
  } catch (error) {
    console.error('页面初始化获取AI任务列表失败:', error);
  }
});
</script>

<style scoped lang="less">
.ai-page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;

  .ai-content {
    flex: 1;
    padding: 16px;
    display: flex;
    gap: 15px;
    overflow: hidden;
  }
}
</style>
