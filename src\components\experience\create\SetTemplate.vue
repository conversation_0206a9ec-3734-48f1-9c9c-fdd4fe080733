<template>
    <div class="new-mask">
        <div class="new_con">
            <header>
                <div class="title">设为模板</div>
            </header>
            <main>
                <el-form ref="ruleFormRef" :model="ruleForm" label-width="120px" class="demo-ruleForm" :rules="rules">
                    <el-form-item label="项目名称" prop="templateName" style="margin-bottom: 20px;">
                        <el-input v-model="params.templateName" placeholder="请输入项目名称" />
                    </el-form-item>
                    <el-form-item label="项目描述" style="margin-top: 12px;">
                        <textarea class="mo-textarea" name="" v-model="params.templateIntroduction" id=""
                            style="width: 332px; height: 96px;" maxlength="60"></textarea>
                    </el-form-item>
                    <el-form-item label="封面图" prop="image">
                        <div class="upload">
                            <input type="file" accept="image/*" @input="handleFileChangeImage" ref="imageRef"
                                style="display: none" />
                            <img class="mask_img" :src="params.templatePicKey.split('?')[0]" alt=""
                                @click="triggerFileInput_img">
                            <span class="upload-img-desc">
                                点击左图可更换封面<br>
                                支持512KB的png或jpg图片<br>
                                推荐尺寸306*210
                            </span>
                        </div>
                    </el-form-item>
                    <el-form-item label="介绍视频" prop="video" style="margin-top: 12px;">
                        <div class="upload">
                            <input type="file" accept="video/mp4" @change="handleFileChangeVideo" ref="videoRef"
                                style="display: none" />
                            <video class="mask_video" v-if="params.templateVideoKey.split('?')[0]"
                                :src="params.templateVideoKey.split('?')[0]" alt="" @click="triggerFileInput"></video>
                            <div class="nothing-box" v-else @click="triggerFileInput">
                                <img src="../../../assets/images/video-icon.png" alt="">
                            </div>
                            <span class="upload-img-desc">
                                点击左图可更换视频<br>
                                仅支持.mp4格式<br>
                                推荐尺寸{{ currentScene && currentScene.scenePlatform == 3 ? '211 * 310px' : '516 * 290px'
                                }}
                            </span>
                        </div>
                    </el-form-item>
                </el-form>
            </main>
            <footer>
                <el-button @click="cancel" style="margin-right: 12px; width: 92px;">取消</el-button>
                <el-button type="primary" @click="confirm" style="margin-right: 24px; width: 92px;" color="#2E76FF">{{
                    _isUpdate ? '更新'
                        :
                        '确定' }}</el-button>
            </footer>
        </div>
        <TipsView></TipsView>
    </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineExpose, onMounted, reactive } from 'vue'
import { uploadTemplateVideo, getOssAccessPath, uploadTemplatePic } from '@/api'
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';
import TipsView from '@/components/TipsView.vue'
import { desensitizte } from '@/utils'

const videoRef = ref()
const imageRef = ref()
const videoFile = ref({})
const imageFile = ref()
const _isUpdate = ref(false)
let isUploadVideo = (false)

const props = defineProps({
    hideAddMask: {
        default: null,
        type: Function
    },
    currentScene: {
        default: null,
        type: Object
    }
})

const store = useStore();

const ruleForm = ref({
    templateName: '',
    templateIntroduction: '',
    templateVideoKey: '',
    templatePicKey: 'http://njyjxr.oss-cn-shanghai.aliyuncs.com/zhanghai/woshidemo.png'
})

const validatePass = (rule: any, value: any, callback: any) => {
    if (!params.value.templateName) {
        callback(new Error('请输入项目名称'))
        return
    }
    callback()
}

const rules = reactive({
    templateName: [{ required: true, validator: validatePass, message: '请输入项目名称', trigger: 'blur' }],
    image: [{ required: true }],
    video: [{ required: true }]
})

const params = ref({
    sceneId: '',
    templateName: '',
    templateIntroduction: '',
    templateVideoKey: '',
    templatePicKey: 'http://njyjxr.oss-cn-shanghai.aliyuncs.com/zhanghai/woshidemo.png'
})

onMounted(() => {

})

const emits = defineEmits(['confirm', 'update'])

const triggerFileInput = () => {
    videoRef.value.click();
}

const triggerFileInput_img = () => {
    imageRef.value.click();
}

const requestGetVideoOssKey = (file) => {
    return new Promise(resolve => {
        const formData = new FormData();
        videoFile.value = file;
        formData.append('videoFile', videoFile.value);
        uploadTemplateVideo(formData).then(res => {
            if (res.code == 200) {
                resolve(res.data)
            }
        })
    })
}

const handleFileChangeVideo = async () => {
    isUploadVideo = true
    const file = event.target.files[0]
    const videoOssKey = await requestGetVideoOssKey(file)
    console.log(file, videoOssKey);

    getOssAccessPath({ key: videoOssKey.key }).then(res => {
        if (res.code == 200) {
            ElMessage({ type: 'success', message: '上传成功！' })
            params.value.templateVideoKey = res.data
            isUploadVideo = false
        }
    })
}

const requestGetImageOssKey = (file) => {
    return new Promise(resolve => {
        const formData = new FormData();
        imageFile.value = file;
        formData.append('picFile', imageFile.value);
        uploadTemplatePic(formData).then(res => {
            if (res.code == 200) {
                resolve(res.data)
            }
        })
    })
}

const handleFileChangeImage = async (event) => {
    const file = event.target.files[0]
    event.target.value = '';
    if (file.size / 1024 > 512) {
        store.state.showTips = '大小不超过512kb'
        return
    }
    const imageOssKey = await requestGetImageOssKey(file)
    getOssAccessPath({ key: imageOssKey }).then(res => {
        if (res.code == 200) {
            ElMessage({ type: 'success', message: '上传成功！' })
            params.value.templatePicKey = res.data
        }
    })
}

const cancel = () => {
    props.hideAddMask()
}

const confirm = async () => {
    if (!params.value.templateName) {
        return ElMessage({ type: 'warning', message: '请输入模板名称' })
    }
    if (!params.value.templateVideoKey) {
        return ElMessage({ type: 'warning', message: '请上传介绍视频' })
    }
    if (isUploadVideo) {
        return ElMessage({ type: 'warning', message: '视频上传中，请稍等...' })
    }
    const hasSensitiveWords = await desensitizte(params.value.templateName, '项目名字不可包含敏感词汇！')
    if (hasSensitiveWords) return;
    if (_isUpdate.value) {
        emits('update', params.value)
    } else {
        emits('confirm', params.value)
    }
}
const closeEvent = () => {
    props.hideAddMask()
}

const setParmasEdit = (params_, isUpdate = false) => {
    if (isUpdate) {
        params.value = params_
        if (params_.templateVideoKey) {
            const splitName = params_.templateVideoKey.split('/')
            videoFile.value['name'] = splitName[splitName.length - 1]
        }
        delete params.value.userId
    } else {
        params.value = {
            sceneId: params_.id,
            templateName: params_.sceneName,
            templateIntroduction: '',
            templateVideoKey: '',
            templatePicKey: 'http://njyjxr.oss-cn-shanghai.aliyuncs.com/zhanghai/woshidemo.png'
        }
    }
    _isUpdate.value = isUpdate
}

defineExpose({
    setParmasEdit
})

</script>
<style scoped lang="less">
:deep(.el-form-item__label) {
    width: 99px !important;
}

:deep(.el-input__wrapper) {
    font-family: "PingFang SC","Lantinghei SC","Microsoft YaHei","HanHei SC","Helvetica Neue","Open Sans","Hiragino Sans GB",'微软雅黑',STHeiti,"WenQuanYi Micro Hei",Arial,SimSun,sans-serif;
    border-radius: 4px !important;
}

.new-mask {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .mask_video {
        width: 120px;
        height: 82px;
    }

    .new_con {
        position: relative;
        width: 453px;
        height: 467px;
        background: #fff;
        border-radius: 8px 8px 8px 8px;
        box-sizing: border-box;
        overflow: hidden;

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            height: 57px;
            background-color: #FFFFFF;
            padding: 0 24px;

            .title {
                font-weight: bold;
                font-weight: bold;
                font-size: 18px;
                color: #1E1E1E;

            }
        }

        main {
            display: flex;
            box-sizing: border-box;

            .input,
            textarea {
                margin-bottom: 20px;
            }

            .upload {
                display: flex;
                align-items: center;

                .nothing-box {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 120px;
                    height: 82px;
                    border: 1px solid #DADADA;
                    cursor: pointer;

                    img {
                        width: 24px;
                        height: 24px;
                    }
                }

                img {
                    width: 120px;
                    height: 82px;
                }

                .upload-img-desc {
                    text-align: left;
                    line-height: 22px;
                    margin-left: 12px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #797979;
                }
            }

            p {
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 7px;
                color: #3D566C;
            }

            .left {
                width: 50%;
                box-sizing: border-box;
                border-right: 1px solid #D8D8D8;
                padding-right: 20px;
                display: flex;
                align-items: self-start;
                flex-direction: column;
                padding-left: 20px;
            }

            .right {
                width: 50%;
                box-sizing: border-box;
                padding-left: 20px;
                display: flex;
                flex-direction: column;
                align-items: self-start;


                .desc {
                    font-weight: 400;
                    font-size: 12px;
                    color: #6F6F6F;
                    margin-top: 7px;
                }
            }
        }

    }

    .desc {
        color: #6F6F6F;
        margin-top: 7px;
        font-size: 12px;
    }

    textarea {
        border: none;
        outline: none;
        padding: 0;
        margin: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: none;
        background-color: transparent;
        font-size: inherit;
        width: 100%;
    }

    textarea:focus {
        outline: none;
    }

    /* 自定义样式 */
    .mo-textarea {
        display: inline-block;
        resize: vertical;
        padding: 5px 15px;
        line-height: 1.5;
        box-sizing: border-box;
        color: #606266;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        border-radius: 4px;
        font-family: "PingFang SC","Lantinghei SC","Microsoft YaHei","HanHei SC","Helvetica Neue","Open Sans","Hiragino Sans GB",'微软雅黑',STHeiti,"WenQuanYi Micro Hei",Arial,SimSun,sans-serif;
    }

    .maskBox {
        position: relative;
        display: flex;
        align-items: flex-end;

        .upload_img {
            margin-left: 12px;
            font-weight: 400;
            font-size: 14px;
            color: #2E76FF;
            text-decoration-line: underline;
            cursor: pointer;
            min-width: 30px;
        }
    }

    footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 16px;
    }

    .box {
        display: flex;
        align-items: center;

        .upload {
            margin-left: 12px;
            cursor: pointer;
            font-weight: 400;
            font-size: 14px;
            color: #2E76FF;
            text-decoration-line: underline;
        }
    }

    .videoText {
        width: 226px;
        height: 36px;
        background: #FFFFFF;
        border-radius: 4px;
        line-height: 36px;
        text-align: left;
        color: #666;
        font-size: 13px;
        padding-left: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        box-sizing: border-box;
    }

    * {
        margin: 0;
    }

}
</style>