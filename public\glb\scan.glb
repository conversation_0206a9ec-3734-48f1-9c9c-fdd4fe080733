{"asset": {"version": "2.0", "generator": "Unity 2022.3.10f1c1 glTFast 5.0.4"}, "nodes": [{"name": "Wall0", "mesh": 0, "translation": [0.423662335, -0.0275265351, 4.46380329], "rotation": [0, -0.6453591, 0, 0.7638794]}, {"name": "Wall_0_grp", "children": [0]}, {"name": "Wall1", "mesh": 1, "translation": [2.66315746, -0.12097726, 6.78064251], "rotation": [0, 0.07849034, 0, 0.996914864], "scale": [1, 0.99999994, 0.99999994]}, {"name": "Wall_1_grp", "children": [2]}, {"name": "Wall2", "mesh": 2, "translation": [4.08421326, 0.320407242, 4.271455], "rotation": [0, 0.764005661, 0, 0.6452096]}, {"name": "Wall_2_grp", "children": [4]}, {"name": "Wall3", "mesh": 3, "translation": [3.55666447, 0.320407242, 2.07272339], "rotation": [0, -0.9964656, 0, 0.08400155], "scale": [0.99999994, 1, 0.99999994]}, {"name": "Wall_3_grp", "children": [6]}, {"name": "Wall4", "mesh": 4, "translation": [3.330885, 0.320407242, 1.65113628], "rotation": [0, 0.764005542, 0, 0.6452098], "scale": [0.9999999, 1, 0.9999999]}, {"name": "Wall_4_grp", "children": [8]}, {"name": "Wall5", "mesh": 5, "translation": [1.36521137, 0.320407242, -0.145534366], "rotation": [0, -0.952306032, 0, -0.305144578]}, {"name": "Wall_5_grp", "children": [10]}, {"name": "Wall6", "mesh": 6, "translation": [-0.6192308, 0.320407242, -1.35778177], "rotation": [0, -0.8852797, 0, 0.465059], "scale": [0.99999994, 1, 0.99999994]}, {"name": "Wall_6_grp", "children": [12]}, {"name": "Wall7", "mesh": 7, "translation": [-1.08336651, 0.391826242, -1.16321743], "rotation": [0, -0.9970546, 0, 0.07669544]}, {"name": "Wall_7_grp", "children": [14]}, {"name": "Wall8", "mesh": 8, "translation": [-1.59872031, 0.391826242, -1.31238925], "rotation": [0, -0.4597221, 0, 0.8880629], "scale": [0.99999994, 1, 0.99999994]}, {"name": "Wall_8_grp", "children": [16]}, {"name": "Wall9", "mesh": 9, "translation": [-8.646795, -0.3136224, -0.220171586], "rotation": [0, -0.644489944, 0, 0.7646128], "scale": [1, 0.99999994, 1]}, {"name": "Wall_9_grp", "children": [18]}, {"name": "Wall10", "mesh": 10, "translation": [-12.8933477, 0.585914, 0.618526161], "rotation": [0, -0.9966528, 0, 0.08175113], "scale": [1, 0.99999994, 1]}, {"name": "Wall_10_grp", "children": [20]}, {"name": "Wall11", "mesh": 11, "translation": [-12.6050625, 0.585914, 0.418334275], "rotation": [0, -0.6451561, 0, 0.764050841], "scale": [1, 0.99999994, 0.9999999]}, {"name": "Wall_11_grp", "children": [22]}, {"name": "Door1", "mesh": 12, "translation": [-13.5466843, -0.0457705632, 4.61307859], "rotation": [0, -0.676893353, 0, 0.7360812], "scale": [1, 0.99999994, 0.99999994]}, {"name": "Wall12", "mesh": 13, "translation": [-13.5511532, -0.0275265351, 4.559842], "rotation": [0, -0.676893234, 0, 0.7360813]}, {"name": "Wall_12_grp", "children": [24, 25]}, {"name": "Wall13", "mesh": 14, "translation": [-10.67884, -0.3136224, -0.05072628], "rotation": [0, -0.9966865, 0, 0.08133908], "scale": [0.99999994, 0.99999994, 1]}, {"name": "Wall_13_grp", "children": [27]}, {"name": "Wall14", "mesh": 15, "translation": [-6.13440752, -0.0275265351, 2.88819122], "rotation": [0, 0.08301003, 0, 0.9965487]}, {"name": "Wall_14_grp", "children": [29]}, {"name": "Wall15", "mesh": 16, "translation": [-12.2713833, -0.0275265351, 3.7972033], "rotation": [0, 0.7633632, 0, 0.64596957], "scale": [0.9999999, 1, 1]}, {"name": "Wall_15_grp", "children": [31]}, {"name": "Wall16", "mesh": 17, "translation": [-16.394022, -0.0275265351, 4.50959444], "rotation": [0, 0.09958867, 0, 0.9950287]}, {"name": "Wall_16_grp", "children": [33]}, {"name": "Door0", "mesh": 18, "translation": [-21.1149254, -0.496646434, 12.805316], "rotation": [0, -0.6384077, 0, 0.7696984]}, {"name": "Wall17", "mesh": 19, "translation": [-20.97189, 0.06657191, 13.5657053], "rotation": [0, -0.6384083, 0, 0.7696979]}, {"name": "Wall_17_grp", "children": [35, 36]}, {"name": "Wall18", "mesh": 20, "translation": [-21.5370884, 0.06657191, 12.3228321], "rotation": [0, 0.09283578, 0, 0.995681465], "scale": [0.99999994, 1, 0.99999994]}, {"name": "Wall_18_grp", "children": [38]}, {"name": "Wall19", "mesh": 21, "translation": [-19.5822258, 0.585914, 10.2925053], "rotation": [0, 0.768637836, 0, 0.63968426], "scale": [1, 0.99999994, 1]}, {"name": "Wall_19_grp", "children": [40]}, {"name": "Arch_grp", "children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 26, 28, 30, 32, 34, 37, 39, 41]}, {"name": "Floor0", "mesh": 22, "translation": [-11.5552225, -1.59930456, 7.100812], "rotation": [-0.45564115, -0.540732145, 0.5407321, -0.455641121], "scale": [0.99999994, 1, 1]}, {"name": "Floor_grp", "children": [43]}, {"name": "Washerdryer0", "mesh": 23, "translation": [-7.338486, -1.13639283, -0.31732595], "rotation": [0, -0.9966865, 0, 0.08133865], "scale": [0.99999994, 1, 1]}, {"name": "Washerdryer_grp", "children": [45]}, {"name": "Television1", "mesh": 24, "translation": [-23.5467587, -0.216006726, 12.0142679], "rotation": [0, 0.07796497, 0, 0.9969561]}, {"name": "Television0", "mesh": 25, "translation": [-0.961741269, -0.0400109068, -0.845295], "rotation": [0, -0.993872941, 0, 0.11052873], "scale": [1.00000012, 0.99999994, 1]}, {"name": "Television_grp", "children": [47, 48]}, {"name": "Storage6", "mesh": 26, "translation": [-19.3091087, -1.20618153, 15.4954023], "rotation": [0, 0.117210843, 0, 0.9931071], "scale": [0.9999999, 1, 0.9999998]}, {"name": "Storage5", "mesh": 27, "translation": [-12.4438238, 0.6592241, 3.842778], "rotation": [0, 0.08300943, 0, 0.9965488], "scale": [0.99999994, 1, 1]}, {"name": "Storage4", "mesh": 28, "translation": [-13.6091919, -1.12217808, 0.699186862], "rotation": [0, -0.9966865, 0, 0.08133932], "scale": [1, 1.00000012, 1.00000012]}, {"name": "Storage3", "mesh": 29, "translation": [-10.2287836, -1.10997558, 0.1458487], "rotation": [0, -0.9966865, 0, 0.08133897], "scale": [1, 1, 1.00000012]}, {"name": "Storage2", "mesh": 30, "translation": [-5.909343, -1.09038448, 1.26658666], "rotation": [0, 0.07509409, 0, 0.997176468], "scale": [0.9999999, 1, 0.9999999]}, {"name": "Storage1", "mesh": 31, "translation": [-2.859455, -1.11621, -1.07058346], "rotation": [0, -0.996686459, 0, 0.08133937], "scale": [0.9999999, 0.99999994, 0.9999998]}, {"name": "Storage0", "mesh": 32, "translation": [-0.5421721, -0.5031262, 1.70804381], "rotation": [0, 0.0615205355, 0, 0.9981058], "scale": [0.99999994, 1, 0.99999994]}, {"name": "Storage_grp", "children": [50, 51, 52, 53, 54, 55, 56]}, {"name": "Table7", "mesh": 33, "translation": [-26.90506, -1.22620249, 7.84133625], "rotation": [0, -0.6468724, 0, 0.7625983]}, {"name": "Table6", "mesh": 34, "translation": [-25.5716839, -1.22455311, 7.317735], "rotation": [0, -0.6533512, 0, 0.757055], "scale": [0.99999994, 0.99999994, 0.9999999]}, {"name": "Table5", "mesh": 35, "translation": [-26.3861, -1.23275518, 10.1320934], "rotation": [0, -0.63155216, 0, 0.7753334], "scale": [0.99999994, 0.99999994, 0.9999998]}, {"name": "Table4", "mesh": 36, "translation": [-23.8612061, -1.22132444, 9.162175], "rotation": [0, -0.650218, 0, 0.7597477]}, {"name": "Table3", "mesh": 37, "translation": [-22.5019073, -1.20282125, 6.640823], "rotation": [0, -0.6524494, 0, 0.757832348], "scale": [1, 1, 0.99999994]}, {"name": "Table2", "mesh": 38, "translation": [-19.4487076, -1.12517214, 1.651944], "rotation": [0, -0.996686459, 0, 0.08133972], "scale": [1, 0.99999994, 1]}, {"name": "Table1", "mesh": 39, "translation": [-16.9526653, -1.10400939, 2.909577], "rotation": [0, -0.9964702, 0, 0.08394756]}, {"name": "Table0", "mesh": 40, "translation": [-2.42658615, -1.07974577, 0.654821336], "rotation": [0, -0.996918261, 0, 0.07844795], "scale": [1, 0.99999994, 0.9999999]}, {"name": "Table_grp", "children": [58, 59, 60, 61, 62, 63, 64, 65]}, {"name": "Sofa0", "mesh": 41, "translation": [-10.2266684, -1.06336355, 1.8872155], "rotation": [0, 0.460175931, 0, 0.887827754], "scale": [1, 0.99999994, 1]}, {"name": "Sofa_grp", "children": [67]}, {"name": "Chair14", "mesh": 42, "translation": [-23.4439487, -1.21051991, 8.50429], "rotation": [0, 0.7645487, 0, 0.644566], "scale": [0.99999994, 1, 0.99999994]}, {"name": "Chair13", "mesh": 43, "translation": [-21.9995384, -1.1906172, 6.588989], "rotation": [0, 0.7829928, 0, 0.622030854], "scale": [0.9999999, 1, 0.9999999]}, {"name": "Chair12", "mesh": 44, "translation": [-21.8744526, -1.19718838, 7.523931], "rotation": [0, 0.780448556, 0, 0.62522], "scale": [1, 0.99999994, 0.9999999]}, {"name": "Chair11", "mesh": 45, "translation": [-23.3535023, -1.19214773, 9.289449], "rotation": [0, 0.782947838, 0, 0.6220874], "scale": [0.9999999, 0.99999994, 0.9999998]}, {"name": "Chair10", "mesh": 46, "translation": [-26.84707, -1.183754, 10.0149927], "rotation": [0, -0.5986885, 0, 0.800981939]}, {"name": "Chair9", "mesh": 47, "translation": [-26.6713924, -1.18473053, 10.8221684], "rotation": [0, -0.6176786, 0, 0.786430657], "scale": [0.99999994, 1, 0.99999994]}, {"name": "Chair8", "mesh": 48, "translation": [-23.1978168, -1.20392549, 9.966724], "rotation": [0, 0.7593106, 0, 0.6507284], "scale": [1.00000012, 1, 1]}, {"name": "Chair7", "mesh": 49, "translation": [-23.54287, -1.1962781, 7.66966152], "rotation": [0, 0.738804936, 0, 0.6739194], "scale": [0.99999994, 0.99999994, 0.9999999]}, {"name": "Chair6", "mesh": 50, "translation": [-21.6066856, -1.20606649, 9.627605], "rotation": [0, 0.744093, 0, 0.668076038]}, {"name": "Chair5", "mesh": 51, "translation": [-21.8048286, -1.19760227, 8.137188], "rotation": [0, 0.783955455, 0, 0.620817065], "scale": [0.9999999, 1, 0.9999999]}, {"name": "Chair4", "mesh": 52, "translation": [-21.6828651, -1.22131538, 8.80594349], "rotation": [0, 0.7471194, 0, 0.6646899], "scale": [0.9999999, 1.00000012, 1]}, {"name": "Chair3", "mesh": 53, "translation": [-23.6147938, -1.19308472, 6.18406], "rotation": [0, 0.7299623, 0, 0.6834874], "scale": [1.00000012, 1, 1]}, {"name": "Chair2", "mesh": 54, "translation": [-22.0778732, -1.174214, 5.921149], "rotation": [0, 0.729145646, 0, 0.6843586], "scale": [0.99999994, 1, 0.99999994]}, {"name": "Chair1", "mesh": 55, "translation": [-23.70877, -1.19609976, 7.06293344], "rotation": [0, 0.7176384, 0, 0.696415961], "scale": [0.99999994, 1, 0.99999994]}, {"name": "Chair0", "mesh": 56, "translation": [-21.9300652, -1.16694641, 1.8858515], "rotation": [0, -0.9983464, 0, 0.0574845374]}, {"name": "Chair_grp", "children": [69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83]}, {"name": "Object_grp", "children": [46, 49, 57, 66, 68, 84]}, {"name": "Mesh_grp", "children": [42, 44, 85]}, {"name": "Section_grp"}, {"name": "scan", "children": [86, 87]}], "buffers": [{"uri": "scan.bin", "byteLength": 68892}], "bufferViews": [{"buffer": 0, "byteLength": 72}, {"buffer": 0, "byteLength": 960, "byteOffset": 72, "byteStride": 40}, {"buffer": 0, "byteLength": 144, "byteOffset": 1032}, {"buffer": 0, "byteLength": 1920, "byteOffset": 1176, "byteStride": 40}, {"buffer": 0, "byteLength": 144, "byteOffset": 3096}, {"buffer": 0, "byteLength": 1920, "byteOffset": 3240, "byteStride": 40}, {"buffer": 0, "byteLength": 144, "byteOffset": 5160}, {"buffer": 0, "byteLength": 1920, "byteOffset": 5304, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 7224}, {"buffer": 0, "byteLength": 960, "byteOffset": 7296, "byteStride": 40}, {"buffer": 0, "byteLength": 144, "byteOffset": 8256}, {"buffer": 0, "byteLength": 1920, "byteOffset": 8400, "byteStride": 40}, {"buffer": 0, "byteLength": 144, "byteOffset": 10320}, {"buffer": 0, "byteLength": 1920, "byteOffset": 10464, "byteStride": 40}, {"buffer": 0, "byteLength": 96, "byteOffset": 12384}, {"buffer": 0, "byteLength": 1200, "byteOffset": 12480, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 13680}, {"buffer": 0, "byteLength": 960, "byteOffset": 13752, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 14712}, {"buffer": 0, "byteLength": 960, "byteOffset": 14784, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 15744}, {"buffer": 0, "byteLength": 960, "byteOffset": 15816, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 16776}, {"buffer": 0, "byteLength": 960, "byteOffset": 16848, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 17808}, {"buffer": 0, "byteLength": 960, "byteOffset": 17880, "byteStride": 40}, {"buffer": 0, "byteLength": 192, "byteOffset": 18840}, {"buffer": 0, "byteLength": 2560, "byteOffset": 19032, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 21592}, {"buffer": 0, "byteLength": 960, "byteOffset": 21664, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 22624}, {"buffer": 0, "byteLength": 960, "byteOffset": 22696, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 23656}, {"buffer": 0, "byteLength": 960, "byteOffset": 23728, "byteStride": 40}, {"buffer": 0, "byteLength": 180, "byteOffset": 24688}, {"buffer": 0, "byteLength": 2400, "byteOffset": 24868, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 27268}, {"buffer": 0, "byteLength": 960, "byteOffset": 27340, "byteStride": 40}, {"buffer": 0, "byteLength": 168, "byteOffset": 28300}, {"buffer": 0, "byteLength": 2240, "byteOffset": 28468, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 30708}, {"buffer": 0, "byteLength": 960, "byteOffset": 30780, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 31740}, {"buffer": 0, "byteLength": 960, "byteOffset": 31812, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 32772}, {"buffer": 0, "byteLength": 960, "byteOffset": 32844, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 33804}, {"buffer": 0, "byteLength": 960, "byteOffset": 33876, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 34836}, {"buffer": 0, "byteLength": 960, "byteOffset": 34908, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 35868}, {"buffer": 0, "byteLength": 960, "byteOffset": 35940, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 36900}, {"buffer": 0, "byteLength": 960, "byteOffset": 36972, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 37932}, {"buffer": 0, "byteLength": 960, "byteOffset": 38004, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 38964}, {"buffer": 0, "byteLength": 960, "byteOffset": 39036, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 39996}, {"buffer": 0, "byteLength": 960, "byteOffset": 40068, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 41028}, {"buffer": 0, "byteLength": 960, "byteOffset": 41100, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 42060}, {"buffer": 0, "byteLength": 960, "byteOffset": 42132, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 43092}, {"buffer": 0, "byteLength": 960, "byteOffset": 43164, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 44124}, {"buffer": 0, "byteLength": 960, "byteOffset": 44196, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 45156}, {"buffer": 0, "byteLength": 960, "byteOffset": 45228, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 46188}, {"buffer": 0, "byteLength": 960, "byteOffset": 46260, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 47220}, {"buffer": 0, "byteLength": 960, "byteOffset": 47292, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 48252}, {"buffer": 0, "byteLength": 960, "byteOffset": 48324, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 49284}, {"buffer": 0, "byteLength": 960, "byteOffset": 49356, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 50316}, {"buffer": 0, "byteLength": 960, "byteOffset": 50388, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 51348}, {"buffer": 0, "byteLength": 960, "byteOffset": 51420, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 52380}, {"buffer": 0, "byteLength": 960, "byteOffset": 52452, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 53412}, {"buffer": 0, "byteLength": 960, "byteOffset": 53484, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 54444}, {"buffer": 0, "byteLength": 960, "byteOffset": 54516, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 55476}, {"buffer": 0, "byteLength": 960, "byteOffset": 55548, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 56508}, {"buffer": 0, "byteLength": 960, "byteOffset": 56580, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 57540}, {"buffer": 0, "byteLength": 960, "byteOffset": 57612, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 58572}, {"buffer": 0, "byteLength": 960, "byteOffset": 58644, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 59604}, {"buffer": 0, "byteLength": 960, "byteOffset": 59676, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 60636}, {"buffer": 0, "byteLength": 960, "byteOffset": 60708, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 61668}, {"buffer": 0, "byteLength": 960, "byteOffset": 61740, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 62700}, {"buffer": 0, "byteLength": 960, "byteOffset": 62772, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 63732}, {"buffer": 0, "byteLength": 960, "byteOffset": 63804, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 64764}, {"buffer": 0, "byteLength": 960, "byteOffset": 64836, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 65796}, {"buffer": 0, "byteLength": 960, "byteOffset": 65868, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 66828}, {"buffer": 0, "byteLength": 960, "byteOffset": 66900, "byteStride": 40}, {"buffer": 0, "byteLength": 72, "byteOffset": 67860}, {"buffer": 0, "byteLength": 960, "byteOffset": 67932, "byteStride": 40}], "accessors": [{"bufferView": 1, "componentType": 5126, "count": 24, "type": "VEC3", "max": [2.6389153, 1.571778, 0.16], "min": [-2.6389153, -1.571778, 0]}, {"bufferView": 1, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 1, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 0, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 3, "componentType": 5126, "count": 48, "type": "VEC3", "max": [1.82117414, 1.47832727, 0.16], "min": [-1.98116493, -1.47832727, 0]}, {"bufferView": 3, "componentType": 5126, "count": 48, "type": "VEC3", "byteOffset": 12}, {"bufferView": 3, "componentType": 5126, "count": 48, "type": "VEC4", "byteOffset": 24}, {"bufferView": 2, "componentType": 5123, "count": 72, "type": "SCALAR"}, {"bufferView": 5, "componentType": 5126, "count": 48, "type": "VEC3", "max": [2.25601888, 1.91971183, 0.160000473], "min": [-2.41779828, -1.91971183, -0.00176891685]}, {"bufferView": 5, "componentType": 5126, "count": 48, "type": "VEC3", "byteOffset": 12}, {"bufferView": 5, "componentType": 5126, "count": 48, "type": "VEC4", "byteOffset": 24}, {"bufferView": 4, "componentType": 5123, "count": 72, "type": "SCALAR"}, {"bufferView": 7, "componentType": 5126, "count": 48, "type": "VEC3", "max": [0.152015984, 1.91971183, 0.160000488], "min": [-0.312016, -1.91971183, 0]}, {"bufferView": 7, "componentType": 5126, "count": 48, "type": "VEC3", "byteOffset": 12}, {"bufferView": 7, "componentType": 5126, "count": 48, "type": "VEC4", "byteOffset": 24}, {"bufferView": 6, "componentType": 5123, "count": 72, "type": "SCALAR"}, {"bufferView": 9, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.453435, 1.91971183, 0.16], "min": [-0.453435, -1.91971183, 0]}, {"bufferView": 9, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 9, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 8, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 11, "componentType": 5126, "count": 48, "type": "VEC3", "max": [2.322224, 1.91971183, 0.16000016], "min": [-2.435023, -1.91971183, 0]}, {"bufferView": 11, "componentType": 5126, "count": 48, "type": "VEC3", "byteOffset": 12}, {"bufferView": 11, "componentType": 5126, "count": 48, "type": "VEC4", "byteOffset": 24}, {"bufferView": 10, "componentType": 5123, "count": 72, "type": "SCALAR"}, {"bufferView": 13, "componentType": 5126, "count": 48, "type": "VEC3", "max": [0.166850746, 1.91971183, 0.16000016], "min": [-0.3295594, -1.91971183, -0.00268563628]}, {"bufferView": 13, "componentType": 5126, "count": 48, "type": "VEC3", "byteOffset": 12}, {"bufferView": 13, "componentType": 5126, "count": 48, "type": "VEC4", "byteOffset": 24}, {"bufferView": 12, "componentType": 5123, "count": 72, "type": "SCALAR"}, {"bufferView": 15, "componentType": 5126, "count": 30, "type": "VEC3", "max": [0.373855948, 1.99113059, 0.16], "min": [-0.373855948, -1.99113083, 0]}, {"bufferView": 15, "componentType": 5126, "count": 30, "type": "VEC3", "byteOffset": 12}, {"bufferView": 15, "componentType": 5126, "count": 30, "type": "VEC4", "byteOffset": 24}, {"bufferView": 14, "componentType": 5123, "count": 48, "type": "SCALAR"}, {"bufferView": 17, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.2527165, 1.99113083, 0.16], "min": [-0.2527165, -1.99113083, 0]}, {"bufferView": 17, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 17, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 16, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 19, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.162275046, 1.2856822, 0.16], "min": [-0.162275046, -1.2856822, 0]}, {"bufferView": 19, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 19, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 18, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 21, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.317755073, 2.18521881, 0.16], "min": [-0.317755073, -2.18521881, 0]}, {"bufferView": 21, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 21, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 20, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 23, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.150540173, 2.18521881, 0.16], "min": [-0.150540173, -2.18521881, 0]}, {"bufferView": 23, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 23, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 22, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 25, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.283158362, 1.553534, 0.08], "min": [-0.283158362, -1.553534, 0]}, {"bufferView": 25, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 25, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 24, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 27, "componentType": 5126, "count": 64, "type": "VEC3", "max": [0.616800547, 1.571778, 0.16], "min": [-0.616800547, -1.571778, 0]}, {"bufferView": 27, "componentType": 5126, "count": 64, "type": "VEC3", "byteOffset": 12}, {"bufferView": 27, "componentType": 5126, "count": 64, "type": "VEC4", "byteOffset": 24}, {"bufferView": 26, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 29, "componentType": 5126, "count": 24, "type": "VEC3", "max": [9.054028, 1.2856822, 0.16], "min": [-9.054028, -1.2856822, 0]}, {"bufferView": 29, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 29, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 28, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 31, "componentType": 5126, "count": 24, "type": "VEC3", "max": [6.20279264, 1.571778, 0.16], "min": [-6.20279264, -1.571778, 0]}, {"bufferView": 31, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 31, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 30, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 33, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.118859865, 1.571778, 0.16], "min": [-0.118859865, -1.571778, 0]}, {"bufferView": 33, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 33, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 32, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 35, "componentType": 5126, "count": 60, "type": "VEC3", "max": [4.1860075, 1.571778, 0.16], "min": [-4.1860075, -1.571778, 0]}, {"bufferView": 35, "componentType": 5126, "count": 60, "type": "VEC3", "byteOffset": 12}, {"bufferView": 35, "componentType": 5126, "count": 60, "type": "VEC4", "byteOffset": 24}, {"bufferView": 34, "componentType": 5123, "count": 90, "type": "SCALAR"}, {"bufferView": 37, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.458876848, 1.10265815, 0.08], "min": [-0.458876848, -1.10265815, 0]}, {"bufferView": 37, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 37, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 36, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 39, "componentType": 5126, "count": 56, "type": "VEC3", "max": [1.32593894, 1.66587651, 0.16], "min": [-1.32593894, -1.66587651, 0]}, {"bufferView": 39, "componentType": 5126, "count": 56, "type": "VEC3", "byteOffset": 12}, {"bufferView": 39, "componentType": 5126, "count": 56, "type": "VEC4", "byteOffset": 24}, {"bufferView": 38, "componentType": 5123, "count": 84, "type": "SCALAR"}, {"bufferView": 41, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.3256903, 1.66587651, 0.16], "min": [-0.3256903, -1.66587651, 0]}, {"bufferView": 41, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 41, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 40, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 43, "componentType": 5126, "count": 24, "type": "VEC3", "max": [5.037059, 2.18521881, 0.16], "min": [-5.037059, -2.18521881, 0]}, {"bufferView": 43, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 43, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 42, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 45, "componentType": 5126, "count": 24, "type": "VEC3", "max": [7.370449, 16.05766, 0.16], "min": [-7.370449, -16.05766, 0]}, {"bufferView": 45, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 45, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 44, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 47, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.349741369, 0.462911785, 0.268529862], "min": [-0.349741369, -0.462911785, -0.268529862]}, {"bufferView": 47, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 47, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 46, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 49, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.815888345, 0.4896045, 0.0400002934], "min": [-0.815888345, -0.4896045, -0.0400002934]}, {"bufferView": 49, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 49, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 48, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 51, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.7036719, 0.4020068, 0.03250001], "min": [-0.7036719, -0.4020068, -0.03250001]}, {"bufferView": 51, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 51, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 50, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 53, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.4758404, 0.393123031, 0.21131365], "min": [-0.4758404, -0.393123031, -0.21131365]}, {"bufferView": 53, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 53, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 52, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 55, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.282235652, 0.7927755, 0.191061154], "min": [-0.282235652, -0.7927755, -0.191061154]}, {"bufferView": 55, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 55, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 54, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 57, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.443368942, 0.47712636, 0.238352165], "min": [-0.443368942, -0.47712636, -0.238352165]}, {"bufferView": 57, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 57, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 56, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 59, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.5732515, 0.48932898, 0.256945074], "min": [-0.5732515, -0.48932898, -0.256945074]}, {"bufferView": 59, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 59, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 58, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 61, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.8203222, 0.5089202, 0.53687495], "min": [-0.8203222, -0.5089202, -0.53687495]}, {"bufferView": 61, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 61, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 60, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 63, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.9836524, 0.4830945, 0.25146544], "min": [-0.9836524, -0.4830945, -0.25146544]}, {"bufferView": 63, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 63, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 62, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 65, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.484872967, 1.09617841, 0.166208461], "min": [-0.484872967, -1.09617841, -0.166208461]}, {"bufferView": 65, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 65, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 64, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 67, "componentType": 5126, "count": 24, "type": "VEC3", "max": [1.04029322, 0.37310195, 0.4137065], "min": [-1.04029322, -0.37310195, -0.4137065]}, {"bufferView": 67, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 67, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 66, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 69, "componentType": 5126, "count": 24, "type": "VEC3", "max": [1.22095728, 0.374751329, 0.440927982], "min": [-1.22095728, -0.374751329, -0.440927982]}, {"bufferView": 69, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 69, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 68, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 71, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.9487403, 0.366549432, 0.388071477], "min": [-0.9487403, -0.366549432, -0.388071477]}, {"bufferView": 71, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 71, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 70, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 73, "componentType": 5126, "count": 24, "type": "VEC3", "max": [1.61500049, 0.3779801, 0.5312102], "min": [-1.61500049, -0.3779801, -0.5312102]}, {"bufferView": 73, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 73, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 72, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 75, "componentType": 5126, "count": 24, "type": "VEC3", "max": [1.142695, 0.396483243, 0.383787543], "min": [-1.142695, -0.396483243, -0.383787543]}, {"bufferView": 75, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 75, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 74, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 77, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.7449088, 0.474132359, 0.248202175], "min": [-0.7449088, -0.474132359, -0.248202175]}, {"bufferView": 77, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 77, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 76, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 79, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.5886526, 0.495295167, 0.7873467], "min": [-0.5886526, -0.495295167, -0.7873467]}, {"bufferView": 79, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 79, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 78, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 81, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.525400341, 0.519558668, 0.5439551], "min": [-0.525400341, -0.519558668, -0.5439551]}, {"bufferView": 81, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 81, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 80, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 83, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.509043, 0.535940945, 0.5356541], "min": [-0.509043, -0.535940945, -0.5356541]}, {"bufferView": 83, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 83, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 82, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 85, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.245920628, 0.2159836, 0.27491197], "min": [-0.245920628, -0.388784617, -0.27491197]}, {"bufferView": 85, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 85, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 84, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 87, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.247690409, 0.225685954, 0.272837341], "min": [-0.247690409, -0.408687234, -0.272837341]}, {"bufferView": 87, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 87, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 86, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 89, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.2099096, 0.232257128, 0.249459624], "min": [-0.2099096, -0.40211606, -0.249459624]}, {"bufferView": 89, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 89, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 88, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 91, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.232309416, 0.197611421, 0.261607528], "min": [-0.232309416, -0.4071568, -0.261607528]}, {"bufferView": 91, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 91, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 90, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 93, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.241526067, 0.170928448, 0.295908749], "min": [-0.241526067, -0.415550679, -0.295908749]}, {"bufferView": 93, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 93, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 92, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 95, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.221445233, 0.171905011, 0.275033981], "min": [-0.221445233, -0.414574116, -0.275033981]}, {"bufferView": 95, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 95, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 94, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 97, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.2526952, 0.20938918, 0.28046608], "min": [-0.2526952, -0.395379037, -0.28046608]}, {"bufferView": 97, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 97, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 96, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 99, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.278818458, 0.201741785, 0.2973725], "min": [-0.278818458, -0.403026432, -0.2973725]}, {"bufferView": 99, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 99, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 98, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 101, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.2416484, 0.393238127, 0.2698456], "min": [-0.2416484, -0.393238127, -0.2698456]}, {"bufferView": 101, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 101, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 100, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 103, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.233591482, 0.401702285, 0.255320519], "min": [-0.233591482, -0.401702285, -0.255320519]}, {"bufferView": 103, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 103, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 102, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 105, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.2291353, 0.377989173, 0.2534895], "min": [-0.2291353, -0.377989173, -0.2534895]}, {"bufferView": 105, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 105, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 104, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 107, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.255320072, 0.406219721, 0.299570918], "min": [-0.255320072, -0.406219721, -0.299570918]}, {"bufferView": 107, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 107, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 106, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 109, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.221933559, 0.209282756, 0.263865054], "min": [-0.221933559, -0.425090432, -0.263865054]}, {"bufferView": 109, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 109, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 108, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 111, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.244882733, 0.403204679, 0.2826037], "min": [-0.244882733, -0.403204679, -0.2826037]}, {"bufferView": 111, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 111, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 110, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 113, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.2679546, 0.4323582, 0.286875069], "min": [-0.2679546, -0.4323582, -0.286875069]}, {"bufferView": 113, "componentType": 5126, "count": 24, "type": "VEC3", "byteOffset": 12}, {"bufferView": 113, "componentType": 5126, "count": 24, "type": "VEC4", "byteOffset": 24}, {"bufferView": 112, "componentType": 5123, "count": 36, "type": "SCALAR"}], "materials": [{"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>) (Instance)", "pbrMetallicRoughness": {"baseColorFactor": [1, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>) (Instance)", "pbrMetallicRoughness": {"baseColorFactor": [1, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.106, 0.525, 0.761, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.106, 0.525, 0.761, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.098, 0.541, 0.067, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.098, 0.541, 0.067, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.098, 0.541, 0.067, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.098, 0.541, 0.067, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.098, 0.541, 0.067, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.098, 0.541, 0.067, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.098, 0.541, 0.067, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.059, 0.6, 0.518, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.059, 0.6, 0.518, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.059, 0.6, 0.518, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.059, 0.6, 0.518, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.059, 0.6, 0.518, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.059, 0.6, 0.518, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.059, 0.6, 0.518, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.059, 0.6, 0.518, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.878, 0.482, 0.094, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"name": "simple(<PERSON><PERSON>)", "pbrMetallicRoughness": {"baseColorFactor": [0.267, 0.339, 0.522, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}], "meshes": [{"name": "Wall0_I3tEJz7O2E+w/PeURUEKZA", "primitives": [{"attributes": {"POSITION": 0, "NORMAL": 1, "TANGENT": 2}, "indices": 3, "material": 0}]}, {"name": "Wall1_SSn69M2czkSznZYvt2P/+g", "primitives": [{"attributes": {"POSITION": 4, "NORMAL": 5, "TANGENT": 6}, "indices": 7, "material": 1}]}, {"name": "Wall2_maUnjHC9KE+XRrlsq+jDcQ", "primitives": [{"attributes": {"POSITION": 8, "NORMAL": 9, "TANGENT": 10}, "indices": 11, "material": 2}]}, {"name": "Wall3_A/3VvPU5Jkq5lYTirTbrBw", "primitives": [{"attributes": {"POSITION": 12, "NORMAL": 13, "TANGENT": 14}, "indices": 15, "material": 3}]}, {"name": "Wall4_LI1pnfMJFkKOcYltDa04KQ", "primitives": [{"attributes": {"POSITION": 16, "NORMAL": 17, "TANGENT": 18}, "indices": 19, "material": 4}]}, {"name": "Wall5_HmdwGQw82kqa79bKYDLa4Q", "primitives": [{"attributes": {"POSITION": 20, "NORMAL": 21, "TANGENT": 22}, "indices": 23, "material": 5}]}, {"name": "Wall6_wPZ+5u4SREeV5bePN/FVxQ", "primitives": [{"attributes": {"POSITION": 24, "NORMAL": 25, "TANGENT": 26}, "indices": 27, "material": 6}]}, {"name": "Wall7_42RGRIIJHk2RKnuT90EBLQ", "primitives": [{"attributes": {"POSITION": 28, "NORMAL": 29, "TANGENT": 30}, "indices": 31, "material": 7}]}, {"name": "Wall8_s9vt1CSNqUS+owz9+XECnQ", "primitives": [{"attributes": {"POSITION": 32, "NORMAL": 33, "TANGENT": 34}, "indices": 35, "material": 8}]}, {"name": "Wall9_ab+bFJ5nu0ycEQhncW83TQ", "primitives": [{"attributes": {"POSITION": 36, "NORMAL": 37, "TANGENT": 38}, "indices": 39, "material": 9}]}, {"name": "Wall10_iO4gSkDZPEKdajSwEFtITA", "primitives": [{"attributes": {"POSITION": 40, "NORMAL": 41, "TANGENT": 42}, "indices": 43, "material": 10}]}, {"name": "Wall11_xeiCSD5uoEealEVEd8apfg", "primitives": [{"attributes": {"POSITION": 44, "NORMAL": 45, "TANGENT": 46}, "indices": 47, "material": 11}]}, {"name": "Door1_K4iMribEjk29y2wWq8tX1A", "primitives": [{"attributes": {"POSITION": 48, "NORMAL": 49, "TANGENT": 50}, "indices": 51, "material": 12}]}, {"name": "Wall12_O/2FC3Dv5katYrbTE3UHUg", "primitives": [{"attributes": {"POSITION": 52, "NORMAL": 53, "TANGENT": 54}, "indices": 55, "material": 13}]}, {"name": "Wall13_z7vTuqx9lEGR+kfWv4OnVw", "primitives": [{"attributes": {"POSITION": 56, "NORMAL": 57, "TANGENT": 58}, "indices": 59, "material": 14}]}, {"name": "Wall14_TtWDMmZK3kCgK2BLotrs/w", "primitives": [{"attributes": {"POSITION": 60, "NORMAL": 61, "TANGENT": 62}, "indices": 63, "material": 15}]}, {"name": "Wall15_xFkFb7UoLkmXS89/f6ZX1w", "primitives": [{"attributes": {"POSITION": 64, "NORMAL": 65, "TANGENT": 66}, "indices": 67, "material": 16}]}, {"name": "Wall16_iTtmtCqmw0y/Af0fvpyGnw", "primitives": [{"attributes": {"POSITION": 68, "NORMAL": 69, "TANGENT": 70}, "indices": 71, "material": 17}]}, {"name": "Door0_ax1dZ4IWRUa1335FQnw9bA", "primitives": [{"attributes": {"POSITION": 72, "NORMAL": 73, "TANGENT": 74}, "indices": 75, "material": 18}]}, {"name": "Wall17_ZIFYEL6eIUKvAMHK6AiHJw", "primitives": [{"attributes": {"POSITION": 76, "NORMAL": 77, "TANGENT": 78}, "indices": 79, "material": 19}]}, {"name": "Wall18_rLK8pCltS0CZX8oX92geiQ", "primitives": [{"attributes": {"POSITION": 80, "NORMAL": 81, "TANGENT": 82}, "indices": 83, "material": 20}]}, {"name": "Wall19_D1NC0sifX0ylVF4u7kCMEg", "primitives": [{"attributes": {"POSITION": 84, "NORMAL": 85, "TANGENT": 86}, "indices": 87, "material": 21}]}, {"name": "Floor0_lFgadZT+hU+i48yp3mTRRQ", "primitives": [{"attributes": {"POSITION": 88, "NORMAL": 89, "TANGENT": 90}, "indices": 91, "material": 22}]}, {"name": "Washerdryer0_S/b6OFAxPke6s8Llr1resg", "primitives": [{"attributes": {"POSITION": 92, "NORMAL": 93, "TANGENT": 94}, "indices": 95, "material": 23}]}, {"name": "Television1_nSNjL3EfgUu9kGZRl2ojEw", "primitives": [{"attributes": {"POSITION": 96, "NORMAL": 97, "TANGENT": 98}, "indices": 99, "material": 24}]}, {"name": "Television0_4dwNijYnZkmYVpymFo7O7Q", "primitives": [{"attributes": {"POSITION": 100, "NORMAL": 101, "TANGENT": 102}, "indices": 103, "material": 25}]}, {"name": "Storage6_dnvz4v9jmUeKy5qzsfG+Nw", "primitives": [{"attributes": {"POSITION": 104, "NORMAL": 105, "TANGENT": 106}, "indices": 107, "material": 26}]}, {"name": "Storage5_Efy/Hg8+UkObT3CSpRVU5Q", "primitives": [{"attributes": {"POSITION": 108, "NORMAL": 109, "TANGENT": 110}, "indices": 111, "material": 27}]}, {"name": "Storage4_iNOomFhlVk6ipgHQC2Peqw", "primitives": [{"attributes": {"POSITION": 112, "NORMAL": 113, "TANGENT": 114}, "indices": 115, "material": 28}]}, {"name": "Storage3_b23qdUhhLkuuSHqDx2jOsg", "primitives": [{"attributes": {"POSITION": 116, "NORMAL": 117, "TANGENT": 118}, "indices": 119, "material": 29}]}, {"name": "Storage2_wFEQM9qgKEy1NIHqj2egXQ", "primitives": [{"attributes": {"POSITION": 120, "NORMAL": 121, "TANGENT": 122}, "indices": 123, "material": 30}]}, {"name": "Storage1_X/FdtBjgcEGo+m+qaoIYfQ", "primitives": [{"attributes": {"POSITION": 124, "NORMAL": 125, "TANGENT": 126}, "indices": 127, "material": 31}]}, {"name": "Storage0_lcnkZAMDZk2BLPBuXzgNpw", "primitives": [{"attributes": {"POSITION": 128, "NORMAL": 129, "TANGENT": 130}, "indices": 131, "material": 32}]}, {"name": "Table7_BsKuzKz+00a2PcGjCLTtgw", "primitives": [{"attributes": {"POSITION": 132, "NORMAL": 133, "TANGENT": 134}, "indices": 135, "material": 33}]}, {"name": "Table6_jRZaEE0OO06L4BKDbXn1mw", "primitives": [{"attributes": {"POSITION": 136, "NORMAL": 137, "TANGENT": 138}, "indices": 139, "material": 34}]}, {"name": "Table5_bkqrx0FJRkK6PDsnWW4mXw", "primitives": [{"attributes": {"POSITION": 140, "NORMAL": 141, "TANGENT": 142}, "indices": 143, "material": 35}]}, {"name": "Table4_zRy9iH+abEiNAqGBpLQGJw", "primitives": [{"attributes": {"POSITION": 144, "NORMAL": 145, "TANGENT": 146}, "indices": 147, "material": 36}]}, {"name": "Table3_tN8RdgPI60yInRYkdZDxHQ", "primitives": [{"attributes": {"POSITION": 148, "NORMAL": 149, "TANGENT": 150}, "indices": 151, "material": 37}]}, {"name": "Table2_+2qB/BSawU6IaLNeqkooVg", "primitives": [{"attributes": {"POSITION": 152, "NORMAL": 153, "TANGENT": 154}, "indices": 155, "material": 38}]}, {"name": "Table1_gkO0Pfm9OEGQRJ10QJl5Jw", "primitives": [{"attributes": {"POSITION": 156, "NORMAL": 157, "TANGENT": 158}, "indices": 159, "material": 39}]}, {"name": "Table0_0ftcUUXGzUWlbDNcAxPIvg", "primitives": [{"attributes": {"POSITION": 160, "NORMAL": 161, "TANGENT": 162}, "indices": 163, "material": 40}]}, {"name": "Sofa0_a89N9woiuECtRM5X4nFiIg", "primitives": [{"attributes": {"POSITION": 164, "NORMAL": 165, "TANGENT": 166}, "indices": 167, "material": 41}]}, {"name": "Chair14_UYYvq5gIMkKctlFlo/n6ig", "primitives": [{"attributes": {"POSITION": 168, "NORMAL": 169, "TANGENT": 170}, "indices": 171, "material": 42}]}, {"name": "Chair13_BvlAL3q/PEKPpNE7DbnNWw", "primitives": [{"attributes": {"POSITION": 172, "NORMAL": 173, "TANGENT": 174}, "indices": 175, "material": 43}]}, {"name": "Chair12_mNycNG/6ckmhiDnA14Gaug", "primitives": [{"attributes": {"POSITION": 176, "NORMAL": 177, "TANGENT": 178}, "indices": 179, "material": 44}]}, {"name": "Chair11_r8YJv8IOHkeaPwl7wmNvzg", "primitives": [{"attributes": {"POSITION": 180, "NORMAL": 181, "TANGENT": 182}, "indices": 183, "material": 45}]}, {"name": "Chair10_TajCcFVlbEqZMtmkNf43tA", "primitives": [{"attributes": {"POSITION": 184, "NORMAL": 185, "TANGENT": 186}, "indices": 187, "material": 46}]}, {"name": "Chair9_iq3lk9z1T0ykC+gGX/npFg", "primitives": [{"attributes": {"POSITION": 188, "NORMAL": 189, "TANGENT": 190}, "indices": 191, "material": 47}]}, {"name": "Chair8_onjP8srTbEWOSOXa733jfg", "primitives": [{"attributes": {"POSITION": 192, "NORMAL": 193, "TANGENT": 194}, "indices": 195, "material": 48}]}, {"name": "Chair7_b+g4XA/x7EK8N5bANYrmhg", "primitives": [{"attributes": {"POSITION": 196, "NORMAL": 197, "TANGENT": 198}, "indices": 199, "material": 49}]}, {"name": "Chair6_joEa33nIX0ySb3blzyIxkQ", "primitives": [{"attributes": {"POSITION": 200, "NORMAL": 201, "TANGENT": 202}, "indices": 203, "material": 50}]}, {"name": "Chair5_wb4RRYvmdE6sgzCY1XlwuA", "primitives": [{"attributes": {"POSITION": 204, "NORMAL": 205, "TANGENT": 206}, "indices": 207, "material": 51}]}, {"name": "Chair4_Hd7etsv6jkC5FhAbhUqVdg", "primitives": [{"attributes": {"POSITION": 208, "NORMAL": 209, "TANGENT": 210}, "indices": 211, "material": 52}]}, {"name": "Chair3_rwCy3AqLdESjdt+URwCkvg", "primitives": [{"attributes": {"POSITION": 212, "NORMAL": 213, "TANGENT": 214}, "indices": 215, "material": 53}]}, {"name": "Chair2_IMjxk45fc0Ci2/JjmzVFSw", "primitives": [{"attributes": {"POSITION": 216, "NORMAL": 217, "TANGENT": 218}, "indices": 219, "material": 54}]}, {"name": "Chair1_IvILzMrVkEKPAEZE1OUW8A", "primitives": [{"attributes": {"POSITION": 220, "NORMAL": 221, "TANGENT": 222}, "indices": 223, "material": 55}]}, {"name": "Chair0_G9LHMBM40UitGH2ek8hL1w", "primitives": [{"attributes": {"POSITION": 224, "NORMAL": 225, "TANGENT": 226}, "indices": 227, "material": 56}]}], "scene": 0, "scenes": [{"nodes": [88]}]}