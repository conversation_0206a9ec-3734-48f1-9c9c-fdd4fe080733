<template>
  <div v-if="modalShow" class="modal">
    <div class="modal-content" :class="{ smallBox: ruleForm.sceneType == 2 }">
      <div class="modal-content-title">
        <div>新建项目</div>
      </div>
      <div class="modal-form">
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="项目名称" prop="sceneName">
            <el-input class="form-input" style="width: 330px;" v-model="ruleForm.sceneName" placeholder="请输入项目名称"
              maxlength="20" />
          </el-form-item>
          <el-form-item label="应用平台" prop="scenePlatform">
            <el-select v-if="!isExperience && !isCopyTemp" v-model="ruleForm.scenePlatform" class="select-default"
              popper-class="select-option" :suffix-icon="DropDown" style="width: 176px; height: 36px;">
              <el-option v-for="(item, index) in platformMap" :key="index" :label="item.name" :value="item.value" />
            </el-select>
            <span v-if="isExperience || isCopyTemp">微信小程序</span>
            <div class="platform-help">
              <div class="help-tips">
                <div class="title">眼镜端支持设备</div>
                <div>Rokid AR Studio</div>
                <div>PICO 4 Enterprise</div>
                <div>PICO 4 Ultra Enterprise</div>
                <div class="title">移动端及微信小程序支持设备</div>
                <div>Android设备需要支持ARcore</div>
                <div>IOS设备需要支持ARkit</div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="类型" prop="sceneType">
            <template v-if="isCopyTemp">
              <span style="color: #0f0f0f;">{{ computeTempName() }}</span>
            </template>
            <template v-if="isExperience && !isCopyTemp">
              <!-- <span>平面识别AR1</span> -->
              <el-select v-model="ruleForm.sceneType" class="select-default" popper-class="select-option"
                :suffix-icon="DropDown" style="width: 176px; height: 36px;">
                <el-option v-for="(item, index) in [sceneTypeList[1], sceneTypeList[2], sceneTypeList[4]]" :key="index"
                  :label="item.name" :value="item.value" />
              </el-select>
            </template>
            <template v-if="!isExperience && !isCopyTemp">
              <div v-if="ruleForm.scenePlatform != 3">
                <span style="color: #0f0f0f;">空间AR</span>
              </div>
              <el-select v-else v-model="ruleForm.sceneType" class="select-default" popper-class="select-option"
                :suffix-icon="DropDown" style="width: 176px; height: 36px;">
                <el-option v-for="(item, index) in sceneTypeList" :key="index" :label="item.name" :value="item.value" />
              </el-select>
            </template>
          </el-form-item>
          <!-- <el-form-item label="所属场馆" prop="stadiumName" v-if="ruleForm.sceneType != 2 && !isCopyTemp">
            <el-input class="form-input" v-model="ruleForm.stadiumName" placeholder="请输入所属场馆" />
          </el-form-item> -->
          <el-form-item label="空间数据" prop="spaceId" v-if="ruleForm.sceneType == 1 && !isCopyTemp">
            <el-select v-model="ruleForm.spaceId" clearable placeholder="请选择空间数据" class="select-default"
              popper-class="select-option" :suffix-icon="DropDown" style="width: 176px; height: 36px;"
              @change="selectSpace">
              <el-option v-for="(item, index) in spaceList" :key="index" :label="item.descriptionName"
                :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="" prop="spaceImage" v-if="ruleForm.sceneType == 1 && !isCopyTemp">
            <div class="space-preview" @click="ruleForm.spaceId ? showPreview = true : null"
              :class="ruleForm.spaceId ? 'active' : ''">空间预览</div>
          </el-form-item>
          <el-form-item label="识别图" class="lable-name2" prop="imagesIdentify"
            v-if="[3, 8].includes(ruleForm.sceneType) && !isCopyTemp">
            <div class="upload-image">
              <div class="image-box">
                <img v-if="!loadedURL" class="image-default" src="@/assets/images/home/<USER>">
                <img v-if="loadedURL" :src="loadedURL" />
                <div class="upload-mask">
                  <img src="@/assets/images/home/<USER>" />
                  <upload-template file-name="item" class-style="upload-btn input-file" :loading="loading" :baseURL="uploadURL" :beforeUpload="beforeUpload" :handleAvatarSuccess="uploadedThumbnail" :handle-change="handleChange"></upload-template>
                </div>
              </div>
              <div class="upload-tips">
                <div>支持5MB的png或jpg图片</div>
                <div class="upload-link" @mousemove="showUploadTips = true" @mouseleave="showUploadTips = false">
                  （上传图片建议）</div>
              </div>
            </div>
          </el-form-item>
          <!-- <el-form-item label="" v-if="[3, 8].includes(ruleForm.sceneType) && !isCopyTemp">
            <div class="examine-tips">审核中……<span style="color: #1E1E1E;">不影响项目编辑</span></div>
          </el-form-item> -->
          <el-form-item class="form-submit">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3" style="margin-right: 24px;">
              <el-button type="primary" @click="submitForm(ruleFormRef)">
                确认
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="upload-tips-list" v-if="showUploadTips">
        <div class="title">上传图片建议</div>
        <div>1、识别图格式：png/jpg</div>
        <div>2、识别图宽高比：9:16 ~ 16:9之间，尺寸240px ~ 2048px，建议为800px</div>
        <div>3、识别图文件大小：5MB</div>
        <div>4、识别图具有丰富的细节与高对比度，避免大面积纯色与空白，避免重复图案</div>
      </div>
    </div>
    <TipsView></TipsView>
    <preview-scene v-if="showPreview" :spaceInfo="currentSpace" :hideModel="hidePreview"></preview-scene>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMention, ElMessage, type FormInstance, type FormRules, type UploadUserFile } from 'element-plus'
import DropDown from '@/components/DropDown.vue'
import { saveSceneMetaBackId, getSpacePage, getSpace, saveWxIdentifySceneInfo } from '@/api/index'
import { useRouter } from 'vue-router'
import { useStore } from "vuex";
import TipsView from '@/components/TipsView.vue'
import PreviewScene from '@/components/template/PreviewScene.vue'
import { desensitizte } from '@/utils'
import { sceneTypeList } from '@/config'
import UploadTemplate from '@/views/template/components/UploadTemplate.vue';

const props = defineProps({
  modalShow: {
    default: false,
    type: Boolean
  },
  handleHide: {
    default: null,
    type: Function
  },
  isExperience: {
    defaut: false,
    type: Boolean
  },
  requestCopyWxScene: {
    default: null,
    type: Function
  },
  isCopyTemp: {
    default: false,
    type: Boolean
  },
  hasRoomScene: {
    default: true,
    type: Boolean
  },
  hasPlaneScene: {
    default: true,
    type: Boolean
  },
  sceneType: {
    require: true,
    type: Number
  }
})

let platformMap = [
  {
    name: '眼镜端',
    value: 1
  },
  {
    name: '移动端',
    value: 2
  },
  {
    name: '微信小程序',
    value: 3
  }
]

const router = useRouter()
let spaceList: any = ref([])
let spaceNameList: any = []
let currentSpace: any = ref({})
const showPreview = ref(false)
const showUploadTips = ref(false)
const loading = ref(false)
const uploadURL = ref('') // 上传素材接口地址
const loadedURL = ref('') // 预览素材照片地址
const store = useStore();
const packageVersion = ref(null);

interface RuleForm {
  sceneName: string
  stadiumName: string
  describe: string
  spaceId: string
  scenePlatform: number,
  sceneType: number
  imagesIdentify: string
}

const ruleFormRef = ref<FormInstance>()
const ruleForm: any = reactive<RuleForm>({
  sceneName: '',
  stadiumName: '',
  describe: '',
  spaceId: '',
  scenePlatform: 1,
  sceneType: 1,
  imagesIdentify: ''
})
const baseURL = process.env.NODE_ENV === 'production' ? '/api' : '/api1' // 基础url
const picUrl = ref('')

const computeTempName = computed(() => {
  return () => {
    console.log(ruleForm.sceneType, 'ruleForm.sceneType');

    if (ruleForm.sceneType == '2') {
      return '平面AR'
    } else if (ruleForm.sceneType == '3') {
      return '图像AR'
    } else if (ruleForm.sceneType == '5') {
      return '身体AR'
    } else if (ruleForm.sceneType == '6') {
      return '人脸AR'
    } else if (ruleForm.sceneType == '7') {
      return '手势AR'
    } else if (ruleForm.sceneType == '8') {
      return '单场景AR'
    }
  }
})

const handleChange = (file: any) => {
  if (!uploadURL.value) {
    loadedURL.value = ''
    return
  }
  loadedURL.value = URL.createObjectURL(file.raw)
  loading.value = false;
}

const uploadedThumbnail = (res: any) => {
  console.log(res)
  picUrl.value = res.data
}

watch(() => ruleForm.scenePlatform, (nv) => {
  if (nv != 3) {
    ruleForm.sceneType = 1
  }
})

watch(() => ruleForm.sceneType, (nv) => {
  if (nv != 1) {
    ruleForm.spaceId = ''
  }
})

const validatePass = (rule: any, value: any, callback: any) => {
  if (spaceNameList.includes(value)) {
    callback(new Error('项目名已经存在。'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules<RuleForm>>({
  sceneName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { validator: validatePass, trigger: 'blur' }
  ],
  stadiumName: [{ required: true, message: '请输入所属场馆', trigger: 'blur' }],
  scenePlatform: [{ required: true, message: '请选择应用平台', trigger: 'blur' }],
  sceneType: [{ required: true, message: '请选择项目类型', trigger: 'blur' }],
  imagesIdentify: [{ required: true, message: '请上传识别图', trigger: 'blur' }],
  spaceId: [
    { required: true, message: '请选择空间数据', trigger: 'blur' },
  ]
})

const submitForm = async (formEl: FormInstance | undefined) => {
  let isAgreeText = false
  let path = props.isExperience ? '/experience_edit?sceneid=' : '/scene_edit?sceneid='
  if (!formEl) return
  if (props.isCopyTemp) {
    if (!ruleForm.sceneName) return ElMessage({ type: 'success', message: '请输入项目名称' })
    const hasSensitiveWords1 = await desensitizte(ruleForm.sceneName, '项目名称不可包含敏感词汇！')
    if (hasSensitiveWords1) return
    props.requestCopyWxScene(ruleForm.sceneName) // 模板的创建同款
    return
  }
  if (!picUrl.value && (ruleForm.sceneType == 3 || ruleForm.sceneType == 8)) {
    ruleForm.imagesIdentify = '';
  } else if (ruleForm.sceneType == 3 || ruleForm.sceneType == 8) {
    ruleForm.imagesIdentify = picUrl.value;
  }
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const hasSensitiveWords2 = await desensitizte(ruleForm.sceneName, '项目名称不可包含敏感词汇！')
      if (hasSensitiveWords2) return
      if (ruleForm.scenePlatform == 3 && [3, 4, 5, 6, 7, 8].includes(+ruleForm.sceneType)) {
        saveWxIdentifySceneInfo({
          sceneMetaDto: {
            sceneName: ruleForm.sceneName,
            scenePlatform: ruleForm.scenePlatform,
            sceneType: ruleForm.sceneType,
          },
          identifyPicKey: ruleForm.sceneType == 3 ? picUrl.value : '',
          identificationKey: ruleForm.sceneType == 8 ? picUrl.value : '',
        }).then((res: any) => {
          if (res.code == 200) {
            props.handleHide(true)
            router.push(`/experience_edit?sceneType=${ruleForm.sceneType}&sceneid=${res.data}`)
          }
        })
      } else {
        saveSceneMetaBackId({ ...ruleForm }).then((res: any) => {
          if (res.code == '200') {
            path = ruleForm.sceneType == 2 ? '/experience_edit?sceneid=' : '/scene_edit?sceneid='
            props.handleHide(true)
            router.push(path + res.data + '&sceneType=' + ruleForm.sceneType)
          }
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const beforeUpload = async (rawFile: any) => {
  let sourceFormat = rawFile.name.split('.')[1]
  if (sourceFormat) {
    sourceFormat = sourceFormat.toUpperCase().toLowerCase();
  }
  if (!['png', 'jpg'].includes(sourceFormat)) {
    store.state.showTips = '您上传的素材格式不正确，请上传与要求相符的素材。'
    uploadURL.value = ''
    return
  }
  uploadURL.value = `${baseURL}/scene/uploadIdentiyElement?sceneType=${ruleForm.sceneType}`;
  loading.value = true;
}

const hidePreview = () => {
  showPreview.value = false;
}

const changeState = () => {
  props.handleHide()
}

const selectSpace = (id: number) => {
  getSpacePage({ pageNo: 1, pageSize: 999, status: 2 }).then((res: any) => {
    spaceList.value = [...res.data.records]
    spaceNameList = res.data.records.map((e: any) => e.sceneName)
    currentSpace.value = spaceList.value.filter((e: any) => e.id == id)[0] || {}
  })
}

onMounted(() => {
  ruleForm.scenePlatform = props.isExperience ? 3 : 1
  ruleForm.sceneType = props.sceneType
  console.log(ruleForm.sceneType, '000000000000000');

  getSpacePage({ pageNo: 1, pageSize: 999, status: 2 }).then((res: any) => {
    spaceList.value = [...res.data.records]
    spaceNameList = res.data.records.map((e: any) => e.sceneName)
  })
  store.dispatch('updateCurrentData', {
    sceneId: router.currentRoute.value.query.sceneId,
    templateId: router.currentRoute.value.query.templateId
  });
  const userInfo = store.state.userInfo;
  if (userInfo?.packageVersion) {
    packageVersion.value = userInfo.packageVersion;
  }
})

watch(() => props.hasPlaneScene, (nv) => {
  if (!nv) {
    platformMap = platformMap.filter(item => item.value != 3)
  }
  if (nv) {
    platformMap[2] = {
      name: '微信小程序',
      value: 3
    }
  }
}, { immediate: true })

watch(() => props.hasRoomScene, (nv) => {
  if (!nv) {
    platformMap = platformMap.filter(item => item.value == 3)
    ruleForm.scenePlatform = 3
  }
  if (nv) {
    ruleForm.scenePlatform = 1
    platformMap[0] = {
      name: '眼镜端',
      value: 1
    }
    platformMap[1] = {
      name: '移动端',
      value: 2
    }
  }
}, { immediate: true })

watch(() => props.isCopyTemp, () => {
  if (!props.isExperience) {
    if (props.isCopyTemp) { // 企业版：如果正在 创建模板的同款
      ruleForm.scenePlatform = 3
    } else {
      ruleForm.scenePlatform = 1 // 企业版，正常创建场景时，需要置为眼镜端
    }
  }
})

watch(() => store.state.userInfo, (newState) => {
  if (newState.packageVersion) {
    packageVersion.value = newState.packageVersion;
  }
})
</script>
<style scoped lang="less">
:deep(.el-form-item__label) {
  width: 100px !important;
}

.platform-help {
  width: 20px;
  height: 20px;
  margin-left: 10px;
  margin-top: -3px;
  background-image: url(~@/assets/images/icon/help.png);
  background-size: 100% 100%;
  position: relative;
  display: inline-block;

  &:hover {
    background-image: url(~@/assets/images/icon/helpA.png);
    background-size: 100% 100%;

    .help-tips {
      display: inline-block;
    }
  }

  .help-tips {
    position: relative;
    position: absolute;
    left: 30px;
    top: -12px;
    width: 205px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px 4px 4px 4px;
    z-index: 1;
    display: none;
    padding: 0 0 10px 12px;
    box-sizing: border-box;
    font-size: 12px;
    color: #fff;
    text-align: left;
    height: auto;
    line-height: 1.5;
    font-weight: 400;

    .title {
      font-size: 14px;
      font-weight: bold;
      margin: 10px 0 4px;
    }

    &::before {
      content: '';
      border: 6px solid transparent;
      position: absolute;
      left: -12px;
      top: 16px;
      border-right-color: rgba(0, 0, 0, 0.70);
    }
  }
}

// :deep(.form-input) {
//   width: 176px !important;
// }
.input-file {
  width: 100%;
  height: 100%;
  // background-color: pink;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  opacity: 0;
  cursor: pointer;
}

.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .modal-content {
    position: relative;
    width: 453px;
    max-height: 90%;
    background: #fff;
    border-radius: 8px;

    .examine-tips {
      background: #F7F7F7;
      border-radius: 4px;
      height: 24px;
      line-height: 24px;
      width: calc(100% - 23px);
      font-weight: 400;
      font-size: 12px;
      color: #2E76FF;
    }

    .upload-tips-list {
      width: 439px;
      height: 147px;
      background: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #2E76FF;
      position: absolute;
      right: -450px;
      bottom: 19px;
      font-weight: 400;
      font-size: 12px;
      color: #1E1E1E;
      line-height: 18px;
      padding: 6px 10px;
      box-sizing: border-box;
      text-align: left;

      &>div {
        margin-top: 6px;
      }

      .title {
        font-weight: bold;
        font-size: 14px;
        color: #1E1E1E;
        padding-left: 4px;
        border-left: 2px solid #2E76FF;
        height: 21px;
        line-height: 20px;
      }

      .title-tips {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        margin: 10px 0 8px;
      }
    }

    .modal-content-title {
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 16px;
      padding-left: 24px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      border-top-left-radius: 14px;
      border-top-right-radius: 14px;

      .icon-close {
        font-size: 26px;
        cursor: pointer;
        font-weight: 400;

        &:hover {
          color: #2E76FF;
        }
      }
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      box-sizing: border-box;

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-textarea {
        width: 432px;
        height: 91px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-submit {
        margin-top: 92px;
      }

      .space-preview {
        width: 176px;
        height: 24px;
        line-height: 24px;
        background: #F7F7F7;
        border-radius: 4px 4px 4px 4px;
        font-size: 12px;
        color: #DADADA;
        text-align: center;
        cursor: no-drop;

        &.active {
          color: #2E76FF;
          cursor: pointer;

          &:hover {
            background-color: #2E76FF;
            color: #fff;
          }
        }
      }

      ::v-deep(.lable-name2 .el-form-item__label) {
        position: relative;
        margin-top: 15px;
      }

      .lable-name2 .upload-image {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 82px;

        .image-box {
          position: relative;
          width: 120px;
          height: 82px;
          background: #FFFFFF;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #DADADA;
          display: flex;
          justify-content: center;
          align-items: center;

          .image-default {
            width: 24px;
            height: 24px;
          }

          &:hover .upload-mask {
            visibility: visible;
          }

          &>img {
            max-width: 100%;
            max-height: 100%;
          }

          .upload-mask {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(0, 0, 0, 0.5);
            visibility: hidden;

            img {
              width: 30px;
              height: 30px;
            }
          }
        }

        .upload-tips {
          height: 100%;
          margin-left: 12px;
          margin-top: 30px;
          text-align: left;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          font-weight: 400;
          font-size: 12px;
          color: #797979;
          line-height: 18px;
          padding-top: 6px;

          .upload-link {
            color: #2E76FF;
            text-decoration-line: underline;
            margin-top: 6px;
            cursor: pointer;
          }
        }
      }
    }
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;
}

.el-upload__text>span {
  margin-left: 3px;
}
</style>