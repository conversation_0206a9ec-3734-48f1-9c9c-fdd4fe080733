import {
  Intersection,
  Sprite,
  SpriteMaterial,
  Raycaster,
  RepeatWrapping,
  TextureLoader,
  Vector3,
  Quaternion,
  Group,
  Vector2
} from "three";
import { CPoint } from "./CPoint";
import { pointRotate } from "@/utils";
import { CSS2DObject } from "../libs/CSS2DRenderer";

export const ADSORPTION_RADIUS = 5; // 点吸附半径

export type CPointAttrs = {
  vertex?: Vector3;
  color?: number;
  size?: number;
  transparent?: boolean;
  url?: string;
  scale?: Vector3;
  constraint?: any; // 约束缓存
  noraycast?: boolean; // 是否不做射线拾取
  type?: string;
  marks?: any; // 标注内容
};
export class CSprite extends Sprite {
  material: SpriteMaterial;
  name: string;
  userData: { [k: string]: any };
  constructor(attrs?: CPointAttrs) {
    super();

    this.name = "csprite";
    this.material = new SpriteMaterial();
    this.userData = {};
    this.initialize(attrs);
  }

  initData(attrs?: CPointAttrs) {
    // 约束/标注/uuid/索引缓存
    this.userData = {
      constraint: {
        links: [],
      },
      marks: "",
      uuid: "",
      index: -1,
    };
    if (attrs && attrs.constraint !== undefined)
      this.userData.constraint = attrs.constraint; // constraint data cache

    if (attrs?.type == "source") {
      this.userData.type = "source";
      // 生成文字标注并隐藏
      const div = document.createElement("div");
      div.innerHTML = attrs?.marks || "";
      div.classList.add("source-title");
      div.style.pointerEvents = "none"; //避免HTML标签遮挡三维场景的鼠标事件
      const tag = new CSS2DObject(div);
      this.add(tag);



      const controls = (window as any).controls;
      const onChange = (e: any) => {
        const zoom = controls.object.zoom;
        div.style.top = 35 * zoom + 5 + 'px'
      };
      
      
      controls.addEventListener("change", onChange);
      // 被销毁后 移除事件
      this.geometry.addEventListener("dispose", (e: any) => {
        controls.removeEventListener("change", onChange);
      });
    }
  }

  initUI(attrs?: CPointAttrs) {
    // 默认坐标原点
    if (attrs) {
      if (attrs.vertex !== undefined) {
        this.setVertex(attrs.vertex);
      }
    }
    if (attrs && attrs.url !== undefined) {
      this.loadTexture(attrs.url); // 默认纹理

      this.userData.baseUrl = attrs.url.split('_a').join('')
    }
    if (attrs && attrs.color !== undefined) this.setColor(attrs.color); // 颜色
    if (attrs && attrs.scale !== undefined) {
      this.setScale(attrs.scale); // 缩放
    } else {
      this.setScale(new Vector3(3.2, 3.2, 3.2));
    }
    if (attrs && attrs.noraycast !== undefined)
      this.userData.noraycast = attrs.noraycast;
    this.center.set(0.5, 0.5);
    this.material.depthTest = false;
  }

  initialize(attrs?: CPointAttrs) {
    this.initData(attrs);
    this.initUI(attrs);
  }

  // @override 射线拾取点
  raycast(raycaster: Raycaster, intersects: Intersection[]) {
    if (this.userData.noraycast) return;
    const ori = (raycaster as any).ray.origin;
    if (this.userData.type == "source") {
      // const r = Math.max(this.material.size / 2, ADSORPTION_RADIUS);
      const ori = (raycaster as any).ray.origin;
      const oriCurrent = ori.clone().project((window as any).camera);
      const v1 = new Vector2(
        (0.5 + oriCurrent.x / 2) * window.innerWidth,
        (0.5 - oriCurrent.y / 2) * window.innerHeight
      );
      const position = this.userData.point.clone();

      const current = position.clone().project((window as any).camera);
      const v2 = new Vector2(
        (0.5 + current.x / 2) * window.innerWidth,
        (0.5 - current.y / 2) * window.innerHeight
      );
      const zoom = (window as any).controls.object.zoom;
      if (v1.distanceTo(v2) <= 20 * zoom) {
        intersects.push({
          distance: current.clone().distanceTo((window as any).camera.position),
          object: this,
          point: position,
        } as any);
      }
      return;
    }
    // 计算旋转时候是否在箭头的位置
    const y1 = this.userData.rpoint[1]
      .clone()
      .sub(this.userData.point)
      .clone()
      .cross(ori.clone().sub(this.userData.point))
      .normalize().y;
    const y2 = ori
      .clone()
      .sub(this.userData.point)
      .clone()
      .cross(this.userData.rpoint[0].clone().sub(this.userData.point))
      .normalize().y;
    const d = this.userData.point.distanceTo(ori);
    if (y1 > 0 && y2 > 0 && d > 1.36 && d < 1.61) {
      intersects.push({
        object: this,
      } as any);
    }
  }

  loadTexture(url: string) {
    const loader = new TextureLoader();
    loader.crossOrigin = 'Anonymous'
    const t = loader.load(url);
    t.wrapS = t.wrapT = RepeatWrapping;
    this.material.map = t;
    return this;
  }

  // 顶点位置： 单个数据
  setVertex(vertex: Vector3) {
    this.position.set(vertex.x, vertex.y, vertex.z);
    if (this.userData.type == "source") {
      this.userData.point = new Vector3(vertex.x, vertex.y, vertex.z);
    }
    return this;
  }

  getVertex() {
    const bufferData = this.geometry.attributes.position.array;
    return new Vector3(bufferData[0], bufferData[1], bufferData[2]);
  }

  setRotate(radian: number) {
    this.material.rotation = radian;

    if (this.userData.noraycast) return;
    // 更新路径点的旋转后位置
    this.userData.rpoint = this.userData.rpoint.map((e: any) =>
      pointRotate(e, this.userData.point, -(this.userData.oldRotate + radian))
    );
  }

  setScale(scale: Vector3) {
    this.scale.set(scale.x, scale.y, scale.z);
  }

  // 颜色
  setColor(color: number) {
    this.material.color.set(color);
    return this;
  }

  getColor() {
    return this.material.color;
  }
}
