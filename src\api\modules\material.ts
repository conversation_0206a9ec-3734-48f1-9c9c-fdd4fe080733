import request from '../request';

// 添加默认素材
export function addDefaultMaterialMeta(data: any) {
  return request({
    url: '/material/addDefaultMaterialMeta',
    method: 'post',
    data,
  });
}

// 添加素材
export function addMaterial(data: any) {
  return request({
    url: '/material/addMaterial',
    method: 'post',
    data,
  });
}

// 添加素材2
export function addModelMaterial(data: any) {
  console.log();
  return request({
    url:
      '/material/addModelMaterial?materialAffiliation=' + data.animationInfoDto.materialAffiliation,
    method: 'post',
    data,
  });
}

// 根据id修改素材
export function updateMaterial(data: any) {
  return request({
    url: '/material/updateMaterial',
    method: 'post',
    data,
  });
}

// 根据id修改素材
export function updatePersonMaterial(data: any) {
  return request({
    url: '/material/updatePersonMaterial',
    method: 'post',
    data,
  });
}

// 根据id修改默认素材
export function updateDefaultMaterial(data: any) {
  return request({
    url: '/material/updateDefaultMaterial',
    method: 'post',
    data,
  });
}

// 根据用户分页查询素材
export function getMaterialPageByUser(data: any) {
  return request({
    url: `/material/getMaterialPageByUser?pageNo=${data.pageNo || ''}&pageSize=${
      data.pageSize || ''
    }${data.materialName ? `&materialName=${data.materialName}` : ''}${
      data.materialType ? `&materialType=${data.materialType}` : ''
    }&getTotal=${!!data.getTotal}&platformV2=${data.platformV2 || ''}`,
    method: 'get',
  });
}

// 根据用户分页查询默认素材
export function getDefaultMaterial(data: any) {
  console.log('DATA', data);
  return request({
    url:
      `/material/getDefaultMaterial?pageNo=${data.pageNo || ''}&pageSize=${data.pageSize || ''}` +
      (data.materialName ? `&materialName=${data.materialName}` : '') +
      (data.materialType ? `&materialType=${data.materialType}` : '') +
      (data.modelType ? `&modelType=${data.modelType}` : '') +
      `&getTotal=${!!data.getTotal}&platformV2=${data.platformV2 || ''}`,
    method: 'get',
  });
}

// 根据id删除素材
export function deleteMaterial(data: any) {
  return request({
    url: '/material/deleteMaterial?materialId=' + data.materialId,
    method: 'post',
    data,
  });
}

// 根据id删除个人素材
export function deletePersonMaterial(data: any) {
  return request({
    url: '/material/deletePersonMaterial?materialId=' + data.materialId,
    method: 'post',
  });
}

// 根据id删除个人素材（批量）
export function deletePersonMaterials(data: any) {
  return request({
    url: '/material/deletePersonMaterials',
    method: 'post',
    data,
  });
}

// 根据id删除默认素材
export function deleteDefaultMaterial(data: any) {
  return request({
    url: '/material/deleteDefaultMaterial?materialId=' + data.materialId,
    method: 'post',
    data,
  });
}

// 新增素材
export function addPersonMaterialMeta(data: any) {
  return request({
    url: '/material/addPersonMaterialMeta',
    method: 'post',
    data,
  });
}

// 获取素材
export function getMaterialPageByUserV2(data: any) {
  return request({
    url: `/material/getMaterialPageByUserV2?pageNo=${data.pageNo || ''}&pageSize=${
      data.pageSize || ''
    }${data.materialName ? `&materialName=${data.materialName}` : ''}${
      data.materialType ? `&materialType=${data.materialType}` : ''
    }&platformV2=${data.platformV2 || ''}`,
    method: 'get',
  });
}

// 删除临时素材
export function deleteTmpMaterial(data: any) {
  return request({
    url: '/material/deleteTmpMaterial',
    method: 'post',
    data,
  });
}

// 获取素材使用的场景
export function getMatUsedScene(data: any) {
  return request({
    url: `/scene/getMatUsedScene?materialId=${data.materialId}&materialAffiliation=${data.materialAffiliation}`,
    method: 'get',
  });
}

// 获取素材生成状态
export function getMaterialGenStatus(id: number) {
  return request({
    url: `/material/getMaterialGenStatus?id=${id}`,
    method: 'get',
  });
}

// 模型分类管理相关API

// 获取所有模型类型
export function getModeType() {
  return request({
    url: '/material/getModeType',
    method: 'post',
  });
}

// 添加模型类型
export function addModelType(data: { typeName: string }) {
  return request({
    url: `/material/addModelType?typeName=${data.typeName}`,
    method: 'post',
  });
}

// 删除模型类型
export function deleteModelType(data: { id: number }) {
  return request({
    url: `/material/deleteModelType?id=${data.id}`,
    method: 'post',
  });
}

// 更新模型类型
export function updateModeType(data: { id: number; typeName: string }) {
  return request({
    url: `/material/updateModeType?id=${data.id}&typeName=${data.typeName}`,
    method: 'post',
  });
}

// 设置材料模型类型
export function setMaterialModelType(data: { id: string; categoryIds: string[] }) {
  return request({
    url: `/material/setMaterialModelType?id=${data.id}`,
    method: 'post',
    data: data.categoryIds,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 更新模型类型排序
export function updateModelTypeSort(data: Array<{ firstData: number; secondData: number }>) {
  return request({
    url: '/material/updateModelTypeSort',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
