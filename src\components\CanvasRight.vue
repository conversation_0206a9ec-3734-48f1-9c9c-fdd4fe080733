<template>
  <div class="canvas-right" :class="hide_side ? 'hide-right-side' : ''">
    <div class="interactive-data" v-if="operateIndex == 0">
      <div class="info-title">
        <img src="@/assets/images/icon/list.png" />
        <span>交互参数</span>
      </div>
      <div v-for="(item, index) in changeRatio" :key="index">
        <div class="info-list" v-show="index == 'position' || !currentActiveSource">
          <span>{{ item.name }}</span>
          <div>
            <span class="axle">X</span>
            <el-input v-model="item.x" type="text" autocomplete="off" :formatter="(value: string) => checkNumber(value)"
              :class="item.name === '位置' ? 'is-position ' : '' + item.name === '旋转' ? 'is-rotate' : ''"
              @input="changeXYZ($event, 'x', index)" :disabled="!!(index == 'rotate' && spatialData)" />
          </div>
          <div>
            <span class="axle">Y</span>
            <el-input v-model="item.y" type="text" autocomplete="off" :formatter="(value: string) => checkNumber(value)"
              :class="item.name === '位置' ? 'is-position ' : '' + item.name === '旋转' ? 'is-rotate' : ''"
              @input="changeXYZ($event, 'y', index)"
              :disabled="!!((index == 'position' || index == 'scale') && spatialData)" />
          </div>
          <div>
            <span class="axle">Z</span>
            <el-input v-model="item.z" type="text" autocomplete="off" :formatter="(value: string) => checkNumber(value)"
              :class="item.name === '位置' ? 'is-position ' : '' + item.name === '旋转' ? 'is-rotate' : ''"
              @input="changeXYZ($event, 'z', index)" :disabled="!!(index == 'rotate' && spatialData)" />
          </div>
        </div>
      </div>
    </div>
    <div class="interactive-data" v-if="operateIndex == 1">
      <div class="info-title">
        <img src="@/assets/images/icon/list.png" />
        <span>导览路线设置</span>
      </div>
      <div v-for="(item, index) in routerStyleList" :key="item.title">
        <div class="style-title" :style="{ marginTop: index == 0 ? '0px' : '40px' }">
          {{ item.title }}
        </div>
        <div class="style-img">
          <img v-for="(e, i) in item.value" :src="e.url" :key="i" />
        </div>
        <div class="style-name">
          <div v-for="(e, i) in item.value" :key="i">
            <img v-if="activeStyle[index] != i" @click="changeStyle(index, i)" src="@/assets/images/icon/radio.png" />
            <img v-if="activeStyle[index] == i" @click="changeStyle(index, i)" src="@/assets/images/icon/radioA.png" />
            <span>{{ e.name }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="scene-info">
      <div v-for="(item, index) in sceneInfoList" :key="index">
        <div v-if="index === 3" class="scene-info-line"></div>
        <div v-if="index == 0 || index == 3" class="info-title">
          <img src="@/assets/images/icon/list.png" />
          <span>{{ index == 0 ? '项目信息' : '空间信息' }}</span>
        </div>
        <div class="scene-info-list">
          <div>{{ item.name }}</div>
          <div>
            <el-input :class="item.type === 'textarea' ? 'scene-info-dec' : ''" v-model="item.value"
              :type="item.type || 'text'" resize="none" maxlength="30" :disabled="item.disabled"
              @change="changeValue(item.value, item.key || '')" />
          </div>
        </div>
      </div>
      <div class="space-model">
        <img v-if="!sceneInfo.spaceDto?.spacePic" src="@/assets/images/background/space.png" />
        <img v-if="sceneInfo.spaceDto?.spacePic" :src="sceneInfo.spaceDto?.spacePic" />
      </div>
      <div class="btn-primary el-size2" @click="exitEditPage">
        <el-button>
          <span class="icon"></span>
          退出编辑
        </el-button>
      </div>
    </div>
    <BehaviorTree></BehaviorTree>
  </div>
  <div v-if="!hide_side" class="hide-side" @click="hide_side = true">
    <img src="@/assets/images/icon/hide-side.png" />
  </div>
  <div v-if="hide_side" class="show-side" @click="hide_side = false">
    <img src="@/assets/images/icon/show-side.png" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, reactive, watch } from 'vue'
import { radiansToAngle } from "@/utils";
import { useRouter } from 'vue-router'
import { useStore } from "vuex";
import BehaviorTree from './BehaviorTree.vue';

const store = useStore();

const props = defineProps({
  sceneInfo: {
    default: null,
    type: Object
  },
  operateIndex: {
    default: 0,
    type: Number
  },
  spatialData: {
    default: null,
    type: Object
  },
  spatialData3D: {
    default: null,
    type: Object
  },
  changeRouterStyle: {
    default: null,
    type: Function
  },
  changeSceneValue: {
    default: null,
    type: Function
  },
  currentActiveSource: {
    default: '',
    type: String
  },
  changeRightValue: {
    default: null,
    type: Function
  },
  exitEdit: {
    default: null,
    type: Function
  }
})

const sceneInfoList: any = ref([]) // TODO 真实接数据时候需要加上key
const hide_side = ref(false)
const hide_route_style = ref(false)
const changeRatio = reactive({
  position: {
    name: '位置',
    x: 0,
    y: 0,
    z: 0
  },
  rotate: {
    name: '旋转',
    x: 0,
    y: 0,
    z: 0
  },
  scale: {
    name: '缩放',
    x: 1,
    y: 1,
    z: 1
  }
})

const routerStyleList = [
  {
    title: '请选择路线样式',
    value: [
      {
        name: '样式一',
        url: require('@/assets/images/icon/style1.png')
      },
      {
        name: '样式二',
        url: require('@/assets/images/icon/style2.png')
      },
      {
        name: '样式三',
        url: require('@/assets/images/icon/style3.png')
      }
    ]
  }
]

const activeStyle: any = ref({ 0: -1, 1: -1 })

const exitEditPage = () => {
  props.exitEdit()
}

const checkNumber = (value: string) => {
  const newValue = value.trim();
  if (newValue == '') return ''
  if (((newValue[0] == '0' && newValue[1] !== '.') || newValue[0] == '.') && newValue.length > 1) {
    return newValue.slice(1)
  }
  if (!Number.isNaN(Number(newValue.slice(-1)[0])) || (value[0] == '-' && value.length == 1) || newValue.slice(-1)[0] == '.') {
    return value
  } else {
    return newValue.slice(0, -1)
  }
}

const changeStyle = (index: number, i: number) => {
  activeStyle.value[index] = i
  props.changeRouterStyle(activeStyle.value)
}

const changeValue = (val: any, key: any) => {
  if (key) {
    props.changeSceneValue(val, key)
  }
}

const changeXYZ = (value: any, key: string, type: string) => {
  if (Number(value) || value == 0) {
    props.changeRightValue(Number(value), key, type)
  }
}

onMounted(() => {
  sceneInfoList.value = [
    {
      name: '项目编号',
      value: props.sceneInfo?.id,
      disabled: true
    },
    {
      name: '项目名称',
      value: props.sceneInfo?.sceneName,
      key: 'sceneName'
    },
    {
      name: '所属场馆',
      value: props.sceneInfo?.stadiumName,
      key: 'stadiumName'
    },
    {
      name: '空间编码',
      value: props.sceneInfo?.spaceDto?.id,
      disabled: true
    },
    {
      name: '空间名称',
      value: props.sceneInfo?.spaceDto?.descriptionName,
      disabled: true
    }
  ]
})

watch(props.spatialData, (newState) => {
  // 设置互动区域右侧数据切换
  if (newState.position) {
    changeRatio.position.x = +(newState.position.x.toFixed(1));
    changeRatio.position.y = +(newState.position.y.toFixed(1));
    changeRatio.position.z = +(newState.position.z.toFixed(1));
  } else {
    changeRatio.position.x = 0
    changeRatio.position.y = 0
    changeRatio.position.z = 0
  }
  if (newState.scale) {
    changeRatio.scale.x = +(newState.scale?.x.toFixed(1));
    changeRatio.scale.z = +(newState.scale?.y.toFixed(1));
  } else {
    changeRatio.scale.x = 1
    changeRatio.scale.z = 1
  }
  if (newState.rotate) {
    changeRatio.rotate.y = +(radiansToAngle(newState.rotate).toFixed(1))
  } else {
    changeRatio.rotate.y = 0
  }

  // 设置路径初始样式
  if (newState.routeStyle) {
    activeStyle.value['0'] = newState.routeStyle;
  } else {
    activeStyle.value['0'] = -1
  }

  if (newState.guideStyle) {
    activeStyle.value['1'] = newState.guideStyle;
  } else {
    activeStyle.value['1'] = -1
  }

  hide_route_style.value = newState.visible == 0
})

watch(props.spatialData3D, (newState) => {
  // 设置互动区域右侧数据切换
  if (newState.location) {
    changeRatio.position.x = +(newState.location.x.toFixed(1));
    changeRatio.position.y = +((newState.location.y).toFixed(1));
    changeRatio.position.z = +(newState.location.z.toFixed(1));
  } else {
    changeRatio.position.x = 0
    changeRatio.position.y = 0
    changeRatio.position.z = 0
  }
  if (newState.scale) {
    changeRatio.scale.x = +(newState.scale?.x.toFixed(1));
    changeRatio.scale.y = +(newState.scale?.y.toFixed(1));
    changeRatio.scale.z = +(newState.scale?.z.toFixed(1));
  } else {
    changeRatio.scale.x = 1
    changeRatio.scale.y = 1
    changeRatio.scale.z = 1
  }
  if (newState.rotation) {
    changeRatio.rotate.x = +(radiansToAngle(newState.rotation.x).toFixed(1))
    changeRatio.rotate.y = +(radiansToAngle(newState.rotation.y).toFixed(1))
    changeRatio.rotate.z = +(radiansToAngle(newState.rotation.z).toFixed(1))
  } else {
    changeRatio.rotate.x = 0
    changeRatio.rotate.y = 0
    changeRatio.rotate.z = 0
  }
})

watch(hide_side, (newState) => {
  if (!document.querySelector('.zoom-operation')) return
  if (newState) {
    (document.querySelector('.zoom-operation') as any).style.right = '30px';
  } else {
    (document.querySelector('.zoom-operation') as any).style.right = '347px';
  }
})

</script>
<style scoped lang="less">
#app .interactive-data .info-list {
  ::v-deep(.el-input__wrapper) {
    padding-left: 12px;
    padding-right: 18px;
    width: 66px;
    height: 34px;
    line-height: 34px;
  }
}

#app .scene-info .scene-info-list {
  ::v-deep(.el-input__wrapper) {
    height: 34px;
  }

  ::v-deep(.el-input__wrapper .el-input__inner) {
    font-size: 14px;
  }
}

.temp {
  height: 0 !important;
}

.interactive-data {
  padding: 20px 16px;
  box-sizing: border-box;

  .flex-double {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .img {
      width: 30px;
      height: 30px;
      transform: translateY(-5px);
      cursor: pointer;
    }
  }
}


.info-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  font-weight: 400;
  color: #6F6F6F;
  margin-bottom: 20px;

  .el-input {
    width: 60px;
    height: 34px;
    vertical-align: middle;
    margin-right: 5px;
    display: inline-block;
  }

  span {
    vertical-align: middle;
  }
}

.axle {
  font-size: 15px;
  font-weight: 400;
  color: #0375FF;
  margin-right: 3px;
  margin-left: 3px;
}

.is-position {
  position: relative;

  &::after {
    content: 'm';
    position: absolute;
    right: 2px;
    top: 0;
    color: rgba(113, 113, 120, 0.5);
  }
}

.is-rotate {
  position: relative;

  &::after {
    content: '°';
    position: absolute;
    right: 2px;
    top: 0;
    color: rgba(113, 113, 120, 0.5);
  }
}

.style-title {
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  text-align: left;
  margin-bottom: 17px;
}

.style-img {
  padding: 0 27px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.style-name {
  padding: 0 27px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  color: #000000;

  &>div {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      margin-right: 3px;
      cursor: pointer;
    }
  }

  &.no-router {
    display: inline-block;
  }
}

.info-title {
  font-size: 12px;
  color: #000000;
  text-align: left;
  margin-bottom: 15px;
  height: 24px;
  line-height: 24px;
  position: relative;

  &>span {
    vertical-align: middle;
  }

  &>img {
    vertical-align: middle;
    margin-right: 6px;
  }
}

.canvas-right {
  position: fixed;
  right: 0;
  top: 59px;
  width: 317px;
  height: calc(100% - 59px);
  background: url(~@/assets/images/background/side-bg.png);
  background-size: 100% 100%;
  z-index: 10;
  padding-top: 69px;
  box-sizing: border-box;
  overflow-y: scroll;

  ::v-deep(.el-input__inner) {
    color: #0F0F0F !important;
  }

  ::v-deep(.el-textarea__inner) {
    color: #0F0F0F !important;
  }
}

.canvas-right.hide-right-side {
  right: -320px;
}

.hide-side,
.show-side {
  position: absolute;
  top: 50%;
  margin-top: -65px;
  right: 317px;
  width: 16px;
  height: 42px;
  transform: rotate(180deg);
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.show-side {
  right: 0;
}

.scene-info {
  width: 315px;
  // height: calc(100% - 226px);
  padding: 20px 16px;
  box-sizing: border-box;
  border-top: 1px solid #C8C8C8;
  overflow: hidden;
  overflow-y: auto;

  .scene-info-list {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    font-size: 14px;
    font-weight: 400;
    color: #0F0F0F;
    line-height: 14px;
    margin-bottom: 16px;

    &>div:first-child {
      font-size: 14px;
      color: #6F6F6F;
      width: 60px;
      margin-top: 9px;
    }

    &>div:last-child {
      width: 208px;
      min-height: 32px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      line-height: 20px;
      // padding: 6px 10px;
      box-sizing: border-box;
      text-align: left;
    }

    .scene-info-dec {
      height: 79px;
      position: relative;

      &::after {
        content: '30\5b57\4ee5\5185';
        position: absolute;
        right: 8px;
        bottom: 5px;
        font-size: 9px;
        transform: scale(0.75);
        transform-origin: 100% 100%;
        font-weight: 400;
        color: #6F6F6F;
      }
    }
  }

  .scene-info-line {
    border-bottom: 1px solid #C8C8C8;
    width: calc(100% + 24px);
    margin: 17px 0;
    margin-left: -12px;
  }

  .space-model {
    width: 212px;
    height: 185px;
    background-size: 100% 100%;
    margin: 30px 0 20px 32px;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.el-size2 {
  position: relative;
  width: 264px;
  height: 41px;
  margin-left: 12px;
  position: absolute;
  top: 24px;

  .icon {
    position: absolute;
    left: 85px;
    top: 12px;
    width: 16px;
    height: 16px;
    background-image: url(~@/assets/images/icon/logout_scene.png);
    background-size: 100% 100%;
    cursor: pointer;
  }
}

.el-size2::v-deep(.el-button>span) {
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  letter-spacing: 1px;
  margin-left: 26px;
}

/* 媒体查询，适配ipad端和小电脑页面 */
@media screen and (max-width: 1200px) {
  .interactive-data {
    padding-right: 5px;

    .info-list .el-input {
      width: 50px;
    }
  }

  .axle {
    font-size: 14px;
  }

  .canvas-right {
    width: 267px;
  }

  .canvas-right.hide-right-side {
    right: -270px;
  }

  .hide-side {
    right: 267px;
  }

  .scene-info {
    padding-right: 10px;
    width: 265px;

    .scene-info-list>div:first-child {
      font-size: 13px;
    }

    .scene-info-list>div:last-child {
      width: 168px;
    }
  }

  .el-size2 {
    width: 211px;
    height: 36px;
  }
}
</style>