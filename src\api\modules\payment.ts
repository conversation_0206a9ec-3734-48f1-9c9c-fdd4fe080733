import request from '../request';

// 获取支付二维码
// @param payType: 支付类型 'WXPay' | 'AliPay'
// @param amount: 支付金额，单位：分（例如：100 = 1元）
export function getPaymentQrCode(params: { payType: string; amount: number }) {
  return request({
    url: `/payment/pc/getQrCode?payType=${params.payType}&amount=${params.amount}`,
    method: 'get',
  });
}

// 查询支付状态
export function getPaymentStatus(params: { orderId: string }) {
  return request({
    url: `/payment/getPaymentStatus`,
    method: 'get',
    params,
  });
}

// 根据订单号查询支付状态
export function getOrderStatusByOrderNo(params: { orderNo: string }) {
  return request({
    url: `/payment/pc/getOrderStatusByOrderNo`,
    method: 'get',
    params,
  });
}

// 获取订单能量列表
export function getOrderEnergyList(params: { pageNo: number; pageSize: number }) {
  return request({
    url: `/payment/energyOrder/getOrderEnergyList`,
    method: 'get',
    params,
  });
}

// 获取发票列表
export function getInvoiceList(params: { pageNo?: number; pageSize?: number }) {
  return request({
    url: `/payment/bill/getBillOrderAndBillAmount`,
    method: 'get',
    params,
  });
}

// 获取未开发票列表
export function getNotOpenBill() {
  return request({
    url: `/payment/energyOrder/getNotOpenBill`,
    method: 'get',
  });
}

// 获取专票
export function getUserMajor() {
  return request({
    url: `/payment/billUserFirmMajor/getUserMajor`,
    method: 'get',
  });
}

// 申请开票
export function applyInvoice(params: { orderId: string; invoiceInfo: any }) {
  return request({
    url: `/payment/invoice/apply`,
    method: 'post',
    data: params,
  });
}

// 下载发票
export function downloadInvoice(params: { invoiceId: string }) {
  return request({
    url: `/payment/invoice/download`,
    method: 'get',
    params,
    responseType: 'blob',
  });
}

// 申请发票
export function applyBill(data: {
  id?: number;
  billNo?: string;
  userId?: number;
  billTitle: string;
  taxpayerNum: string;
  billContent: string;
  registeredAddress?: string;
  registeredPhone?: string;
  billBank?: string;
  bankNum?: string;
  billPersonFirm: number; // 1:个人 2:企业
  billType: number; // 1:电子专票 2:电子普票
  billOrderStatus?: number;
  rejectRemark?: string;
  remark?: string;
  billPdfUrl?: string;
  createTime?: string;
  updateTime?: string;
  orderEnergyIds: number[];
  operationType: number;
}) {
  return request({
    url: `/payment/bill/applyBill`,
    method: 'post',
    data,
  });
}
