<template>
  <div style="height: 100%">
    <div>
      <search-form
        :form-keys-data="keysData"
        :search-data-event="searchData"
        :exchange-positions="true"
        currentRoute="/path_navigation"
        :upload-buttons="spaceUploadButtons"></search-form>
    </div>
    <div>
      <table-list
        ref="tableRefs"
        :data="tableData"
        :column-list="columnList"
        :change-page="changePage"
        :delete-dataEvent="deleteDataEvent"
        :data-total="pageTotal"
        :page-size="searchForm.pageSize"
        :page-no="searchForm.pageNo"
        create-list="创建路径"
        :handle-create="handleCreate"
        :delete-content="deleteContent"
        :empty-image="emptyImage"
        :updata-data="getData"
        :exchange-positions="true"
        :handle-filter="handleFilter"
        :operation-items="operationItems"></table-list>
    </div>
  </div>

  <create-path-navigation v-if="modalShow" :handle-hide="handleHide"></create-path-navigation>
  <graph-preview
    titleName="路径效果预览"
    v-if="showPreview"
    :spaceInfo="spaceInfo"
    :hideModel="hidePreview"
    :resData="[...graphInfo]"></graph-preview>
  <remove-graph
    v-if="deleteShow"
    :handle-hide="handleHide2"
    :default-graph-data="defaultGraphData"></remove-graph>
</template>
<script lang="ts" setup>
import { ref, watch, reactive, onMounted } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import CreatePathNavigation from '@/views/path-navigation/components/CreatePathNavigation.vue';
import {
  getNjyjGraphPage,
  deleteNjyjGraph,
  getSpaceById,
  getNjyjNodes,
  getSpacePage,
  getUserStorage,
} from '@/api/index';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import GraphPreview from '@/components/preview/GraphPreview.vue';
import RemoveGraph from '@/views/path-navigation/components/RemoveGraph.vue';
import type { OperationItems, TableRow } from '@/types/operation';
import { formatDateOnly } from '@/utils';
const pageTotal = ref(0); // 总计数据条数
const tableData: any = ref([]); // 表格数据
const modalShow = ref(false); // 显示新建
const defaultValue = ref({}); // 进入编辑页的初始值
const deleteContent = ref({
  // 删除tips
  title: '删除当前路径',
  content: '确认删除当前路径导航？',
});
const searchForm: any = reactive({
  // 查询对象
  pageSize: 10,
  pageNo: 1,
});
const tableRefs = ref();
const keysData = reactive([
  {
    key: 'graphName',
    type: 'input',
    label: '搜索路径',
  },
]);

const spaceUploadButtons = [
  {
    icon: require('@/assets/images/anchorround.png'),
    prefix: '创建',
    label: '路径导航',
    action: 'create',
    type: 'navigation',
  },
];
const emptyImage = require('@/assets/images/nolistimg.png');
const router = useRouter();
const spaceInfo = ref({});
const showPreview = ref(false);
const graphInfo = ref([]);
const deleteShow = ref(false); // 显示新建
const defaultGraphData = ref({});

const store = useStore();

// 获取用户存储容量
const getCurrentUserStorage = () => {
  getUserStorage().then((res: any) => {
    if (res.code === 200) {
      const storageData = JSON.parse(JSON.stringify(store.state.storageData));
      storageData.material = { ...res.data };
      store.state.storageData = { ...storageData };
    }
  });
};

const editData = (data: any) => {
  defaultValue.value = { ...data };
  router.push('/path_navigation_edit?graphId=' + data.id);
};

const previewEvent = (data: any) => {
  getNjyjNodes({ graphId: data.id }).then((res: any) => {
    if (res.code == 200) {
      graphInfo.value = res.data;
      getSpaceById({ spaceId: data.spaceId }).then((res2: any) => {
        spaceInfo.value = { ...data, ...res2.data };
        showPreview.value = true;
      });
    }
  });
};

const deleteDataEvent = (data: any) => {
  deleteShow.value = true;
  defaultGraphData.value = data;
};

// Operation items configuration
const operationItems = reactive<OperationItems>([
  {
    label: '编辑',
    key: 'edit',
    onClick: (row: TableRow) => {
      editData(row);
    },
  },
  {
    label: '删除',
    key: 'delete',
    onClick: (row: TableRow) => {
      deleteDataEvent(row);
    },
  },
  {
    label: '预览',
    key: 'preview',
    onClick: (row: TableRow) => {
      previewEvent(row);
    },
  },
]);

const columnList = ref([
  {
    prop: 'graphName',
    label: '路径名称',
  },
  {
    prop: 'spaceName',
    label: '空间名称',
    resetKey: 'spaceId',
    // filters: []
  },
  {
    prop: 'graphPicPath',
    type: 'image',
    label: '路径样式',
    imageWidth: 150,
    imageHeight: 90,
  },
  {
    prop: 'graphInfo',
    label: '描述信息',
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    formatter: (value: string) => formatDateOnly(value),
  },
  {
    prop: 'operate',
    label: '操作',
    width: 120,
    type: 'operation',
  },
]);

const hidePreview = () => {
  showPreview.value = false;
};

const handleCreate = () => {
  modalShow.value = true;
};

const handleHide = (renew?: boolean) => {
  modalShow.value = false;
  defaultValue.value = {};

  if (renew) {
    // 判断是否需要重新渲染
    getData();
  }
};

const handleFilter = (key: string, value: any) => {
  searchForm[key] = value ? value[0] : null;
  searchForm.pageNo = 1;
  getData();
  tableRefs.value.selectDataArr = null;
};

const handleHide2 = (renew?: boolean) => {
  deleteShow.value = false;

  if (renew) {
    // 判断是否需要重新渲染
    if (
      searchForm.pageNo - 1 == (pageTotal.value - 1) / searchForm.pageSize &&
      searchForm.pageNo > 1
    ) {
      searchForm.pageNo -= 1;
    }
    getData();
  }
};

const searchData = (data: any) => {
  // 处理按钮点击事件
  if (data.action === 'create' && data.type === 'navigation') {
    handleCreate();
    return;
  }

  // 处理搜索表单数据
  for (const key in data) {
    searchForm[key] = data[key];
  }
  getData();
};

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getData();
};

const getData = () => {
  getNjyjGraphPage({ ...searchForm }).then((res: any) => {
    pageTotal.value = res.data.total;
    tableData.value = [...res.data.records];
  });
};

onMounted(async () => {
  getData();
  getCurrentUserStorage();
  // getSpacePage({ pageNo: 1, pageSize: 999, status: 2 }).then((res: any) => {
  //   columnList.value[1].filters = res.data.records.map((e: any) => ({ text: e.descriptionName, value: e.id }))
  // })
});
</script>
