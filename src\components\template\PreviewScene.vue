<template>
  <div class="preview-scene-box">
    <div>
      <div class="title">
        <div>{{ titleName }}</div>
        <div class="icon iconfont icon-close" @click="closeEvent"></div>
      </div>
      <div class="preview-canvas">
        <canvas-preview ref="canvasRef"></canvas-preview>
      </div>
      <div class="canvas-tips">可滑动鼠标查看空间信息及样式</div>
    </div>
  </div>
</template>

<script>
import CanvasPreview from '@/components/CanvasPreview.vue'
import { Vector3 } from 'three'
import { loadZipFileForJSZip, base64ToBlob } from "@/utils";
import { getOssAccessPath } from '@/api/modules/storage'
import { updateSpaceName } from '@/api/modules/space'
import axios from "axios";

export default {
  name: 'PreviewSceneModel',
  components: {
    CanvasPreview
  },
  props: {
    spaceInfo: {
      default: () => ({}),
      type: Object
    },
    hideModel: {
      default: null,
      type: Function
    },
    titleName: {
      default: '空间预览',
      type: String
    }
  },
  data() {
    return {
      baseURL: process.env.NODE_ENV === 'production' ? 'https://vps.njyjxr.com:18443/vps' : 'http://*************/vps',
      token: window.localStorage.getItem('token'),
      headers: {
        'Content-Type': 'text/plain',
        token: window.localStorage.getItem('token')
      }
    }
  },
  methods: {
    closeEvent() {
      this.hideModel()
    }
  },
  async mounted() {
    console.log('地图地图', this.spaceInfo)
    // 加载地图
    try {
      const res = await getOssAccessPath({ key: this.spaceInfo.roomStructureKey })
      const camera = this.$refs.canvasRef.getCamera();
      camera.position.set(0, 30, 0);
      camera.lookAt(new Vector3(0, 0, 0));
      
      loadZipFileForJSZip(res.data, async (glb) => {
        if (glb.scene) {
          this.$refs.canvasRef.addMesh(glb.scene)
        } else {
          this.$refs.canvasRef.addMesh(glb)
        }
        
        if (this.spaceInfo.spacePic == '/images/space-default.png') {
          const renderer = this.$refs.canvasRef.getRenderer();
          const camera = this.$refs.canvasRef.getCamera();
          const scene = this.$refs.canvasRef.getScene();
          renderer.render(scene, camera)
          let imgData = renderer.domElement.toDataURL("image/png")
          const formData = base64ToBlob(imgData, 'spaceCover');
          const { data } = await axios.post(`${this.baseURL}/space/uploadSpaceCover?fileName=${new Date().getTime()}&spaceId=${this.spaceInfo.id}`, formData, {
            headers: this.headers
          })
          updateSpaceName({ spaceId: this.spaceInfo.id, spacePic: data.data, spaceName: this.spaceInfo.descriptionName })
        }
      })
    } catch (error) {
      console.error('加载预览失败:', error);
    }
  }
}
</script>

<style scoped lang="less">
.preview-scene-box {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  &>div {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 16px 24px;
    box-sizing: border-box;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 18px;
      color: #1E1E1E;
      margin-bottom: 16px;

      .icon-close {
        font-size: 24px;
        cursor: pointer;
        font-weight: 400;
        color: #797979;

        &:hover {
          color: #2E76FF;
        }
      }
    }

    .preview-canvas {
      width: 1210px;
      height: 680px;
      background: #D9D9D9;
      border-radius: 10px;
    }

    .canvas-tips {
      font-weight: 400;
      font-size: 12px;
      color: #797979;
      height: 32px;
      line-height: 32px;
    }
  }
}
</style> 