<template>
  <div class="tips-model" v-if="isOpenTip">
    <div class="tips-content">
      <div class="title">{{ tipsTitle }}</div>
      <div>{{ store.state.showTipsV2 }}</div>
      <slot></slot>
      <div v-if="!showBtn" class="btn" @click="handleCancel">知道了</div>
      <div v-if="showBtn" class="submit-btns">
        <div @click="handleCancle">取消</div>
        <div @click="handleSure">{{ showBtn }}</div>
      </div>
      <div class="close" @click="handleCancle"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue';
import { useStore } from 'vuex';
const headBack = require('../assets/images/head_back.png')
const headGou = require('../assets/images/head_gou.png')
const props = defineProps({
  isOpenTip: {
    default: false,
    type: Boolean
  },
  tipsTitle: {
    default: '提示',
    type: String
  },
  showBtn: {
    default: '',
    type: String
  },
  sureEvent: {
    default: null,
    type: Function
  },
  cancleEvent: {
    default: null,
    type: Function
  },
  showV2: {
    default: false,
    type: Boolean
  }
})

const store = useStore();

const handleCancle = () => {
  props.cancleEvent && props.cancleEvent()
  store.state.showTipsV2 = ''
}

const handleSure = () => {
  props.sureEvent && props.sureEvent()
}

const handleCancel = () => {
  if (store.state.showTips) {
    store.state.showTipsV2 = ''
  }
}

</script>

<style scoped lang="less">
.tips-model {
  position: fixed !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5) !important;

  .tips-content {
    position: relative;
    width: 524px;
    min-height: 182px;
    background: #FFFFFF;
    border-radius: 6px;
    border: 1px solid #E1E1E1;
    padding: 24px 24px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
    color: #797979;
    z-index: 9999;
    padding-bottom: 64px;

    .headImage {
      width: 160px;
      height: 160px;
    }

    .title {
      font-weight: bold;
      font-size: 18px;
      color: #1E1E1E;
      margin-bottom: 15px;
    }

    .login_title {
      font-size: 20px;
      text-align: center;
      margin-bottom: 24px;
    }

    .login_tip {
      font-weight: 400;
      font-size: 14px;
      color: #797979;
    }

    .btn {
      width: 112px;
      height: 36px;
      background: linear-gradient(225deg, #0375FF 0%, #3C96FF 100%);
      box-shadow: inset -1px -1px 0px 0px rgba(255, 255, 255, 0.2), inset 1px 1px 0px 0px rgba(11, 91, 225, 0.4);
      border-radius: 4px;
      line-height: 36px;
      text-align: center;
      font-weight: 600;
      font-size: 14px;
      color: #FFFFFF;
      position: absolute;
      right: 24px;
      bottom: 24px;
      cursor: pointer;
      letter-spacing: 1px;
    }

    .login_btn {
      position: relative;
      right: 0;
      bottom: 0;
      margin-top: 24px;
      background: #D6DFF1;
      border-radius: 4px 4px 4px 4px;
      font-weight: bold;
      font-size: 14px;
      color: #2E76FF;
      box-shadow: none;
      &:hover {
        background: rgba(46, 118, 255, 1);
        color: rgba(255, 255, 255, 1);
      }
    }

    .close {
      width: 43px;
      height: 43px;
      background: url(~@/assets/images/close-tips.png);
      background-size: 100% 100%;
      position: absolute;
      right: 17px;
      top: 11px;
      cursor: pointer;
    }

    .submit-btns {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      position: absolute;
      right: 24px;
      bottom: 30px;

      &>div {
        width: 92px;
        height: 32px;
        border-radius: 4px;
        line-height: 30px;
        text-align: center;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        border: 1px solid #DADADA;
        margin-left: 12px;
        box-sizing: border-box;
        color: #797979;
      }

      &>div:last-child {
        background: #2E76FF;
        border-color: #2E76FF;
        color: #FFFFFF;
      }
    }
  }

  .login_tip_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    min-height: 227px;
    max-height: 227px;
    padding-bottom: 32px;
  }
}
</style>