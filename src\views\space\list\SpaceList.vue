<template>
  <div class="space-box">
    <div>
      <search-form
        :form-keys-data="keysData"
        :search-data-event="searchData"
        currentRoute="/spacelist"
        :upload-buttons="spaceUploadButtons"></search-form>
    </div>
    <div>
      <table-list
        ref="tableRefs"
        :data="tableData"
        :column-list="columnList"
        :operation-items="operationItems"
        :change-page="changePage"
        :data-total="pageTotal"
        :page-size="searchForm.pageSize"
        :page-no="searchForm.pageNo"
        create-list="新建空间"
        :handle-create="handleCreate"
        :handle-filter="handleFilter"
        :empty-image="emptyImage"
        :updata-data="getData"></table-list>
    </div>
    <create-space v-if="modalShow" :handle-hide="handleHide"></create-space>
    <update-space
      v-if="showEditModal"
      :handle-hide="handleHide3"
      :default-value="defaultValue"></update-space>
    <remove-space
      v-if="deleteShow"
      :handle-hide="handleHide2"
      :default-space-data="defaultSpaceData"></remove-space>
    <preview-scene-model
      v-if="previewShow"
      :space-info="previewSpaceInfo"
      :hide-model="hidePreview"></preview-scene-model>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive, onMounted, computed } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import CreateSpace from '@/views/space/components/CreateSpace.vue';
import UpdateSpace from '@/views/space/components/UpdateSpace.vue';
import RemoveSpace from '@/views/space/components/RemoveSpace.vue';
import PreviewSceneModel from '../../../components/template/PreviewScene.vue';
import { getSpacePage, getSpaceStorage, getSceneMetaPageForWeb } from '@/api/index';
import { useStore } from 'vuex';
import type { OperationItems } from '@/types/operation';
import { formatDateOnly } from '@/utils';
const pageTotal = ref(0); // 总计数据条数
const tableData: any = ref([]); // 表格数据
const modalShow = ref(false); // 显示新建
const showEditModal = ref(false);
const deleteShow = ref(false); // 显示新建
const defaultValue = ref({}); // 进入编辑页的初始值
const defaultSpaceData = ref({});
const previewShow = ref(false); // 显示预览
const previewSpaceInfo = ref({}); // 预览空间信息
const searchForm: any = reactive({
  // 查询对象
  pageSize: 10,
  pageNo: 1,
});
const tableRefs = ref();
const keysData = reactive([
  {
    key: 'descriptionName',
    type: 'input',
    label: '空间名称',
  },
]);
const emptyImage = require('@/assets/images/nolistimg.png');

// 空间管理页面的上传按钮配置
const spaceUploadButtons = [
  {
    icon: require('@/assets/images/upload222.png'),
    prefix: '上传',
    label: '空间数据',
    action: 'upload',
    type: 'space',
  },
];

const store = useStore();

const editData = (data: any) => {
  defaultValue.value = { ...data };
  showEditModal.value = true;
};

const previewEvent = (data: any) => {
  console.log('预览空间数据:', data);
  previewSpaceInfo.value = data;
  previewShow.value = true;
};

const hidePreview = () => {
  previewShow.value = false;
  previewSpaceInfo.value = {};
};

const operationItems = computed<OperationItems>(() => [
  {
    label: '预览',
    key: 'preview',
    show: () => true,
    onClick: previewEvent,
  },
  {
    label: '修改名称',
    key: 'edit',
    show: () => true,
    onClick: editData,
  },
]);

const columnList = ref([
  {
    prop: 'spacePic',
    label: '空间照片',
    type: 'image',
    imageWidth: 120, // 自定义图片宽度
    imageHeight: 80, // 自定义图片高度
  },
  {
    prop: 'descriptionName',
    label: '空间名称',
  },
  {
    prop: 'photoNum',
    label: '图片数量',
  },
  {
    prop: 'sceneName',
    label: '所属项目',
    spaceType: 'usedSpaceSceneNames',
  },
  {
    prop: 'pointCloudDataFetchTimeStr',
    label: '上传时间',
    formatter: (value: string) => formatDateOnly(value),
  },
  {
    prop: 'pointCloudDataStatus',
    label: '当前状态',
    resetKey: 'status',
    list: {
      0: '未处理',
      1: '处理中',
      2: '已处理',
    },
    filters: [
      { text: '未处理', value: '0' },
      { text: '处理中', value: '1' },
      { text: '已处理', value: '2' },
    ],
  },
  {
    prop: 'operate',
    label: '操作',
    width: 110,
  },
]);

const handleFilter = (key: string, value: any) => {
  searchForm[key] = value ? value[0] : null;
  searchForm.pageNo = 1;
  getData();
  tableRefs.value.selectDataArr = null;
};

const handleCreate = () => {
  modalShow.value = true;
};

const handleHide = (renew?: boolean) => {
  modalShow.value = false;
  defaultValue.value = {};

  if (renew) {
    // 判断是否需要重新渲染
    getData();
  }
};

const handleHide2 = (renew?: boolean) => {
  deleteShow.value = false;

  if (renew) {
    // 判断是否需要重新渲染
    getData();
  }
};

const handleHide3 = (renew?: boolean) => {
  showEditModal.value = false;

  if (renew) {
    // 判断是否需要重新渲染
    getData();
  }
};

const searchData = (data: any, type?: string) => {
  // 处理按钮点击事件
  if (data && typeof data === 'object' && data.action) {
    if (data.action === 'upload') {
      // 处理上传按钮点击
      console.log('上传空间数据按钮点击:', data);
      // 可以跳转到空间数据上传页面或打开上传弹窗
      handleCreate(); // 直接调用新建空间函数
      return;
    }
  }

  // 处理搜索表单数据
  for (const key in data) {
    if (key !== 'action') {
      searchForm[key] = data[key];
    }
  }
  if (type == 'reset') {
    searchForm.status = '';
    tableRefs.value.clearFilterEvent();
  }
  getData();
  tableRefs.value.selectDataArr = null;
};

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getData();
};

const getData = () => {
  getSpacePage({ ...searchForm }).then((res: any) => {
    pageTotal.value = res.data.total;
    tableData.value = [...res.data.records].map((e) => {
      if (!e.spacePic) {
        e.spacePic = '/images/space-default.png';
      }
      return e;
    });
  });
  getSpaceStorage().then((res) => {
    const storageData = JSON.parse(JSON.stringify(store.state.storageData));
    storageData.space = { ...res.data };
    store.state.storageData = { ...storageData };
  });
};

onMounted(async () => {
  getData();
});
</script>
<style scoped lang="less">
.space-box {
  height: 100%;
}
</style>
