<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div>{{ defaultValue.id ? '修改名称' : '添加设备' }}</div>
      </div>
      <div class="modal-form">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm">
          <el-form-item label="设备名称" prop="equipName" style="margin-bottom: 18px">
            <el-input
              class="form-input"
              v-model="ruleForm.equipName"
              placeholder="请输入设备名称" />
          </el-form-item>
          <div :style="{ opacity: !spaceNameError ? 1 : 0 }" class="tips-text">
            仅支持2~18位中文、英文、数字或字符组合
          </div>
          <!-- <el-form-item label="设备序列号" prop="snCode">
            <el-input class="form-input" :disabled="!!props.defaultValue.id" v-model="ruleForm.snCode"
              placeholder="请输入设备序列号" />
          </el-form-item> -->
          <el-form-item class="form-submit">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef)">确认</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { addEquipment, updateEquipment } from '@/api';
import { useStore } from 'vuex';
import { desensitizte } from '@/utils';

const props = defineProps({
  handleHide: {
    default: null,
    type: Function,
  },
  defaultValue: {
    default: null,
    type: Object,
  },
});

interface RuleForm {
  equipName: string;
  snCode: string;
}

const ruleFormRef = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  equipName: '',
  snCode: '',
});
const spaceNameError = ref(false);

const store = useStore();

const validatePass = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入设备名称'));
    spaceNameError.value = true;
    return;
  }
  if (value.trim() === '') {
    callback(new Error('设备名称不能为空'));
    spaceNameError.value = true;
    return;
  }
  if (value.length < 2 || value.length > 18) {
    callback(new Error('仅支持2~18位中文、英文、数字或字符组合'));
    spaceNameError.value = true;
    return;
  }
  spaceNameError.value = false;
  callback();
};

const rules = reactive<FormRules<RuleForm>>({
  equipName: [{ required: true, validator: validatePass, trigger: 'blur' }],
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      ruleForm.equipName = ruleForm.equipName.trim();
      ruleForm.snCode = ruleForm.snCode.trim();
      const hasSensitiveWords = await desensitizte(
        ruleForm.equipName,
        '设备名称不可包含敏感词汇！'
      );
      if (hasSensitiveWords) return;
      if (props.defaultValue.id) {
        updateEquipment({ ...ruleForm })
          .then((res: any) => {
            if (res.code === 200 || res.code === '200') {
              // 显示设备更新完成提示
              ElMessage({
                message: '设备更新完成',
                type: 'success',
              });
              props.handleHide(true);
            } else {
              ElMessage({
                message: res.message || '设备更新失败',
                type: 'error',
              });
            }
          })
          .catch((error: any) => {
            console.error('设备更新失败:', error);
            ElMessage({
              message: '设备更新失败，请重试',
              type: 'error',
            });
          });
      } else {
        addEquipment({ ...ruleForm })
          .then((res: any) => {
            if (res.code === 200 || res.code === '200') {
              // 显示设备添加完成提示
              ElMessage({
                message: '设备添加完成',
                type: 'success',
              });
              props.handleHide(true);
            } else {
              ElMessage({
                message: res.message || '设备添加失败',
                type: 'error',
              });
            }
          })
          .catch((error: any) => {
            console.error('设备添加失败:', error);
            ElMessage({
              message: '设备添加失败，请重试',
              type: 'error',
            });
          });
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

const changeState = () => {
  props.handleHide();
};

onMounted(() => {
  // 编辑页初始数据
  if (props.defaultValue.id) {
    ruleForm.equipName = props.defaultValue.equipmentName || '';
    ruleForm.snCode = props.defaultValue.snCode || '';
  }
});
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;

  .modal-content {
    width: 470px;
    height: 199px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -235px;
    margin-top: -116px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;

    .modal-content-title {
      padding: 16px 0 0 24px;
      font-weight: bold;
      font-size: 20px;
      color: #1e1e1e;
      text-align: left;
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      padding: 0 30px 0 0;
      box-sizing: border-box;

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-submit {
        margin-top: 92px;
      }

      .tips-text {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        margin-top: -19px;
        text-align: left;
        padding-left: 100px;
      }
    }
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;

  & > button {
    box-shadow: none;
    border-radius: 4px;
  }
}

.btn-primary .el-size3 > button {
  background: #2e76ff;
}
</style>
