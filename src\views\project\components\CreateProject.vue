<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div>{{ defaultGroupData ? '修改项目组' : '新建项目组' }}</div>
      </div>
      <div class="modal-form">
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="72px" class="demo-ruleForm">
          <el-form-item label="名称" prop="groupName" style="margin-bottom: 18px;">
            <el-input class="form-input" v-model="ruleForm.groupName" placeholder="请输入项目组名称" />
          </el-form-item>
          <el-form-item class="form-submit">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef)">确认</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { addSceneGroup, updateSceneGroupName } from '@/api'
import { useStore } from 'vuex'
import { desensitizte } from '@/utils'

const props = defineProps({
  handleHide: {
    default: null,
    type: Function
  },
  userInfo: {
    default: null,
    type: Object
  },
  defaultGroupData: {
    default: null,
    type: Object
  }
})

interface RuleForm {
  groupName: string
}

const ruleFormRef = ref<FormInstance>()
const ruleForm: any = reactive<RuleForm>({
  groupName: '',
})

const store = useStore()

const validatePass = (rule: any, value: any, callback: any) => {
  if (!value || value.length < 1 || value.length > 20) {
    callback(new Error('支持1~20个字符的项目组名称'))
    return
  }
  callback()
}

const rules = reactive<FormRules<RuleForm>>({
  groupName: [
    { required: true, validator: validatePass, trigger: 'blur' }
  ]
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      ruleForm.groupName = ruleForm.groupName.trim()
      const hasSensitiveWords = await desensitizte(ruleForm.groupName, '项目组名称不可包含敏感词汇！')
      if (hasSensitiveWords) return
      if (props.defaultGroupData) {
        updateSceneGroupName({ ...ruleForm, id: props.defaultGroupData.id }).then((res: any) => {
          props.handleHide(true)
        })
      } else {
        addSceneGroup({ ...ruleForm, userId: props.userInfo.userDto.id }).then((res: any) => {
          props.handleHide(true)
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const changeState = () => {
  props.handleHide()
}

onMounted(() => {
  if (props.defaultGroupData) {
    ruleForm.groupName = props.defaultGroupData.groupName
  }
})
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;

  .modal-content {
    width: 470px;
    height: 199px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -235px;
    margin-top: -116px;
    background: #FFFFFF;
    border-radius: 8px;
    overflow: hidden;

    .modal-content-title {
      padding: 16px 0 12px 24px;
      font-weight: bold;
      font-size: 20px;
      color: #1E1E1E;
      text-align: left;
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      padding: 0 30px 0 0;
      box-sizing: border-box;

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-submit {
        width: calc(100% - 30px);
        position: absolute;
        bottom: 24px;
        margin-bottom: 0 !important;
      }

      .tips-text {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        margin-top: -19px;
        text-align: left;
        padding-left: 100px;
      }
    }
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;

  &>button {
    box-shadow: none;
    border-radius: 4px;
  }
}

.btn-primary .el-size3>button {
  background: #2E76FF;
}
</style>