// - 基本颜色
const POINT_BASIC_GEOMETRY = '/png/dot_dot.png'

// - 大红色， 内切、外接多边形辅助用的点
const POINT_CLICK_GEOMETRY = '/png/dot_click.png'

// 点 默认大小
const POINT_DEFAULT_SIZE = 10

// - 绘制过程
const POINT_DRAW_GEOMETRY = '/png/dot_hollow.png'

// - 移动高亮
const POINT_HOVER_GEOMETRY = '/png/dot_hover.png'

// - 基本颜色
const COLOR_BASIC_GEOMETRY = 0x19588C

// 线宽 默认
const LINE_DEFAULT_WIDTH = 1.5

// 线宽 高亮 默认
const LINE_HIGHLIGHT_DEFAULT_WIDTH = 3

const ACCURACY = 0.0001 //精度，用作组合图形时候消除共面问题




// - 绘制过程中的水平，竖直辅助线的颜色/ 选中颜色
const COLOR_HELPER_GEOMETRY = 0xF45B6D

// - move 高亮色  粉色
const COLOR_MOVE_GEOMETRY = 0xffb4b6

// 虚线的间隙和破折号 默认距离
const LINE_DASH_SIZE = 1
const LINE_GAP_SIZE = 1


// 区分当前页面的canvas的id
const canvasIds: any = {
    home_test: 'home_test_id',
    home: 'canvas',
    scene_edit: 'canvas-edit',
    experience_edit: 'experience_edit'
}

export {
    POINT_BASIC_GEOMETRY,
    POINT_DRAW_GEOMETRY,
    POINT_HOVER_GEOMETRY,
    POINT_CLICK_GEOMETRY,
    POINT_DEFAULT_SIZE,
    COLOR_HELPER_GEOMETRY,
    COLOR_MOVE_GEOMETRY,
    LINE_DASH_SIZE,
    LINE_GAP_SIZE,
    COLOR_BASIC_GEOMETRY,
    LINE_DEFAULT_WIDTH,
    LINE_HIGHLIGHT_DEFAULT_WIDTH,
    ACCURACY,
    canvasIds
}
