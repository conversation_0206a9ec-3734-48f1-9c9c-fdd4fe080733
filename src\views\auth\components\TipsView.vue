<template>
  <div class="tips-model" v-if="(store.state.showTips && isOpenTip) || store.state.loginShowInfo">
    <div class="tips-content" :class="{ login_tip_content: store.state.loginShowInfo }">
      <img :src="imgPath" alt="" class="headImage" v-if="store.state.loginShowInfo" />
      <div class="title" :class="{ login_title: store.state.loginShowInfo }">
        {{ store.state.loginShowInfo ? store.state.loginShowInfo.title : tipsTitle }}
      </div>
      <div :class="{ login_tip: store.state.loginShowInfo }">
        {{ store.state.loginShowInfo ? store.state.loginShowInfo.message : store.state.showTips }}
      </div>
      <slot></slot>
      <div
        v-if="!showBtn"
        class="btn"
        :class="{ login_btn: store.state.loginShowInfo }"
        @click="handleCancel">
        知道了
      </div>
      <div v-if="showBtn" class="submit-btns">
        <div class="btn-default el-size3">
          <el-button @click="handleCancle">取消</el-button>
        </div>
        <div class="btn-primary el-size3">
          <el-button @click="handleSure" color="#2e76ff">{{ showBtn }}</el-button>
        </div>
      </div>
      <div class="close" @click="store.state.showTips = ''" v-if="!store.state.loginShowInfo"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue';
import { useStore } from 'vuex';
const headBack = require('@/assets/images/head_back.png');
const headGou = require('@/assets/images/head_gou.png');
const headT = require('@/assets/images/headT.png');
const props = defineProps({
  isOpenTip: {
    default: true,
    type: Boolean,
  },
  tipsTitle: {
    default: '提示',
    type: String,
  },
  showBtn: {
    default: '',
    type: String,
  },
  sureEvent: {
    default: null,
    type: Function,
  },
  cancleEvent: {
    default: null,
    type: Function,
  },
});

const store = useStore();

const imgPath = computed(() => {
  console.log(store.state.loginShowInfo, 'store.state.loginShowInfo');

  if (store.state.loginShowInfo.title === '安全验证提示') {
    return headBack;
  } else if (store.state.loginShowInfo.title === '提示') {
    return headT;
  } else {
    return headGou;
  }
});

const handleCancle = () => {
  props.cancleEvent && props.cancleEvent();
  store.state.showTips = '';
};

const handleSure = () => {
  props.sureEvent && props.sureEvent();
};

const handleCancel = () => {
  if (store.state.loginShowInfo) {
    store.state.loginShowInfo = null;
  }
  if (store.state.showTips) {
    store.state.showTips = '';
  }
};
</script>

<style scoped lang="less">
.tips-model {
  position: fixed !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5) !important;

  .tips-content {
    position: relative;
    width: 524px;
    min-height: 182px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e1e1e1;
    padding: 24px 24px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
    color: #797979;
    z-index: 9999;
    padding-bottom: 64px;

    .headImage {
      width: 160px;
      height: 160px;
    }

    .title {
      font-weight: bold;
      font-size: 18px;
      color: #1e1e1e;
      margin-bottom: 15px;
    }

    .login_title {
      font-size: 20px;
      text-align: center;
      margin-bottom: 24px;
    }

    .login_tip {
      font-weight: 400;
      font-size: 14px;
      color: #797979;
    }

    .btn {
      width: 112px;
      height: 36px;
      background: linear-gradient(225deg, #0375ff 0%, #3c96ff 100%);
      box-shadow: inset -1px -1px 0px 0px rgba(255, 255, 255, 0.2),
        inset 1px 1px 0px 0px rgba(11, 91, 225, 0.4);
      border-radius: 4px;
      line-height: 36px;
      text-align: center;
      font-weight: 600;
      font-size: 14px;
      color: #ffffff;
      position: absolute;
      right: 24px;
      bottom: 24px;
      cursor: pointer;
      letter-spacing: 1px;

      &:hover {
        background: #1251c8 !important;
      }
    }

    .login_btn {
      position: relative;
      right: 0;
      bottom: 0;
      margin-top: 24px;
      background: #d6dff1;
      border-radius: 4px 4px 4px 4px;
      font-weight: bold;
      font-size: 14px;
      color: #2e76ff;
      box-shadow: none;

      &:hover {
        background: rgba(46, 118, 255, 1);
        color: rgba(255, 255, 255, 1);
      }
    }

    .close {
      width: 43px;
      height: 43px;
      background: url(~@/assets/images/close-tips.png);
      background-size: 100% 100%;
      position: absolute;
      right: 17px;
      top: 11px;
      cursor: pointer;
    }

    .submit-btns {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      position: absolute;
      right: 24px;
      bottom: 24px;
    }
  }

  .login_tip_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    min-height: 227px;
    max-height: 227px;
    padding-bottom: 32px;
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;
}
</style>
