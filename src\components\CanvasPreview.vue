<template>
  <div id="source-view" class="canvas-view borderR14"></div>
  <img v-if="showLoading" class="loading-img" src="@/assets/images/icon/loading-b.png" />
</template>

<script lang="ts" setup>
import { onMounted, ref, onUnmounted } from 'vue'
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls"
import { Vector3, Scene, OrthographicCamera, WebGLRenderer, AmbientLight, Box3, MOUSE, TOUCH, PointLight } from 'three'
import { initSize } from "@/utils";

const props = defineProps({
  handleMouseUp: {
    default: null,
    type: Function
  },
})

let camera: OrthographicCamera;
let width: number, height: number;
let scene: Scene;
let renderer: any;
const showLoading = ref(true);
let animationFrameId: any = null;

onMounted(() => {
  scene = new Scene();
  let canvas = document.getElementById('source-view')
  width = canvas?.getBoundingClientRect().width || 0
  height = canvas?.getBoundingClientRect().height || 0
  let k = width / height
  let s = 6


  camera = new OrthographicCamera(-s * k, s * k, s, -s, 0.1, 1000)
  // 设置相机位置以显示模型正面（从前方稍微偏上的角度观看）
  camera.position.set(0, 2, 10)
  camera.lookAt(new Vector3(0, 0, 0));

  renderer = new WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setClearColor(0xffffff, 0)
  canvas?.appendChild(renderer.domElement)
  renderer.render(scene, camera);

  // 添加控制器
  const controls = new OrbitControls(camera, renderer.domElement);
  controls.mouseButtons = {
    LEFT: MOUSE.ROTATE,
    MIDDLE: MOUSE.DOLLY,
    RIGHT: MOUSE.PAN
  }
  controls.touches = {
    ONE: TOUCH.ROTATE,
    TWO: TOUCH.DOLLY_PAN
  }
  // 使用控制器
  controls.enableDamping = false;

  // 环境光
  const ambient = new AmbientLight(0xffffff, 1);
  scene.add(ambient);

  // 点光源
  const light = new PointLight(0xffffff, 1, 100);
  light.position.set(10, 10, 10);
  light.castShadow = true; // default false
  scene.add(light);

  animate()
  function animate() {
    // 使用 requestAnimationFrame 执行动画
    animationFrameId = requestAnimationFrame(animate)

    renderer.render(scene, camera)
  }

  // 双击选中
  canvas?.addEventListener('mouseup', () => {
    props.handleMouseUp && props.handleMouseUp()
  }, false);

})

onUnmounted(() => {
  cancelAnimationFrame(animationFrameId);
})

// 分析模型的最佳正面视角
const findBestFrontView = (obj: any, boxSize: Vector3) => {
  // 根据模型的尺寸比例来判断最佳视角
  const { x, y, z } = boxSize;

  console.log('模型尺寸分析 - X:', x.toFixed(2), 'Y:', y.toFixed(2), 'Z:', z.toFixed(2));

  // 分析模型中网格的分布来判断正面方向
  let meshCount = { front: 0, back: 0, left: 0, right: 0 };

  obj.traverse((child: any) => {
    if (child.isMesh && child.geometry) {
      // 简单的启发式：检查网格的位置分布
      const position = child.position;
      if (position.z > 0) meshCount.front++;
      if (position.z < 0) meshCount.back++;
      if (position.x > 0) meshCount.right++;
      if (position.x < 0) meshCount.left++;
    }
  });

  // 根据模型的尺寸比例来判断最佳视角
  const ratioXZ = x / z;
  const ratioYZ = y / z;

  // 默认从正前方观看 (Z轴正方向)
  let viewAngle = { x: 0, y: 1, z: 1 };

  // 如果模型在 Z 轴方向很薄，从 X 轴方向观看可能更好
  if (ratioXZ > 2.5 && ratioYZ > 2.5) {
    viewAngle = { x: 1, y: 1, z: 0 };
    console.log('模型在Z轴方向较薄，选择从侧面观看');
  }
  // 对于大多数情况，使用正前方稍微偏上的视角
  else {
    viewAngle = { x: 0, y: 1, z: 1 };
    console.log('使用默认正面视角');
  }

  console.log('网格分布:', meshCount);
  return viewAngle;
}

// 设置预定义的视角
const setViewAngle = (angle: 'front' | 'back' | 'left' | 'right' | 'top' | 'bottom') => {
  const distance = 10;

  switch (angle) {
    case 'front':
      camera.position.set(0, 2, distance);
      break;
    case 'back':
      camera.position.set(0, 2, -distance);
      break;
    case 'left':
      camera.position.set(-distance, 2, 0);
      break;
    case 'right':
      camera.position.set(distance, 2, 0);
      break;
    case 'top':
      camera.position.set(0, distance, 0);
      break;
    case 'bottom':
      camera.position.set(0, -distance, 0);
      break;
  }

  camera.lookAt(0, 0, 0);

  if (renderer) {
    renderer.render(scene, camera);
  }

  console.log(`视角切换到: ${angle}, 相机位置:`, camera.position);
}

const addMesh = (obj: any, callback?: any) => {
  console.log('CanvasPreview addMesh 被调用，对象:', obj);

  try {
    // 安全地移除之前的模型（跳过光源）
    const objectsToRemove: any[] = [];
    scene.traverse((child) => {
      if (child !== scene && child.type !== 'AmbientLight' && child.type !== 'PointLight') {
        objectsToRemove.push(child);
      }
    });
    objectsToRemove.forEach(child => scene.remove(child));

    // 添加新模型
    scene.add(obj);
    console.log('模型已添加到场景');

    // 简化的缩放和定位逻辑
    const boxInfo = new Box3().expandByObject(obj);
    console.log('模型边界框:', boxInfo);

    if (!boxInfo.isEmpty()) {
      const boxSize = boxInfo.getSize(new Vector3());
      const maxDimension = Math.max(boxSize.x, boxSize.y, boxSize.z);

      if (maxDimension > 0) {
        // 简单的缩放逻辑
        const targetSize = 5;
        const scaleValue = targetSize / maxDimension;
        obj.scale.set(scaleValue, scaleValue, scaleValue);

        // 居中模型
        const center = boxInfo.getCenter(new Vector3());
        obj.position.set(-center.x * scaleValue, -center.y * scaleValue, -center.z * scaleValue);

        // 调整相机位置以显示模型正面
        const distance = maxDimension * scaleValue * 2;

        // 分析模型的朝向，选择最佳的正面视角
        const bestViewAngle = findBestFrontView(obj, boxSize);

        // 根据最佳视角设置相机位置
        camera.position.set(
          bestViewAngle.x * distance,
          bestViewAngle.y * distance * 0.3, // 稍微从上方观看
          bestViewAngle.z * distance
        );
        camera.lookAt(0, 0, 0);

        console.log('模型缩放:', scaleValue, '位置:', obj.position);
        console.log('最佳正面视角:', bestViewAngle);
        console.log('相机调整到正面视角:', camera.position);
      }
    } else {
      console.warn('边界框为空，使用默认设置');
      obj.scale.set(1, 1, 1);
      obj.position.set(0, 0, 0);
      // 使用默认相机位置
      camera.position.set(0, 2, 10);
      camera.lookAt(0, 0, 0);
    }

    // 确保材质可见
    obj.traverse((child: any) => {
      if (child.isMesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((mat: any) => {
          mat.visible = true;
          if (mat.transparent !== undefined) mat.transparent = false;
          if (mat.opacity !== undefined) mat.opacity = 1.0;
          mat.needsUpdate = true;
        });
        child.visible = true;
      }
    });

    // 强制重新渲染
    if (renderer) {
      renderer.render(scene, camera);
      console.log('强制渲染场景');
    }

    callback && callback();
    showLoading.value = false;
    console.log('模型加载完成');

  } catch (error) {
    console.error('addMesh 过程中发生错误:', error);
    showLoading.value = false;
  }
}

const getCamera = () => {
  return camera;
}

const getRenderer = () => {
  return renderer;
}

const getScene = () => {
  return scene;
}

const debugScene = () => {
  try {
    console.log('=== 场景调试信息 ===');
    console.log('场景子对象数量:', scene.children.length);
    scene.children.forEach((child, index) => {
      console.log(`子对象 ${index}:`, child.type, child.name || 'unnamed');
      if (child.type === 'Group' || child.type === 'Object3D') {
        child.traverse((subChild: any) => {
          if (subChild.isMesh) {
            console.log('  - 网格:', subChild.name || 'unnamed mesh', '几何体:', !!subChild.geometry);
          }
        });
      }
    });
    console.log('相机位置:', camera.position);
    console.log('==================');
  } catch (error) {
    console.error('debugScene 错误:', error);
  }
}

defineExpose({
  addMesh,
  getCamera,
  getRenderer,
  getScene,
  debugScene,
  setViewAngle
})
</script>
<style scoped lang="less">
.canvas-view {
  width: 100%;
  height: 100%;
}

.loading-img {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -16px;
  margin-top: -16px;
  width: 32px;
  height: 32px;
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>