<template>
  <div class="list-wrap" :class="expandGroup ? 'expand-style' : ''">
    <table-list2 
      v-if="tableData.length" 
      :data-total="pageTotal" 
      :page-size="pageSize" 
      :page-no="pageNo" 
      :changePage="changePage" 
      :tableData="tableData" 
      :sceneGroups="sceneGroups" 
      :expandChangeEvent="expandChangeEvent" 
      :handleShare="handleShare" 
      :handleMoveTo="handleMoveTo" 
      :handleSetTemplate="handleSetTemplate" 
      :deleteScene="deleteScene" 
      :handleOpenScene="handleOpenScene" 
      :handleRemove="handleRemove" 
      :expandGroup="expandGroup" 
      :updateGroup="updateGroup" 
      :deleteGroup="deleteGroup">
    </table-list2>
  </div>
</template>

<script lang="ts" setup>
import TableList2 from '@/components/TableList2.vue'

interface TableItem {
  id: number;
  sceneName: string;
  scenePic: string;
  scenePlatform: number;
  sceneType: number;
  sceneStatus: number;
  updateTimeStr: string;
  groupId?: number;
  children?: TableItem[];
}

interface SceneGroup {
  id: number;
  groupName: string;
}

const props = defineProps<{
  tableData: TableItem[];
  pageTotal: number;
  pageSize: number;
  pageNo: number;
  sceneGroups: SceneGroup[];
  expandGroup: SceneGroup | null;
}>()

const emit = defineEmits<{
  (e: 'changePage', page: number): void;
  (e: 'expandChange', data: any): void;
  (e: 'share', data: TableItem): void;
  (e: 'moveTo', data: string): void;
  (e: 'setTemplate', data: TableItem): void;
  (e: 'delete', data: TableItem): void;
  (e: 'openScene', data: TableItem): void;
  (e: 'remove', data: TableItem): void;
  (e: 'updateGroup', data: SceneGroup): void;
  (e: 'deleteGroup', data: SceneGroup): void;
}>()

const changePage = (page: number) => {
  emit('changePage', page)
}

const expandChangeEvent = (data: any) => {
  emit('expandChange', data)
}

const handleShare = (data: TableItem) => {
  emit('share', data)
}

const handleMoveTo = (data: string) => {
  emit('moveTo', data)
}

const handleSetTemplate = (data: TableItem) => {
  emit('setTemplate', data)
}

const deleteScene = (data: TableItem) => {
  emit('delete', data)
}

const handleOpenScene = (data: TableItem) => {
  emit('openScene', data)
}

const handleRemove = (data: TableItem) => {
  emit('remove', data)
}

const updateGroup = (data: SceneGroup) => {
  emit('updateGroup', data)
}

const deleteGroup = (data: SceneGroup) => {
  emit('deleteGroup', data)
}
</script>

<style scoped lang="less">
.list-wrap {
  // 从ItemListView.vue中复制相关样式
}
</style> 