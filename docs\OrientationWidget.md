# 3D 方向控制器 (OrientationWidget) 使用指南

## 功能概述

3D 方向控制器是一个独立的 Vue 组件，为 3D 编辑器提供直观的视角导航功能。它显示为一个半透明的圆球，内部包含 3D 坐标系模型，用户可以通过交互来快速切换 3D 场景的视角。

## 主要特性

### 🎯 **视觉效果**

- **半透明圆球外壳**：提供清晰的视觉边界，不遮挡主要编辑区域
- **3D 坐标系指示器**：显示 X、Y、Z 轴的方向，每个轴有不同的颜色标识
  - X 轴：红色 (正方向) / 深红色 (负方向)
  - Y 轴：绿色 (正方向) / 深绿色 (负方向)
  - Z 轴：蓝色 (正方向) / 深蓝色 (负方向)
- **悬停高亮**：鼠标悬停时方向指示器会变为黄色高亮
- **缩放动画**：鼠标悬停时控件会轻微放大

### 🖱️ **交互功能**

- **拖拽旋转**：拖拽圆球内的坐标系模型，主场景摄像机会相应旋转
- **快速视角切换**：点击方向指示器可快速切换到对应的标准视角
- **实时同步**：控件内的坐标系会实时反映主摄像机的旋转状态

### 📍 **标准视角**

- **右视图** (X+)：从右侧观察场景
- **左视图** (X-)：从左侧观察场景
- **顶视图** (Y+)：从上方俯视场景
- **底视图** (Y-)：从下方仰视场景
- **前视图** (Z+)：从前方观察场景
- **后视图** (Z-)：从后方观察场景

## 技术实现

### 🏗️ **架构设计**

- **独立渲染器**：使用独立的 Three.js 渲染器，不影响主场景性能
- **独立场景**：拥有自己的 Scene、Camera 和光源系统
- **递归射线检测**：支持精确的鼠标交互检测
- **四元数同步**：通过四元数实现平滑的旋转同步

### 🔧 **组件属性**

```typescript
interface Props {
  mainCamera?: any; // 主场景的摄像机对象
  mainControls?: any; // 主场景的控制器对象
  size?: number; // 控件大小，默认120px
}
```

### 📦 **暴露方法**

```typescript
// 设置视角方向
setViewDirection(direction: string): void

// 重置视角到默认位置
resetView(): void
```

## 集成方式

### 1. **在 CanvasThree.vue 中集成**

```vue
<template>
  <div id="edit_3d" style="position: relative;">
    <!-- 3D方向控制器 -->
    <OrientationWidget
      ref="orientationWidgetRef"
      :main-camera="camera"
      :main-controls="controls"
      :size="120" />
  </div>
</template>

<script setup lang="ts">
import OrientationWidget from './OrientationWidget.vue';
// ... 其他代码
</script>
```

### 2. **通过 defineExpose 暴露**

```typescript
defineExpose({
  // ... 其他暴露的方法
  orientationWidget: orientationWidgetRef,
});
```

## 使用方法

### 🎮 **基本操作**

1. **拖拽旋转**：在圆球内按住鼠标左键拖拽，主场景视角会跟随旋转
2. **快速切换**：点击任意方向指示器，立即切换到对应的标准视角
3. **悬停预览**：鼠标悬停在方向指示器上会显示黄色高亮

### 🧪 **测试功能**

在左侧面板中点击 🧭 按钮，或在控制台中运行：

```javascript
testOrientationWidget();
```

这会自动测试所有 6 个标准视角的切换。

### 🔧 **编程控制**

```javascript
// 获取方向控制器引用
const orientationWidget = canvasThreeRef.value?.orientationWidget;

// 切换到特定视角
orientationWidget?.setViewDirection('x'); // 右视图
orientationWidget?.setViewDirection('-y'); // 底视图

// 重置到默认视角
orientationWidget?.resetView();
```

## 性能优化

### ⚡ **优化特性**

- **独立渲染循环**：不影响主场景的渲染性能
- **资源自动清理**：组件卸载时自动清理 Three.js 资源
- **高效射线检测**：只在需要时进行交互检测
- **材质复用**：相同颜色的材质会被复用

### 📊 **性能监控**

控件会在控制台输出详细的调试信息：

```
🧭 方向控制器初始化完成
🧭 主摄像机已连接到方向控制器
🧭 设置视角方向: x
✅ 视角已切换到 x 方向
```

## 自定义配置

### 🎨 **样式自定义**

可以通过 CSS 变量自定义控件外观：

```css
.orientation-widget {
  --widget-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  --widget-background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(0, 0, 0, 0.2) 100%
  );
  --hover-scale: 1.05;
}
```

### 🔧 **位置调整**

默认位置在底部中央，可以通过修改 CSS 调整：

```css
.orientation-widget {
  bottom: 20px; /* 距离底部距离 */
  left: 50%; /* 水平居中 */
  transform: translateX(-50%);
}
```

## 故障排除

### ❌ **常见问题**

1. **控件不显示**：检查主摄像机和控制器是否正确传入
2. **交互无响应**：确保主场景的控制器支持 update 方法
3. **视角切换异常**：检查主摄像机的 lookAt 方法是否正常工作
4. **拖拽无效果**：检查鼠标事件是否正确绑定，控制台是否有拖拽日志

### 🔍 **调试方法**

```javascript
// 检查控件状态
console.log('主摄像机:', camera);
console.log('主控制器:', controls);

// 测试视角切换
testOrientationWidget();

// 测试拖拽功能
testDragFunction();
```

### 🧪 **拖拽功能测试步骤**

1. **点击 🔄 按钮**或在控制台运行 `testDragFunction()`
2. **检查调试输出**：确保主摄像机和控制器已正确连接
3. **手动测试拖拽**：
   - 在轨迹球内按住鼠标左键
   - 拖拽鼠标，观察控制台日志
   - 检查主场景视角是否跟随变化
4. **预期日志输出**：
   ```
   🖱️ 开始拖拽轨迹球
   🔄 拖拽移动: deltaX=10, deltaY=5
   📍 摄像机新位置: (8.66, 5.00, 8.66)
   🖱️ 结束拖拽轨迹球
   ```

### 🔧 **修复拖拽问题的检查清单**

- [ ] 主摄像机对象是否正确传入组件
- [ ] 主控制器对象是否正确传入组件
- [ ] 控制器是否有 update 方法
- [ ] Canvas 元素是否正确渲染
- [ ] 鼠标事件是否正确绑定
- [ ] 射线检测是否能识别轨迹球对象

## 未来扩展

### 🚀 **计划功能**

- **自定义视角预设**：允许用户保存和加载自定义视角
- **动画过渡**：视角切换时添加平滑的动画效果
- **触摸支持**：优化移动设备的触摸交互
- **键盘快捷键**：支持键盘快速切换视角

### 🎯 **API 扩展**

```typescript
// 未来可能的API扩展
interface OrientationWidgetAPI {
  saveViewPreset(name: string): void;
  loadViewPreset(name: string): void;
  animateToView(direction: string, duration: number): void;
  setKeyboardShortcuts(enabled: boolean): void;
}
```

## 🎨 坐标轴显示测试指南

### **测试方法**

1. **点击 🎨 按钮**或在控制台运行 `testAxisDisplay()`
2. **检查调试输出**：确保显示 6 个坐标轴指示器
3. **视觉验证**：
   - X 轴（红色）：应该水平显示，左右方向
   - Y 轴（绿色）：应该垂直显示，上下方向
   - Z 轴（蓝色）：应该前后显示，进出屏幕方向
   - 每个轴都有正负两个方向的箭头

### **预期的控制台输出**

```
🎨 开始创建坐标轴指示器
🎨 创建坐标轴: x, 颜色: #ff3333, 位置: (1.2, 0, 0)
🎨 创建坐标轴: -x, 颜色: #cc0000, 位置: (-1.2, 0, 0)
🎨 创建坐标轴: y, 颜色: #33ff33, 位置: (0, 1.2, 0)
🎨 创建坐标轴: -y, 颜色: #00cc00, 位置: (0, -1.2, 0)
🎨 创建坐标轴: z, 颜色: #3333ff, 位置: (0, 0, 1.2)
🎨 创建坐标轴: -z, 颜色: #0000cc, 位置: (0, 0, -1.2)
✅ 坐标轴指示器创建完成
```

### **坐标轴显示问题排查**

如果坐标轴显示异常：

1. **检查几何体创建**：

   - 轴线应该使用 CylinderGeometry（圆柱体）
   - 箭头应该使用 ConeGeometry（圆锥体）

2. **检查旋转设置**：

   - X 轴：cylinder.rotation.z = π/2, cone.rotation.z = -π/2
   - Y 轴：无需旋转（默认垂直）
   - Z 轴：cylinder.rotation.x = π/2, cone.rotation.x = π/2

3. **检查位置设置**：

   - 轴线位置：direction \* axisLength / 2
   - 箭头位置：direction \* (axisLength + coneHeight / 2)

4. **检查负方向处理**：
   - 负方向应该正确翻转位置和箭头方向

## ✨ 拖动顺滑性优化指南

### **优化内容**

1. **修复上下拖动方向**：

   - 问题：上下拖动方向与预期相反
   - 修复：将 `spherical.phi += deltaY` 改为 `spherical.phi -= deltaY`

2. **提升拖动灵敏度**：

   - 降低最小移动阈值：从 1px 降至 0.5px
   - 提高旋转速度：从 0.005 提升至 0.008
   - 减少日志输出噪音，只在大幅移动时输出

3. **平滑处理**：
   - 添加平滑系数：0.15（为未来扩展预留）
   - 优化响应性和平滑度的平衡

### **测试方法**

1. **点击 ✨ 按钮**或在控制台运行 `testDragSmoothness()`
2. **手动测试**：
   - 在轨迹球内进行缓慢拖动
   - 在轨迹球内进行快速拖动
   - 测试上下拖动方向是否正确
   - 测试左右拖动方向是否正确

### **预期行为**

- **向上拖动**：摄像机视角向上移动
- **向下拖动**：摄像机视角向下移动
- **向左拖动**：摄像机视角向左旋转
- **向右拖动**：摄像机视角向右旋转
- **拖动过程**：平滑无卡顿，响应及时

## 🌊 阻尼效果实现指南

### **功能特性**

1. **惯性滑动**：

   - 用户停止拖拽时，摄像机继续按照拖拽方向移动
   - 移动距离和时间取决于拖拽时的速度

2. **阻尼衰减**：

   - 摄像机旋转速度按指数衰减规律逐渐减慢
   - 阻尼系数：0.92（每帧速度乘以 0.92）

3. **平滑过渡**：
   - 拖拽开始时立即响应
   - 拖拽结束时启动惯性动画
   - 速度低于阈值时平滑停止

### **技术参数**

- **阻尼系数**：0.92（值越小衰减越快）
- **最小速度阈值**：0.001（低于此值停止动画）
- **最大速度限制**：0.5（防止过快旋转）
- **动画实现**：requestAnimationFrame 循环

### **测试方法**

1. **点击 🌊 按钮**或在控制台运行 `testDampingEffect()`
2. **手动测试**：
   - 快速拖动轨迹球后松开鼠标
   - 观察摄像机是否继续惯性移动
   - 检查移动速度是否逐渐减慢
   - 验证最终是否平滑停止

### **预期行为**

- **快速拖动后松开**：摄像机继续移动 0.5-1 秒
- **缓慢拖动后松开**：摄像机立即或很快停止
- **惯性移动过程**：速度逐渐减慢，无突然停止
- **控制台日志**：显示阻尼动画的启动和结束

### **调试信息**

正常工作时控制台应显示：

```
🖱️ 结束拖拽轨迹球，当前速度: (0.120, -0.080)
🌊 启动阻尼动画，初始速度: 0.120 -0.080
🛑 阻尼动画结束
```

## 🔮 圆球指示器改进指南

### **改进内容**

1. **替换箭头为圆球**：

   - 问题：原来使用三角形箭头（ConeGeometry）作为方向指示器
   - 改进：使用圆球（SphereGeometry）替代箭头，提供更清晰的视觉效果

2. **确保 6 个方向显示**：

   - 上下左右前后 6 个方向都应该有圆球指示器
   - 每个方向都有对应的轴线和圆球

3. **优化圆球尺寸**：
   - 圆球半径：coneRadius \* 1.2（比原箭头稍大）
   - 圆球分辨率：16x16（提供平滑的球面）

### **技术实现**

- **几何体**：SphereGeometry 替代 ConeGeometry
- **位置计算**：圆球位置 = direction \* (axisLength + sphereRadius)
- **旋转简化**：圆球不需要旋转，简化了代码逻辑

### **测试方法**

1. **点击 🔮 按钮**或在控制台运行 `testSphereDisplay()`
2. **检查统计信息**：

   - 应该显示"圆球数量=6, 轴线数量=6"
   - 所有圆球都应该可见

3. **视觉验证**：
   - X 轴：红色圆球（左右两个）
   - Y 轴：绿色圆球（上下两个）
   - Z 轴：蓝色圆球（前后两个）

### **预期的控制台输出**

```
🎨 开始创建坐标轴指示器
🎨 创建坐标轴: x, 颜色: #ff3333, 位置: (1.2, 0, 0), 轴线位置: (0.60, 0.00, 0.00), 圆球位置: (1.30, 0.00, 0.00)
🎨 创建坐标轴: -x, 颜色: #cc0000, 位置: (-1.2, 0, 0), 轴线位置: (-0.60, 0.00, 0.00), 圆球位置: (-1.30, 0.00, 0.00)
🎨 创建坐标轴: y, 颜色: #33ff33, 位置: (0, 1.2, 0), 轴线位置: (0.00, 0.60, 0.00), 圆球位置: (0.00, 1.30, 0.00)
🎨 创建坐标轴: -y, 颜色: #00cc00, 位置: (0, -1.2, 0), 轴线位置: (0.00, -0.60, 0.00), 圆球位置: (0.00, -1.30, 0.00)
🎨 创建坐标轴: z, 颜色: #3333ff, 位置: (0, 0, 1.2), 轴线位置: (0.00, 0.00, 0.60), 圆球位置: (0.00, 0.00, 1.30)
🎨 创建坐标轴: -z, 颜色: #0000cc, 位置: (0, 0, -1.2), 轴线位置: (0.00, 0.00, -0.60), 圆球位置: (0.00, 0.00, -1.30)
✅ 坐标轴指示器创建完成
📊 统计: 圆球数量=6, 轴线数量=6
```

## 🎨 新颜色方案更新

### **颜色配置**

已更新为更现代化的颜色方案：

1. **X 轴（水平方向）**：

   - 正方向：#D95153（现代红色）
   - 负方向：#B73E40（深红色）

2. **Y 轴（垂直方向）**：

   - 正方向：#31DEAA（现代绿色）
   - 负方向：#28B88A（深绿色）

3. **Z 轴（前后方向）**：
   - 正方向：#268EFF（现代蓝色）
   - 负方向：#1F73CC（深蓝色）

### **颜色特点**

- **更现代化**：使用了更柔和、更现代的色调
- **更好的对比度**：正负方向使用不同深度的同色系
- **更好的可读性**：颜色更容易区分和识别

### **测试方法**

1. **点击 🎨 按钮**或在控制台运行 `testNewColors()`
2. **检查颜色应用**：
   - 查看控制台输出的实际颜色值
   - 确认轨迹球内的圆球和轴线使用了新颜色

### **预期的控制台输出**

```
🎨 新颜色方案测试
颜色配置:
- X轴正方向: #D95153 (红色)
- X轴负方向: #B73E40 (深红色)
- Y轴正方向: #31DEAA (绿色)
- Y轴负方向: #28B88A (深绿色)
- Z轴正方向: #268EFF (蓝色)
- Z轴负方向: #1F73CC (深蓝色)
📊 实际应用的颜色:
  x: #D95153
  -x: #B73E40
  y: #31DEAA
  -y: #28B88A
  z: #268EFF
  -z: #1F73CC
```

## 🎯 穿透点击检测优化

### **问题解决**

修复了被背景球体遮挡的圆球指示器无法点击的问题：

1. **原问题**：

   - 某些方向的圆球指示器位于轨迹球背景球体后方时被遮挡
   - 点击被遮挡的圆球无法触发视角切换功能
   - 射线检测只返回第一个相交对象（背景球体）

2. **解决方案**：
   - **穿透检测**：获取所有相交对象，而不是只取第一个
   - **优先级处理**：优先查找圆球指示器（SphereGeometry）
   - **智能识别**：确认圆球属于方向指示器组
   - **保持拖拽**：空白区域仍可正常拖拽

### **技术实现**

```typescript
// 改进的射线检测逻辑
const getIntersectedObject = (event: MouseEvent) => {
  // 获取所有相交对象
  const intersects = raycaster.intersectObjects(orientationGroup.children, true);

  // 优先查找圆球指示器
  for (const intersect of intersects) {
    if (intersect.object.type === 'Mesh') {
      const geometry = intersect.object.geometry;
      if (geometry.type === 'SphereGeometry') {
        // 确认属于方向指示器组
        const parent = intersect.object.parent;
        if (parent?.name && directionObjects[parent.name]) {
          return intersect; // 返回圆球指示器
        }
      }
    }
  }

  // 未找到圆球指示器，返回背景对象（用于拖拽）
  return intersects[0] || null;
};
```

### **功能特性**

1. **穿透检测**：能够检测到被背景球体遮挡的圆球指示器
2. **优先级处理**：圆球指示器优先于背景球体
3. **保持兼容**：不影响原有的拖拽功能
4. **智能识别**：只识别真正的方向指示器圆球

### **测试方法**

1. **点击 🎯 按钮**或在控制台运行 `testPenetrationClick()`
2. **手动测试**：
   - 旋转轨迹球，使某些圆球指示器位于后方
   - 尝试点击被遮挡的圆球指示器
   - 验证是否能正确触发视角切换

### **预期行为**

- **点击可见圆球**：立即触发视角切换
- **点击被遮挡圆球**：穿透背景球体，触发视角切换
- **点击空白区域**：正常启动拖拽旋转
- **控制台日志**：显示检测到的对象类型和距离

### **调试信息**

正常工作时控制台应显示：

```
🎯 检测到圆球指示器: z, 距离: 1.90
🎯 点击圆球指示器: z
🧭 设置视角方向: "z"
```

或者对于背景区域：

```
🔍 检测到背景对象: Mesh, 距离: 1.80
🔍 点击背景区域，将启动拖拽模式
```
