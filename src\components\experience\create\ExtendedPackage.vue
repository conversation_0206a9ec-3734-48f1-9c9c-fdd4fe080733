<template>
  <div class="new-mask">
    <div>
      <div class="header">确认扩展服务包内容</div>
      <div class="line" style="align-items: flex-start; margin-top: 20px;">
        <div class="left h1" style="margin-top: 2px;">扩展项目数量：</div>
        <main>
          <div class="demo">
            &nbsp; 大空间定位AR项目 &nbsp; <span>{{ info.arSceneNum }} </span> &nbsp; 个
          </div>
          <div class="demo">
            &nbsp;  小程序平面识别AR项目 &nbsp; <span>{{ info.planeSceneNum }} </span> &nbsp; 个
          </div>
        </main>
      </div>

      <div class="line">
        <div class="left h1">扩展空间上限：</div>
        <div class="right">&nbsp;&nbsp;<span>{{ info.spacePicTotalNum }} </span>&nbsp; 张</div>
      </div>
      <div class="line">
        <div class="left h1">扩展素材空间：</div>
        <div class="right">&nbsp;&nbsp;<span>{{ info.materialUploadSize }} </span>&nbsp; MB</div>
      </div>
      <div class="btn">
        <el-button class="_btn" style="width: 112px; border-radius: 4px;" size="large"
          @click="closeEvent(false)">取消</el-button>
        <el-button class="_btn" style="width: 112px; border-radius: 4px;" size="large" type="primary" color="#2E76FF"
          @click="closeEvent(true)">确认</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const emits = defineEmits(['hiddenMask'])
const props = defineProps({
  info: {
    default: {},
    type: Object
  }
})


const closeEvent = (flag: boolean) => {
  emits('hiddenMask', flag)
}

</script>
<style scoped lang="less">
.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  &>div {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 453px;
    height: 251px;
    background: #FFFFFF;
    border-radius: 8px;
    box-sizing: border-box;
    text-align: left;
    padding: 24px;
    justify-content: center;

    .header {
      width: 100%;
      box-sizing: border-box;
      font-weight: bold;
      font-size: 18px;
      color: #1E1E1E;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    main {
      div {
        display: flex;
        align-items: center;
      }
    }

    .demo {
      text-align: right;
      font-weight: 500;
      font-size: 14px;
      color: #3D566C;

      span {
        font-weight: bold;
        font-size: 16px;
        color: #2E76FF;
      }
    }

    .line {
      display: flex;
      margin-top: 12px;

      .left {
        font-weight: 500;
        font-size: 14px;
        color: #3D566C;
      }

      .right {
        text-align: left;
        width: 200px;
        font-weight: 400 !important;

        span {
          font-weight: bold;
          font-size: 16px;
          color: #2E76FF;
        }
      }
    }

    .closed {
      width: 17px;
      height: 17px;
      position: absolute;
      right: 31px;
      top: 31px;
      cursor: pointer;
    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 18px;
      justify-content: flex-end;

      ._btn {
        width: 120px;
        font-weight: 700;

        &:first-child {
          margin-right: 20px;
        }

        &:last-child {
          margin-left: 20px;
        }
      }
    }
  }
}
</style>