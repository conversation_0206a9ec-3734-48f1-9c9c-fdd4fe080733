<template>
  <div class="new-mask">
    <div>
      <header>
        <div class="title">新建项目</div>
        <img class="closed" src="@/assets/images/experience-icon/mask-close.png" alt="" @click="closeEvent">
      </header>
      <div class="main">
        <div class="scene-name line">
          <div class="first-scene">项目名称</div>
          <el-input class="input" v-model="sceneName" placeholder="请输入项目名称" />
        </div>
        <div class="application-platform line">
          <div>应用平台</div>
          <p>微信小程序</p>
        </div>
        <div class="scene-type line">
          <div>项目类型</div>
          <p>平面识别AR项目</p>
        </div>
      </div>

      <div class="btn">
        <el-button class="_btn" size="large">取消</el-button>
        <el-button class="_btn" size="large" type="primary" @click="addScene">确认</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { saveSceneMetaBackId } from '@/api'
import { useRouter } from 'vue-router'

const props = defineProps({
  hideAddMask: {
    default: null,
    type: Function
  }
})

const router = useRouter()
const sceneName = ref('')

const addScene = () => {
  saveSceneMetaBackId({ sceneName: sceneName.value, sceneType: 1 }).then((res: any) => {
    if (res.code == '200') {
      props.hideAddMask()
      router.push('/experience_edit?sceneid=' + res.data)
    }
  })
}

const closeEvent = () => {
  props.hideAddMask()
}
</script>
<style scoped lang="less">
.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  * {
    margin: 0;
    padding: 0;
  }

  &>div {
    position: relative;
    width: 474px;
    height: 325px;
    background: #EDEFF2;
    border-radius: 8px;
    box-sizing: border-box;

    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      height: 57px;
      background-color: #FFFFFF;
      padding: 0 24px;

      .title {
        font-weight: bold;
        font-size: 18px;
        color: #3D566C;
      }

      .closed {
        width: 17px;
        height: 17px;
        cursor: pointer;
      }

    }

    .main {
      display: flex;
      flex-direction: column;
      align-items: center;

      .input {
        width: 262px;
      }

      .line:first-child {
        margin-top: 24px;

        div {
          margin-right: 14px !important;
        }
      }

      .line {
        display: flex;
        align-items: center;
        margin-bottom: 24px;

        &>div {
          font-weight: 400;
          font-size: 14px;
          color: #3D566C;
          text-align: left;
          margin-right: 16px;
        }

        p {
          font-weight: bold;
          font-size: 14px;
          color: #3D566C;
          width: 264px;
          text-align: left;
          width: 272px;
        }
      }


    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 16px;

      ._btn {
        width: 120px;
        font-weight: 700;

        &:first-child {
          margin-right: 20px;
        }

        &:last-child {
          margin-left: 20px;
        }
      }
    }


  }
}
</style>