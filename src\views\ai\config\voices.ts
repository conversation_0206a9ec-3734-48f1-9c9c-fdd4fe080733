export interface Voice {
  name: string;
  age: string;
  gender: string;
  voiceType: string;
}

export const voices: Voice[] = [
  {
    name: '爽快思思',
    age: 'youth',
    gender: 'female',
    voiceType: 'zh_female_daimengchuanmei_moon_bigtts',
  },
  {
    name: '温柔小雅',
    age: 'youth',
    gender: 'female',
    voiceType: 'zh_female_daimengchuanmei_moon_bigtts',
  },
  {
    name: '磁性大叔',
    age: 'middle',
    gender: 'male',
    voiceType: 'zh_male_daimengchuanmei_moon_bigtts',
  },
  {
    name: '阳光少年',
    age: 'youth',
    gender: 'male',
    voiceType: 'zh_male_daimengchuanmei_moon_bigtts',
  },
  {
    name: '成熟女声',
    age: 'middle',
    gender: 'female',
    voiceType: 'zh_female_daimengchuanmei_moon_bigtts',
  },
  {
    name: '可爱童音',
    age: 'child',
    gender: 'female',
    voiceType: 'zh_female_daimengchuanmei_moon_bigtts',
  },
  {
    name: '稳重男声',
    age: 'middle',
    gender: 'male',
    voiceType: 'zh_male_daimengchuanmei_moon_bigtts',
  },
  {
    name: '清新少女',
    age: 'youth',
    gender: 'female',
    voiceType: 'zh_female_daimengchuanmei_moon_bigtts',
  },
  {
    name: '深沉男低',
    age: 'middle',
    gender: 'male',
    voiceType: 'zh_male_daimengchuanmei_moon_bigtts',
  },
  {
    name: '活力少年',
    age: 'youth',
    gender: 'male',
    voiceType: 'zh_male_daimengchuanmei_moon_bigtts',
  },
  {
    name: '知性女声',
    age: 'middle',
    gender: 'female',
    voiceType: 'zh_female_daimengchuanmei_moon_bigtts',
  },
];
