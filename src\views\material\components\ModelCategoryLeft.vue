<template>
  <div class="model-category-left">
    <div class="category-header">
      <div class="category-title">
        <span>模型分类</span>
        <img
          src="@/assets/images/add343.png"
          alt=""
          class="model-category-icon"
          @click="showAddCategoryDialog" />
      </div>
    </div>
    <div class="category-list" ref="categoryListRef" @scroll="handleScroll" v-loading="loading">
      <div
        class="category-item"
        :class="{ active: currentCategory === null }"
        @click="handleSelectAllCategory">
        <div class="item-left">
          <span class="item-name" style="color: #2e76ff">所有模型</span>
          <span class="item-count" style="color: #2e76ff">({{ props.totalCount }})</span>
        </div>
      </div>
      <draggable
        v-model="categories"
        item-key="id"
        handle=".drag-handle"
        ghost-class="ghost-item"
        chosen-class="chosen-item"
        @start="onDragStart"
        @end="onDragEnd"
        @change="onDragChange">
        <template #item="{ element, index }">
          <div
            class="category-item"
            :class="{ active: currentCategory === element.id }"
            @click="handleSelectCategory(element)">
            <div class="item-left">
              <span class="drag-handle">⋮⋮</span>
              <span class="item-name">{{ element.name }}</span>
              <span class="item-count">({{ element.count }})</span>
            </div>
            <div class="item-right">
              <img
                src="@/assets/images/edit4545.png"
                alt="编辑"
                class="edit-btn"
                @click.stop="showEditCategoryDialog(element)"
                style="width: 16px; height: 16px; cursor: pointer; position: relative; left: 5px" />
              <img
                src="@/assets/images/delete4545.png"
                alt="删除"
                class="delete-btn"
                @click.stop="handleDelete(element)"
                style="width: 16px; height: 16px; cursor: pointer; margin-left: 8px" />
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <!-- 添加模型分类弹窗 -->
    <el-dialog
      v-model="addCategoryDialogVisible"
      title="添加模型分类"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form
        ref="addCategoryFormRef"
        :model="addCategoryForm"
        :rules="addCategoryRules"
        label-width="0px">
        <el-form-item prop="categoryName">
          <el-input
            v-model="addCategoryForm.categoryName"
            placeholder="请输入模型分类名称"
            show-word-limit
            clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAddCategory">取消</el-button>
          <el-button type="primary" @click="confirmAddCategory">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑模型分类弹窗 -->
    <el-dialog
      v-model="editCategoryDialogVisible"
      title="编辑模型分类"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form
        ref="editCategoryFormRef"
        :model="editCategoryForm"
        :rules="addCategoryRules"
        label-width="0px">
        <el-form-item prop="categoryName">
          <el-input
            v-model="editCategoryForm.categoryName"
            placeholder="请输入模型分类名称"
            show-word-limit
            clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEditCategory">取消</el-button>
          <el-button type="primary" @click="confirmEditCategory">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import draggable from 'vuedraggable';
import { ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import {
  getModeType,
  addModelType,
  deleteModelType,
  updateModeType,
  updateModelTypeSort,
} from '@/api/modules/material';

// 定义 props
interface Props {
  totalCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  totalCount: 0,
});

// 定义分类类型接口
interface Category {
  id: number;
  name: string;
  count: number;
  checkedCategories: string[];
}

// 状态管理
const categories = ref<Category[]>([]);
const loading = ref(false);
const currentCategory = ref(null);
const categoryListRef = ref<HTMLElement>();
const isDragging = ref(false);
// 添加分类弹窗相关
const addCategoryDialogVisible = ref(false);
const addCategoryFormRef = ref<FormInstance>();
const addCategoryForm = ref({
  categoryName: '',
});

// 编辑分类弹窗相关
const editCategoryDialogVisible = ref(false);
const editCategoryFormRef = ref<FormInstance>();
const editCategoryForm = ref({
  categoryName: '',
});
const currentEditCategory = ref<any>(null);

// 自定义校验函数：检查是否只输入了空格
const validateCategoryName = (_rule: any, value: string, callback: (error?: Error) => void) => {
  if (!value) {
    callback(new Error('请输入模型分类名称'));
  } else if (value.trim() === '') {
    callback(new Error('模型分类名称不能只包含空格'));
  } else if (value.length < 1 || value.length > 20) {
    callback(new Error('支持1~20个字符的名称'));
  } else {
    callback();
  }
};

// 表单验证规则
const addCategoryRules: FormRules = {
  categoryName: [{ validator: validateCategoryName, trigger: 'blur' }],
};

// API调用函数
// 获取所有模型类型
const getAllModelTypes = async () => {
  try {
    loading.value = true;
    const response = await getModeType();

    // 假设API返回的数据格式需要转换
    if (response && response.data && Array.isArray(response.data)) {
      categories.value = response.data.map((item: any) => ({
        id: item.id,
        name: item.typeName || item.name,
        count: item.matNum || 0,
        checkedCategories: [],
      }));
    }
  } catch (error) {
    ElMessage.error('获取分类列表失败');
  } finally {
    loading.value = false;
  }
};

// 添加模型类型
const addModelTypeHandler = async (typeName: string) => {
  try {
    loading.value = true;
    await addModelType({ typeName });
    return true;
  } catch (error) {
    ElMessage.error('添加分类失败');
    return false;
  } finally {
    loading.value = false;
  }
};

// 删除模型类型
const deleteModelTypeHandler = async (id: number) => {
  try {
    loading.value = true;
    await deleteModelType({ id });
    return true;
  } catch (error) {
    ElMessage.error('删除分类失败');
    return false;
  } finally {
    loading.value = false;
  }
};

// 更新模型类型
const updateModelTypeHandler = async (id: number, typeName: string) => {
  try {
    loading.value = true;
    await updateModeType({ id, typeName });
    return true;
  } catch (error) {
    ElMessage.error('更新分类失败');
    return false;
  } finally {
    loading.value = false;
  }
};
const handleSelectCategory = (category: any) => {
  currentCategory.value = category.id;
  emit('select-category', category.id);
};
const handleSelectAllCategory = () => {
  currentCategory.value = null;
  emit('select-category', null);
};
const handleDelete = async (category: Category) => {
  try {
    const success = await deleteModelTypeHandler(category.id);
    if (success) {
      // 从本地列表中移除
      const index = categories.value.findIndex((cat) => cat.id === category.id);
      if (index !== -1) {
        categories.value.splice(index, 1);
      }
      ElMessage.success('分类删除成功');
    }
  } catch (error) {
    console.error('删除分类失败:', error);
  }
};
const onDragStart = (evt: any) => {
  isDragging.value = true;
};

const onDragEnd = async (evt: any) => {
  isDragging.value = false;

  // 如果拖拽位置发生了变化，则更新排序
  if (evt.oldIndex !== evt.newIndex) {
    await updateCategoriesSort();
  }
};

const onDragChange = (evt: any) => {
  // vuedraggable 会自动更新 categories 数组的顺序
  console.log('拖拽变化:', evt);
};

// 更新分类排序
const updateCategoriesSort = async () => {
  try {
    // 构建排序数据，基于当前 categories 数组的顺序
    const sortData = categories.value.map((category, index) => ({
      firstData: category.id, // 分类ID
      secondData: index + 1, // 排序位置（1-based）
    }));

    console.log('更新排序数据:', sortData);

    // 调用API更新排序
    await updateModelTypeSort(sortData);

    ElMessage.success('分类排序更新成功');
  } catch (error) {
    console.error('更新分类排序失败:', error);
    ElMessage.error('更新分类排序失败');

    // 如果API调用失败，重新加载分类数据以恢复原始顺序
    await getAllModelTypes();
  }
};
const handleScroll = () => {
  if (!categoryListRef.value) return;
  const element = categoryListRef.value;
  const scrollTop = element.scrollTop;
  const scrollHeight = element.scrollHeight;
  const clientHeight = element.clientHeight;
  const maxScrollTop = scrollHeight - clientHeight;
  const scrollPercentage = maxScrollTop > 0 ? scrollTop / maxScrollTop : 0;
  const scrollbarHeight = 60;
  const trackStartTop = 60;
  const trackHeight = clientHeight;
  const availableTrackHeight = trackHeight - scrollbarHeight;
  const sliderTop = trackStartTop + scrollPercentage * availableTrackHeight;
  const container = element.closest('.model-category-left') as HTMLElement;
  if (container) {
    container.style.setProperty('--scrollbar-top', `${sliderTop}px`);
  }
};

// 添加分类弹窗相关方法
const showAddCategoryDialog = () => {
  addCategoryDialogVisible.value = true;
  // 重置表单
  addCategoryForm.value.categoryName = '';
  // 清除表单验证状态
  if (addCategoryFormRef.value) {
    addCategoryFormRef.value.clearValidate();
  }
};

const cancelAddCategory = () => {
  addCategoryDialogVisible.value = false;
  addCategoryForm.value.categoryName = '';
};

const confirmAddCategory = async () => {
  if (!addCategoryFormRef.value) return;

  try {
    await addCategoryFormRef.value.validate();

    // 验证通过，处理添加分类逻辑
    const categoryName = addCategoryForm.value.categoryName.trim();

    // 提交时二次校验：确保不是只有空格
    if (categoryName === '') {
      ElMessage.error('模型分类名称不能只包含空格');
      return;
    }

    // 调用API添加分类
    const success = await addModelTypeHandler(categoryName);
    if (success) {
      // 重新获取分类列表以获取最新数据
      await getAllModelTypes();
      ElMessage.success('分类添加成功');
      addCategoryDialogVisible.value = false;
      addCategoryForm.value.categoryName = '';

      // 通知父组件分类已更新，需要刷新右侧的popover选项
      emit('categoryAdded');
    }
  } catch (error) {
    console.log('表单验证失败:', error);
  }
};

// 编辑分类弹窗相关方法
const showEditCategoryDialog = (category: any) => {
  currentEditCategory.value = category;
  editCategoryForm.value.categoryName = category.name;
  editCategoryDialogVisible.value = true;
  // 清除表单验证状态
  if (editCategoryFormRef.value) {
    editCategoryFormRef.value.clearValidate();
  }
};

const cancelEditCategory = () => {
  editCategoryDialogVisible.value = false;
  editCategoryForm.value.categoryName = '';
  currentEditCategory.value = null;
};

const confirmEditCategory = async () => {
  if (!editCategoryFormRef.value || !currentEditCategory.value) return;

  try {
    await editCategoryFormRef.value.validate();

    // 验证通过，处理编辑分类逻辑
    const categoryName = editCategoryForm.value.categoryName.trim();

    // 提交时二次校验：确保不是只有空格
    if (categoryName === '') {
      ElMessage.error('模型分类名称不能只包含空格');
      return;
    }

    // 调用API更新分类
    const success = await updateModelTypeHandler(currentEditCategory.value.id, categoryName);
    if (success) {
      // 更新本地数据
      const categoryIndex = categories.value.findIndex(
        (cat) => cat.id === currentEditCategory.value.id
      );
      if (categoryIndex !== -1) {
        categories.value[categoryIndex].name = categoryName;
      }

      ElMessage.success('分类编辑成功');
      editCategoryDialogVisible.value = false;
      editCategoryForm.value.categoryName = '';
      currentEditCategory.value = null;
    }
  } catch (error) {
    console.log('表单验证失败:', error);
  }
};

// 刷新分类列表的方法
const refreshCategories = async () => {
  await getAllModelTypes();
};

// 暴露方法给父组件
defineExpose({
  refreshCategories,
});

const emit = defineEmits(['select-category', 'categoryAdded']);

onMounted(() => {
  handleScroll();
  // 加载分类数据
  getAllModelTypes();
});
</script>

<style scoped lang="less">
.model-category-left {
  width: 240px;
  height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  position: relative;
  // 弹窗按钮样式
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-shrink: 0;
    .category-title {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      display: flex;
      align-items: center;
      margin-left: 22px;
      .model-category-icon {
        width: 24px;
        height: 24px;
        margin-left: 8px;
        cursor: pointer;
      }
    }
  }
  .category-list {
    flex: 1;
    overflow-y: auto;
    padding-left: 12px;
    position: relative;
    &::-webkit-scrollbar {
      width: 0;
      display: none;
    }
    .category-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      padding: 0 12px;
      margin-bottom: 4px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      &:hover {
        background: rgba(0, 96, 255, 0.06);
        border-radius: 4px 4px 4px 4px;
      }
      &.active {
        background: rgba(0, 96, 255, 0.06);
        border-radius: 8px;
        .item-name {
          color: #2e76ff;
        }
      }
      .item-left {
        display: flex;
        align-items: center;
        gap: 8px;
        .drag-handle {
          color: #999999;
          font-size: 12px;
          cursor: grab;
          user-select: none;
          opacity: 0;
          transition: opacity 0.2s ease;
          &:active {
            cursor: grabbing;
          }
        }
        .item-name {
          font-size: 14px;
          color: #333333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 120px;
        }
        .item-count {
          font-size: 14px;
          color: #999999;
          margin-left: 4px;
        }
      }
      .item-right {
        display: none;
        .edit-btn,
        .delete-btn {
          font-size: 14px;
          color: #666666;
          cursor: pointer;
          &:hover {
            color: #2e76ff;
          }
        }
      }
      &:hover {
        .drag-handle {
          opacity: 1;
        }
        .item-right {
          display: flex;
        }
      }
    }
    .ghost-item {
      opacity: 0.5;
      background: #f0f0f0;
      border: 2px dashed #2e76ff;
    }
    .chosen-item {
      background: rgba(46, 118, 255, 0.1);
      border: 1px solid #2e76ff;
      transform: rotate(2deg);
    }
  }
  &::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 60px;
    width: 2px;
    height: calc(100% - 80px);
    background: #e6edf7;
    border-radius: 2px;
    z-index: 100;
  }
  &::after {
    content: '';
    position: absolute;
    left: 16px;
    top: var(--scrollbar-top, 60px);
    width: 3px;
    height: 60px;
    background: #2e76ff;
    border-radius: 2px;
    z-index: 101;
    transition: top 0.1s ease;
    will-change: top;
  }
}
.category-edit-popover {
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 18px 24px 12px 24px;
  width: auto;
  min-width: unset;
  max-width: unset;
  display: inline-block;
}
.category-checkbox-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 15px;
}
.category-checkbox-item {
  display: flex;
  align-items: center;
  font-size: 15px;
  cursor: pointer;
  user-select: none;
  width: 120px;
}
.category-checkbox-item input[type='checkbox'] {
  margin-right: 8px;
  accent-color: #2e76ff;
}
.category-checkbox-item .checked {
  color: #2e76ff;
  font-weight: 500;
}
.category-checkbox-item input[type='checkbox']:checked + span {
  color: #2e76ff;
  font-weight: 500;
}
.category-checkbox-item input[type='checkbox']:checked {
  accent-color: #2e76ff;
  border-color: #2e76ff;
}
</style>
