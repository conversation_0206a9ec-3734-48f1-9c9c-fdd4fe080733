# OrientationWidget 平滑动画功能指南

## 功能概述

OrientationWidget 组件现在支持平滑的相机视角切换动画，当用户点击轨迹球上的方向指示器时，相机会通过流畅的动画过渡到目标视角，而不是瞬间跳转。

## 新增功能特性

### 1. 平滑动画切换
- **动画时长**：默认1000毫秒，可自定义
- **缓动函数**：使用 easeInOutCubic 缓动，提供自然的加速和减速效果
- **插值算法**：使用 Three.js 的 Vector3.lerp() 进行位置插值

### 2. 用户交互友好
- **动画中断**：用户可以通过拖拽轨迹球或其他操作中断正在进行的动画
- **状态管理**：提供动画状态查询，避免重复触发
- **调试信息**：提供详细的动画进度日志

### 3. 灵活配置
- **启用/禁用**：可以通过 `enableAnimation` 属性控制是否启用动画
- **自定义时长**：通过 `animationDuration` 属性设置动画持续时间
- **向后兼容**：禁用动画时回退到原来的瞬间切换模式

## 使用方法

### 基本用法

```vue
<template>
  <OrientationWidget
    :main-camera="camera"
    :main-controls="controls"
    :size="120"
    :animation-duration="1000"
    :enable-animation="true"
  />
</template>
```

### 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `animationDuration` | number | 1000 | 动画持续时间（毫秒） |
| `enableAnimation` | boolean | true | 是否启用平滑动画 |
| `mainCamera` | Camera | - | 主场景相机对象 |
| `mainControls` | Controls | - | 主场景控制器对象 |
| `size` | number | 120 | 控件大小 |

### 方法调用

```javascript
// 获取组件引用
const orientationWidget = ref();

// 手动触发视角切换（带动画）
orientationWidget.value.setViewDirection('x');

// 停止当前动画
orientationWidget.value.stopViewAnimation();

// 检查是否正在执行动画
const isAnimating = orientationWidget.value.isAnimating();
```

## 技术实现细节

### 动画流程

1. **触发检查**：检查是否已有动画在执行，如有则先停止
2. **参数计算**：计算起始位置和目标位置
3. **动画循环**：使用 `requestAnimationFrame` 执行动画循环
4. **插值计算**：使用缓动函数和线性插值计算当前位置
5. **状态更新**：更新相机位置和控制器状态
6. **完成处理**：动画完成后清理状态和输出调试信息

### 缓动函数

使用 easeInOutCubic 缓动函数：
```javascript
const easeInOutCubic = (t: number): number => {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
};
```

### 动画中断机制

- **用户拖拽**：在 `onMouseDown` 中检测并中断动画
- **新动画触发**：新的视角切换会自动停止当前动画
- **组件卸载**：在 `onBeforeUnmount` 中清理所有动画

## 性能优化

1. **日志优化**：动画进度日志每10%输出一次，减少控制台噪音
2. **状态管理**：使用 `isAnimating` 状态避免重复动画
3. **资源清理**：组件卸载时自动清理动画资源
4. **帧率优化**：使用 `requestAnimationFrame` 确保流畅的60fps动画

## 调试功能

### 控制台输出
- 动画开始：`🧭 开始平滑切换到视角方向: "x"`
- 动画进度：`🎬 视角切换动画进度: 50%`
- 动画完成：`✅ 视角已平滑切换到 x 方向`
- 用户中断：`🛑 用户操作中断视角切换动画`

### 调试方法
```javascript
// 输出相机状态
orientationWidget.value.debugCameraState('当前状态');

// 诊断视野裁切问题
orientationWidget.value.diagnoseViewClipping();
```

## 兼容性说明

- **向后兼容**：设置 `enableAnimation: false` 可回退到原来的瞬间切换模式
- **浏览器支持**：支持所有现代浏览器，需要 `requestAnimationFrame` 支持
- **Three.js版本**：兼容当前项目使用的 Three.js 版本

## 最佳实践

1. **动画时长**：建议使用 800-1200ms，提供最佳用户体验
2. **性能考虑**：在低性能设备上可以禁用动画或缩短动画时长
3. **用户反馈**：保持动画进度的视觉反馈，让用户了解操作状态
4. **错误处理**：确保相机和控制器对象有效性检查

## 示例代码

完整的使用示例请参考 `src/components/scene_web/CanvasThree.vue` 中的实现。
