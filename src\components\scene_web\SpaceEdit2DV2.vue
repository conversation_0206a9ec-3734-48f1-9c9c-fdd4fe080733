<template>
  <div class="edit-box" v-show="store.state.isPlanStyle">
    <canvas-two
      ref="canvas2dRef"
      :handle-mouse-move="handleMouseMove"
      :handle-mouse-down="handleMouseDown"
      :handle-mouse-up="handleMouseUp"></canvas-two>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, reactive, nextTick } from 'vue';
import { Vector3, Quaternion, Scene, Group, Vector2, Raycaster } from 'three';
import CanvasTwo from '@/components/scene_web/CanvasTwo.vue';
import { CPoint, CPlan, CSprite } from '@/core/primitives';
import { materialUrls } from '@/config';
import {
  worldToScreenVector,
  searchMaterial,
  screenToWorldVector,
  angleToRadians,
  searchMaterialFromUuid,
  changeActiveWithArea,
} from '@/utils';
import { useStore } from 'vuex';
import { saveScene, getOssAccessPath } from '@/api';
import { MouseStyle, updateMouseStyle } from '@/core/mouse';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';

const props = defineProps({
  sceneData: {
    default: null,
    type: Object,
  },
});

const canvas2dRef = ref();
const editSceneData: any = ref({});
let scene = new Scene();
const initSize = new Vector2(1, 1); // 原始的互动区域的二维尺寸
const activeInteractionName = ref(''); // 选中高亮的互动区域
const activeMaterialName = ref(''); // 当前高亮的素材
const draggingInfo: any = ref(null); // 当前操作的素材的位置
let pointObject: any = null; // 互动区域旋转精灵贴图对象
const interactionOperationStatus = ref('');
let startRotatePoint: any = null; // 开始旋转的点位
let controlPoint: any = null; // 当前拖拽缩放的控制点
const router = useRouter();
const store = useStore();

const exitEdit = () => {
  // 更新场景之前先截图保存
  const pageQuery: any = router.currentRoute.value.query;
  const sceneId = pageQuery.sceneid || '';
  const renderer = (window as any).renderer;
  const camera = (window as any).camera;
  const token = window.localStorage.getItem('token');
  if (!token) return;
  renderer.render(scene, camera);
  let imgData = renderer.domElement.toDataURL('image/jpeg');
  saveScene({ sceneId: sceneId, base64Pic: imgData }).then((res: any) => {
    if (res.code == 200) {
      router.push('home');
    }
  });
};

const handleMouseMove = (point3d: Vector3, point2d: Vector2) => {
  if (draggingInfo.value) {
    // 拖拽素材
    if (draggingInfo.value && draggingInfo.value != store.state.activeAreaUuid) {
      // 互动区域内素材
      if (!activeMaterialName.value) return;
      const activeMaterial: any = scene.getObjectByName(activeMaterialName.value);
      activeMaterial.setVertex(point3d);
      if (draggingInfo.value.data) {
        editSceneData.value.interactionDtoList[draggingInfo.value.index].materialMetaDtoList[
          draggingInfo.value.i
        ].location = { x: point3d.x, y: point3d.y, z: point3d.z };
        if (
          editSceneData.value.interactionDtoList[draggingInfo.value.index].materialMetaDtoList[
            draggingInfo.value.i
          ].flag != 'add'
        ) {
          editSceneData.value.interactionDtoList[draggingInfo.value.index].materialMetaDtoList[
            draggingInfo.value.i
          ].flag = 'update';
        }
      } else {
        editSceneData.value.outerMaterialMetaDtoList[draggingInfo.value.index].location = {
          x: point3d.x,
          y: point3d.y,
          z: point3d.z,
        };
        if (editSceneData.value.outerMaterialMetaDtoList[draggingInfo.value.index].flag != 'add') {
          editSceneData.value.outerMaterialMetaDtoList[draggingInfo.value.index].flag = 'update';
        }
      }
      editSceneData.value.changeTime = new Date().getTime();
      store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData.value));
    }
  } else {
    // 对挥动区域的操作
    let ray = new Raycaster();
    ray.set(point3d, new Vector3(0, 1, 0));
    const r1 = ray.intersectObjects(scene.children, true);
    if (!r1.length && pointObject && !interactionOperationStatus.value) {
      pointObject.loadTexture(require('@/assets/images/background/rotate-default.png'));
      pointObject.userData.state = 'default';
    }

    if (interactionOperationStatus.value) {
      const activeInteractionIndex = editSceneData.value.interactionDtoList?.findIndex(
        (e: any) => e.uuid == activeInteractionName.value
      );
      const activeInteractionData = editSceneData.value.interactionDtoList[activeInteractionIndex];
      if (activeInteractionData && activeInteractionData.flag != 'add') {
        activeInteractionData.flag = 'update';
      }
      const activeInteractionObject: any = scene.getObjectByName(activeInteractionName.value)
        ?.children[0];
      if (interactionOperationStatus.value == 'scale') {
        // 互动区域拖拽缩放
        // 拖拽控制点
        if (controlPoint && activeInteractionData) {
          const oldScale = activeInteractionData.scale;
          if ([0, 2, 4, 6, 8].includes(controlPoint.object.userData.index)) {
            const scale = activeInteractionObject.dragPoint(
              'vertex',
              point3d,
              new Vector2(oldScale.x, oldScale.z),
              controlPoint.object.userData.index
            );
            activeInteractionData.scale = { x: oldScale.x * scale, y: 1, z: oldScale.z * scale };
          } else {
            const scale = activeInteractionObject.dragPoint(
              'vertexCenter',
              point3d,
              new Vector2(oldScale.x, oldScale.z),
              controlPoint.object.userData.index
            );
            activeInteractionData.scale = { x: scale.x, y: 1, z: scale.y };
          }

          // 缩放之后重置位置
          const center2D = activeInteractionObject.userData?.center;
          const centerFor3D = screenToWorldVector(center2D.x, center2D.y);
          activeInteractionData.position = centerFor3D.clone();
        }
      }
      if (interactionOperationStatus.value == 'rotate' && pointObject) {
        // 互动区域拖拽旋转
        const oldRotate = activeInteractionData.rotation.y;
        const startRotate = pointObject.userData.startRotate || 0;
        const currentRotate = activeInteractionObject.rotatePoint(
          point3d.clone(),
          oldRotate,
          startRotatePoint.clone(),
          startRotate
        );
        activeInteractionData.rotation.y = currentRotate;
        pointObject.userData.oldRotate = oldRotate;
        pointObject.setRotate(-currentRotate);
      }
      if (interactionOperationStatus.value == 'position') {
        const cplan: any = scene.getObjectByName(activeInteractionName.value)?.children[0];
        const activeInteractionIndex = editSceneData.value.interactionDtoList.findIndex(
          (e: any) => e.uuid == activeInteractionName.value
        );
        if (activeInteractionIndex != -1 && cplan) {
          const activeInteraction = editSceneData.value.interactionDtoList[activeInteractionIndex];
          cplan.setVertexs(
            initSize.x * activeInteraction.scale.x,
            initSize.y * activeInteraction.scale.z,
            point2d
          );
          editSceneData.value.interactionDtoList[activeInteractionIndex].location = {
            x: point3d.x,
            y: point3d.y,
            z: point3d.z,
          };
        }
      }

      editSceneData.value.changeTime = new Date().getTime();
      store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData.value));
      return;
    }

    if (r1.length) {
      // 拾取旋转图标高亮
      const rotatePoint: any = r1.filter((e: any) => e.object.userData.type == 'rotatePoint')[0];
      if (rotatePoint && activeInteractionName.value == rotatePoint?.object.parent.parent.uuid) {
        pointObject = rotatePoint.object;
        if (pointObject.userData.state == 'default') {
          pointObject.loadTexture(require('@/assets/images/background/rotate-active.png'));
          pointObject.userData.state = 'active';
          pointObject.userData.startRotate = editSceneData.value.interactionDtoList.filter(
            (e: any) => e.uuid == activeInteractionName.value
          )[0].rotation.y;
        }
      } else {
        changeRotateState('clear');
      }
    } else if (pointObject) {
      changeRotateState('clear');
    }
  }
};

// 切换旋转图片状态
const changeRotateState = (state: string) => {
  if (pointObject) {
    if (state == 'view') {
      pointObject.loadTexture(require('@/assets/images/background/rotate-view.png'));
      pointObject.userData.state = 'view';
    } else {
      pointObject.loadTexture(require('@/assets/images/background/rotate-default.png'));
      pointObject.userData.state = 'default';
      pointObject = null;
    }
  }
};

const handleMouseDown = (point3d: Vector3) => {
  let ray = new Raycaster();
  ray.set(point3d, new Vector3(0, 1, 0));
  const r1 = ray.intersectObjects(scene.children, true);
  if (r1.length) {
    const activeObject: any = searchSourchOrInteraction(r1);
    const rotatePoint: any = r1.filter((e: any) => e.object.userData.type == 'rotatePoint')[0];
    const scalePoint: any = r1.filter((target) => target.object.type === 'Points')[0];
    if (scalePoint) {
      controlPoint = { object: scalePoint.object, point: scalePoint.point };
      if ([0, 3, 4, 7].includes(controlPoint.object.userData.index)) {
        updateMouseStyle(MouseStyle.mouseScaleLR, true);
      }
      if ([1, 2, 5, 6].includes(controlPoint.object.userData.index)) {
        updateMouseStyle(MouseStyle.mouseScaleTB, true);
      }
      interactionOperationStatus.value = 'scale';
    } else if (rotatePoint) {
      const pointObject: any = rotatePoint.object;
      changeRotateState('view');
      interactionOperationStatus.value = 'rotate';
      startRotatePoint = point3d.clone();
      updateMouseStyle(MouseStyle.mouseDrag, true);
      activeInteractionName.value = pointObject.parent.parent.uuid;
    } else if (activeObject) {
      if (store.state.isDragLoading || store.state.isRequesting) {
        // 射线拾取到了模型再判断是否保存好
        return ElMessage({ message: '数据加载中，请勿频繁操作', type: 'warning' });
      }
      if (activeObject.type == 'cplan') {
        // 选中互动区域
        store.state.activeAreaUuid = activeObject.object.uuid;
        interactionOperationStatus.value = 'position';
        updateMouseStyle(MouseStyle.mouseDrag, true);
      } else if (activeObject.type == 'sprite') {
        // 选中资源
        store.state.operateType = '移动';
        draggingInfo.value = searchMaterialFromUuid(editSceneData.value, activeObject.object.name);
        changeActiveWithArea(activeObject.object.name);
      }
    }
  }
};

const handleMouseUp = (point3d: Vector3) => {
  draggingInfo.value = null;
  controlPoint = null;
  // 取消旋转
  if (pointObject && pointObject.userData.state == 'view') {
    changeRotateState('clear');
  }
  updateMouseStyle(MouseStyle.default, true);
  interactionOperationStatus.value = '';
};

const searchSourchOrInteraction = (rays: any) => {
  // 查询点击的是资源还是互动区域
  let obj = null;
  rays.forEach((ele: any) => {
    if (ele.object.name == 'cplan') {
      obj = {
        type: 'cplan',
        object: ele.object,
      };
    }
    if (ele.object.userData.type == 'source' && ele.object.type == 'Sprite') {
      obj = {
        type: 'sprite',
        object: ele.object,
      };
    }
  });
  return obj;
};

// 互动区域高亮
const interactionActive = (object: any) => {
  if (activeInteractionName.value) {
    const interactionGroup = scene.getObjectByName(activeInteractionName.value);
    interactionActiveCancle(interactionGroup?.children[0]);
  }
  object.children[0].setHeightStyle();
  object.children[1].children[0].visible = true; // 旋转提示图片
  activeInteractionName.value = object.uuid;
};

// 互动区域取消高亮
const interactionActiveCancle = (object: any) => {
  if (object && object.children) {
    object.children[0].setBaseStyle();
    object.children[1].children[0].visible = false; // 旋转提示图片
    if (activeInteractionName.value == object.uuid) {
      activeInteractionName.value = '';
    }
  } else {
    activeInteractionName.value = '';
  }
};

// 取消素材高亮
const materialActiveCancle = (object: any) => {
  if (!object) return;
  object.children[0].element.classList.remove('active');
};

// 高亮素材
const materialActive = (object: any) => {
  // 被拖拽的素材添加高亮
  object.children[0].element.classList.add('active');
};

// 初始化创建以及后面拉取创建
const createModel = (ele: any, key: string, index: number, i?: number, data?: any) => {
  let interactionGroup: any = new Group();
  if (key == 'public') {
    if (!editSceneData.value.outerMaterialMetaDtoList.length) {
      interactionGroup.name = key; // 公共放在public里，互动区域的放在互动区域里
      scene.add(interactionGroup);
    }
    interactionGroup = scene.getObjectByName('public');
  } else {
    if (!editSceneData.value.interactionDtoList[index]) {
      createInteraction(interactionGroup, index, data);
    }
    interactionGroup = scene.getObjectByName(editSceneData.value.interactionDtoList[index].uuid);
  }
  getOssAccessPath({ key: ele.materialDto.thumbnail }).then((res: any) => {
    const cSprite = new CSprite({
      vertex: new Vector3(ele.location.x, ele.location.y, ele.location.z),
      scale: new Vector3(1.2, 1.2, 1.2),
      url: res.data || materialUrls[ele.materialDto.materialType].url_a,
      type: 'source',
      marks: ele.elementName || ele.materialDto.materialName,
    });
    cSprite.name = ele.uuid;
    interactionGroup.add(cSprite);
  });

  if (i == undefined) {
    editSceneData.value.outerMaterialMetaDtoList[index] = { ...ele };
  } else {
    editSceneData.value.interactionDtoList[index].materialMetaDtoList[i] = { ...ele };
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData.value));
};

const addNewInteraction = (index: number, data: any) => {
  let interactionGroup: any = new Group();
  if (!editSceneData.value.interactionDtoList[index]) {
    createInteraction(interactionGroup, index, data);
  }
};

const createInteraction = (interactionGroup: any, index: number, data: any) => {
  const scale = data.scale;
  const location = data.location;
  const cPlan = new CPlan({
    width: initSize.x,
    height: initSize.y,
    url: 'sourceType/interactive-area.png',
    areaIndex: index,
    sizeScale: new Vector2(data.scale.x, data.scale.z),
    transparent: true,
    reversalDepthTest: true,
  }); // 生成对象
  editSceneData.value.interactionDtoList[index] = {
    ...JSON.parse(JSON.stringify(data)),
    uuid: cPlan.uuid,
    materialMetaDtoList: [],
  };
  interactionGroup.name = cPlan.uuid;
  interactionGroup.add(cPlan);
  scene.add(interactionGroup);
  interactionActiveCancle(cPlan);
  // 互动区域位移初始化 TODO 这里的位置有点问题
  cPlan.setVertexs(
    initSize.x * scale.x,
    initSize.y * scale.z,
    worldToScreenVector(new Vector3(location.x, location.y, location.z))
  );
  // 互动区域旋转初始化
  const rotation = data.rotation.y;
  if (rotation && !cPlan.userData.rotate) {
    cPlan.rotateY(-rotation);
    cPlan.userData.rotate = rotation;
    const csprite: any = cPlan.getObjectByName('csprite');
    if (csprite) {
      csprite.userData.oldRotate = 0;
      csprite.setRotate(-rotation);
    }
  }
  store.state.editSceneData.interactionDtoList[index] = JSON.parse(
    JSON.stringify(editSceneData.value.interactionDtoList[index])
  );
};

onMounted(() => {
  scene = (window as any).scene;
});

watch(
  () => store.state.editSceneData,
  (newState: any) => {
    if (newState.changeTime == 0) {
      // 保存之后重置数据
      store.state.editSceneData.changeTime = editSceneData.value.changeTime;
      editSceneData.value = JSON.parse(JSON.stringify(store.state.editSceneData));
      return;
    }
    if (newState.changeTime && newState.changeTime != editSceneData.value.changeTime) {
      editSceneData.value.changeTime = newState.changeTime;
      if (newState.changeType == 'deleteInteraction') {
        // 删除互动区域
        const currentDeleteData: any = { ...store.state.currentDeleteData };
        let curInteraction = {
          ...editSceneData.value.interactionDtoList[currentDeleteData.deleteIndex],
        };
        if (store.state.activeAreaUuid == curInteraction.uuid) {
          store.state.activeAreaUuid = '';
          curInteraction.materialMetaDtoList.forEach((d: any) => {
            if (d.uuid == store.state.activeMaterial) {
              store.state.activeMaterial = '';
            }
          });
        }
        const group: any = scene.getObjectByName(curInteraction.uuid);

        // 删除文字标签
        group.traverse((e: any) => {
          if (e.element) {
            e.parent?.remove(e);
          }
        });
        // 删除互动区域元素
        scene.remove(group);
        editSceneData.value.interactionDtoList.splice(currentDeleteData.deleteIndex, 1);
        store.state.randerTotal += 1;
        return;
      } else if (newState.changeType == 'deleteSource') {
        const currentDeleteData: any = { ...store.state.currentDeleteData };
        let deleteIndex = currentDeleteData.deleteIndex;
        const deleteIndexs = deleteIndex.split(',');
        if (deleteIndexs.length == 2) {
          editSceneData.value.interactionDtoList[deleteIndexs[0]].materialMetaDtoList.splice(
            deleteIndexs[1],
            1
          )[0];
        } else {
          editSceneData.value.outerMaterialMetaDtoList.splice(deleteIndexs[0], 1)[0];
        }
        if (store.state.activeMaterial == currentDeleteData.uuid) {
          store.state.activeMaterial = '';
        }
        const group: any = scene.getObjectByName(currentDeleteData.uuid);
        // 删除文字标签
        group.traverse((e: any) => {
          if (e.element) {
            e.parent?.remove(e);
          }
        });
        // 删除互动区域元素
        group.parent?.remove(group);
        store.state.randerTotal += 1;
        return;
      }
      const oldOuterMaterialMetaDtoList = editSceneData.value.outerMaterialMetaDtoList || [];
      const currentOuterMaterialMetaDtoList = newState.outerMaterialMetaDtoList || [];
      const oldInteractionDtoList = editSceneData.value.interactionDtoList || [];
      const currentInteractionDtoList = newState.interactionDtoList || [];
      currentOuterMaterialMetaDtoList.forEach((ele: any, index: number) => {
        const oldEle = oldOuterMaterialMetaDtoList[index];
        if (oldEle) {
          oldOuterMaterialMetaDtoList[index].elementName = ele.elementName;
        }
        if (!oldEle) {
          createModel(ele, 'public', index);
        } else if (JSON.stringify(oldEle.location) !== JSON.stringify(ele.location)) {
          (scene.getObjectByName(ele.uuid) as any).setVertex(
            new Vector3(ele.location.x, ele.location.y, ele.location.z)
          );
          oldOuterMaterialMetaDtoList[index].location = { ...ele.location };
          // diff location 移动
        } else if (JSON.stringify(oldEle.rotation) !== JSON.stringify(ele.rotation)) {
          // diff rotation 旋转
          nextTick(() => {
            oldOuterMaterialMetaDtoList[index].rotation = {
              ...store.state.editSceneData.outerMaterialMetaDtoList[index].rotation,
            };
            oldOuterMaterialMetaDtoList[index].metaInfo =
              store.state.editSceneData.outerMaterialMetaDtoList[index].metaInfo;
          });
        } else if (JSON.stringify(oldEle.scale) !== JSON.stringify(ele.scale)) {
          // diff scale 缩放
          nextTick(() => {
            oldOuterMaterialMetaDtoList[index].scale = {
              ...store.state.editSceneData.outerMaterialMetaDtoList[index].scale,
            };
          });
        }
      });
      currentInteractionDtoList.forEach((ele: any, index: number) => {
        const cPlan: any = scene.getObjectByName(ele.uuid)?.children[0];
        // 互动区域移动
        if (
          cPlan &&
          JSON.stringify(oldInteractionDtoList[index]?.location) !== JSON.stringify(ele.location)
        ) {
          const point = new Vector3(ele.location.x, ele.location.y, ele.location.z);
          const point2D = worldToScreenVector(point);
          cPlan.setVertexs(initSize.x * ele.scale.x, initSize.y * ele.scale.z, point2D);
          oldInteractionDtoList[index].location = { ...ele.location };
        }
        // 互动区域旋转
        if (
          cPlan &&
          JSON.stringify(oldInteractionDtoList[index]?.rotation) !== JSON.stringify(ele.rotation)
        ) {
          const oldRotate = oldInteractionDtoList[index].rotation.y;
          const newRotate = ele.rotation.y;
          cPlan.rotateY(oldRotate - newRotate);
          const currentRotate = cPlan.children[1].children[0]; // 旋转图标
          currentRotate.userData.oldRotate = oldRotate;
          currentRotate.setRotate(-newRotate);
          oldInteractionDtoList[index].rotation.y = newRotate;
        }
        // 互动区域缩放
        if (
          cPlan &&
          JSON.stringify(oldInteractionDtoList[index]?.scale) !== JSON.stringify(ele.scale)
        ) {
          const scale = ele.scale;
          const position = cPlan.userData.center.clone();
          cPlan.setVertexs(initSize.x * scale.x, initSize.y * scale.z, position);
          cPlan.setUV(scale.x, scale.z);
          oldInteractionDtoList[index].scale = { ...scale };
        }
        if (ele.materialMetaDtoList && ele.materialMetaDtoList.length) {
          ele.materialMetaDtoList.forEach((e: any, i: number) => {
            const oldEle = oldInteractionDtoList[index]?.materialMetaDtoList
              ? oldInteractionDtoList[index].materialMetaDtoList[i]
              : null;
            if (oldEle) {
              oldInteractionDtoList[index].materialMetaDtoList[i].elementName = e.elementName;
            }
            if (!oldEle) {
              createModel(e, ele.uuid, index, i, ele);
            } else if (JSON.stringify(oldEle.location) !== JSON.stringify(e.location)) {
              (scene.getObjectByName(e.uuid) as any).setVertex(
                new Vector3(e.location.x, e.location.y, e.location.z)
              );
              oldInteractionDtoList[index].materialMetaDtoList[i].location = { ...e.location };
              // diff location 移动
            } else if (JSON.stringify(oldEle.rotation) !== JSON.stringify(e.rotation)) {
              // diff rotation 旋转
              nextTick(() => {
                oldInteractionDtoList[index].materialMetaDtoList[i].rotation = {
                  ...store.state.editSceneData.interactionDtoList[index].materialMetaDtoList[i]
                    .rotation,
                };
                oldInteractionDtoList[index].materialMetaDtoList[i].metaInfo =
                  store.state.editSceneData.interactionDtoList[index].materialMetaDtoList[
                    i
                  ].metaInfo;
              });
            } else if (JSON.stringify(oldEle.scale) !== JSON.stringify(e.scale)) {
              // diff scale 缩放
              nextTick(() => {
                oldInteractionDtoList[index].materialMetaDtoList[i].scale = {
                  ...store.state.editSceneData.interactionDtoList[index].materialMetaDtoList[i]
                    .scale,
                };
              });
            }
          });
        } else if (!ele.uuid) {
          addNewInteraction(index, ele);
        }
      });
    }
  }
);

// 初始化时候生成模型用一下
watch(
  () => props.sceneData,
  (newState: any) => {
    editSceneData.value = { ...newState, outerMaterialMetaDtoList: [], interactionDtoList: [] };
    const currentOuterMaterialMetaDtoList = newState.outerMaterialMetaDtoList;
    const currentInteractionDtoList = newState.interactionDtoList;
    if (currentOuterMaterialMetaDtoList) {
      currentOuterMaterialMetaDtoList.forEach((ele: any, index: number) => {
        createModel(ele, 'public', index);
      });
    }

    if (currentInteractionDtoList) {
      currentInteractionDtoList.forEach((ele: any, index: number) => {
        if (ele.materialMetaDtoList && ele.materialMetaDtoList.length) {
          ele.materialMetaDtoList.forEach((e: any, i: number) => {
            createModel(e, ele.uuid, index, i, ele);
          });
        } else {
          addNewInteraction(index, ele);
        }
      });
    }
  }
);

watch(
  () => store.state.activeAreaUuid,
  (newState) => {
    const interactionObject = scene.getObjectByName(newState);
    const oldInteractionObject = scene.getObjectByName(activeInteractionName.value);
    if (newState) {
      interactionActive(interactionObject?.children[0]);
    } else {
      if (activeInteractionName.value) {
        interactionActiveCancle(oldInteractionObject?.children[0]);
      }
    }
  }
);

watch(
  () => store.state.activeMaterial,
  (newState) => {
    if (!newState || activeMaterialName.value) {
      materialActiveCancle(scene.getObjectByName(activeMaterialName.value));
    }
    if (newState) {
      materialActive(scene.getObjectByName(newState));
      activeMaterialName.value = newState;
    }
  }
);

defineExpose({
  exitEdit,
});
</script>
<style scoped lang="less">
.edit-box {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
}
</style>
