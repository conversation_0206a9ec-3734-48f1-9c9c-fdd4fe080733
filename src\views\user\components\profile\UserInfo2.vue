<template>
  <div class="modal">
    <div class="modal-content" :class="{ smallModal: [6, 7].includes(userType) }">
      <div class="modal-content-title">
        <div>用户信息</div>
        <div class="icon iconfont icon-close" @click="changeState"></div>
      </div>
      <div class="modal-form">
        <div class="user-info-box">
          <div class="user-info">
            <div>
              <div class="user-info-img">
                <upload-template
                  class-style="avatar-uploader"
                  :baseURL="baseURL"
                  :beforeUpload="beforeUpload"
                  :handleAvatarSuccess="handleAvatarSuccess">
                  <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                  <div class="avatar-icon"></div>
                </upload-template>
              </div>
              <el-tooltip :content="packageInfo.base.packageName" placement="bottom" effect="dark">
                <!-- <div class="version">{{ packageInfo.base.packageName }}</div> -->
              </el-tooltip>
            </div>
            <div>
              <div class="corporate-name">{{ props.userType == 6 ? '个人体验版' : '定制版' }}</div>
              <div class="package-time" v-if="![6, 7].includes(props.userType)">
                <span>有效期：</span>
                <span style="color: #1e1e1e">
                  {{ getDayTime(packageInfo.base.packageStartTime).split('-').join('.') }} ~
                </span>
                <span style="color: #1e1e1e">
                  {{ getDayTime(packageInfo.base.packageEndTime).split('-').join('.') }}
                </span>
              </div>
            </div>
          </div>
          <div class="logout">
            <div @click="router.push('/login')">退出登录</div>
          </div>
        </div>
        <div class="information">
          <div class="title" v-if="![6, 7].includes(userType)">企业信息</div>
          <div class="information-info">
            <div>
              <div v-if="![6, 7].includes(userType)" class="item-info">
                <span class="information-info-list-name">企业名称</span>
                <span class="bold-text">{{ packageInfo.orgnizationInfo.orgnizationName }}</span>
              </div>
              <div class="item-info">
                <span class="information-info-list-name">账号</span>
                <span class="bold-text">{{ compute_ }}</span>
              </div>
              <div class="item-info">
                <span class="information-info-list-name">密码</span>
                <span class="bold-text">
                  <span v-if="!isView">******</span>
                  <span v-if="isView">{{ pass_ }}</span>
                  <span class="password-view" @click="isView = !isView">
                    <img v-if="isView" src="@/assets/images/icon/view.png" />
                    <img v-if="!isView" src="@/assets/images/icon/view_off.png" />
                  </span>
                  <span class="update-passWord" @click="updatePassWord">更新密码</span>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="service-pack" :class="{ njyjxrPack: ![6, 7].includes(userType) }">
          <div class="title">产品服务信息</div>
          <div class="package-box">
            <div>
              <div class="title">基础服务</div>
              <div>
                <div>项目数量</div>
                <div>{{ packageData.sceneNum }}个</div>
              </div>
              <div>
                <div>素材总容量</div>
                <div>{{ packageData.materialUploadSize }}GB</div>
              </div>
              <div>
                <div>单个素材上限</div>
                <div>{{ packageData.singleUploadSize }}MB</div>
              </div>
              <div v-if="props.userType == 6">
                <div>访问次数</div>
                <div>每天100次</div>
              </div>
              <div v-if="props.userType != 6">
                <div>扩展访问次数</div>
                <div>
                  {{ packageData.shareVisitCount ? `${packageData.shareVisitCount}次` : '-' }}
                </div>
              </div>
              <div>
                <div>空间容量</div>
                <div>
                  {{ packageInfo.base.spaceAr ? `${packageData.spacePicTotalNum}张` : '-' }}
                </div>
              </div>
              <div>
                <div>默认并发数量</div>
                <div>{{ packageInfo.base.spaceAr ? (packageData.vpsQps || 5) + '个' : '-' }}</div>
              </div>
              <div>
                <div>扩展并发数量</div>
                <div>{{ packageData.availabeQps ? `${packageData.availabeQps}个` : '-' }}</div>
              </div>
            </div>
            <div>
              <div class="title">支持项目类型</div>
              <div>
                <div>【眼镜端】空间AR</div>
                <div>{{ packageInfo.base.spaceAr ? '支持' : '-' }}</div>
              </div>
              <div>
                <div>【移动端】空间AR</div>
                <div>{{ packageInfo.base.spaceAr ? '支持' : '-' }}</div>
              </div>
              <div>
                <div>【小程序】空间AR</div>
                <div>{{ packageInfo.base.spaceAr ? '支持' : '-' }}</div>
              </div>
              <div>
                <div>【小程序】平面AR</div>
                <div>{{ packageInfo.base.wxPlaneAr ? '支持' : '-' }}</div>
              </div>
              <div>
                <div>【小程序】图像AR</div>
                <div>{{ packageInfo.base.wxPicAr ? '支持' : '-' }}</div>
              </div>
              <div>
                <div>【小程序】单场景AR</div>
                <div>{{ packageInfo.base.wxSingleSceneAr ? '支持' : '-' }}</div>
              </div>
              <div>
                <div>【小程序】人脸AR</div>
                <div>{{ packageInfo.base.wxFaceAr ? '支持' : '-' }}</div>
              </div>
              <div>
                <div>【小程序】身体AR</div>
                <div>{{ packageInfo.base.wxBodyAr ? '支持' : '-' }}</div>
              </div>
              <div>
                <div>【小程序】手势AR</div>
                <div>{{ packageInfo.base.wxHandAr ? '支持' : '-' }}</div>
              </div>
            </div>
          </div>
          <div
            class="qps-menu"
            v-if="packageData.availabeQps"
            :style="{ left: 220 + String(packageData.availabeQps).length * 9 + 'px' }">
            <div
              class="qps-menu-box"
              :style="{ top: packageInfo.expands.filter((e: any)=> e.vpsQps).length > 1 ? '-94px' : '-30px' }">
              <div
                v-for="(item, index) in packageInfo.expands.filter((e: any)=> e.vpsQps)"
                :key="index">
                <div>{{ getDayTime(item.createTime) }}扩展</div>
                <div>
                  <span>{{ item.vpsQps }}个/{{ item.vpsQps / 5 }}组</span>
                  <span style="font-size: 12px; font-weight: 400">
                    {{ getDayTime(item.qpsUseStartTime) }} ~ {{ getDayTime(item.qpsUseEndTime) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="view-agree" :class="{ newView: [6, 7].includes(userType) }">
        <span @click="showAgree = 1">《服务协议》</span>
        、
        <span @click="showAgree = 2">《隐私政策》</span>
      </div>
    </div>
  </div>
  <consent-agreement
    v-if="showAgree"
    :show-type="showAgree"
    :handle-hide="() => (showAgree = 0)"
    :hide-mask="true"></consent-agreement>
  <tips-view></tips-view>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import TipsView from '@/components/TipsView.vue';
import { getOrgnizationPackage } from '@/api';
import { useRouter } from 'vue-router';
import ConsentAgreement from '@/components/ConsentAgreement.vue';
import { getDayTime } from '@/utils';
import { truncate } from 'fs/promises';
import type { UploadProps } from 'element-plus';
import { getOssAccessPath } from '@/api';
import { useStore } from 'vuex';
import UploadTemplate from '@/views/template/components/UploadTemplate.vue';

const router = useRouter();
const showAgree = ref(0);
const isView = ref(false);
const packageInfo: any = ref({ base: {}, expands: [], orgnizationInfo: {} });
const baseURL =
  (process.env.NODE_ENV === 'production' ? '/api' : '/api1') + '/authority/addprofilePicture'; // 基础url
const expireDay = ref('');
const singleUploadSize = ref('');

const packageData: any = ref({
  sceneNum: 50,
  materialUploadSize: 30,
  singleUploadSize: 100,
  shareVisitCount: 0,
  spacePicTotalNum: 1660,
  vpsQps: 5,
  availabeQps: 0,
});

const props = defineProps({
  changeEvent: {
    default: null,
    type: Function,
  },
  data: {
    default: null,
    type: Object,
  },
  userType: {
    default: 6,
  },
});

const headerStyle = {
  height: '32px',
  background: 'rgba(230,237,247,0.3)',
  border: '1px solid #E6EDF7',
  textAlign: 'center',
};

const tableData = ref([
  {
    prop: 'itemCount',
    service: '项目数量',
    all: true,
  },
  {
    prop: 'roomLimit',
    service: '空间限制',
  },
  {
    prop: 'assetsRoom',
    service: '素材空间',
    all: true,
  },
  {
    prop: 'onlyAssetLimit',
    service: '单个素材上传上限',
    all: true,
  },
  {
    prop: 'packageTime',
    service: '套餐时长',
  },
  {
    prop: 'roomPlatform',
    service: '大空间定位平台支持',
  },
  {
    prop: 'roomCount',
    service: '空间数量',
  },
]);
const pass_ = ref('');
const imageUrl = ref('');

const store = useStore();

const beforeUpload = (uploadFile) => {
  if (uploadFile.size / 1024 / 1024 > 5) {
    store.state.showTips = '大小不超过5MB';
    return false;
  }
};

const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  imageUrl.value = URL.createObjectURL(uploadFile.raw!);
  store.state.profilePic = imageUrl.value;
  store.state.profilePicture = response.data;
};
const changeState = () => {
  props.changeEvent('');
};

const compute_ = computed(() => {
  return [6, 7].includes(props.userType)
    ? localStorage.getItem('phoneNo')
    : localStorage.getItem('userName');
});

const updatePassWord = () => {
  props.changeEvent('password');
};

onMounted(() => {
  packageInfo.value.base = props.data.userBindPackageDto;
  packageInfo.value.expands = props.data.userBindPackageExtensionDtos || [];
  packageInfo.value.orgnizationInfo =
    props.data.orgnizationDto || JSON.parse(sessionStorage.getItem('packageInfoDto') || '{}');
  packageData.value.sceneNum = props.data.userBindPackageDto.sceneNum || 0;
  packageData.value.materialUploadSize = props.data.userBindPackageDto.materialUploadSize
    ? Math.round(props.data.userBindPackageDto.materialUploadSize / 1024)
    : 0;
  packageData.value.singleUploadSize = props.data.userBindPackageDto.singleUploadSize || 0;
  packageData.value.vpsQps = props.data.userBindPackageDto.vpsQps || 0;
  packageInfo.value.expands.forEach((e: any) => {
    packageData.value.sceneNum += e.sceneNum;
    packageData.value.materialUploadSize += Math.round(e.materialUploadSize / 1024);
    packageData.value.singleUploadSize += e.singleUploadSize;
    packageData.value.shareVisitCount += e.shareExtensionVisitCount;
    packageData.value.spacePicTotalNum += e.spacePicTotalNum;
    packageData.value.availabeQps += e.vpsQps;
  });
  console.log(packageData.value);

  if (props.data.userDto?.profilePicture) {
    getOssAccessPath({ key: props.data.userDto.profilePicture }).then((res: any) => {
      imageUrl.value = res.data;
    });
  } else {
    imageUrl.value = require('@/assets/images/ailongmask.png');
  }
  singleUploadSize.value = packageInfo.value.base.singleUploadSize;
  expireDay.value = packageInfo.value.base.expireDay;

  packageInfo.value.resultExpand = {
    arSceneNum: 0,
    planeSceneNum: 0,
    materialUploadSize: 0,
    spacePicTotalNum: 0,
  };
  packageInfo.value.expands.forEach((item) => {
    if (item.arSceneNum) {
      packageInfo.value.resultExpand.arSceneNum += item.arSceneNum;
    }
    if (item.planeSceneNum) {
      packageInfo.value.resultExpand.planeSceneNum += item.planeSceneNum;
    }
    if (item.materialUploadSize) {
      packageInfo.value.resultExpand.materialUploadSize += Math.round(
        item.materialUploadSize / 1024
      );
    }
    if (item.spacePicTotalNum) {
      packageInfo.value.resultExpand.spacePicTotalNum += item.spacePicTotalNum;
    }
  });

  if ([6, 7].includes(props.userType)) {
    tableData.value = tableData.value.filter((item) => item.all);
  }

  pass_.value = props.data.userDto.password || '';
});

watch(
  () => props.data,
  () => {
    packageInfo.value.base = props.data.userBindPackageDto;
    packageInfo.value.expands = props.data.userBindPackageExtensionDtos;
    packageInfo.value.orgnizationInfo = props.data.orgnizationDto || {};
  },
  { deep: true }
);
</script>
<style scoped lang="less">
:deep(.el-table__cell) {
  border-right: none !important;
}

.newView {
  transform: translateY(-7px);
}

:deep(.rowStyle) {
  height: 60px !important;
  border-left: 1px solid #e6edf7;
  border-right: 1px solid #e6edf7;
  border-bottom: 1px solid #e6edf7;
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;
}

.samllWidth {
  min-width: 32px !important;
  width: 32px !important;
}

.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 11;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .cell-center {
    text-align: center;
  }

  .item-count {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .tiyanMain {
    height: 40px;
  }

  .height120 {
    height: 120px !important;
    line-height: 120px;
  }

  .maxHeight {
    height: 60px !important;
    line-height: 60px !important;
  }

  .newLine {
    line-height: 40px !important;
  }

  .modal-content {
    width: 664px;
    height: 778px;
    max-height: 90%;
    background: #fff;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    border: 1px solid #edeff2;
    overflow: hidden;

    .modal-content-title {
      height: 76px;
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;

      & > div:first-child {
        font-weight: bold;
        font-size: 18px;
        color: #1e1e1e;
      }

      .icon-close {
        font-size: 26px;
        cursor: pointer;
        font-weight: 400;

        &:hover {
          color: #2e76ff;
        }
      }
    }

    .modal-form {
      position: relative;
      width: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      padding: 0 20px;

      .user-info-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .logout {
          position: relative;
          font-weight: 500;
          font-size: 14px;
          color: #2e76ff;
          cursor: pointer;

          &::before {
            content: '';
            width: 16px;
            height: 16px;
            position: absolute;
            left: -20px;
            top: 2px;
            background-image: url(~@/assets/images/icon/logout-1.png);
            background-size: 100% 100%;
          }

          &:hover {
            color: rgba(18, 81, 200, 1);

            &::before {
              background-image: url(~@/assets/images/icon/logout-1A.png);
              background-size: 100% 100%;
            }
          }
        }

        .user-info {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 12px;

          & > div:first-child {
            position: relative;
            margin-right: 20px;
          }

          .user-info-img {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;

            div {
              width: 65px;
              height: 65px;
            }

            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
          }

          .avatar-uploader .avatar-icon {
            position: absolute;
            width: 24px;
            height: 24px;
            left: 47px;
            top: 46px;
          }

          .avatar-icon {
            background-image: url(~@/assets/images/icon/renew.png);
            background-size: 100% 100%;

            &:hover {
              background-image: url(~@/assets/images/icon/renewA.png);
              background-size: 100% 100%;
            }
          }

          .version {
            position: absolute;
            left: 50%;
            top: 73px;
            width: 69px;
            height: 25px;
            line-height: 24px;
            text-align: center;
            background: linear-gradient(225deg, #0375ff 0%, #3c96ff 100%);
            box-shadow: inset -1px -1px 0px 0px rgba(255, 255, 255, 0.2),
              inset 1px 1px 0px 0px rgba(11, 91, 225, 0.4);
            border-radius: 13px;
            margin-left: -35px;
            font-size: 13px;
            font-weight: 400;
            color: #ffffff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .corporate-name {
            font-weight: bold;
            font-size: 14px;
            color: #1e1e1e;
          }

          .package-time {
            font-weight: 500;
            font-size: 14px;
            color: #797979;
            margin-top: 4px;
          }

          .corporate-email {
            font-size: 20px;
            font-weight: 400;
            color: #000000;
          }

          & > div {
            text-align: left;
          }
        }
      }

      .qps-menu {
        position: relative;
        position: absolute;
        left: 215px;
        bottom: 83px;
        width: 16px;
        height: 16px;
        background-image: url(~@/assets/images/icon/menu-icon.png);
        background-size: 100% 100%;
        padding: 0;
        color: #fff;
        text-align: left;
        font-size: 14px;

        .qps-menu-box {
          position: absolute;
          left: 26px;
          top: -94px;
          width: 240px;
          max-height: 193px;
          background: rgba(0, 0, 0, 0.7);
          border-radius: 4px 4px 4px 4px;
          padding: 12px;
          padding-right: 0;
          box-sizing: border-box;
          overflow: hidden;
          overflow-y: auto;
          display: none;

          & > div {
            margin-bottom: 8px;

            & > div {
              display: flex;
              justify-content: flex-start;
              align-items: center;

              & > span:first-child {
                margin-right: 10px;
              }
            }

            & > div:first-child {
              height: 21px;
              line-height: 21px;
              font-weight: bold;
              padding-left: 4px;
              border-left: 2px solid #2e76ff;
              margin-bottom: 8px;
            }

            span {
              display: inline-block;
              vertical-align: middle;
            }
          }

          & > div:last-child {
            margin-bottom: 0;
          }
        }

        &:hover {
          .qps-menu-box {
            display: block;
          }

          &::after {
            display: inline-block;
          }
        }

        &::after {
          content: '';
          position: absolute;
          left: 14px;
          top: 2px;
          border: 6px solid transparent;
          border-right-color: rgba(0, 0, 0, 0.7);
          display: none;
        }
      }

      .information,
      .service-pack {
        position: relative;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
        border: 2px solid #ffffff;
        box-sizing: border-box;

        .package-box {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-top: 12px;

          & > div {
            width: 300px;
            border: 1px solid #e6edf7;
            border-radius: 4px;
            overflow: hidden;
            font-weight: 400;
            font-size: 14px;
            color: #1e1e1e;

            .title {
              width: 300px;
              height: 32px;
              line-height: 32px;
              background: rgba(230, 237, 247, 0.3);
              text-align: left;
              padding-left: 12px;
              font-weight: 400;
              font-size: 12px;
              color: #797979;

              & > div {
                height: 35px;
                background-color: 35px;
              }
            }

            & > div:not(.title) {
              border-top: 1px solid #e6edf7;
              height: 36px;
              box-sizing: border-box;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              text-align: left;

              & > div {
                height: 35px;
                line-height: 35px;
                padding-left: 12px;
              }

              & > div:first-child {
                width: 60%;
              }
            }
          }

          & > div:last-child > div:not(.title) > div {
            padding-left: 5px;
          }
        }

        & > .title {
          position: relative;
          font-weight: bold;
          font-size: 14px;
          color: #1e1e1e;
          text-align: left;
          line-height: 1;
        }

        .information-info {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          text-align: left;
          font-size: 16px;
          font-weight: 400;
          color: #333333;

          .item-info {
            margin-top: 12px;

            & > span:first-child {
              min-width: 56px;
              text-align: left;
              margin-right: 8px;
            }
          }

          .information-info-list-name {
            display: inline-block;
            font-weight: 500;
            font-size: 14px;
            color: #797979;
          }

          .bold-text {
            font-weight: 500;
            font-size: 14px;
            color: #1e1e1e;

            & > span {
              vertical-align: middle;
            }
          }

          .password-view {
            margin-left: 5px;
            cursor: pointer;
            font-size: 0;
            height: 16px;
            display: inline-block;

            img {
              width: 16px;
              height: 16px;
            }
          }

          .right {
            width: 62px;
          }

          .update-passWord {
            position: relative;
            width: 80px;
            height: 24px;
            line-height: 24px;
            background: rgba(46, 118, 255, 0.1);
            border-radius: 12px;
            display: inline-block;
            font-size: 14px;
            font-weight: 400;
            color: #1e84ff;
            text-align: center;
            margin-left: 10px;
            padding-left: 19px;
            box-sizing: border-box;
            cursor: pointer;

            &::before {
              content: '';
              position: absolute;
              left: 11px;
              top: 5px;
              width: 14px;
              height: 14px;
              background-image: url(~@/assets/images/icon/modify.png);
              background-size: 100% 100%;
            }

            &:hover {
              background-color: rgba(46, 118, 255, 0.27);
            }
          }
        }

        .service-pack-info {
          margin-top: 12px;
          // font-size: 16px;
          font-weight: 400;
          color: #333333;
          width: 652px;
          overflow: hidden;
          overflow-x: auto;
          font-size: 0;

          .table-header {
            height: 32px;
            background: rgba(230, 237, 247, 0.3);
            font-weight: 400;
            font-size: 12px !important;
            color: #797979;
            line-height: 32px;
          }

          .table-line {
            & > div,
            & > main {
              border-bottom: 1px solid #e6edf7;
            }
          }

          & > div {
            text-align: center;
            white-space: nowrap;

            & > div {
              width: 216px;
              line-height: 40px;
              display: inline-block;
              box-sizing: border-box;
              font-size: 16px;
            }
          }

          & > div:first-child {
            font-size: 16px;
            font-weight: 500;
            color: #6f6f6f;
            margin-bottom: 5px;
          }

          & > div:last-child {
            height: 280px;
            overflow-x: hidden;
            overflow-y: scroll;
            display: flex;

            & > div:last-child {
              border: none;
            }
          }
        }
      }

      .information {
        margin-top: 25px;
      }

      .service-pack {
        margin-top: 30px;
      }
    }

    .view-agree {
      padding-top: 13px;
      text-align: left;
      padding-left: 20px;
      z-index: 999;
      font-weight: 400;
      font-size: 14px;
      color: #2e76ff;
      text-align: left;
      position: relative;

      & > span {
        cursor: pointer;
        text-decoration-line: underline;
      }
    }
  }

  .smallModal {
    height: 724px;
  }
}
</style>
