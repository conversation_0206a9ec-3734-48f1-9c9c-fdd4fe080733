<template>
  <div :id="canvasId" class="canvas-view"></div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue'
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls"
import { Vector3, Scene, OrthographicCamera, WebGLRenderer, Vector2, Group, AmbientLight, PlaneGeometry, MeshBasicMaterial, Mesh, TextureLoader } from 'three'
import { CPoint, CLine } from '@/core/primitives'
import { CPointAttrs } from '@/core/primitives/CPoint'
import { CLineAttrs } from '@/core/primitives/CLine'
import { screenToWorldVector } from "@/utils";
import { CSS2DRenderer } from "@/core/libs/CSS2DRenderer";


const props = defineProps({
  canvasId: {
    default: 'canvas',
    type: String
  },
  handleClick: {
    default: null,
    type: Function
  },
  handleMouseMove: {
    default: null,
    type: Function
  },
  handleMouseLeave: {
    default: null,
    type: Function
  },
  handleDblClick: {
    default: null,
    type: Function
  },
  handleMouseDown: {
    default: null,
    type: Function
  },
  handleMouseUp: {
    default: null,
    type: Function
  },
  handleKeyup: {
    default: null,
    type: Function
  },
  isPlanStyle: {
    default: false,
    type: Boolean
  }
})

let camera: OrthographicCamera;
let width: number, height: number;
let scene: Scene;
let mouseTime = 0;
let renderer: any;
let labelRenderer: any;

onMounted(() => {
  scene = new Scene();
  (window as any).scene = scene;
  let canvas = document.getElementById(props.canvasId)
  width = canvas?.getBoundingClientRect().width || 0
  height = canvas?.getBoundingClientRect().height || 0
  let k = width / height
  let s = 15

  const markGroup = new Group() // 用来存放标注
  markGroup.name = 'markGroup-init'
  scene.add(markGroup)


  camera = new OrthographicCamera(-s * k, s * k, s, -s, 1, 1000)
  camera.position.set(0, 750, 0)
  camera.lookAt(new Vector3(0, 0, 0));
  (window as any).camera = camera;


  window.sessionStorage.setItem('width', width + '');
  window.sessionStorage.setItem('height', height + '');

  // 辅助线
  // const axesHelper = new AxesHelper(2);
  // axesHelper.name = 'axesHelper-init'
  // scene.add(axesHelper);


  renderer = new WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setClearColor(0xC3C7CB, 1)
  canvas?.appendChild(renderer.domElement)
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.render(scene, camera);
  (window as any).renderer = renderer;

  // 添加控制器
  const controls = new OrbitControls(camera, renderer.domElement);
  controls.maxZoom = 2.5
  controls.minZoom = 0.5
  // 使用控制器
  // controls.enableDamping = true;
  // controls.enabled = false; // 页面禁止转动
  controls.enableRotate = false;
  // controls.enableZoom = false;

  // 环境光
  const ambient = new AmbientLight(0xffffff, 1);
  ambient.name = 'ambient-init'
  scene.add(ambient);

  (window as any).controls = controls;

  labelRenderer = new CSS2DRenderer();
  labelRenderer.setSize(width, height);
  labelRenderer.domElement.style.position = 'absolute';
  // 相对鼠标的相对偏移
  labelRenderer.domElement.style.top = '0px';
  labelRenderer.domElement.style.left = '0px';
  // //设置.pointerEvents=none，以免模型标签HTML元素遮挡鼠标选择场景模型
  labelRenderer.domElement.style.pointerEvents = 'none';
  canvas?.appendChild(labelRenderer.domElement);
  labelRenderer.render(scene, camera)

  animate()
  function animate() {
    if (props.isPlanStyle) {
      renderer.render(scene, camera)
      labelRenderer.render(scene, camera)
      if (renderer.domElement.style.width == '0px') {
        resizeCanvas()
      }
    }
    // 使用 requestAnimationFrame 执行动画
    requestAnimationFrame(animate)
  }

  // 双击选中
  canvas?.addEventListener('dblclick', () => {
    props.handleDblClick && props.handleDblClick()
  }, false);



  // 鼠标落下
  canvas?.addEventListener('mousedown', mousedownCanvas);

  // 鼠标抬起
  canvas?.addEventListener('mouseup', mouseupCanvas);

  // 滑动悬浮部分逻辑
  canvas?.addEventListener('mousemove', mousemoveCanvas, false);

  // 滑动悬浮部分逻辑
  canvas?.addEventListener('mouseleave', mouseLeaveCanvas, false);

  // 键盘操作
  document.addEventListener('keyup', keyupEvent, false);

  window.addEventListener('resize', resizeCanvas, false)

  const geometry = new PlaneGeometry(100, 100);
  const material = new MeshBasicMaterial({ side: 2 });
  const t = new TextureLoader().load('sourceType/ground.png');
  material.map = t;
  const plane = new Mesh(geometry, material);
  plane.name = 'ground-init'
  plane.lookAt(0, 1, 0)
  scene.add(plane);


  // const cmeshpoint = new CMeshPoint()
  // cmeshpoint.name = 'meshpoint-init'
  // // cmeshpoint.material.color = 0x55dd55
  // cmeshpoint.rotateY(2)
  // scene.add(cmeshpoint);
})

onUnmounted(() => {
  let canvas = document.getElementById(props.canvasId)
  canvas?.removeEventListener('mousemove', mousemoveCanvas, false);
  canvas?.removeEventListener('mouseleave', mouseLeaveCanvas, false);
  canvas?.removeEventListener('mousedown', mousedownCanvas, false);
  canvas?.removeEventListener('mouseup', mouseupCanvas, false);
  canvas?.removeEventListener('keyup', keyupEvent, false);
})

const resizeCanvas = () => {
  if (renderer) {
    let canvas = document.getElementById(props.canvasId)
    width = canvas?.getBoundingClientRect().width || 0
    height = canvas?.getBoundingClientRect().height || 0

    renderer.setSize(width, height);
    labelRenderer.setSize(width, height);
    // 重置相机投影的相关参数
    const k = width / height;//窗口宽高比
    const s = 15
    camera.left = -s * k;
    camera.right = s * k;
    camera.top = s;
    camera.bottom = -s;
    camera.updateProjectionMatrix();
  }
}

// 画点
const drawPoint = (data: CPointAttrs, name?: string) => {
  const cpoint = new CPoint({ ...data })
  cpoint.name = name || '';
  scene.add(cpoint)
}
// 画线
const drawLine = (data: CLineAttrs, name?: string) => {
  const cline = new CLine({ ...data, color: 0x0d5ca7, lineWidth: 2 })
  cline.name = name || '';
  scene.add(cline)
  return cline;
}

// 画虚线
const drawDashLine = (data: CLineAttrs, name?: string, lineWidth?: number) => {
  const cline = new CLine({ ...data, color: 0x0EFFE6, lineWidth: lineWidth || 4, dashed: true, dashSize: 0.5, gapSize: 0.5 })
  cline.name = name || '';
  scene.add(cline)
  return cline;
}

// 鼠标移动事件
const mousemoveCanvas = (e: any) => {
  const worldVector = screenToWorldVector(e.clientX, e.clientY)
  props.handleMouseMove && props.handleMouseMove(worldVector, new Vector2(e.clientX, e.clientY));
}

// 鼠标离开canvas事件
const mouseLeaveCanvas = (e: any) => {
  props.handleMouseLeave && props.handleMouseLeave();
}

// 鼠标点下事件
const mousedownCanvas = (e: any) => {
  if (props.isPlanStyle) {
    const worldVector = screenToWorldVector(e.clientX, e.clientY)
    props.handleMouseDown && props.handleMouseDown(worldVector)
    mouseTime = new Date().getTime()
  }
}

// 鼠标抬起事件
const mouseupCanvas = (e: any) => {
  let currentTime = new Date().getTime();
  const worldVector = screenToWorldVector(e.clientX, e.clientY)
  if (currentTime - mouseTime < 300) {
    props.handleClick && props.handleClick(worldVector)
  }
  props.handleMouseUp && props.handleMouseUp(worldVector)
  mouseTime = new Date().getTime()
}

// 键盘事件
const keyupEvent = (e: any) => {
  // 触发键盘事件
  props.handleKeyup && props.handleKeyup(e)
}

const clearMesh = (mesh: any) => {
  scene.remove(mesh)
}

defineExpose({
  drawPoint,
  drawLine,
  drawDashLine,
  clearMesh
})
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
h3 {
  margin: 40px 0 0;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  display: inline-block;
  margin: 0 10px;
}

a {
  color: #42b983;
}

.canvas-view {
  width: 100%;
  height: 100%;
}
</style>