<template>
  <div class="menu-list">
    <el-menu
      active-text-color="#1B7AF8"
      background-color="#FFFFFF"
      class="el-menu-vertical-demo"
      :default-active="defaultPath"
      text-color="#798092"
      @select="handleSelect"
      :router="true"
      :unique-opened="true"
    >
      <el-menu-item :index="routerList.home">
        <el-icon>
          <img
            v-if="currentPath !== '/home' && currentPath !== '/experience_home'"
            src="@/assets/images/icon/tubiao.png"
          />
          <img
            v-if="currentPath === '/home' || currentPath === '/experience_home'"
            src="@/assets/images/icon/homeA.png"
          />
        </el-icon>
        <span>首页</span>
      </el-menu-item>
      <el-menu-item index="/item_list">
        <el-icon>
          <img v-if="currentPath !== '/item_list'" src="@/assets/images/icon/project-team.png" />
          <img v-if="currentPath === '/item_list'" src="@/assets/images/icon/project-teamA.png" />
        </el-icon>
        <span>我的项目</span>
      </el-menu-item>
      <div class="template-line"></div>
      <el-menu-item :index="routerList.source_material">
        <el-icon>
          <img
            v-if="currentPath !== '/source_material' && currentPath !== '/experience_material'"
            src="@/assets/images/icon/source.png"
          />
          <img
            v-if="currentPath === '/source_material' || currentPath === '/experience_material'"
            src="@/assets/images/icon/sourceA.png"
          />
        </el-icon>
        <span>素材管理</span>
      </el-menu-item>
      <el-menu-item
        index="/spacelist"
        v-if="
          ![6, 7, 0].includes(userType) &&
          (store.state.hasSpaceAr || userInfo.packageVersion != 'V2')
        "
      >
        <el-icon>
          <img v-if="currentPath !== '/spacelist'" src="@/assets/images/icon/operate.png" />
          <img v-if="currentPath === '/spacelist'" src="@/assets/images/icon/operateA.png" />
        </el-icon>
        <span>空间管理</span>
      </el-menu-item>
      <el-menu-item
        index="/path_navigation"
        v-if="
          ![6, 7, 0].includes(userType) &&
          (store.state.hasSpaceAr || userInfo.packageVersion != 'V2')
        "
      >
        <el-icon>
          <img v-if="currentPath !== '/path_navigation'" src="@/assets/images/icon/path.png" />
          <img v-if="currentPath === '/path_navigation'" src="@/assets/images/icon/pathA.png" />
        </el-icon>
        <span>路径导航</span>
      </el-menu-item>
      <el-menu-item index="/devicelist" v-if="![6, 7, 0].includes(userType)">
        <el-icon>
          <img v-if="currentPath !== '/devicelist'" src="@/assets/images/icon/device.png" />
          <img v-if="currentPath === '/devicelist'" src="@/assets/images/icon/deviceA.png" />
        </el-icon>
        <span>设备管理</span>
      </el-menu-item>
      <div class="template-line"></div>
      <el-menu-item index="/square">
        <el-icon>
          <img v-if="currentPath !== '/square'" src="@/assets/images/icon/square-temp.png" />
          <img v-if="currentPath === '/square'" src="@/assets/images/icon/square-tempA.png" />
        </el-icon>
        <span>模板广场</span>
      </el-menu-item>
      <el-menu-item v-if="userType == 1" index="/customer">
        <el-icon>
          <img v-if="currentPath !== '/customer'" src="@/assets/images/icon/custom.png" />
          <img v-if="currentPath === '/customer'" src="@/assets/images/icon/customA.png" />
        </el-icon>
        <span>客户管理</span>
      </el-menu-item>
      <!-- <el-menu-item index="/template_manage" v-if="![6, 7, 0].includes(userType)">
        <el-icon>
          <img v-if="currentPath !== '/template_manage'" src="@/assets/images/icon/device.png" />
          <img v-if="currentPath === '/template_manage'" src="@/assets/images/icon/deviceA.png" />
        </el-icon>
        <span>模板管理</span>
      </el-menu-item> -->
      <el-menu-item index="/default_material" v-if="userType == 1">
        <el-icon>
          <img
            v-if="currentPath !== '/default_material'"
            src="@/assets/images/icon/public-asset.png"
          />
          <img
            v-if="currentPath === '/default_material'"
            src="@/assets/images/icon/default-materialA.png"
          />
        </el-icon>
        <span>公共素材</span>
      </el-menu-item>
      <el-menu-item index="/package" v-if="userType == 1">
        <el-icon>
          <img
            v-if="currentPath !== '/package'"
            src="http://njyjxr.oss-cn-shanghai.aliyuncs.com/hunkong/blue.png"
          />
          <img
            v-if="currentPath === '/package'"
            src="http://njyjxr.oss-cn-shanghai.aliyuncs.com/hunkong/blue-active.png"
          />
        </el-icon>
        <span>套餐管理</span>
      </el-menu-item>
    </el-menu>
    <div class="info_side" v-if="[6, 7].includes(userType)">
      <!-- <div class="info">
        <div class="box accunt">
          <div class="lt">当前账号</div>
          <div class="rt blue">体验账号</div>
        </div>
        <div class="box">
          <div class="lt" style="margin-right: 4px;">素材空间</div>
          <el-tooltip class="box-item" effect="dark"
            :content="`${materialStorage.userUsedStorage}MB/${materialStorage.userPackageStorage}MB`" placement="top"
            v-if="materialStorage.userPackageStorage">
            <div class="rt showTip">{{ materialStorage.userUsedStorage }}MB/{{ materialStorage.userPackageStorage }}MB
            </div>
          </el-tooltip>
        </div>
        <div class="box">
          <div class="lt">项目数量</div>
          <div class="rt">{{ sceneStorage && sceneStorage.planeArSceneUsedNum }}/3个</div>
        </div>
        <div class="box">
          <div class="lt">项目类型</div>
          <div class="rt">平面识别AR</div>
        </div>
        <div class="box" v-if="![6, 7].includes(userType)">
          <div class="lt">设备数量</div>
          <div class="rt">5个/不限</div>
        </div>
      </div> -->

      <!-- <div class="yellow" @click="upgradeAccount">升级企业版体验更多</div> -->
      <header>
        <div class="icon">
          <img src="http://njyj.oss-cn-shanghai.aliyuncs.com/arror.png" alt="" />
        </div>
        <div class="head">升级企业版</div>
      </header>
      <header>
        <div class="icon"></div>
        <div class="con">享受更多用户权益</div>
      </header>
      <div class="btn" @click="upgradeAccount">联系客服</div>
    </div>
    <WeChatQr v-if="isWechatQr" :hide-add-mask="hideMaskQr"></WeChatQr>
    <upgradeAlert v-if="isOpen" :hide-add-mask="hideAddMask"></upgradeAlert>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, onUnmounted, watch, reactive, computed } from 'vue';
import { useStore } from 'vuex';
import { getUserTypeByToken, getSceneStorage } from '@/api/index';
import upgradeAlert from '../experience/create/upgradeAlert.vue';
import WeChatQr from '@/components/experience/create/WeChatQr.vue';
import { calculateSize } from '@/utils';
import { isNumber } from 'lodash';
import { getUserStorage } from '@/api/index';

const props = defineProps({
  currentRoute: {
    default: null,
    type: Object,
  },
});

const store = useStore();

const sceneStorage = ref();
const isWechatQr = ref(false);
const defaultPath = ref('');
const currentPath = ref('');
const userType = ref(0);
const isOpen = ref(false);
const materialStorage = ref({});
const userInfo: any = ref({});
const userStorage = ref({}); //空间
const routerList = reactive({
  home: '/home',
  source_material: '/source_material',
});

watch(
  () => store.state.userInfo,
  (nv) => {
    userInfo.value = nv;
    if (isNumber(userInfo.value.materialUploadSize) && nv.packageVersion != 'V2') {
      userInfo.value.materialUploadSize = calculateSize(userInfo.value.materialUploadSize);
    }
  },
  { deep: true }
);

const upgradeAccount = () => {
  isOpen.value = true;
};

const hideAddMask = (isWechat: boolean) => {
  isOpen.value = false;
  if (isWechat) {
    isWechatQr.value = true;
  }
};

const hideMaskQr = () => {
  isWechatQr.value = false;
};

const requestGetUserStorage = () => {
  getUserStorage().then((res: any) => {
    if (res.code === 200) {
      userStorage.value = res.data;
    }
  });
};

const scneStorageSize = () => {
  getSceneStorage().then((res: any) => {
    sceneStorage.value = { ...res.data };
    store.state.storageData = {
      home: res.data,
      material: {
        userUsedStorage: res.data.materialUsedStorage,
        userPackageStorage: res.data.materialPackageStorage,
      },
      space: {
        spaceUseNum: res.data.spaceUseNum,
        packageNum: res.data.packageNum,
        singleUploadNum: res.data.singleUploadNum,
      },
    };
  });
};

watch(
  () => store.state.storageData,
  (newState) => {
    materialStorage.value = newState.material;
    console.log(materialStorage.value, 'store.state.storageData');
  },
  { deep: true }
);

onMounted(() => {
  scneStorageSize();
  defaultPath.value = window.sessionStorage.getItem('path') || '/home';
  currentPath.value = defaultPath.value;
  requestGetUserStorage();
  getUserTypeByToken().then((res: any) => {
    userType.value = res.data;

    if ([6, 7].includes(userType.value)) {
      routerList.home = '/experience_home';
      routerList.source_material = '/experience_material';
    }
  });
});

const handleSelect = (key: string, keyPath: string[]) => {
  currentPath.value = key;
};

watch(props.currentRoute, (newState) => {
  if (newState) {
    currentPath.value = newState.value;
    defaultPath.value = newState.value;
  }
});
</script>
<style scoped lang="less">
.info_side {
  position: fixed;
  left: 24px;
  bottom: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 184px;
  height: 109px;
  background: #e6edf7;
  border-radius: 16px 16px 16px 16px;
  box-sizing: border-box;
  padding: 16px 15px;

  header {
    width: 154px;
    display: flex;
    align-items: center;

    .icon {
      width: 16px;
      height: 21px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .head {
      font-weight: 500;
      font-size: 14px;
      color: #000000;
      margin-left: 4px;
    }
  }

  .con {
    font-weight: 500;
    font-size: 12px;
    color: #797979;
    margin-top: 6px;
    margin-bottom: 8px;
    margin-left: 4px;
  }

  .btn {
    width: 154px;
    height: 32px;
    background: #2e76ff;
    border-radius: 4px 4px 4px 4px;
    text-align: center;
    font-weight: 500;
    font-size: 12px;
    color: #ffffff;
    line-height: 32px;
    cursor: pointer;

    &:hover {
      background-color: #1251c8;
    }
  }
}

.el-menu-vertical-demo {
  border-right: none;
}

.template-line {
  width: 193px;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.06);
  margin: 8px 14px;
}

.menu-list {
  font-weight: 500;
  // padding-top: 40px;
  box-sizing: border-box;

  span {
    font-size: 14px;
  }

  img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}

ul.el-menu.el-menu--inline .el-menu-item {
  height: 40px;

  &:hover {
    background: rgba(0, 96, 255, 0.06);
  }
}

:deep(.el-menu-item) {
  width: 193px;
}

.el-menu.el-menu--vertical.el-menu-vertical-demo .el-menu-item {
  height: 40px;
  margin: 8px 32px;
  border-radius: 4px;
  margin-left: 14px;
  line-height: 1;

  &:hover {
    background: rgba(240, 242, 246, 1);
  }
}

.el-menu.el-menu--vertical.el-menu-vertical-demo .el-menu-item.is-active {
  background: rgba(0, 96, 255, 0.06);
}

.el-menu {
  background-color: transparent;
}
</style>
