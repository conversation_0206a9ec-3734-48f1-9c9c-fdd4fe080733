<template>
  <div class="modal">
    <!-- 这里针对几种资源设置不同尺寸的展示框和UI -->
    <div class="modal-content" :class="sourceType">
      <img v-if="sourceType == 'image' || sourceType == 'text'" :src="sourceUrl" class="borderR14" />
      <video v-if="sourceType == 'video'" :src="sourceUrl" ref="videoRef" class="borderR14" @mouseover="showVideo"
        @mouseleave="hideVideo"></video>
      <audio v-if="sourceType == 'audio'" :src="sourceUrl" ref="audioRef" class="borderR14"></audio>
      <canvas-preview v-if="sourceType == 'model'" ref="canvasRef"></canvas-preview>
      <img class="play-icon" v-if="sourceType == 'video' && !playing" src="@/assets/images/play-icon.png"
        @click="playVideo" />
      <img class="paused-icon" v-if="sourceType == 'video' && playing && hoverVideo" src="@/assets/images/paused-icon.png"
        @click="pausedVideo" @mouseover="showVideo" />
      <div class="progress-bar" v-if="sourceType == 'video'">
        <div class="bar-content" :style="{ width: barContentRatio * 100 + '%' }"></div>
      </div>
      <div class="progress-bar-audio" v-if="sourceType == 'audio'">
        <div class="bar-content" :style="{ width: barContentRatio * 100 + '%' }"></div>
      </div>
      <div class="audio-image" v-if="sourceType == 'audio'"></div>
      <div class="audio-play" @click="playAudio" v-if="sourceType == 'audio' && !playing"></div>
      <div class="audio-paused" @click="pausedAudio" v-if="sourceType == 'audio' && playing"></div>
      <div class="close" @click.stop="hideModal"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import CanvasPreview from '@/components/CanvasPreview.vue'
import { loader } from '@/config/threeJs';

const props = defineProps({
  sourceType: {
    default: '',
    type: String
  },
  handleHide: {
    default: null,
    type: Function
  },
  sourceUrl: {
    default: '',
    type: String
  },
  modelType: {
    default: '',
    type: String
  }
})

// 使用增强的loader配置，包含改进的FBX加载器

const canvasRef = ref()
const videoRef = ref()
const audioRef = ref()
const playing = ref(false)
const barContentRatio = ref(0)
let timer: any = null
const hoverVideo = ref(false)
let t1: any = null

const showVideo = () => {
  hoverVideo.value = true
  clearTimeout(t1)
}

const hideVideo = () => {
  clearTimeout(t1)
  t1 = setTimeout(() => {
    hoverVideo.value = false
  }, 150)
}

const hideModal = () => {
  props.handleHide()
  clearInterval(timer)
  barContentRatio.value = 0;
  playing.value = false
}

const pausedVideo = () => {
  videoRef.value.pause()
  playing.value = false
  clearInterval(timer)
}

const playVideo = () => {
  videoRef.value.play()
  playing.value = true
  if (barContentRatio.value == 1) {
    barContentRatio.value = 0;
  }
  videoRef.value.addEventListener('play', (e: any) => {
    clearInterval(timer)
    timer = setInterval(() => {
      barContentRatio.value = Math.min((videoRef.value.currentTime || 0.1) / (videoRef.value.duration || 1), 1)
    }, 500)
  })
  videoRef.value.addEventListener('ended', (e: any) => {
    barContentRatio.value = 1;
    clearInterval(timer)
    playing.value = false
  })
}

const playAudio = () => {
  audioRef.value.play()
  playing.value = true
  if (barContentRatio.value == 1) {
    barContentRatio.value = 0;
  }
  audioRef.value.addEventListener('play', (e: any) => {
    clearInterval(timer)
    timer = setInterval(() => {
      barContentRatio.value = Math.min((audioRef.value.currentTime || 0.1) / (audioRef.value.duration || 1), 1)
    }, 500)
  })
  audioRef.value.addEventListener('ended', (e: any) => {
    barContentRatio.value = 1;
    clearInterval(timer)
    playing.value = false
  })
}

const pausedAudio = () => {
  audioRef.value.pause()
  playing.value = false
  clearInterval(timer)
}

onMounted(() => {
  if (props.sourceType == 'model') {
    console.log('开始加载模型:', props.modelType, props.sourceUrl);

    loader[props.modelType].load(
      props.sourceUrl,
      function (object: any) {
        console.log('模型加载成功:', object);
        try {
          if (object.scene) {
            canvasRef.value.addMesh(object.scene)
          } else {
            canvasRef.value.addMesh(object)
          }
        } catch (error) {
          console.error('添加模型到场景失败:', error);
        }
      },
      function (progress: any) {
        console.log('模型加载进度:', progress);
      },
      function (error: any) {
        console.error('模型加载失败:', error);
        // 可以在这里添加错误处理，比如显示错误信息给用户
      }
    )
  }
})
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .borderR14 {
    border-radius: 14px;
    overflow: hidden;
  }

  .modal-content {
    position: relative;
    background: #000;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    // overflow: hidden;

    .progress-bar,
    .progress-bar-audio {
      position: absolute;
      bottom: 10px;
      left: 10px;
      width: 1002px;
      height: 8px;
      background: rgba(255, 255, 255, 0.4);
      border-radius: 3px;
      width: calc(100% - 20px);
      overflow: hidden;

      .bar-content {
        height: 100%;
        width: 0;
        background-color: #0375FF;
      }
    }

    .progress-bar-audio {
      bottom: 20px;
      left: 20px;
      width: calc(100% - 40px);
    }

    .audio-image {
      position: absolute;
      top: 70px;
      left: 50%;
      width: 155px;
      height: 155px;
      margin-left: -78px;
      background: url(~@/assets/images/audio.png);
      background-size: 100% 100%;
    }

    .audio-play, .audio-paused {
      position: absolute;
      left: 34px;
      bottom: 38px;
      width: 51px;
      height: 51px;
      background: url(~@/assets/images/play-icon-audio.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
    .audio-paused {
      background: url(~@/assets/images/paused-icon-audio.png);
      background-size: 100% 100%;
    }

    .close {
      width: 39px;
      height: 39px;
      position: absolute;
      right: -50px;
      top: -38px;
      background: url(~@/assets/images/close.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
  }

  .video,
  .image {
    width: 53.33%;
    height: 63.33%;
    max-width: 1024px;
    max-height: 684px;
    font-size: 0;

    img,
    video {
      width: 100%;
      height: 100%;
    }

    video {
      object-fit: fill;
    }

    .play-icon,
    .paused-icon {
      width: 104px;
      height: 103px;
      position: absolute;
      left: 50%;
      top: 50%;
      margin-top: -52px;
      margin-left: -52px;
      cursor: pointer;
    }
  }

  .audio,
  .model,
  .text {
    width: 27%;
    height: 32%;
    max-width: 512px;
    max-height: 342px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 14px;
    border: 1px solid #EDEFF2;
    backdrop-filter: blur(50px);
  }

  .text {
    padding: 10px;
    height: auto;
    width: auto;
    background-color: transparent;
    box-shadow: none;
    backdrop-filter: blur(0px);
    border: none;
  }
}
</style>