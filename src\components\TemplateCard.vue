<template>
  <article
    class="template-card"
    :class="{ 'template-card--active': templateData.isHover }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    :id="`template-card-${cardIndex}`">
    <!-- 视频预览 -->
    <div
      class="back_video"
      :class="{
        newBack_video: isNewTemplate,
        lineBack_video: isLineTemplate,
        'back_video--left': templateData.isLeft,
      }"
      v-if="templateData.templateVideoKey && templateData.isHover && !disableVideoPreview">
      <video :src="templateData.templateVideoKey.split('?')[0]" autoplay muted loop></video>
    </div>

    <div class="template-card__image-wrapper">
      <img
        class="template-card__image"
        :src="templateData.templatePicKey.split('?')[0]"
        :alt="templateData.templateName || templateData.sceneName" />

      <div
        class="template-card__type-badge"
        :class="{
          'template-card__type-badge--green': templateData?.sceneType === 1,
          'template-card__type-badge--wechat': templateData?.scenePlatform === 3,
          'template-card__type-badge--mobile': templateData?.scenePlatform === 2,
        }">
        {{ sceneTypeText }}
      </div>

      <!-- 悬停遮罩层和操作按钮 -->
      <div class="template-card__overlay" v-if="templateData.isHover">
        <div class="template-card__actions">
          <!-- 排序模式下的按钮 -->
          <template v-if="isSortMode">
            <!-- 推荐模板区域显示修改和删除按钮 -->
            <template v-if="isRecommendArea">
              <button
                class="template-card__action-btn template-card__action-btn--primary"
                v-if="showEditButton"
                @click="handleEdit"
                type="button">
                修改
              </button>
              <button
                class="template-card__action-btn template-card__action-btn--danger"
                @click="handleRemoveRecommend"
                type="button">
                {{ removeButtonText }}
              </button>
            </template>
            <!-- 所有模板区域显示添加至推荐模版按钮 -->
            <template v-else>
              <button
                class="template-card__action-btn template-card__action-btn--primary"
                @click="handleAddToRecommend"
                type="button">
                添加至推荐模版
              </button>
            </template>
          </template>

          <!-- 普通模式下的按钮 -->
          <template v-else>
            <button
              class="template-card__action-btn template-card__action-btn--primary"
              v-if="showEditButton"
              @click="handleEdit"
              type="button">
              修改
            </button>
            <button
              class="template-card__action-btn template-card__action-btn--primary"
              @click="handleExperience"
              type="button">
              体验
            </button>
            <button
              class="template-card__action-btn template-card__action-btn--secondary"
              @click="handleCreateSimilar"
              v-if="templateData.sceneType != 1"
              type="button">
              创建同款
            </button>
          </template>
        </div>
      </div>
    </div>

    <div class="template-card__info">
      <div class="template-card__platform-icon">
        <img
          v-if="templateData.scenePlatform == 1"
          src="@/assets/images/home/<USER>"
          alt="AR眼镜" />
        <img
          v-if="templateData.scenePlatform == 2"
          src="@/assets/images/home/<USER>"
          alt="空间场景" />
        <img
          v-if="templateData.scenePlatform == 3"
          src="@/assets/images/home/<USER>"
          alt="微信小程序" />
      </div>
      <h3 class="template-card__title">
        {{ templateData.templateName }}
      </h3>
    </div>
  </article>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  templateData: {
    type: Object,
    required: true,
  },
  cardIndex: {
    type: Number,
    required: true,
  },
  domMaskIndexs: {
    type: Array,
    default: () => [],
  },
  showEditButton: {
    type: Boolean,
    default: true,
  },
  isSortMode: {
    type: Boolean,
    default: false,
  },
  disableVideoPreview: {
    type: Boolean,
    default: false,
  },
  isRecommendArea: {
    type: Boolean,
    default: false,
  },
  removeButtonText: {
    type: String,
    default: '删除',
  },
});

// Emits
const emit = defineEmits([
  'mouseenter',
  'mouseleave',
  'edit',
  'experience',
  'create-similar',
  'add-to-recommend',
  'remove-recommend',
]);

// Computed
const isNewTemplate = computed(() => {
  return props.domMaskIndexs.includes(props.cardIndex);
});

const isLineTemplate = computed(() => {
  return props.templateData.sceneType == 1 && props.templateData.scenePlatform != 3;
});

const sceneTypeText = computed(() => {
  const sceneTypeMap = {
    1: '空间AR',
    2: '平面AR',
    3: '图像AR',
    5: '身体AR',
    6: '人脸AR',
    7: '手势AR',
    8: '单场景AR',
  };
  return sceneTypeMap[props.templateData.sceneType] || '未知类型';
});

// Methods
const handleMouseEnter = () => {
  emit('mouseenter', props.templateData, props.cardIndex);
};

const handleMouseLeave = () => {
  emit('mouseleave', props.templateData);
};

const handleEdit = () => {
  emit('edit', props.templateData);
};

const handleExperience = () => {
  emit('experience', props.templateData);
};

const handleCreateSimilar = () => {
  emit('create-similar', props.templateData);
};

const handleAddToRecommend = () => {
  emit('add-to-recommend', props.templateData);
};

const handleRemoveRecommend = () => {
  emit('remove-recommend', props.templateData);
};
</script>

<style lang="less" scoped>
// 单个模板卡片
.template-card {
  position: relative;
  background: #fff;
  border-radius: 10px;
  overflow: visible; // 允许视频预览超出卡片边界
  cursor: pointer;
  transition: all 0.3s ease;
  // 预留边框空间，避免hover时尺寸变化
  border: 2px solid transparent;
  // 添加内边距，让图片与边框之间有间距
  padding: 2px;
  z-index: 0; // 降低卡片z-index，确保不会遮挡视频预览

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &--active {
    border-color: #2e76ff;
    box-shadow: 0 4px 16px rgba(46, 118, 255, 0.2);
    z-index: 10; // 提高active卡片的z-index，但仍低于视频预览

    .template-card__image {
      transform: scale(1.02);
    }
  }
}

// 卡片图片容器
.template-card__image-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 68.5%; // 211/308 ≈ 0.685，保持图片原始比例
  overflow: hidden;
  border-radius: 10px; // 减小圆角以适应内边距
}

// 卡片图片
.template-card__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  // 确保图片缩放不会影响容器尺寸
  transform-origin: center center;
}

// 类型标签
.template-card__type-badge {
  position: absolute;
  left: 0;
  bottom: 0;
  padding: 6px 12px;
  font-size: 12px;
  color: #ffffff;
  background: rgba(46, 118, 255, 0.8);
  border-radius: 0 10px 0 0;

  &--green {
    background: rgba(46, 118, 255, 0.8);
  }

  // 微信小程序平台 - 绿色背景
  &--wechat {
    background: rgba(6, 198, 97, 0.8); // #06C661 with opacity
  }

  // 移动端平台 - 紫色背景
  &--mobile {
    background: rgba(129, 95, 204, 0.8); // #815FCC with opacity
  }
}

// 悬浮遮罩层
.template-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1; // 默认显示，通过v-if控制显示/隐藏
  transition: opacity 0.3s ease;
  border-radius: 8px 8px 8px 8px; // 与图片容器保持一致
  pointer-events: auto; // 允许点击事件
}

// 操作按钮组
.template-card__actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

// 操作按钮
.template-card__action-btn {
  padding: 8px 24px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-width: 92px;

  &--primary {
    background: #2e76ff;
    color: #ffffff;

    &:hover {
      background: #1251cb;
    }
  }

  &--secondary {
    background: #669aff;
    color: #ffffff;

    &:hover {
      background: #2e76ff;
    }
  }

  &--danger {
    background: #f56565;
    color: #ffffff;

    &:hover {
      background: #e53e3e;
    }
  }
}

// 卡片信息区域
.template-card__info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  background: #fff;
  border-radius: 0 0 8px 8px; // 与图片容器保持一致
}

// 平台图标
.template-card__platform-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
}

// 卡片标题
.template-card__title {
  flex: 1;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #1e1e1e;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

// 视频预览功能 - 智能定位避免超出边界
.back_video {
  position: absolute;
  right: -270px; // 默认显示在右侧
  width: 252px;
  height: 370px;
  background: #fff;
  z-index: 999; // 使用原版z-index
  display: flex;
  justify-content: center;
  top: 0; // 与卡片顶部平行
  box-shadow: 0px 0px 10px 0px #2e76ff;
  border-radius: 12px;
  border: 2px solid #ffffff;

  video {
    width: 252px;
    height: 370px;
    object-fit: cover;
    border-radius: 12px;
  }

  // 当卡片靠右边时，视频预览显示在左侧
  &--left {
    right: auto;
    left: -270px; // 显示在左侧
  }
}

.lineBack_video {
  width: 516px;
  height: 290px;
  top: 0; // 与卡片顶部平行
  right: -534px; // 负值让视频显示在卡片右侧外部

  video {
    width: 516px;
    height: 290px;
  }

  // 线性场景当卡片靠右边时，视频预览显示在左侧
  &.back_video--left {
    right: auto;
    left: -534px; // 显示在左侧
  }
}

.newBack_video {
  left: -291px !important; // 默认显示在卡片左侧外部
  top: 0 !important; // 与卡片顶部平行
  background: url('@/assets/images/new_right.png') no-repeat;
  background-size: 100%;

  video {
    transform: translate(-5px, 14px);
  }

  // 新版场景当卡片靠右边时，反而显示在右侧
  &.back_video--left {
    left: auto !important;
    right: -291px !important; // 显示在右侧
  }
}
</style>
