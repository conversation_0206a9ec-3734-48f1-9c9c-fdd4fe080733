.itemWechatLeft {
    left: -270px !important;
}

.itemWechatTop {
    bottom: -120px !important;
    top: auto !important;
}

.itemEyeLeft {
    left: -534px !important;
}

.itemEyeTop {
    bottom: -40px !important;
    top: auto !important;
}

@media screen and (max-width: 10000px) {

    .itemDiv,
    .div {
        min-width: 5% !important;
        max-width: 5% !important;
    }
}


@media screen and (max-width: 5600px) {

    .itemDiv,
    .div {
        min-width: 10% !important;
        max-width: 10% !important;
    }
}


@media screen and (max-width: 3600px) {

    .itemDiv,
    .div {
        min-width: 15% !important;
        max-width: 15% !important;
    }
}

@media screen and (max-width: 2380px) {

    .itemDiv,
    .div {
        min-width: 18% !important;
        max-width: 18% !important;
    }
}

@media screen and (max-width: 2150px) {

    .itemDiv,
    .div {
        min-width: 18% !important;
        max-width: 18% !important;
    }
}

@media screen and (max-width: 1920px) {

    .itemDiv,
    .div {
        min-width: 23% !important;
        max-width: 23% !important;
    }
}

@media screen and (max-width: 1700px) {

    .itemDiv,
    .div {
        min-width: 23% !important;
        max-width: 23% !important;
    }
}


@media screen and (max-width: 1500px) {

    .itemDiv,
    .div {
        min-width: 30% !important;
        max-width: 30% !important;
    }
}


@media screen and (max-width: 1300px) {

    .itemDiv,
    .div {
        min-width: 30% !important;
        max-width: 30% !important;
    }
}

@media screen and (max-width: 1100px) {

    .itemDiv,
    .div {
        min-width: 46% !important;
        max-width: 46% !important;
    }
}


@media screen and (max-width: 900px) {

    .itemDiv,
    .div {
        min-width: 46% !important;
        max-width: 46% !important;
    }
}