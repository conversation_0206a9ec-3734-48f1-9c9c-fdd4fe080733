<template>
  <canvas id="text-canvas" width="546" height="208"></canvas>
</template>
<script lang="ts" setup>
import { base64ToBlob } from '@/utils/index'
const canvasText = (content: string, callback: any) => {

  // 获取canvas元素
  var canvas: any = document.getElementById('text-canvas');
  var ctx = canvas.getContext('2d');
  // 创建新的Image对象
  var image = new Image();

  // 设置图片源地址
  image.src = '/images/text-btn.png';

  // 确保图片加载完成后再绘制到canvas
  image.onload = function () {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    // 绘制图片到canvas, 大小为图片实际大小
    ctx.drawImage(image, 0, 0, 546, 208);
    let text = content
    text = text.slice(0, 99)
    let fontSize = 16;
    let lineHeight = 24;
    if (text.length < 150) {
      fontSize = 18;
      lineHeight = 27
    }
    const x = 26;
    const y = 104; //25 + (fontSize / 2)
    const maxWidth = 496;

    const dataURL = drawWrappedText(ctx, text, x, y, maxWidth, lineHeight, fontSize, canvas);
    const formData = base64ToBlob(dataURL);
    callback(formData, dataURL)
  };
}

const drawWrappedText = (ctx: any, text: string, x: number, y: number, maxWidth: number, lineHeight: number, fontSize: number, canvas: any) => {
  ctx.font = `${fontSize}px Arial`; // 设置字体大小和类型，可以根据需要修改
  ctx.textAlign = 'start';
  ctx.fillStyle = 'black';
  const words = text.split('');
  const words2 = text.split('');
  let line = '';
  let line2 = '';
  let lineCount = 0;

  for (let n = 0; n < words.length; n++) {
    const testLine = line + words[n] + '';
    const metrics = ctx.measureText(testLine);
    const testWidth = metrics.width;
    if (testWidth > maxWidth && n > 0) {
      lineCount++
      line = words[n];
    } else {
      line = testLine;
    }
  }
  lineCount++;
  if (lineCount) {
    y = 104 - (((lineCount - 1) / 2) * lineHeight)
  }

  for (let n = 0; n < words2.length; n++) {
    const testLine = line2 + words2[n] + '';
    const metrics = ctx.measureText(testLine);
    const testWidth = metrics.width;
    if (testWidth > maxWidth && n > 0) {
      ctx.fillText(line2, x, y);
      line2 = words2[n];
      y += lineHeight;
    } else {
      line2 = testLine;
      if (lineCount == 1) {
        x = (maxWidth - testWidth) / 2 + fontSize
      }
    }
  }
  ctx.fillText(line2, x, y);
  var dataURL = canvas.toDataURL("image/png");
  return dataURL;
}
defineExpose({
  canvasText
})
</script>
<style scoped lang="less">
#text-canvas {
  visibility: hidden;
  position: absolute;
}
</style>