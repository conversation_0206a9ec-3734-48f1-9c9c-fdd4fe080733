<template>
  <el-dialog
    v-model="dialogVisible"
    width="1200px"
    :close-on-click-modal="false"
    class="invoice-management-dialog"
    title="发票管理">
    <div class="dialog-content">
      <!-- 顶部区域：提示信息和发票金额 -->
      <div class="top-section">
        <!-- 左侧提示信息 -->
        <div class="invoice-tip">
          <div class="custom-alert">
            <div class="alert-icon">
              <img src="@/assets/images/Attention1.png" style="width: 32px; height: 32px" />
            </div>
            <div class="alert-content">
              <div class="tip-content">
                <p>1.发票需基于实际充值的能量值金额（不含赠送），方可申请开具发票。</p>
                <p>2.一般会在收到发票申请后3~7个工作日内为您开具发票。</p>
                <p>3.可开票金额：当前账号已充值总金额-历史已经申请过开票（包括开票中）的金额。</p>
                <p>4.发票保存时效：自申请开具日期起有效期为1年，超出之后自动清空。</p>
              </div>
            </div>
            <!-- 发票金额区域 -->
            <div class="invoice-amount">
              <div class="amount-title">当前发票金额</div>
              <div class="amount-value">¥{{ totalBillAmount.toFixed(2) }}</div>
              <div class="amount-actions">
                <div
                  class="apply-invoice-btn"
                  :class="{ active: totalBillAmount > 0 }"
                  @click="handleApplyInvoice">
                  申请开票
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-section">
        <CommonTable
          :data="tableData"
          :columns="tableColumns"
          :loading="loading"
          :total="total"
          :current-page="currentPage"
          :page-size="pageSize"
          @current-change="handlePageChange"
          @size-change="handleSizeChange" />
      </div>
    </div>
  </el-dialog>

  <!-- 申请开票弹窗 -->
  <ApplyInvoiceDialog
    v-model:show="showApplyDialog"
    :amount="totalBillAmount"
    @success="handleApplySuccess" />

  <!-- 拒绝理由弹窗 -->
  <TipsView :tips-title="'拒绝开票'" :is-open-tip="true" :cancle-event="closeRejectReasonDialog">
    <div class="reject-reason-content" v-if="currentRejectReason">
      <el-icon class="reject-icon"><WarningFilled /></el-icon>
      <span class="reject-text">{{ currentRejectReason }}</span>
    </div>
  </TipsView>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue';
import { ElMessage, ElButton, ElText, ElIcon } from 'element-plus';
import { WarningFilled } from '@element-plus/icons-vue';
import { useStore } from 'vuex';
import CommonTable from '@/components/CommonTable.vue';
import ApplyInvoiceDialog from './ApplyInvoiceDialog.vue';
import TipsView from '@/components/TipsView.vue';
import { getInvoiceList } from '@/api';
import dayjs from 'dayjs';

// Props
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});

// Emits
const emit = defineEmits(['update:show']);

// Vuex store
const store = useStore();

// 响应式数据
const dialogVisible = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val),
});

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const totalBillAmount = ref(0); // 当前发票总金额
const showApplyDialog = ref(false); // 申请开票弹窗显示状态

// 拒绝理由相关状态
const currentRejectReason = ref(''); // 当前拒绝理由内容

// 表格列配置 - 根据后端数据库表结构定义
const tableColumns = ref([
  {
    prop: 'billNo',
    label: '发票编号',
    formatter: (props: any) => props.row.billNo || '-',
  },
  {
    prop: 'createTime',
    label: '申请时间',
    formatter: (props: any) => {
      const time = props.row.createTime;
      if (!time) return '-';
      return dayjs(time).format('YYYY-MM-DD HH:mm');
    },
  },
  {
    prop: 'billTitle',
    label: '发票抬头',
    formatter: (props: any) => props.row.billTitle || '-',
  },
  {
    prop: 'billPersonFirm',
    label: '发票主体',
    formatter: (props: any) => {
      const type = props.row.billPersonFirm;
      const typeMap = {
        1: '个人',
        2: '企业',
      };
      return typeMap[type as keyof typeof typeMap] || '-';
    },
  },
  {
    prop: 'billType',
    label: '发票类型',
    formatter: (props: any) => {
      const type = props.row.billType;
      const typeMap = {
        1: '电子专票',
        2: '电子普票',
      };
      return typeMap[type as keyof typeof typeMap] || '-';
    },
  },
  {
    prop: 'amount',
    label: '金额（¥）',
    formatter: (props: any) => {
      const amount = props.row.amount;
      if (!amount) return '-';
      // 后端金额单位为分，需要转换为元
      const yuan = (amount / 100).toFixed(2);
      return `¥${yuan}`;
    },
  },
  {
    prop: 'remark',
    label: '备注',
    formatter: (props: any) => props.row.remark || '-',
  },
  {
    prop: 'billOrderStatus',
    label: '发票状态',
    formatter: (props: any) => {
      const status = props.row.billOrderStatus;
      const statusMap = {
        1: '开具中',
        2: '已完成',
        3: '拒绝',
      };
      return statusMap[status as keyof typeof statusMap] || '未知';
    },
  },
  {
    prop: 'actions',
    label: '操作',
    width: 140,
    formatter: (props: any) => {
      const status = props.row.billOrderStatus;

      if (status === 2) {
        // 已完成 - 显示下载发票按钮
        return h(
          ElButton,
          {
            type: 'primary',
            link: true,
            onClick: () => handleDownload(props.row),
          },
          () => '下载发票'
        );
      } else if (status === 1) {
        // 开具中 - 显示等待处理文本
        return h(
          ElText,

          () => '等待处理'
        );
      } else if (status === 3) {
        // 拒绝 - 显示查看理由按钮
        return h(
          ElButton,
          {
            type: 'warning',
            link: true,
            onClick: () => handleViewReason(props.row),
          },
          () => '查看理由'
        );
      }
      return '-';
    },
  },
]);

// 获取发票列表数据
const fetchInvoiceList = async () => {
  loading.value = true;
  try {
    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
    };

    console.log('调用发票列表API，参数:', params);
    const response = await getInvoiceList(params);
    console.log('发票列表API响应:', response);

    const responseData = response as any;
    if (responseData.code === '200' || responseData.code === '2000') {
      // 根据实际API响应结构处理数据
      const data = responseData.data;
      const pageData = data.page;

      // 处理分页数据
      tableData.value = pageData.records || [];
      total.value = pageData.total || 0;

      // 更新当前开票金额
      totalBillAmount.value = data.sumAmount / 100 || 0;

      console.log('处理后的表格数据:', tableData.value);
      console.log('总数:', total.value);
      console.log('当前开票金额:', totalBillAmount.value);
    } else {
      console.error('API返回错误:', responseData);
      ElMessage.error(responseData.message || '获取发票列表失败');
    }
  } catch (error) {
    console.error('获取发票列表失败:', error);
    ElMessage.error('获取发票列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 申请开票
const handleApply = (id: string) => {
  ElMessage.success(`申请开票 ${id}`);
  // TODO: 实现申请开票功能
};

// 下载发票
const handleDownload = async (rowData: any) => {
  const billPdfUrl = rowData.billPdfUrl;

  if (!billPdfUrl) {
    ElMessage.error('发票下载链接不存在');
    return;
  }

  try {
    // 生成文件名
    const fileName = `发票_${rowData.billTitle || rowData.id}.pdf`;

    // 从 ? 处截断 URL，去掉查询参数
    const cleanUrl = billPdfUrl.split('?')[0];

    // 检查是否是完整的 URL
    const downloadUrl = cleanUrl.startsWith('http')
      ? cleanUrl
      : `${window.location.origin}${cleanUrl}`;

    // 创建一个隐藏的 a 标签进行下载
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';

    // 添加到 DOM 并触发点击
    document.body.appendChild(link);
    link.click();

    // 延迟清理 DOM，确保下载开始
    setTimeout(() => {
      document.body.removeChild(link);
    }, 100);

    console.log('下载发票:', {
      id: rowData.id,
      originalUrl: billPdfUrl,
      cleanUrl: cleanUrl,
      downloadUrl: downloadUrl,
      fileName: fileName,
    });
  } catch (error) {
    console.error('下载发票失败:', error);
    ElMessage.error('下载发票失败，请稍后重试');
  }
};

// 申请开票（顶部按钮）
const handleApplyInvoice = () => {
  if (totalBillAmount.value > 0) {
    showApplyDialog.value = true;
  } else {
    ElMessage.warning('当前没有可开票金额');
  }
};

// 申请开票成功回调
const handleApplySuccess = () => {
  // 刷新发票列表
  fetchInvoiceList();
};

// 查看拒绝理由
const handleViewReason = (rowData: any) => {
  const rejectReason = rowData.rejectRemark || '暂无拒绝理由';

  // 设置拒绝理由内容并通过 store 显示弹窗
  // 使用空格作为占位符，这样 TipsView 会显示但不会显示重复内容
  currentRejectReason.value = rejectReason;
  store.state.showTips = ' ';

  console.log('查看拒绝理由:', {
    id: rowData.id,
    rejectReason: rejectReason,
    rowData: rowData,
  });
};

// 关闭拒绝理由弹窗
const closeRejectReasonDialog = () => {
  store.state.showTips = '';
  currentRejectReason.value = '';
};

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchInvoiceList();
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchInvoiceList();
};

// 监听弹窗显示状态
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      // 重置状态
      currentPage.value = 1;
      pageSize.value = 10;
      tableData.value = [];
      totalBillAmount.value = 0;
      total.value = 0;

      // 获取发票列表
      fetchInvoiceList();
    }
  }
);
</script>

<style scoped lang="less">
.invoice-management-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;

    .el-dialog__header {
      padding: 20px 24px 16px;
      border-bottom: 1px solid #e4e7ed;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #1e1e1e;
      }
    }

    .el-dialog__body {
      padding: 24px;
    }
  }
}

.dialog-content {
  .top-section {
    margin-bottom: 24px;

    .invoice-tip {
      .custom-alert {
        background: #e6edf7;
        border-radius: 4px 4px 4px 4px;
        padding: 24px;
        display: flex;
        align-items: center;
        gap: 12px;

        .alert-icon {
          flex-shrink: 0;
          margin-top: 2px;
        }

        .alert-content {
          flex: 1;

          .tip-content {
            p {
              margin: 4px 0;
              line-height: 1.5;
              font-weight: 400;
              font-size: 12px;
              color: #1e1e1e;
            }
          }
        }

        .invoice-amount {
          flex-shrink: 0;
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin-left: 24px;

          .amount-title {
            font-size: 14px;
            color: #666;
            text-align: center;
          }

          .amount-value {
            font-size: 24px;
            font-weight: 600;
            color: #1e1e1e;
            text-align: center;
          }

          .amount-actions {
            display: flex;
            justify-content: center;

            .apply-invoice-btn {
              width: 80px;
              height: 28px;
              background: #d8d8d8;
              border-radius: 4px 4px 4px 4px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              color: #999;
              cursor: not-allowed;
              user-select: none;
              transition: all 0.3s ease;

              &.active {
                background: #2e76ff;
                color: #fff;
                cursor: pointer;

                &:hover {
                  background: #1e66ff;
                }
              }
            }
          }
        }
      }
    }
  }

  .table-section {
    min-height: 400px;
  }
}

// 拒绝理由内容样式
.reject-reason-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;

  .reject-icon {
    color: #f56565;
    font-size: 16px;
    margin-top: 2px;
    flex-shrink: 0;
  }

  .reject-text {
    color: #f56565;
    font-weight: 500;
    line-height: 1.5;
    word-break: break-word;
  }
}

// 隐藏 TipsView 默认内容，只显示插槽内容
:deep(.tips-content) {
  div:not(.title):not(.btn):not(.submit-btns):not(.close):not(.reject-reason-content) {
    display: none !important;
  }

  // 自定义取消和确认按钮样式
  .submit-btns {
    gap: 4px;

    .btn-default {
      .el-button {
        width: 92px;
        height: 32px;
        background: #ffffff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #666666;
        font-size: 14px;

        &:hover {
          border-color: #2e76ff;
          color: #2e76ff;
        }
      }
    }

    .btn-primary {
      .el-button {
        width: 92px;
        height: 32px;
        background: #2e76ff;
        border: 1px solid #2e76ff;
        border-radius: 4px;
        color: #ffffff;
        font-size: 14px;

        &:hover {
          background: #1c5dd6;
          border-color: #1c5dd6;
        }
      }
    }
  }
}
</style>
