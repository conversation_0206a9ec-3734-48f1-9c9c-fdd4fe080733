import {
    PlaneGeometry,
    Mesh,
    Intersection,
    MeshBasicMaterial,
    Raycaster,
    RepeatWrapping,
    TextureLoader,
    Vector2,
    Vector3,
    Matrix4,
    Quaternion,
  } from "three";
  
  export const ADSORPTION_RADIUS = 10; // 点吸附半径
  
  export type CMeshPointAttrs = {
    position?: Vector2;
    transparent?: boolean;
    normal?: Vector3;
    url?: string;
  };
  export class CMeshPoint extends Mesh {
    geometry: PlaneGeometry;
    material: MeshBasicMaterial;
    name: string;
    userData: { [k: string]: any };
    constructor(attrs?: CMeshPointAttrs) {
      super();
  
      this.name = "meshpoint";
      this.geometry = new PlaneGeometry(10, 10);
      this.material = new MeshBasicMaterial({ side: 2 });
      this.userData = {};
      this.initialize(attrs);
      // this.lookAt(0, 1, 0)
    }
    getType() {
      // 获取类型
      return "meshPoint";
    }
  
    initData(attrs?: CMeshPointAttrs) {
      const normal =
      attrs && attrs.normal !== undefined
        ? attrs.normal.normalize()
        : new Vector3(0, 1, 0); // 当前planeGeometry传入的法向量
    const matrix = new Matrix4().makeRotationFromQuaternion(
      new Quaternion().setFromUnitVectors(
        new Vector3(0, 0, 1), // 初始向量
        normal // 面所在的法向量
      )
    );
    this.userData.matrix = matrix.clone();
    }
  
    initialize(attrs?: CMeshPointAttrs) {
      this.initData(attrs);
    }
  
    // @override 射线拾取点
    raycast(raycaster: Raycaster, intersects: Intersection[]) {
      const ori = (raycaster as any).ray.origin;
      // const oriCurrent = ori.clone().project((window as any).camera)
      // const v1 =  new Vector2((0.5 + oriCurrent.x / 2) * window.innerWidth, (0.5 - oriCurrent.y / 2) * window.innerHeight)
      // const position = new Vector3(this.geometry.attributes.position.array[0], this.geometry.attributes.position.array[1], this.geometry.attributes.position.array[2])
      // const current = position.clone().project((window as any).camera)
      // const v2 = new Vector2((0.5 + current.x / 2) * window.innerWidth, (0.5 - current.y / 2) * window.innerHeight)
      // if (v1.distanceToSquared(v2) <= ADSORPTION_RADIUS * ADSORPTION_RADIUS) {
      //     intersects.push({
      //         distance: current.clone().distanceTo((window as any).camera.position),
      //         object: this,
      //         point: position
      //     } as any)
      // }
    }
  
    loadTexture(url: string) {
      const t = new TextureLoader().load(url);
      t.wrapS = t.wrapT = RepeatWrapping;
      this.material.map = t;
      return this;
    }
  
    //
    setDepthTest(depthTest: boolean) {
      this.material.depthTest = depthTest;
      return this;
    }
  
    setTransparent(transparent: boolean) {
      this.material.transparent = transparent;
      this.material.opacity = 0.2;
      return this;
    }
  
    // 通过位置和半径生成圆
    setVertexs(radius: number, position: Vector2) {
      return this;
    }
  }
  