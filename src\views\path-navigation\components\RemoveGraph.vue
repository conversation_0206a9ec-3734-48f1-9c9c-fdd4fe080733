<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div>{{ graphBindScene.length ? '提示' : '删除当前路径' }}</div>
      </div>
      <div class="modal-form">
        <div class="sure-title">{{ graphBindScene.length ? '当前路径已经在以下项目中使用，不可删除。' : `确认要删除名为“${defaultGraphData.graphName}”的路径，删除后不可恢复` }} </div>
        <div class="scene-list">
          <div v-for="(item, index) in graphBindScene" :key="index">
            “{{ item }}”
          </div>
        </div>
        <div class="btn-box" v-if="!graphBindScene.length">
          <div class="btn-default el-size3">
            <el-button @click="changeState">取消</el-button>
          </div>
          <div class="btn-primary el-size3">
            <el-button @click="submitForm()">确认</el-button>
          </div>
        </div>
        <div class="btn-box" v-else>
          <div class="btn-primary el-size3">
            <el-button @click="changeState">知道了</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { getGraphUserdScene, deleteNjyjGraph } from "@/api";

const props = defineProps({
  handleHide: {
    default: null,
    type: Function,
  },
  defaultGraphData: {
    default: null,
    type: Object,
  },
});

const graphBindScene: any = ref([]);

const submitForm = async () => {
  deleteNjyjGraph({ njyjGraphId: props.defaultGraphData.id }).then((res: any) => {
    if (res.code == 200) {
      props.handleHide(true);
    }
  });
};

const changeState = () => {
  props.handleHide();
};

onMounted(() => {
  getGraphUserdScene({ njyjGraphId: props.defaultGraphData.id }).then((res: any) => {
    graphBindScene.value = [...res.data];
  });
});
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;

  .modal-content {
    width: 453px;
    border-radius: 8px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 24px;
    box-sizing: border-box;

    .modal-content-title {
      font-weight: bold;
      font-size: 18px;
      color: #1E1E1E;
      height: 27px;
      line-height: 27px;
      text-align: left;
    }

    .modal-form {
      font-weight: 400;
      font-size: 14px;
      color: #797979;
      
      .sure-title {
        text-align: left;
        margin-top: 12px;
      }

      .scene-list {
        margin-top: 12px;
        overflow: hidden;
        overflow-y: auto;
        text-align: left;
      }

      .btn-box {
        display: flex;
        justify-content: right;
        align-items: center;
        margin-top: 24px;
      }
    }
  }
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;
}
</style>