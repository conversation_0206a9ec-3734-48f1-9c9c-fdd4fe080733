<template>
  <div class="new-mask">
    <div>
      <img class="closed" src="@/assets/images/experience-icon/mask-close.png" alt="" @click="closeEvent">
      <div>姓名</div>
      <el-input style="width: 100%;margin-top: 20px;" v-model="sceneName" placeholder="请输入姓名" />
      <div class="sure-btn" @click="addScene">提交</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { saveSceneMeta } from '@/api'

const props = defineProps({
  hideAddMask: {
    default: null,
    type: Function
  }
})

const sceneName = ref('')

const addScene = () => {
  // saveSceneMeta({ sceneName: sceneName.value, sceneType: 1 }).then(() => {
  //   props.hideAddMask()
  // })
}

const closeEvent = () => {
  props.hideAddMask()
}
</script>
<style scoped lang="less">
.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  &>div {
    position: relative;
    width: 520px;
    height: 279px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 60px 40px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 600;
    font-size: 18px;
    color: #3D566C;

    .closed {
      width: 17px;
      height: 17px;
      position: absolute;
      right: 31px;
      top: 31px;
      cursor: pointer;
    }

    .sure-btn {
      width: 340px;
      height: 42px;
      line-height: 42px;
      background: #3671FE;
      border-radius: 6px;
      margin: 30px 0 0 50px;
      font-weight: bold;
      font-size: 16px;
      color: #FFFFFF;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>