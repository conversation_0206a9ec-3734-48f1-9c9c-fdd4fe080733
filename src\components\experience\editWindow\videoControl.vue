<template>
  <div class="new-mask">
    <div>
      <img class="closed" src="@/assets/images/experience-icon/mask-close.png" alt="" @click="closeEvent">
      <div class="title">视频控制</div>
      <div class="list-title">功能名称</div>
      <el-input style="width: 100%;margin-top: 20px;" v-model="modelData.name" placeholder="请输入功能名称" />
      <div class="list-title">功能设置</div>
      <el-select v-model="modelData.model" clearable class="select-default" @change="changeModel"
        style="width: 210px;margin-top: 20px;" popper-class="select-option" :suffix-icon="DropDown" placeholder="请选择AR或透明视频">
        <el-option label="" value="" />
      </el-select>
      <el-select v-model="modelData.model" clearable class="select-default" @change="changeModel"
        style="width: 210px;margin-top: 20px;margin-left: 20px;" popper-class="select-option" :suffix-icon="DropDown"
        placeholder="请选择操作">
        <el-option label="" value="" />
      </el-select>
      <div class="list-title">触发条件</div>
      <el-select v-model="modelData.model" clearable class="select-default" @change="changeModel"
        style="width: 210px;margin-top: 20px;" popper-class="select-option" :suffix-icon="DropDown"
        placeholder="请选择触发条件">
        <el-option label="" value="" />
      </el-select>
      <div class="sure-btn" @click="onSubmit">完成</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import DropDown from '@/components/DropDown.vue'

const props = defineProps({
  hideMask: {
    default: null,
    type: Function
  }
})
const modelData = reactive({ name: '', model: '' })

const onSubmit = () => {
  props.hideMask()
}

const changeModel = () => {
  //
}

const closeEvent = () => {
  props.hideMask()
}
</script>
<style scoped lang="less">
.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  &>div {
    position: relative;
    width: 520px;
    height: 573px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 60px 40px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 600;
    font-size: 18px;
    color: #3D566C;

    .closed {
      width: 17px;
      height: 17px;
      position: absolute;
      right: 31px;
      top: 31px;
      cursor: pointer;
    }

    .title {
      font-weight: 600;
      font-size: 22px;
      color: #3D566C;
      text-align: center;
    }

    .list-title {
      font-weight: 600;
      font-size: 18px;
      color: #3D566C;
      text-align: left;
      margin-top: 30px;
    }

    .sure-btn {
      width: 340px;
      height: 42px;
      line-height: 42px;
      background: #3671FE;
      border-radius: 6px;
      margin: 30px 0 0 50px;
      font-weight: bold;
      font-size: 16px;
      color: #FFFFFF;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>