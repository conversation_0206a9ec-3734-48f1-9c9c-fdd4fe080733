<template>
  <div class="page-header">
    <div class="scene-name">
      <img src="@/assets/images/experience-icon/back.png" alt="" @click="router.back()">
      <span>项目1 测试用的</span>
    </div>
    <div class="header-tools">
      <div class="profile-picture">E</div>
      <div class="save" @click="saveSceneData">
        <img src="@/assets/images/experience-icon/save.png" alt="">
        <span>保存</span>
      </div>
      <div class="release" @click="showShare">
        <img src="@/assets/images/experience-icon/release.png" alt="">
        <span>发布</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router'

const props = defineProps({
  saveData: {
    default: null,
    type: Function
  },
  showShare: {
    default: null,
    type: Function
  }
})

const router = useRouter()

const saveSceneData = () => {
  props.saveData()
}
const showShare = () => {
  props.showShare()
}
</script>
<style scoped lang="less">
.page-header {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 60px;
  background: #FFFFFF;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px;
  box-sizing: border-box;
  font-weight: 600;
  font-size: 16px;
  color: #3D566C;
  z-index: 1;

  .scene-name {
    span {
      vertical-align: middle;
    }

    img {
      vertical-align: middle;
      width: 16px;
      height: 16px;
      margin-right: 10px;
      cursor: pointer;
    }
  }

  .header-tools {
    width: 240px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .profile-picture {
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 50%;
      background: rgba(54, 113, 254, 0.3);
    }

    .save {
      width: 70px;
      height: 30px;
      line-height: 30px;
      border-radius: 6px;
      border: 1px solid #3671FE;
      margin-left: 40px;
      margin-right: 20px;
      box-sizing: border-box;
      padding: 0 10px;
      font-weight: bold;
      font-size: 14px;
      color: #3D566C;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;

      img {
        width: 16px;
        height: 16px;
      }
    }

    .release {
      width: 70px;
      height: 30px;
      line-height: 30px;
      background: #3671FE;
      border-radius: 6px;
      padding: 0 10px;
      box-sizing: border-box;
      font-weight: bold;
      font-size: 14px;
      color: #FFFFFF;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;

      img {
        width: 16px;
        height: 16px;
      }
    }
  }
}
</style>