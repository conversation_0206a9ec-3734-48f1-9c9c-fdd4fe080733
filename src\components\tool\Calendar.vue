<template>
    <div class="new-mask">
        <div class="content">
            <header>
                <div>更新账号有效时间</div>
                <img class="closed" src="http://njyjxr.oss-cn-shanghai.aliyuncs.com/mask-close.png" alt=""
                    @click="closeEvent">
            </header>
            <main>
                <el-date-picker v-model="monthValue" type="month" placeholder="请选择月份" popper-class="date-picker-popper"
                    @change="changeMonth" />
                <footer>
                    <el-button style="width: 80px;" @click="closeEvent">取消</el-button>
                    <el-button type="primary" style="width: 80px;" @click="confirm">确定</el-button>
                </footer>
            </main>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { updateUserPackageTime } from '@/api';
import { ElMessage } from 'element-plus';
const emits = defineEmits(['showCreateCustomer', 'updateValue'])
const monthValue = ref('')

const props = defineProps({
    hideAddMask: {
        default: null,
        type: Function
    },
    defaultValue: {
        default: null,
        type: Object
    }
})

onMounted(() => {
    monthValue.value = props.defaultValue.expireTimeStr
})

const changeMonth = (val) => {
    console.log(val);
    const date = new Date(val)
    emits('updateValue', date)
}

// const getMonthPicker: number = (usa_str: string) => {
//     switch (usa_str) {
//         case 'Jan':
//             return 1
//         case 'Feb':
//             return 2
//         case 'Mar':
//             return 3
//         case 'Apr':
//             return 4
//         case 'May':
//             return 5
//         case 'Jun':
//             return 6
//         case 'Jul':
//             return 7
//         case 'Aug':
//             return 8
//         case 'Seq':
//             return 9
//         case 'Oct':
//             return 10
//         case 'Nov':
//             return 11
//         case 'Dec':
//             return 12
//     }
// }


const confirm = () => {
    const params = {
        updateUserId: props.defaultValue.adminUserInfo.id,
        startTime: props.defaultValue.packageStartTimeStr,
        endTime: props.defaultValue.expireTimeStr
    }
    updateUserPackageTime(params).then(res => {
        if (res.code == 200) {
            ElMessage({ type: 'success', message: '更新时间成功!' })
            closeEvent()
        }
    })
}


const closeEvent = () => {
    props.hideAddMask()
    emits('showCreateCustomer')
}
</script>






<style>
.date-picker-popper {
    border: none !important;
    box-shadow: none !important;
}
</style>
<style scoped lang="less">
.new-mask {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .content {
        width: 490px;
        height: 408px;
        background: #FFFFFF;
        border-radius: 8px;
        padding: 20px;
        box-sizing: border-box;
        text-align: left;
        font-weight: 600;
        font-size: 18px;
        color: #3D566C;

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #D9D9D9;
            padding-bottom: 18px;

            &>div {
                font-weight: bold;
                font-size: 20px;
                color: #1E1E1E;
            }

            .closed {
                cursor: pointer;
                width: 16px;
                height: 16px;
            }
        }

        main {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            padding-top: 20px;

            footer {
                margin-top: 230px;
                width: 100%;
                display: flex;
                justify-content: flex-end;
            }
        }
    }
}
</style>