<template>
  <base-breadcrumb :breadcrumb-list="breadcrumbList"></base-breadcrumb>
  <el-form
    ref="ruleFormRef"
    :model="ruleForm"
    :rules="rules"
    label-width="120px"
    class="demo-ruleForm"
    :size="formSize"
  >
    <el-form-item label="Activity name" prop="name">
      <el-input v-model="ruleForm.name" />
    </el-form-item>
    <el-form-item label="Activity zone" prop="region">
      <el-select v-model="ruleForm.region" placeholder="Activity zone" popper-class="select-option">
        <el-option label="Zone one" value="shanghai" />
        <el-option label="Zone two" value="beijing" />
      </el-select>
    </el-form-item>
    <el-form-item label="Activity form" prop="desc">
      <el-input v-model="ruleForm.desc" type="textarea" />
    </el-form-item>
    <el-form-item label="Activity form" prop="resource">
      <el-upload
        v-model:file-list="ruleForm.resource"
        class="upload-demo"
        action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
        :on-success="handlePreview"
        :show-file-list="false"
        :on-remove="handleRemove"
        list-type="picture"
      >
        <el-button type="primary">Click to upload</el-button>
      </el-upload>
    </el-form-item>
    <div class="source-preview">
      <div>素材预览</div>
      <div>111</div>
    </div>
    <el-form-item>
      <el-button type="primary" @click="submitForm(ruleFormRef)"> Create </el-button>
      <el-button @click="resetForm(ruleFormRef)">Reset</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import BaseBreadcrumb from '@/components/layouts/BaseBreadcrumb.vue';
import type { FormInstance, FormRules, UploadProps, UploadUserFile } from 'element-plus';

const breadcrumbList = reactive([
  {
    name: '素材管理',
    path: '/source_material',
  },
  {
    name: '上传素材',
    path: '/upload_source',
  },
]);

interface RuleForm {
  name: string;
  region: string;
  desc: string;
  resource: string;
}

const formSize = ref('default');
const ruleFormRef = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  name: 'Hello',
  region: '',
  desc: '',
  resource: '',
});
ruleForm.resource = ref<UploadUserFile[]>([]);

const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {};

const handlePreview: UploadProps['onPreview'] = (file) => {};

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: 'Please input Activity name', trigger: 'blur' },
    { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
  ],
  region: [
    {
      required: true,
      message: 'Please select Activity zone',
      trigger: 'change',
    },
  ],
  desc: [{ required: true, message: 'Please input activity form', trigger: 'blur' }],
  resource: [{ required: true, message: 'Please input activity form', trigger: 'blur' }],
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {});
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
</script>
<style scoped lang="less">
.source-preview {
  text-align: left;
  margin-bottom: 60px;

  & > div {
    display: inline-block;
    vertical-align: top;
  }

  & > div:first-child {
    width: 120px;
    box-sizing: border-box;
    padding-right: 12px;
    text-align: right;
  }

  & > div:last-child {
    width: 200px;
    height: 200px;
    background-color: #ddd;
    // margin-left: 30px;
  }
}
</style>
