<template>
  <div class="personal-account">
    <div>
      <div class="default-avatar"></div>
      <div class="user-name">{{ phoneNo }}</div>
      <!-- <div class="user-phone">***********</div> -->
      <div class="log-out-btn" @click="router.push('/login2')">
        <img src="@/assets/images/experience-icon/log-out-icon.png" alt="">
        <span>退出登录</span>
      </div>
      <div class="service-agreement">
        <span @click="showAgree = 1">《服务协议》</span>
        <span @click="showAgree = 2">《隐私政策》</span>
      </div>
    </div>
  </div>
  <consent-agreement v-if="showAgree" :show-type="showAgree" :handle-hide="() => showAgree = 0"></consent-agreement>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import ConsentAgreement from '@/components/ConsentAgreement.vue'
import { useRouter } from 'vue-router'

const showAgree = ref(0)
const router = useRouter()
const phoneNo = ref('')

onMounted(() => {
  phoneNo.value =  window.localStorage.getItem('userName') || ''
})
</script>
<style scoped lang="less">
.personal-account {
  width: 290px;
  height: 342px;
  background-color: #FFFFFF;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  position: fixed;
  right: 60px;
  top: 80px;
  z-index: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-image: url(~@/assets/images/experience-icon/user-info-bg.png);
  background-size: 100% auto;
  background-repeat: no-repeat;
  padding: 60px 0 20px;
  box-sizing: border-box;
  z-index: 2;

  &>div {
    width: 150px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .default-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: url(~@/assets/images/default-avatar.png);
    background-size: 100% 100%;

    &::after {
      content: '\4f53\9a8c\5ba2\6237';
      position: absolute;
      left: 14px;
      top: 70px;
      width: 52px;
      height: 18px;
      line-height: 18px;
      font-weight: 600;
      background: #DDE2EC;
      border-radius: 2px;
      font-size: 10px;
      color: #3671FE;
    }
  }

  .user-name {
    margin: 28px 0 10px;
    font-weight: bold;
    font-size: 18px;
    color: #3D566C;
  }

  .user-phone {
    font-weight: 400;
    font-size: 14px;
    color: rgba(61, 86, 108, 0.3);
  }

  .log-out-btn {
    margin: 30px 0;
    width: 150px;
    height: 42px;
    line-height: 42px;
    background: rgba(54, 113, 254, 0.06);
    border-radius: 6px;
    font-weight: bold;
    font-size: 16px;
    color: #F05139;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    box-sizing: border-box;
    cursor: pointer;

    img {
      width: 16px;
      height: 16px;
    }
  }

  .service-agreement {
    font-weight: 400;
    font-size: 12px;
    color: #3671FE;
    width: 150px;
    display: flex;
    justify-content: space-between;

    &>span {
      cursor: pointer;
    }
  }
}
</style>