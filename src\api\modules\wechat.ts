import request from "../request";

const PROCESS_ENV = window.location.origin.indexOf("192.168.1.170") > -1 ? 'trial' : process.env.NODE_ENV == 'production' ? 'release' : 'develop'

// 获取小程序二维码
function getRequestWxAppQrCode(data: any) {
  return request({
    url: `/scene/getWxAppQrCode?sceneId=${data.sceneId || ""}&path=${data.path
      }&env_version=${PROCESS_ENV}`,
    method: "get",
  });
}

// 获取小程序分享链接
function getRequestWxUrlLink(data: any) {
  return request({
    url: "/scene/getWxUrlLink?sceneId=" + data.sceneId + '&path=pages/njyjxr/scene&env=' + PROCESS_ENV,
    method: "get",
  });
}

// 获取小程序二维码
export function getWxAppQrCode(data: any) {
  return getRequestWxAppQrCode(data)
}

// 获取小程序分享链接
export function getWxUrlLink(data: any) {
  return getRequestWxUrlLink(data)
}

// WeChat Mini Program APIs
export const wxApi = {
  // 获取小程序二维码
  getWxAppQrCode(data: any) {
    return getRequestWxAppQrCode(data)
  },

  // 获取小程序分享链接
  getWxUrlLink(data: any) {
    return getRequestWxUrlLink(data)
  }
};

// 小程序分享数据提交
export function addSceneShare(data: any) {
  return request({
    url: "/scene/addSceneShareV2",
    method: "post",
    data: data,
  });
}

// 获取小程序分享数据
export function getShareInfo(data: any) {
  return request({
    url: "/scene/getShareInfoV2?sceneId=" + data.sceneId,
    method: "get",
  });
}

// 更新小程序分享数据
export function updateSceneShare(data: any) {
  return request({
    url: "/scene/updateSceneShare",
    method: "post",
    data
  });
}

// 重置分享限制
export function resetShareLimit(data: any) {
  return request({
    url: "/scene/resetShareLimit?sceneId=" + data.sceneId,
    method: "post",
  });
}

// 新建小程序识别
export function saveWxIdentifySceneInfo(data: any) {
  return request({
    url: "/scene/saveWxIdentifySceneInfo",
    method: "post",
    data,
  });
}

// 获取微信小程序识别场景
export function getWxNotifySceneInfo(data: any) {
  return request({
    url: "/scene/getWxNotifySceneInfo?sceneId=" + data.sceneId,
    method: "post",
  });
}

// 更新点位绑定关系
export function updateWxIdentifyAttachInfo(data: any) {
  return request({
    url: "/scene/updateWxIdentifyAttachInfo?sceneId=" + data.sceneId,
    method: "post",
    data
  });
}

// 创建同款模板
export function copyWxScene(data: any) {
  return request({
    url: `/scene/copyWxScene?sourceSceneId=${data.sourceSceneId}&sceneName=${data.sceneName}`,
    method: 'post'
  });
}

// 保存场景
export function saveWxNoLocationScene(data: any) {
  return request({
    url: "/scene/saveWxNoLocationScene",
    method: "post",
    data,
  });
}

// 通过id获取场景
export function getWxNoLocationScene(data: any) {
  return request({
    url: "/scene/getWxNoLocationScene?sceneId=" + data.sceneId,
    method: "get",
  });
} 