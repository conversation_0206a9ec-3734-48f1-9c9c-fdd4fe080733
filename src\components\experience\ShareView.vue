<template>
  <div class="page">
    <div class="new-mask" v-if="!isUpdateTime">
      <div class="share" :class="{
        room_share: sceneData.sceneType == 1 && sceneData.scenePlatform == 3, eye_share: sceneData.sceneType == 1 && sceneData.scenePlatform != 3, roomHeight: sceneData.sceneType == 1 && sceneData.scenePlatform == 3
      }" v-loading="isUpdateLoading">
        <header>
          <div class="title">分享设置</div>
          <div class="closed" alt="" @click="closeEvent"></div>
        </header>
        <div class="content_"
          v-if="sceneData.sceneType != 1 || (sceneData.sceneType == 1 && sceneData.scenePlatform == 3)">
          <div class="c_left">
            <img class="image__" src="http://njyj.oss-cn-shanghai.aliyuncs.com/njyj/four/reset_.png" alt=""
              @click="handleClick">
            <img class="image" :src="defaultImage" @load="loadImg" />
          </div>
          <div class="c_right">
            <div class="name">项目名称</div>
            <div class="namevalue">{{ store.state.editSceneData.sceneName }}</div>
            <div class="tip1 tip">*支持jpg,png格式，宽高比为5:4</div>
            <div class="tip2 tip">推荐尺寸800X640px</div>
            <div class="upload" @click="clickCover">上传小程序封面</div>
            <input type="file" ref="coverRef" @change="handleChange" />
          </div>
        </div>
        <div class="room_con" v-if="sceneData.sceneType == 1">
          <div class="left">
            <img :src="defaultMapImage" alt="">
          </div>
          <div class="right">
            <div class="top">{{ sceneData.scenePlatform == 3 ? '项目地址' : '项目名称' }} </div>
            <el-input v-if="sceneData.scenePlatform == 3" v-model="sceneLocation" class="input-text" maxlength="40"
              placeholder="请输入演示地址"></el-input>
            <div class="namevalue" v-else>{{ store.state.editSceneData.sceneName }}</div>
            <div class="three" :class="{ top22: sceneData.scenePlatform != 3 }">*支持jpg,png格式，宽高比为5:4，推荐尺寸800X640px</div>
            <div class="upload" @click="clickLocal">上传地址图片</div>
            <input type="file" ref="localRef" @change="handleMapPhoto" />
          </div>
        </div>
        <div class="look" v-if="sceneData.sceneType != 1 || (sceneData.sceneType == 1 && sceneData.scenePlatform == 3)">
          <header>访问限制</header>
          <div class="con">
            <div class="left">
              <div class="top">今日免费访问次数剩余</div>
              <div class="bto">{{ shareInfo.visitCount }}次</div>
            </div>
            <div class="center">
              <div class="top">扩展访问次数剩余
                <el-tooltip class="box-item" popper-class="custom-tooltip" effect="dark"
                  :content="`每日免费分享次数${shareInfo.visitCount}次，超过之后将开始消耗扩展访问次数余量。24:00刷新免费访问次数。`" placement="top"><img
                    :src="isQuestion ? 'http://njyj.oss-cn-shanghai.aliyuncs.com/njyj/four/active-help.png' : 'http://njyj.oss-cn-shanghai.aliyuncs.com/njyj/four/help.png'"
                    @mouseenter="enterQuestion" @mouseleave="leaveQuestion" alt="">
                </el-tooltip>
              </div>
              <div class="bto">{{ shareInfo.extensionVisitCount || 0 }}次</div>
            </div>
            <div class="right">
              <div class="top" @mouseenter="enterSetting" @mouseleave="leaveSetting">访问时间段 <img
                  :src="isSetting ? 'http://njyj.oss-cn-shanghai.aliyuncs.com/njyj/four/blue-setting.png' : 'http://njyj.oss-cn-shanghai.aliyuncs.com/njyj/four/tup.png'"
                  alt="" @click="openTimeCard"></div>
              <div class="bto" v-if="startDay && endDay">{{ startDay }}~{{ endDay }}</div>
              <div class="bto" v-else>账号有效期内</div>
            </div>
          </div>
        </div>
        <div class="eye_local" v-if="sceneData.scenePlatform != 3">
          <div class="top">项目地址</div>
          <el-input v-model="sceneLocation" class="input-text" placeholder="请输入演示地址" maxlength="40"></el-input>
        </div>
        <div class="btn_layer">
          <el-button class="pri-btn" @click="closeEvent">取消</el-button>
          <el-button type="primary" class="pri-btn" color="#2E76FF" @click="addScene"
            style="margin-left: 12px;">保存</el-button>
        </div>
      </div>
      <div class="card" :class="{ eye_card: sceneData.scenePlatform != 3 }">
        <div class="content_">
          <div class="countAndHot" v-loading="isLoading">
            <img class="mask_img" :src="codeUrl" alt="" @load="load" v-if="sceneData.scenePlatform == 3">
            <qrcode-vue :value="qrContent" :size="138" foreground="#000000" class="qrcode" v-else />
          </div>
          <div class="right">
            <div class="top">{{ sceneData.scenePlatform == 3 ? '微信' : '' }}扫一扫立马体验</div>
            <div class="center" v-if="sceneData.scenePlatform == 3">*可以在微信小程序里分享给好友</div>
            <div class="bto botCode" @click="downloadQrCode">下载二维码</div>
          </div>
        </div>
        <div class="line" v-if="sceneData.scenePlatform == 3">
          <div class="url">{{ urlLink }}</div>
          <div class="right" @click="copyMiniPath">复制链接</div>
        </div>
      </div>
    </div>
    <div class="alert" v-else>
      <header>设置访问时间段</header>
      <el-date-picker v-model="timeValue" Placement="bottom-start" type="daterange" :clearable="false"
        start-placeholder="开始时间" end-placeholder="结束时间" />
      <footer>
        <el-button class="cancel_ btn" @click="isUpdateTime = false">取消</el-button>
        <el-button type="primary" class="pri-btn-plat btn" @click="updateTIme">更新</el-button>
      </footer>
    </div>
    <tips-view></tips-view>
  </div>
</template>

<script lang="ts" setup>
import qrcodeVue from 'qrcode.vue' // https://gitcode.com/gh_mirrors/qr/qrcode.vue/overview?utm_source=artical_gitcode&index=top&type=card&webUrl&isLogin=1
import { ref, onMounted, watch } from 'vue'
import { getShareInfo, addSceneShare, updateSceneShare, getWxAppQrCode, getWxUrlLink, getOssAccessPath, resetShareLimit, uploadLocationPic } from '@/api'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';
import TipsView from '@/components/TipsView.vue'
import html2canvas from 'html2canvas' // dom转canvas
import { desensitizte } from '@/utils'

const token = window.localStorage.getItem('token');
const njyj_version = window.localStorage.getItem("njyj-version");
const qrContent = ref<any>('')
const props = defineProps({
  hideMask: {
    default: null,
    type: Function
  },
  sceneData: {
    default: {},
    type: Object
  }
})

const startDay = ref('')
const endDay = ref('')
const timeValue = ref([])
const isUpdateTime = ref(false)
const isQuestion = ref(false)
const isSetting = ref(false)
const isSharePicLoading = ref(true)
let packageInfoDto = ref({})
const isLoading = ref(true)
const img_ = require('../../assets/images/demohaha.png')
const mapImg = require('../../assets/images/room_map.png')
const defaultImage = ref('http://njyj.oss-cn-shanghai.aliyuncs.com/save__.png')
const defaultMapImage = ref('http://njyj.oss-cn-shanghai.aliyuncs.com/njyj/four/map.png')
const router = useRouter()
const shareName = ref('')
const codeUrl = ref('')
const uploadImg: any = ref({})
const shareInfo: any = ref({})
const sceneId = ref('')
const isUpdateLoading = ref(false)
const sceneLocation = ref('')
const remainDay = ref(0)
const remainCount = ref(0)
const localRef = ref()
const coverRef = ref()
const urlLink = ref('加载中 ~ ~ ~')
const localPicKey = ref({ key: '' })
const headers: any = {
  'Content-Type': 'text/plain',
  token: token
}
if (njyj_version) {
  // headers["njyj-version"] = "tony";
}

const store = useStore();

const enterQuestion = () => {
  isQuestion.value = true
}

const leaveQuestion = () => {
  isQuestion.value = false
}

const updateTIme = () => {
  startDay.value = formateTime(timeValue.value[0])
  endDay.value = formateTime(timeValue.value[1])
  isUpdateTime.value = false
}

const formateTime = (dateStr) => {
  const date = new Date(dateStr);
  // 获取年、月、日
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要+1
  const day = String(date.getDate()).padStart(2, '0');
  // 组合成 xxxx-yy-dd 格式
  const formattedDate = `${year}-${month}-${day}`;
  return formattedDate
}

const load = () => {
  isLoading.value = false
}

const enterSetting = () => {
  isSetting.value = true
}

const leaveSetting = () => {
  isSetting.value = false
}

const openTimeCard = () => {
  isUpdateTime.value = true
}

const handleClick = () => {
  defaultImage.value = 'http://njyj.oss-cn-shanghai.aliyuncs.com/save__.png'
  uploadImg.value.picBase64Str = null
}

const clickCover = () => {
  coverRef.value.click()
}

const clickLocal = () => {
  localRef.value.click()
}

const loadImg = (e) => {
  e.target.crossOrigin = 'Anonymous'
  const canvas = document.createElement('canvas')
  canvas.width = e.target.clientWidth
  canvas.height = e.target.clientHeight
  const ctx = canvas.getContext('2d')
  ctx?.drawImage(e.target, 0, 0, canvas.width, canvas.height)
  const ext = e.target.src.substring(e.target.src.lastIndexOf('.') + 1).toLowerCase()
  const dataURL = canvas.toDataURL('image/' + ext)
  uploadImg.value.picBase64Str = dataURL
}

const copyMiniPath = () => {
  // 创建一个隐藏的文本区域
  var textArea = document.createElement('textarea');
  textArea.value = urlLink.value;
  document.body.appendChild(textArea);
  // 选中文本
  textArea.select();
  // 执行复制命令
  try {
    var successful = document.execCommand('copy');
    var msg = successful ? '成功' : '失败';
    ElMessage({ type: 'success', message: '路径已复制到剪贴板' })
  } catch (err) {
    console.error('无法复制文本', err);
  }
  // 移除文本区域
  document.body.removeChild(textArea);
}

const addScene = async () => {
  let mapInfo = {}
  const pageQuery: any = router.currentRoute.value.query
  const scene_id = pageQuery.sceneid || ''
  const hasSensitiveWords = await desensitizte(shareName.value, '分享名称不可包含敏感词汇！')
  if (hasSensitiveWords) return
  isUpdateLoading.value = true

  if (props.sceneData.sceneType == 1) {
    mapInfo['sceneLocation'] = sceneLocation.value
    mapInfo['sceneLocationPicKey'] = localPicKey.value.key
  }
  shareName.value = props.sceneData.sceneName
  updateSceneShare({ ...mapInfo, picBase64Str: uploadImg.value.picBase64Str, startDate: startDay.value, endDate: endDay.value, id: shareInfo.value.id, shareName: shareName.value, sceneId: +sceneId.value, userId: shareInfo.value.userId, qrCode: codeUrl.value }).then(async (res: any) => {
    if (res.code == 200) {
      await getWXCode()
      await requestGetWxUrlLink(scene_id)
      ElMessage({ type: 'success', message: '更新成功' })
    } else {
      store.state.showTips = res.data.message
    }
    isUpdateLoading.value = false
  }).then().catch(() => {
    isUpdateLoading.value = false
  })
}

const requestResetShareLimit = () => {
  const pageQuery: any = router.currentRoute.value.query
  const scene_id = pageQuery.sceneid || ''
  const params = {
    sceneId: scene_id
  }
  resetShareLimit(params).then(res => {
    if (res.code == 200 && res.data) {
      remainDay.value = res.data.secondData
      remainCount.value = res.data.firstData
      shareInfo.value.canReset = false
    }
  })
}


const downloadQrCode = () => {
  if (props.sceneData.scenePlatform == 3) {
    const link = document.createElement('a');
    link.href = codeUrl.value;
    link.download = props.sceneData.sceneName; // 指定下载的文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } else {
    const element = document.querySelector('.countAndHot') // 获取到要转为图片的dom
    html2canvas(element).then(canvas => {
      const dataURL = canvas.toDataURL('image/png');
      const a = document.createElement('a');
      a.href = dataURL;
      a.download = props.sceneData.sceneName + '.png';
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    })
  }
}

const closeEvent = () => {
  props.hideMask()
}


const getWXCode = async () => {
  return new Promise(async resolve => {
    const { data } = await getWxAppQrCode({ sceneId: sceneId.value, path: 'pages/njyjxr/scene' });
    if (data) {
      codeUrl.value = 'data:image/jpeg;base64,' + data;
    }
    resolve(1)
  })
}

const handleMapPhoto = (e: any) => {
  const file = e.target.files[0]; // 获取文件引用
  e.target.value = ''
  console.log(file);

  if (!file) return;

  var imageType = /image.*/;

  let sourceName = file.name.split('.').slice(0, -1).join('.');
  if (sourceName.includes('.') || sourceName.includes('&')) {
    store.state.showTips = '请检查图片命名，避免使用一些特殊字符';
    return false
  }

  if (!file.type.match(imageType)) {
    store.state.showTips = '请选择图片'
    return
  }
  if (file.size / 1024 > 512) {
    store.state.showTips = '大小不超过512kb'
    return
  }
  const reader = new FileReader(); // 创建FileReader对象
  reader.onload = function (e: any) {
    const base64String = e.target.result;
    // 在这里你可以使用base64String，它是图片的Base64编码
    const img = new Image();
    img.onload = async function () {
      defaultMapImage.value = base64String
      localPicKey.value = await requestUploadLocationPic(file)
    };
    img.src = base64String;
  };

  reader.onerror = function (e: any) {
    console.error("File could not be read! Code " + e.target.error.code);
  };

  reader.readAsDataURL(file); // 读取文件并转换为Base64
}


const requestUploadLocationPic = (file) => {
  return new Promise(resolve => {
    const formData = new FormData();
    formData.append('pic', file);
    uploadLocationPic(formData).then(res => {
      if (res.code == 200) {
        resolve(res.data)
      }
    })
  })
}

const handleChange = (e: any) => {
  const file = e.target.files[0]; // 获取文件引用
  e.target.value = ''
  if (!file) return;

  var imageType = /image.*/;
  let sourceName = file.name.split('.').slice(0, -1).join('.');
  if (sourceName.includes('.') || sourceName.includes('&')) {
    store.state.showTips = '请检查图片命名，避免使用一些特殊字符';
    return false
  }

  if (!file.type.match(imageType)) {
    store.state.showTips = '请选择图片'
    return
  }
  if (file.size / 1024 > 512) {
    store.state.showTips = '大小不超过512kb'
    return
  }


  const reader = new FileReader(); // 创建FileReader对象
  reader.onload = function (e: any) {
    const base64String = e.target.result;
    // 在这里你可以使用base64String，它是图片的Base64编码
    const img = new Image();
    img.onload = function () {
      uploadImg.value.picBase64Str = base64String
      defaultImage.value = base64String
    };
    img.src = base64String;
  };

  reader.onerror = function (e: any) {
    console.error("File could not be read! Code " + e.target.error.code);
  };

  reader.readAsDataURL(file); // 读取文件并转换为Base64
}

const requestGetWxUrlLink = (scene_id) => {
  return new Promise(resolve => {
    const params = {
      sceneId: scene_id
    }
    getWxUrlLink(params).then(res => {
      if (res.code == 200 && res.data) {
        urlLink.value = res.data
        resolve(1)
      }
    })
  })
}

const getShareUrlOsskey = (ossKey) => {
  getOssAccessPath({ key: ossKey }).then(async (res: any) => {
    if (res.code == 200) {
      defaultImage.value = res.data
      isSharePicLoading.value = false
    }
  })
}

const getShareMapUrlOssKey = (ossKey) => {
  getOssAccessPath({ key: ossKey }).then(async (res: any) => {
    if (res.code == 200) {
      defaultMapImage.value = res.data
    }
  })
}

watch(() => shareInfo.value, (nv) => {
  if (nv) {
    remainDay.value = nv.limitDate
    remainCount.value = nv.visitCount
  }
})


const getRequestLocalOsskey = (ossKey) => {
  getOssAccessPath({ key: ossKey }).then((res: any) => {
    defaultMapImage.value = res.data
  })
}

const designMakeRecursion = (scene_id) => {
  getShareInfo({ sceneId: scene_id }).then(async (res: any) => {
    if (res.code == '200') {
      if (res.data) {
        shareInfo.value = res.data;
        shareName.value = res.data.shareName
        uploadImg.value = { ...uploadImg.value, sharePic: res.data.sharePic }
        codeUrl.value = res.data.qrCode
        if (res.data.startDate) {
          timeValue.value[0] = new Date(res.data.startDate)
          timeValue.value[1] = new Date(res.data.endDate)
          startDay.value = res.data.startDate.split('T')[0]
          endDay.value = res.data.endDate.split('T')[0]
        }
        if (res.data.sharePic) {
          getShareUrlOsskey(res.data.sharePic)
        }
        if (res.data.sceneLocationPicKey) {
          getRequestLocalOsskey(res.data.sceneLocationPicKey)
        }
        if (res.data.sceneLocation) {
          sceneLocation.value = res.data.sceneLocation
        }
        await getWXCode()
      } else {
        // 没有小程序二维码情况下生成二维码
        await getWXCode()
        shareName.value = props.sceneData.sceneName
        addSceneShare({ picBase64Str: uploadImg.value.picBase64Str || null, shareName: shareName.value, sceneId: +sceneId.value, qrCode: codeUrl.value }).then((res: any) => {
          if (res.code == 200) {
            designMakeRecursion(scene_id)
          }
        })
      }
    }
  })
}

const handleCodeInfo = () => {
  isLoading.value = false
  // 示例
  const key = "h7K3s9jW5n2D1qXo";  // 必须 16 字节密钥
  const encryptedMessage = encryptString(props.sceneData.id + '', key);
  qrContent.value = encryptedMessage
}


function encryptString(message, key) {
  // 确保 key 长度为 16 字节
  const keyUtf8 = CryptoJS.enc.Utf8.parse(key.substring(0, 16));
  const iv = CryptoJS.enc.Utf8.parse("1234567890123456"); // 固定 16 字节 IV
  // 使用 AES 加密
  const encrypted = CryptoJS.AES.encrypt(message, keyUtf8, { iv: iv, padding: CryptoJS.pad.Pkcs7 });
  // 将 IV 和密文一起返回（Base64 编码）
  return iv.toString(CryptoJS.enc.Base64) + ":" + encrypted.toString();
}



onMounted(() => {
  packageInfoDto.value = JSON.parse(window.sessionStorage.getItem('packageInfoDto'))
  const pageQuery: any = router.currentRoute.value.query
  const scene_id = pageQuery.sceneid || ''
  sceneId.value = scene_id
  requestGetWxUrlLink(scene_id)
  designMakeRecursion(scene_id)

  if (props.sceneData.scenePlatform != 3) {
    handleCodeInfo()
  }
})
</script>

<style>
.custom-tooltip {
  max-width: 200px !important;
  background: rgba(0, 0, 0, 0.70) !important;
  word-break: break-word !important;
}
</style>
<style scoped lang="less">
.page {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow-x: hidden;
  overflow-y: scroll;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);

}

.new-mask {
  width: 100vw;
  // min-height: 500px;
  overflow-y: scroll;


  .roomHeight {
    height: 590px !important;
    min-height: 590px;
  }

  .share {
    position: relative;
    padding: 24px;
    box-sizing: border-box;
    width: 551px;
    height: 420px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    overflow: hidden;
    margin-left: 50%;
    transform: translateX(-50%);

    header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-weight: bold;
        font-size: 18px;
        color: #1E1E1E;
      }

      .closed {
        width: 28px;
        height: 28px;
        background: url('http://njyj.oss-cn-shanghai.aliyuncs.com/close.png') no-repeat;
        background-size: 100% 100%;
        cursor: pointer;

        &:hover {
          background: url('http://njyj.oss-cn-shanghai.aliyuncs.com/blue_icon.png') no-repeat;
          background-size: 100% 100%;
        }
      }
    }

    .content_ {
      display: flex;
      background: #fff !important;
      margin-top: 25px;

      .c_left {
        position: relative;
        width: 191px;
        height: 153px;

        .image {
          width: 100%;
          height: 100%;
        }

        .image__ {
          width: 20px !important;
          height: 20px !important;
          position: absolute;
          right: 4px;
          top: 4px;
          z-index: 99;
          object-fit: cover;
          cursor: pointer;
        }
      }

      .c_right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 12px;

        .name {
          font-weight: 500;
          font-size: 14px;
          color: #797979;
          text-align: left;
          margin-bottom: 6px;
        }

        .namevalue {
          font-weight: 400;
          font-size: 14px;
          color: #1E1E1E;
          text-align: left;
          margin-bottom: 14px;
        }

        .tip {
          font-weight: 400;
          font-size: 12px;
          color: #797979;
          line-height: 14px;
          text-align: left;
        }

        .tip2 {
          margin-top: 1px;
        }

        .upload {
          position: relative;
          width: 116px;
          height: 26px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #2E76FF;
          font-size: 12px;
          color: #2E76FF;
          line-height: 26px;
          text-align: center;
          margin-top: 14px;
          cursor: pointer;

          &:hover {
            background: #2e76ff;
            color: #fff;
          }
        }

        input {
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 0;
          opacity: 0;
        }
      }
    }

    .look {
      margin-top: 24px;

      header {
        font-weight: bold;
        font-size: 14px;
        color: #797979;
        margin-bottom: 12px;
      }

      .con {
        width: 503px;
        height: 55px;
        background: #F7F7F7;
        box-sizing: border-box;
        display: flex;
        justify-content: space-around;
        align-items: center;


        .left,
        .center,
        .right {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 39px;
          border-right: 1px solid #DADADA;

          .top {
            font-weight: 400;
            font-size: 12px;
            display: flex;
            align-items: center;
            color: #797979;

            img {
              width: 16px;
              height: 16px;
              margin-left: 3px;
              cursor: pointer;
              transform: translateY(-1px);
            }
          }

          .bto {
            font-weight: bold;
            font-size: 14px;
            color: #000000;
          }
        }

        .left {
          width: 149px;
        }

        .center {
          width: 157px;
        }

        .right {
          width: 197px;
          border: none;
          padding-right: 0;
        }
      }
    }

    .btn_layer {
      display: flex;
      justify-content: center;
      align-items: center;

      .pri-btn {
        width: 92px;
        height: 32px;
        border-radius: 4px 4px 4px 4px;
        margin-top: 24px;
      }
    }
  }

  .room_con {
    display: flex;
    align-items: center;
    margin-block: 24px;

    .left {
      width: 191px;
      height: 153px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .right {
      margin-left: 12px;

      .top {
        font-weight: 500;
        font-size: 14px;
        color: #797979;
        margin-bottom: 11px;
        text-align: left;
      }

      .namevalue {
        font-weight: 400;
        font-size: 14px;
        color: #1E1E1E;
        text-align: left;
        margin-bottom: 14px;
      }

      .input-text {
        width: 302px;
        height: 32px;
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #DADADA;

        &::placeholder {
          font-size: 14px;
          color: #797979;
        }
      }

      .three {
        font-size: 12px;
        color: #797979;
        margin-top: 12px;
      }

      .top22 {
        margin-top: 22px;
      }

      .upload {
        position: relative;
        width: 116px;
        height: 26px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #2E76FF;
        font-size: 12px;
        color: #2E76FF;
        line-height: 26px;
        text-align: center;
        margin-top: 14px;
        cursor: pointer;

        &:hover {
          background: #2e76ff;
          color: #fff;
        }
      }
    }

    input {
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      opacity: 0;
    }
  }

  .room_share {
    height: 559px;
  }

  .eye_share {
    height: 396px;
  }

  .eye_local {
    margin-top: 24px;

    .top {
      font-weight: 500;
      font-size: 14px;
      color: #797979;
      text-align: left;
      margin-bottom: 12px;
    }

    .input-text {
      width: 100%;
      height: 32px;
      border-radius: 4px 4px 4px 4px;
    }
  }

  .card {
    width: 550px;
    height: 242px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 24px;
    box-sizing: border-box;
    margin-top: 6px;
    margin-left: 50%;
    transform: translateX(-50%);

    .content_ {
      display: flex;
      align-items: center;

      .countAndHot {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 138px;
        height: 138px;
        border: 1px solid #E7E7E7;

        .mask_img {
          width: 99%;
          height: 99%;
        }
      }

      .right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        margin-left: 12px;

        .top {
          font-weight: 500;
          font-size: 14px;
          color: #797979;
          margin-bottom: 12px;
        }

        .center {
          font-weight: 400;
          font-size: 12px;
          color: #797979;
          margin-bottom: 12px;
        }

        .bto {
          width: 92px;
          height: 26px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #2E76FF;
          font-size: 12px;
          color: #2E76FF;
          line-height: 26px;
          text-align: center;
          cursor: pointer;

          &:hover {
            background: #2E76FF;
            color: #fff;
          }
        }
      }
    }

    .line {
      margin-top: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .url {
        width: 406px;
        height: 32px;
        line-height: 32px;
        background: #F7F7F7;
        border-radius: 10px 10px 10px 10px;
        font-weight: 400;
        font-size: 14px;
        color: #1E1E1E;
        box-sizing: border-box;
        padding-left: 12px;
        text-align: left;
      }

      .right {
        width: 88px;
        height: 32px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #2E76FF;
        cursor: pointer;
        font-size: 14px;
        color: #2E76FF;
        line-height: 32px;
        text-align: center;

        &:hover {
          background: #2E76FF;
          color: #fff;
        }
      }
    }
  }

  .eye_card {
    height: 186px;
  }


}

.alert {
  position: relative;
  width: 389px;
  height: 190px;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  box-sizing: border-box;
  padding-top: 26px;

  header {
    text-align: left;
    font-weight: bold;
    font-size: 18px;
    color: #1E1E1E;
    margin-bottom: 25px;
  }

  footer {
    position: absolute;
    right: 24px;
    bottom: 24px;
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;

    .btn {
      width: 92px;
    }
  }
}

.botCode:hover {
  background: #2e76ff;
  color: #fff;
}
</style>
