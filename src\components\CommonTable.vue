<template>
  <div class="common-table-wrapper">
    <el-table
      :data="tableData"
      style="width: 100%"
      v-loading="loading"
      :element-loading-text="loadingText"
      :header-cell-style="headerCellStyle"
      class="common-table">
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth">
        <!-- 表头自定义 -->
        <template #header v-if="column.headerFormatter">
          <component :is="column.headerFormatter" />
        </template>
        <!-- 单元格内容 -->
        <template #default="scope" v-if="column.formatter">
          <component :is="column.formatter" :row="scope.row" :value="scope.row[column.prop]" />
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div class="empty-state">
          <img :src="emptyImage" class="empty-img" />
          <p class="empty-text">{{ emptyText }}</p>
        </div>
      </template>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="showPagination && total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// 表格列配置接口
interface TableColumn {
  prop: string;
  label: string;
  width?: string | number;
  minWidth?: string | number;
  formatter?: any;
  headerFormatter?: any; // 添加表头格式化函数
}

// Props
interface Props {
  data: any[];
  columns: TableColumn[];
  loading?: boolean;
  loadingText?: string;
  showPagination?: boolean;
  total?: number;
  currentPage?: number;
  pageSize?: number;
  pageSizes?: number[];
  paginationLayout?: string;
  emptyText?: string;
  emptyImage?: string;
  headerCellStyle?: object;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  columns: () => [],
  loading: false,
  loadingText: '加载中...',
  showPagination: true,
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: () => [10, 20, 50],
  paginationLayout: 'prev, pager, next',
  emptyText: '暂无内容',
  emptyImage: require('@/assets/images/nolistimg.png'),
  headerCellStyle: () => ({ background: '#f8f9fa', color: '#333' }),
});

// Emits
const emit = defineEmits<{
  'update:currentPage': [value: number];
  'update:pageSize': [value: number];
  'size-change': [size: number];
  'current-change': [page: number];
}>();

// 响应式数据
const tableData = computed(() => props.data);
const currentPage = computed({
  get: () => props.currentPage,
  set: (value: number) => emit('update:currentPage', value),
});
const pageSize = computed({
  get: () => props.pageSize,
  set: (value: number) => emit('update:pageSize', value),
});

// 分页事件处理
const handleSizeChange = (size: number) => {
  emit('update:pageSize', size);
  emit('size-change', size);
};

const handleCurrentChange = (page: number) => {
  emit('update:currentPage', page);
  emit('current-change', page);
};
</script>

<style scoped lang="less">
.common-table-wrapper {
  .common-table {
    background-color: transparent !important;
    border-radius: 4px;
    overflow: hidden;

    .el-table__inner-wrapper:before {
      background-color: transparent;
    }
  }

  .pagination-wrapper {
    margin-top: 24px;
    display: flex;
    width: 100%;
    justify-content: center;

    .el-pagination {
      .el-pager {
        .number {
          &.is-active {
            background-color: #2e76ff;
            border-color: #2e76ff;
          }
        }
      }

      .btn-next,
      .btn-prev {
        &:hover {
          color: #2e76ff;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;

    .empty-img {
      width: 120px;
      height: 120px;
      margin-bottom: 0px;
      opacity: 0.6;
    }

    .empty-text {
      position: relative;
      top: -30px;
      font-size: 14px;
      color: #999;
      margin: 0;
    }
  }
}
</style>

<style lang="less">
// 全局样式，参考 TableList2.vue 的实现方式
.common-table-wrapper .common-table {
  .el-table__empty-block {
    border-left: 1px solid #e6edf7;
    border-right: 1px solid #e6edf7;
    box-sizing: border-box;
  }
}

.common-table-wrapper .el-table th.el-table__cell,
.common-table-wrapper .el-table tr {
  height: 42px;
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;

  & > td {
    border-bottom: 1px solid #e6edf7;
    box-sizing: border-box;
  }

  & > th {
    border-top: 1px solid #e6edf7 !important;
  }

  & > th:first-child {
    border-left: 1px solid #e6edf7 !important;
  }

  & > th:last-child {
    border-right: 1px solid #e6edf7 !important;
  }
}

.common-table-wrapper .el-table tr td.el-table__cell:last-child {
  border-right: 1px solid #e6edf7 !important;
}

.common-table-wrapper .el-table tr td.el-table__cell:first-child {
  border-left: 1px solid #e6edf7 !important;
}

.common-table-wrapper .el-table__header-wrapper th {
  background-color: rgba(230, 237, 247, 0.3) !important;
  font-weight: 400 !important;
  font-size: 12px !important;
  color: #797979 !important;
}

.common-table-wrapper .el-table__header-wrapper tr {
  height: 32px;
}

.common-table-wrapper .el-table th {
  text-align: center;
}

.common-table-wrapper .el-table th:first-child {
  text-align: left;
}

.common-table-wrapper .el-table th:last-child {
  text-align: right;
}

.common-table-wrapper .el-table td {
  text-align: center;
}

.common-table-wrapper .el-table td:first-child {
  text-align: left;
}

.common-table-wrapper .el-table td:last-child {
  text-align: right;
}
</style>
