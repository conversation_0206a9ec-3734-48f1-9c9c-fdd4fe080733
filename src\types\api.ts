// 通用响应类型
export interface ApiResponse<T = any> {
  code: number | string;
  data: T;
  message?: string;
  error: boolean;
  ok: boolean;
}

// 分页参数类型
export interface PaginationParams {
  pageNo: number;
  pageSize: number;
  getTotal?: boolean;
}

// 分页响应类型
export interface PaginationResponse<T> {
  list: T[];
  total: number;
  pageNo: number;
  pageSize: number;
}

// 用户相关类型
export interface LoginParams {
  username: string;
  password: string;
}

export interface UserInfo {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  organizationId?: string;
}

// 素材相关类型
export interface MaterialParams extends PaginationParams {
  materialName?: string;
  materialType?: string;
  platformV2?: string;
}

export interface MaterialData {
  materialId: string;
  materialName: string;
  materialType: string;
  createTime: string;
  updateTime: string;
}

// 组织相关类型
export interface OrganizationParams extends PaginationParams {
  organizationName?: string;
  organizationType?: string;
  packageId?: string;
}

export interface OrganizationInfo {
  id: string;
  name: string;
  type: string;
  packageId?: string;
  createTime: string;
  updateTime: string;
}

// 场景相关类型
export interface SceneMetaData {
  id: string;
  name: string;
  type: string;
  createTime: string;
  updateTime: string;
}

// 路径导航相关类型
export interface NaviNodeData {
  nodeId: string;
  nodeName: string;
  nodeType: number;
  x: number;
  y: number;
  z: number;
}

export interface GraphData {
  id: string;
  graphName: string;
  graphInfo?: string;
  planeHeight?: number;
}

// 组织机构相关类型
export interface OrganizationCreateParams {
  packageId: string;
  isOperation: boolean;
  sceneDuration: number;
  organizationName: string;
  organizationType: string;
  [key: string]: any;
}

// 场景相关类型
export interface SceneParams extends PaginationParams {
  sceneName?: string;
  sceneType?: string;
}

export interface SceneData {
  id: string;
  sceneName: string;
  sceneType: string;
  createTime: string;
  updateTime: string;
}

// 设备相关类型
export interface EquipmentParams extends PaginationParams {
  equipName?: string;
}

export interface EquipmentData {
  id: string;
  equipName: string;
  equipType: string;
  status: number;
  createTime: string;
  updateTime: string;
}

// 系统日志相关类型
export interface SystemLogParams extends PaginationParams {
  optType: string;
}

export interface SystemLogData {
  id: string;
  optType: string;
  optContent: string;
  createTime: string;
  operator: string;
}

// 空间相关类型
export interface SpaceParams extends PaginationParams {
  spaceName?: string;
}

export interface SpaceData {
  id: string;
  spaceName: string;
  spaceType: string;
  roomStructurePath?: string;
  createTime: string;
  updateTime: string;
}

export interface Package {
  packageName: string;
  expireDay: number;
  arSceneNum: number;
  planeSceneNum: number;
  packagePlatformList: string[];
  spacePicSingleNum: number;
  spacePicTotalNum: number;
  immersalToken: string;
  spaceNum: number | null;
  materialUploadSize: number;
  singleUploadSize: number;
}
