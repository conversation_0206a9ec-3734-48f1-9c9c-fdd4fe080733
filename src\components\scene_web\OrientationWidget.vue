<template>
  <div
    ref="widgetContainer"
    class="orientation-widget"
    :style="{
      position: 'absolute',
      bottom: '20px',
      left: '50%',
      transform: 'translateX(-50%)',
      width: widgetSize + 'px',
      height: widgetSize + 'px',
      zIndex: 1000,
      pointerEvents: 'auto',
    }">
    <canvas
      ref="widgetCanvas"
      :width="widgetSize"
      :height="widgetSize"
      @mousedown="onMouseDown"
      @mousemove="onMouseMove"
      @mouseup="onMouseUp"
      @mouseleave="onMouseUp"
      style="border-radius: 50%; cursor: grab" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import {
  Scene,
  PerspectiveCamera,
  WebGLRenderer,
  SphereGeometry,
  MeshBasicMaterial,
  Mesh,
  Group,
  BoxGeometry,
  CylinderGeometry,
  ConeGeometry,
  Vector3,
  Euler,
  Quaternion,
  Raycaster,
  Vector2,
  Color,
  AmbientLight,
  DirectionalLight,
} from 'three';

// Props
interface Props {
  mainCamera?: any; // 主场景的摄像机
  mainControls?: any; // 主场景的控制器
  size?: number; // 控件大小
  animationDuration?: number; // 视角切换动画时长（毫秒）
  enableAnimation?: boolean; // 是否启用平滑动画
  showSphere?: boolean; // 是否显示外壳球体
}

const props = withDefaults(defineProps<Props>(), {
  size: 120,
  animationDuration: 1000,
  enableAnimation: true,
  showSphere: false, // 默认隐藏外壳，获得更简洁的视觉效果
});

// Refs
const widgetContainer = ref<HTMLDivElement>();
const widgetCanvas = ref<HTMLCanvasElement>();

// 组件状态
const widgetSize = ref(props.size);
const isDragging = ref(false);
const previousMouse = ref(new Vector2());

// 平滑拖动相关状态
const smoothingFactor = 0.15; // 平滑系数，值越小越平滑但响应越慢

// 阻尼和惯性相关状态
const velocity = ref(new Vector2(0, 0)); // 当前旋转速度向量
const dampingFactor = 0.92; // 阻尼系数，值越小衰减越快
const minVelocity = 0.001; // 最小速度阈值，低于此值停止动画
const maxVelocity = 0.5; // 最大速度限制，防止过快旋转
let dampingAnimationId: number | null = null; // 阻尼动画ID

// 相机视角切换动画相关状态
const isAnimating = ref(false); // 是否正在执行视角切换动画
let viewAnimationId: number | null = null; // 视角切换动画ID

// Three.js 对象
let scene: Scene;
let camera: PerspectiveCamera;
let renderer: WebGLRenderer;
let orientationGroup: Group;
let raycaster: Raycaster;
let hoveredObject: Mesh | null = null;

// 方向指示器对象
const directionObjects: { [key: string]: Mesh } = {};

// 初始化Three.js场景
const initThreeJS = () => {
  // 创建场景
  scene = new Scene();

  // 创建摄像机
  camera = new PerspectiveCamera(50, 1, 0.1, 1000);
  camera.position.set(0, 0, 5);

  // 创建渲染器
  renderer = new WebGLRenderer({
    canvas: widgetCanvas.value!,
    alpha: true,
    antialias: true,
  });
  renderer.setSize(widgetSize.value, widgetSize.value);
  renderer.setClearColor(0x000000, 0.1); // 半透明背景

  // 创建射线投射器
  raycaster = new Raycaster();

  // 添加光源
  const ambientLight = new AmbientLight(0xffffff, 0.6);
  scene.add(ambientLight);

  const directionalLight = new DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(1, 1, 1);
  scene.add(directionalLight);

  // 创建方向控制器
  createOrientationWidget();

  // 开始渲染循环
  animate();

  console.log('🧭 方向控制器初始化完成');
};

// 创建方向控制器
const createOrientationWidget = () => {
  orientationGroup = new Group();

  // 创建半透明外壳球体（可选显示）
  const sphereGeometry = new SphereGeometry(1.8, 32, 32);
  const sphereMaterial = new MeshBasicMaterial({
    color: 0x444444,
    transparent: true,
    opacity: 0.1,
    wireframe: true,
  });
  const sphere = new Mesh(sphereGeometry, sphereMaterial);
  sphere.visible = props.showSphere; // 根据props控制外壳球体的可见性
  sphere.name = 'orientation-sphere'; // 添加名称便于识别
  orientationGroup.add(sphere);

  // 创建坐标轴
  createAxisIndicators();

  scene.add(orientationGroup);
};

// 创建坐标轴指示器
const createAxisIndicators = () => {
  const axisLength = 1.8; // 稍微缩短轴长度
  const axisRadius = 0.03; // 稍微细一点的轴线
  const coneHeight = 0.25; // 稍微短一点的箭头
  const coneRadius = 0.08; // 稍微细一点的箭头

  console.log('🎨 开始创建坐标轴指示器');

  // X轴 (红色) - 水平方向
  createAxis(
    'x',
    new Vector3(axisLength, 0, 0),
    0xd95153,
    axisLength,
    axisRadius,
    coneHeight,
    coneRadius
  );
  createAxis(
    '-x',
    new Vector3(-axisLength, 0, 0),
    0xb73e40, // 稍微深一点的红色
    axisLength,
    axisRadius,
    coneHeight,
    coneRadius
  );

  // Y轴 (绿色) - 垂直方向
  createAxis(
    'y',
    new Vector3(0, axisLength, 0),
    0x31deaa,
    axisLength,
    axisRadius,
    coneHeight,
    coneRadius
  );
  createAxis(
    '-y',
    new Vector3(0, -axisLength, 0),
    0x28b88a, // 稍微深一点的绿色
    axisLength,
    axisRadius,
    coneHeight,
    coneRadius
  );

  // Z轴 (蓝色) - 前后方向
  createAxis(
    'z',
    new Vector3(0, 0, axisLength),
    0x268eff,
    axisLength,
    axisRadius,
    coneHeight,
    coneRadius
  );
  createAxis(
    '-z',
    new Vector3(0, 0, -axisLength),
    0x1f73cc, // 稍微深一点的蓝色
    axisLength,
    axisRadius,
    coneHeight,
    coneRadius
  );

  console.log('✅ 坐标轴指示器创建完成');
};

// 创建单个坐标轴
const createAxis = (
  name: string,
  position: Vector3,
  color: number,
  axisLength: number,
  axisRadius: number,
  coneHeight: number,
  coneRadius: number
) => {
  const group = new Group();

  // 创建轴线 - 使用CylinderGeometry而不是BoxGeometry
  const cylinderGeometry = new CylinderGeometry(axisRadius, axisRadius, axisLength, 8);
  const cylinderMaterial = new MeshBasicMaterial({ color });
  const cylinder = new Mesh(cylinderGeometry, cylinderMaterial);

  // 创建圆球指示器（替代箭头）
  const sphereGeometry = new SphereGeometry(coneRadius * 2, 16, 16); // 更大的圆球，更容易看到
  const sphereMaterial = new MeshBasicMaterial({ color });
  const sphere = new Mesh(sphereGeometry, sphereMaterial);

  // 计算位置时直接使用传入的position向量

  // 根据轴向设置正确的旋转（只需要旋转轴线，圆球不需要旋转）
  if (name.includes('x')) {
    // X轴：水平方向（红色）
    cylinder.rotation.z = Math.PI / 2;
  } else if (name.includes('y')) {
    // Y轴：垂直方向（绿色）- 默认方向已正确
    // 圆柱体默认是垂直的，不需要旋转
  } else if (name.includes('z')) {
    // Z轴：前后方向（蓝色）
    cylinder.rotation.x = Math.PI / 2;
  }

  // 直接使用传入的position来设置位置，不需要额外的负方向处理
  cylinder.position.copy(
    position
      .clone()
      .normalize()
      .multiplyScalar(axisLength / 2)
  );
  // 将圆球放置在背景球体外部，确保可见（背景球体半径1.8）
  sphere.position.copy(
    position.clone().normalize().multiplyScalar(1.9) // 稍微超出背景球体
  );

  group.add(cylinder);
  group.add(sphere);
  group.name = name;
  group.userData = { axis: name, originalColor: color };

  orientationGroup.add(group);
  directionObjects[name] = group as any;

  console.log(
    `🎨 创建坐标轴: ${name}, 颜色: #${color.toString(16).padStart(6, '0')}, 位置: (${position.x}, ${
      position.y
    }, ${position.z}), 轴线位置: (${cylinder.position.x.toFixed(2)}, ${cylinder.position.y.toFixed(
      2
    )}, ${cylinder.position.z.toFixed(2)}), 圆球位置: (${sphere.position.x.toFixed(
      2
    )}, ${sphere.position.y.toFixed(2)}, ${sphere.position.z.toFixed(2)}), 圆球可见: ${
      sphere.visible
    }, 圆球半径: ${(coneRadius * 2).toFixed(3)}`
  );
};

// 渲染循环
const animate = () => {
  requestAnimationFrame(animate);

  // 同步主摄像机的旋转到方向控制器
  if (props.mainCamera) {
    orientationGroup.quaternion.copy(props.mainCamera.quaternion).invert();
  }

  renderer.render(scene, camera);
};

// 鼠标事件处理
const onMouseDown = (event: MouseEvent) => {
  // 如果正在执行视角切换动画，允许用户操作中断动画
  if (isAnimating.value) {
    console.log('🛑 用户操作中断视角切换动画');
    stopViewAnimation();
  }

  // 检查是否点击了方向指示器
  const intersect = getIntersectedObject(event);

  if (intersect) {
    console.log(
      '🔍 射线检测到对象:',
      intersect.object.name,
      '父对象:',
      intersect.object.parent?.name,
      '对象类型:',
      intersect.object.type
    );

    // 检查是否点击了圆球指示器
    if (intersect.object.type === 'Mesh') {
      const geometry = (intersect.object as any).geometry;
      if (geometry && geometry.type === 'SphereGeometry') {
        const parent = intersect.object.parent;
        if (parent && parent.name && Object.keys(directionObjects).includes(parent.name)) {
          setViewDirection(parent.name);
          console.log(`🎯 点击圆球指示器: ${parent.name}`);
          return; // 点击圆球指示器时不启动拖拽
        }
      }
    }

    // 如果不是圆球指示器，检查是否是其他方向指示器组件
    let directionGroup = null;
    let currentObject = intersect.object;

    // 向上遍历对象层级，寻找方向指示器组
    while (currentObject && !directionGroup) {
      if (currentObject.name && Object.keys(directionObjects).includes(currentObject.name)) {
        directionGroup = currentObject;
        break;
      }
      currentObject = currentObject.parent;
    }

    // 如果找到了方向指示器组
    if (directionGroup && directionGroup.name) {
      setViewDirection(directionGroup.name);
      console.log(`🎯 点击方向指示器: ${directionGroup.name}`);
      return; // 点击方向指示器时不启动拖拽
    }

    console.log('🔍 点击背景区域，将启动拖拽模式');
  }

  // 启动拖拽模式
  isDragging.value = true;
  previousMouse.value.set(event.clientX, event.clientY);

  if (widgetCanvas.value) {
    widgetCanvas.value.style.cursor = 'grabbing';
  }

  // 添加全局鼠标事件监听
  addGlobalMouseEvents();

  console.log('🖱️ 开始拖拽轨迹球');
  event.preventDefault();
};

const onMouseMove = (event: MouseEvent) => {
  if (isDragging.value && props.mainCamera && props.mainControls) {
    // 计算鼠标移动量
    const deltaX = event.clientX - previousMouse.value.x;
    const deltaY = event.clientY - previousMouse.value.y;

    // 降低最小移动阈值，提高响应性
    if (Math.abs(deltaX) < 0.5 && Math.abs(deltaY) < 0.5) {
      return;
    }

    // 停止当前的阻尼动画
    stopDampingAnimation();

    // 更新速度向量（带有平滑处理）
    const rotationSpeed = 0.008;
    const newVelocityX = deltaX * rotationSpeed;
    const newVelocityY = deltaY * rotationSpeed;

    // 限制最大速度
    velocity.value.x = Math.max(-maxVelocity, Math.min(maxVelocity, newVelocityX));
    velocity.value.y = Math.max(-maxVelocity, Math.min(maxVelocity, newVelocityY));

    // 只在较大移动时输出日志，减少控制台噪音
    if (Math.abs(deltaX) > 2 || Math.abs(deltaY) > 2) {
      console.log(
        `🔄 拖拽移动: deltaX=${deltaX}, deltaY=${deltaY}, 速度: (${velocity.value.x.toFixed(
          3
        )}, ${velocity.value.y.toFixed(3)})`
      );
    }

    // 使用轨迹球旋转算法
    rotateCameraWithVelocity(velocity.value.x / rotationSpeed, velocity.value.y / rotationSpeed);

    previousMouse.value.set(event.clientX, event.clientY);
  } else if (!isDragging.value) {
    // 只在非拖拽状态下处理悬停高亮
    handleHover(event);
  }
};

// 轨迹球旋转算法（带速度参数）
const rotateCameraWithVelocity = (deltaX: number, deltaY: number) => {
  if (!props.mainCamera || !props.mainControls) return;

  const rotationSpeed = 0.008; // 提高旋转速度，使拖动更灵敏

  // 获取当前摄像机的位置和目标点
  const camera = props.mainCamera;
  const controls = props.mainControls;
  const target = controls.target || new Vector3(0, 0, 0);

  // 计算摄像机相对于目标的位置向量
  const offset = camera.position.clone().sub(target);
  const distance = offset.length();

  // 将位置向量转换为球坐标
  const spherical = {
    radius: distance,
    theta: Math.atan2(offset.x, offset.z), // 水平角度
    phi: Math.acos(Math.max(-1, Math.min(1, offset.y / distance))), // 垂直角度
  };

  // 应用鼠标移动到球坐标
  spherical.theta -= deltaX * rotationSpeed;
  spherical.phi -= deltaY * rotationSpeed; // 修复上下拖动方向

  // 限制垂直角度，防止翻转
  spherical.phi = Math.max(0.01, Math.min(Math.PI - 0.01, spherical.phi));

  // 将球坐标转换回笛卡尔坐标
  const newOffset = new Vector3(
    spherical.radius * Math.sin(spherical.phi) * Math.sin(spherical.theta),
    spherical.radius * Math.cos(spherical.phi),
    spherical.radius * Math.sin(spherical.phi) * Math.cos(spherical.theta)
  );

  // 更新摄像机位置
  camera.position.copy(target.clone().add(newOffset));
  camera.lookAt(target);

  // 更新控制器
  if (controls.update) {
    controls.update();
  }

  // 只在较大移动时输出位置日志
  if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
    console.log(
      `📍 摄像机新位置: (${camera.position.x.toFixed(2)}, ${camera.position.y.toFixed(
        2
      )}, ${camera.position.z.toFixed(2)})`
    );
  }
};

const onMouseUp = () => {
  if (isDragging.value) {
    console.log(
      `🖱️ 结束拖拽轨迹球，当前速度: (${velocity.value.x.toFixed(3)}, ${velocity.value.y.toFixed(
        3
      )})`
    );
    // 移除全局鼠标事件监听
    removeGlobalMouseEvents();

    // 启动阻尼动画（惯性滑动）
    startDampingAnimation();
  }
  isDragging.value = false;
  if (widgetCanvas.value) {
    widgetCanvas.value.style.cursor = 'grab';
  }
};

// 添加全局鼠标事件监听，确保在canvas外部也能结束拖拽
const addGlobalMouseEvents = () => {
  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
};

const removeGlobalMouseEvents = () => {
  document.removeEventListener('mousemove', onMouseMove);
  document.removeEventListener('mouseup', onMouseUp);
};

// 获取鼠标位置的交集对象（支持穿透检测）
const getIntersectedObject = (event: MouseEvent) => {
  const rect = widgetCanvas.value!.getBoundingClientRect();
  const mouse = new Vector2(
    ((event.clientX - rect.left) / rect.width) * 2 - 1,
    -((event.clientY - rect.top) / rect.height) * 2 + 1
  );

  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(orientationGroup.children, true);

  if (intersects.length === 0) {
    return null;
  }

  // 优先查找圆球指示器（SphereGeometry），即使被背景球体遮挡
  for (const intersect of intersects) {
    const object = intersect.object;

    // 检查是否是圆球指示器
    if (object.type === 'Mesh') {
      const geometry = (object as any).geometry;
      if (geometry && geometry.type === 'SphereGeometry') {
        // 确认这个圆球属于方向指示器组
        const parent = object.parent;
        if (parent && parent.name && Object.keys(directionObjects).includes(parent.name)) {
          console.log(
            `🎯 检测到圆球指示器: ${parent.name}, 距离: ${intersect.distance.toFixed(2)}`
          );
          return intersect;
        }
      }
    }
  }

  // 如果没有找到圆球指示器，返回第一个相交对象（用于拖拽）
  console.log(
    `🔍 检测到背景对象: ${intersects[0].object.type}, 距离: ${intersects[0].distance.toFixed(2)}`
  );
  return intersects[0];
};

// 处理悬停高亮
const handleHover = (event: MouseEvent) => {
  const intersect = getIntersectedObject(event);

  // 重置之前的高亮
  if (hoveredObject) {
    resetObjectColor(hoveredObject);
    hoveredObject = null;
  }

  // 设置新的高亮
  if (intersect && intersect.object.parent) {
    hoveredObject = intersect.object as Mesh;
    highlightObject(hoveredObject);

    if (widgetCanvas.value) {
      widgetCanvas.value.style.cursor = 'pointer';
    }
  } else {
    if (widgetCanvas.value) {
      widgetCanvas.value.style.cursor = 'grab';
    }
  }
};

// 高亮对象
const highlightObject = (object: Mesh) => {
  if (object.material && 'color' in object.material) {
    (object.material as any).color.setHex(0xffff00); // 黄色高亮
  }
};

// 重置对象颜色
const resetObjectColor = (object: Mesh) => {
  const parent = object.parent;
  if (parent && parent.userData.originalColor !== undefined) {
    if (object.material && 'color' in object.material) {
      (object.material as any).color.setHex(parent.userData.originalColor);
    }
  }
};

// 启动阻尼动画（惯性滑动）
const startDampingAnimation = () => {
  // 如果速度太小，直接停止
  if (Math.abs(velocity.value.x) < minVelocity && Math.abs(velocity.value.y) < minVelocity) {
    velocity.value.set(0, 0);
    return;
  }

  console.log(
    '🌊 启动阻尼动画，初始速度:',
    velocity.value.x.toFixed(3),
    velocity.value.y.toFixed(3)
  );

  const dampingLoop = () => {
    // 应用阻尼衰减
    velocity.value.x *= dampingFactor;
    velocity.value.y *= dampingFactor;

    // 检查是否需要停止动画
    if (Math.abs(velocity.value.x) < minVelocity && Math.abs(velocity.value.y) < minVelocity) {
      velocity.value.set(0, 0);
      dampingAnimationId = null;
      console.log('🛑 阻尼动画结束');
      return;
    }

    // 应用旋转
    const rotationSpeed = 0.008;
    rotateCameraWithVelocity(velocity.value.x / rotationSpeed, velocity.value.y / rotationSpeed);

    // 继续动画循环
    dampingAnimationId = requestAnimationFrame(dampingLoop);
  };

  dampingAnimationId = requestAnimationFrame(dampingLoop);
};

// 停止阻尼动画
const stopDampingAnimation = () => {
  if (dampingAnimationId !== null) {
    cancelAnimationFrame(dampingAnimationId);
    dampingAnimationId = null;
    console.log('⏹️ 停止阻尼动画');
  }
};

// 停止视角切换动画
const stopViewAnimation = () => {
  if (viewAnimationId !== null) {
    cancelAnimationFrame(viewAnimationId);
    viewAnimationId = null;
    isAnimating.value = false;
    console.log('⏹️ 停止视角切换动画');
  }
};

// 缓动函数 - easeInOutCubic
const easeInOutCubic = (t: number): number => {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
};

// 控制外壳球体的可见性
const setSphereVisibility = (visible: boolean) => {
  if (orientationGroup) {
    const sphere = orientationGroup.getObjectByName('orientation-sphere');
    if (sphere) {
      sphere.visible = visible;
      console.log(`🔮 外壳球体可见性设置为: ${visible}`);
    }
  }
};

// 设置视角方向（带平滑动画）
const setViewDirection = (direction: string) => {
  if (!props.mainCamera || !props.mainControls) {
    console.log('❌ 主摄像机或控制器未连接');
    return;
  }

  if (!direction) {
    console.log('❌ 方向参数为空');
    return;
  }

  // 如果正在执行动画，先停止当前动画
  if (isAnimating.value) {
    stopViewAnimation();
  }

  // 定义各个方向的摄像机位置
  const directions: { [key: string]: Vector3 } = {
    x: new Vector3(10, 0, 0), // 右视图
    '-x': new Vector3(-10, 0, 0), // 左视图
    y: new Vector3(0, 10, 0), // 顶视图
    '-y': new Vector3(0, -10, 0), // 底视图
    z: new Vector3(0, 0, 10), // 前视图
    '-z': new Vector3(0, 0, -10), // 后视图
  };

  const targetPosition = directions[direction];
  if (!targetPosition) {
    console.log(`❌ 未找到方向 "${direction}" 的配置`);
    return;
  }

  // 获取当前目标点（通常是场景中心或选中对象的位置）
  const target = props.mainControls.target || new Vector3(0, 0, 0);

  // 计算目标位置
  const targetCameraPosition = target.clone().add(targetPosition);

  // 如果禁用动画，直接切换
  if (!props.enableAnimation) {
    console.log(`🧭 瞬间切换到视角方向: "${direction}"`);
    props.mainCamera.position.copy(targetCameraPosition);
    props.mainCamera.lookAt(target);

    // 更新控制器
    if (props.mainControls.update) {
      props.mainControls.update();
    }

    // 调试：输出摄像机状态
    debugCameraState(`视角瞬间切换到 ${direction} 方向后`);
    console.log(`✅ 视角已瞬间切换到 ${direction} 方向`);
    return;
  }

  console.log(`🧭 开始平滑切换到视角方向: "${direction}"`);

  // 记录起始位置
  const startPosition = props.mainCamera.position.clone();
  const startTime = performance.now();

  // 设置动画状态
  isAnimating.value = true;

  // 动画循环函数
  const animateViewTransition = () => {
    const currentTime = performance.now();
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / props.animationDuration, 1);

    // 应用缓动函数
    const easedProgress = easeInOutCubic(progress);

    // 插值计算当前位置
    const currentPosition = startPosition.clone().lerp(targetCameraPosition, easedProgress);

    // 更新相机位置
    props.mainCamera.position.copy(currentPosition);
    props.mainCamera.lookAt(target);

    // 更新控制器
    if (props.mainControls.update) {
      props.mainControls.update();
    }

    // 输出动画进度（每10%输出一次，减少日志噪音）
    if (Math.floor(progress * 10) !== Math.floor((progress - 0.1) * 10)) {
      console.log(`🎬 视角切换动画进度: ${Math.round(progress * 100)}%`);
    }

    // 检查动画是否完成
    if (progress < 1) {
      viewAnimationId = requestAnimationFrame(animateViewTransition);
    } else {
      // 动画完成
      isAnimating.value = false;
      viewAnimationId = null;

      // 调试：输出摄像机状态
      debugCameraState(`视角平滑切换到 ${direction} 方向后`);
      console.log(`✅ 视角已平滑切换到 ${direction} 方向`);
    }
  };

  // 开始动画
  viewAnimationId = requestAnimationFrame(animateViewTransition);
};

// 调试摄像机状态
const debugCameraState = (context: string) => {
  if (!props.mainCamera || !props.mainControls) return;

  const camera = props.mainCamera as any;
  const controls = props.mainControls;

  console.log(`📷 ${context} - 摄像机状态:`);
  console.log(
    `- 位置: (${camera.position.x.toFixed(2)}, ${camera.position.y.toFixed(
      2
    )}, ${camera.position.z.toFixed(2)})`
  );
  console.log(
    `- 目标: (${controls.target?.x?.toFixed(2) || 0}, ${controls.target?.y?.toFixed(2) || 0}, ${
      controls.target?.z?.toFixed(2) || 0
    })`
  );

  // 计算距离
  const target = controls.target || new Vector3(0, 0, 0);
  const distance = camera.position.distanceTo(target);
  console.log(`- 距离: ${distance.toFixed(2)} 单位`);

  // 检查关键参数
  console.log(`- near: ${camera.near}, far: ${camera.far}`);
  console.log(`- fov: ${camera.fov}°, aspect: ${camera.aspect?.toFixed(2)}`);

  // 检查潜在问题
  const warnings = [];
  if (camera.near > 1) warnings.push(`near平面过大(${camera.near})`);
  if (camera.near >= camera.far) warnings.push('near >= far');
  if (distance < 2) warnings.push(`距离过近(${distance.toFixed(2)})`);
  if (
    Math.abs(camera.position.x) < 1 &&
    Math.abs(camera.position.y) < 1 &&
    Math.abs(camera.position.z) < 1
  ) {
    warnings.push('摄像机可能在场景内部');
  }

  if (warnings.length > 0) {
    console.log(`⚠️ 潜在问题: ${warnings.join(', ')}`);
  } else {
    console.log('✅ 摄像机参数正常');
  }
};

// 诊断视野裁切问题
const diagnoseViewClipping = () => {
  console.log('🔍 === 视野裁切问题诊断 ===');

  if (!props.mainCamera || !props.mainControls) {
    console.log('❌ 主摄像机或控制器未连接');
    return;
  }

  // 输出当前状态
  debugCameraState('当前状态');

  // 测试各个方向的视角切换
  console.log('🧪 测试各方向视角切换...');
  const testDirections = ['x', 'y', 'z'];

  testDirections.forEach((dir, index) => {
    setTimeout(() => {
      console.log(`\n🎯 测试 ${dir} 方向:`);
      setViewDirection(dir);

      // 延迟检查，确保切换完成
      setTimeout(() => {
        debugCameraState(`${dir} 方向切换后`);

        // 模拟用户拖拽后的检查
        setTimeout(() => {
          debugCameraState(`${dir} 方向 - 模拟拖拽后`);
        }, 100);
      }, 50);
    }, index * 1000);
  });

  console.log('💡 诊断说明:');
  console.log('1. 将测试X、Y、Z三个方向的视角切换');
  console.log('2. 检查每次切换后的摄像机参数');
  console.log('3. 观察是否有参数异常导致裁切问题');
  console.log('4. 手动测试：切换视角后尝试拖拽旋转');
};

// 深度诊断视野裁切问题
const deepDiagnoseClipping = () => {
  console.log('🔬 === 深度视野裁切诊断 ===');

  if (!props.mainCamera || !props.mainControls) {
    console.log('❌ 主摄像机或控制器未连接');
    return;
  }

  const camera = props.mainCamera as any;
  const controls = props.mainControls;

  // 1. 详细的摄像机参数检查
  console.log('📷 详细摄像机参数:');
  console.log(`- type: ${camera.type}`);
  console.log(`- position: (${camera.position.x}, ${camera.position.y}, ${camera.position.z})`);
  console.log(`- rotation: (${camera.rotation.x}, ${camera.rotation.y}, ${camera.rotation.z})`);
  console.log(
    `- quaternion: (${camera.quaternion.x}, ${camera.quaternion.y}, ${camera.quaternion.z}, ${camera.quaternion.w})`
  );
  console.log(`- near: ${camera.near}`);
  console.log(`- far: ${camera.far}`);
  console.log(`- fov: ${camera.fov}`);
  console.log(`- aspect: ${camera.aspect}`);
  console.log(`- zoom: ${camera.zoom}`);

  // 2. 投影矩阵检查
  console.log('🔢 投影矩阵:');
  const pm = camera.projectionMatrix.elements;
  console.log(
    `- [0-3]: ${pm[0].toFixed(3)}, ${pm[1].toFixed(3)}, ${pm[2].toFixed(3)}, ${pm[3].toFixed(3)}`
  );
  console.log(
    `- [4-7]: ${pm[4].toFixed(3)}, ${pm[5].toFixed(3)}, ${pm[6].toFixed(3)}, ${pm[7].toFixed(3)}`
  );
  console.log(
    `- [8-11]: ${pm[8].toFixed(3)}, ${pm[9].toFixed(3)}, ${pm[10].toFixed(3)}, ${pm[11].toFixed(3)}`
  );
  console.log(
    `- [12-15]: ${pm[12].toFixed(3)}, ${pm[13].toFixed(3)}, ${pm[14].toFixed(3)}, ${pm[15].toFixed(
      3
    )}`
  );

  // 3. 视图矩阵检查
  console.log('👁️ 视图矩阵:');
  const vm = camera.matrixWorldInverse.elements;
  console.log(
    `- [0-3]: ${vm[0].toFixed(3)}, ${vm[1].toFixed(3)}, ${vm[2].toFixed(3)}, ${vm[3].toFixed(3)}`
  );
  console.log(
    `- [4-7]: ${vm[4].toFixed(3)}, ${vm[5].toFixed(3)}, ${vm[6].toFixed(3)}, ${vm[7].toFixed(3)}`
  );
  console.log(
    `- [8-11]: ${vm[8].toFixed(3)}, ${vm[9].toFixed(3)}, ${vm[10].toFixed(3)}, ${vm[11].toFixed(3)}`
  );
  console.log(
    `- [12-15]: ${vm[12].toFixed(3)}, ${vm[13].toFixed(3)}, ${vm[14].toFixed(3)}, ${vm[15].toFixed(
      3
    )}`
  );

  // 4. 控制器状态检查
  console.log('🎮 控制器状态:');
  console.log(`- target: (${controls.target.x}, ${controls.target.y}, ${controls.target.z})`);
  console.log(`- enabled: ${controls.enabled}`);
  console.log(`- enableDamping: ${controls.enableDamping}`);
  console.log(`- dampingFactor: ${controls.dampingFactor}`);
  console.log(`- minDistance: ${controls.minDistance}`);
  console.log(`- maxDistance: ${controls.maxDistance}`);
  console.log(`- minPolarAngle: ${controls.minPolarAngle}`);
  console.log(`- maxPolarAngle: ${controls.maxPolarAngle}`);

  // 5. 计算视锥体
  console.log('📐 视锥体计算:');
  const aspect = camera.aspect;
  const fov = (camera.fov * Math.PI) / 180; // 转换为弧度
  const near = camera.near;
  const far = camera.far;

  const nearHeight = 2 * Math.tan(fov / 2) * near;
  const nearWidth = nearHeight * aspect;
  const farHeight = 2 * Math.tan(fov / 2) * far;
  const farWidth = farHeight * aspect;

  console.log(`- near平面尺寸: ${nearWidth.toFixed(3)} x ${nearHeight.toFixed(3)}`);
  console.log(`- far平面尺寸: ${farWidth.toFixed(3)} x ${farHeight.toFixed(3)}`);

  // 6. 检查可能的问题
  console.log('⚠️ 问题检查:');
  const issues = [];

  if (camera.near <= 0) issues.push('near平面 <= 0');
  if (camera.near >= camera.far) issues.push('near >= far');
  if (camera.fov <= 0 || camera.fov >= 180) issues.push('fov异常');
  if (camera.aspect <= 0) issues.push('aspect异常');
  if (camera.zoom !== 1) issues.push(`zoom不为1 (${camera.zoom})`);

  // 检查投影矩阵是否有异常值
  const hasNaN = pm.some((val) => isNaN(val));
  const hasInfinity = pm.some((val) => !isFinite(val));
  if (hasNaN) issues.push('投影矩阵包含NaN');
  if (hasInfinity) issues.push('投影矩阵包含无穷值');

  // 检查摄像机是否在原点
  const distanceFromOrigin = Math.sqrt(
    camera.position.x ** 2 + camera.position.y ** 2 + camera.position.z ** 2
  );
  if (distanceFromOrigin < 0.1) issues.push('摄像机过于接近原点');

  if (issues.length > 0) {
    console.log(`🚨 发现 ${issues.length} 个问题:`);
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  } else {
    console.log('✅ 未发现明显的参数问题');
  }

  // 7. 建议的修复步骤
  console.log('💡 建议的修复步骤:');
  console.log('1. 运行 fixCameraParameters() 修复基本参数');
  console.log('2. 检查渲染器的视口设置');
  console.log('3. 检查CSS样式是否影响canvas尺寸');
  console.log('4. 检查是否有其他几何体遮挡视野');
  console.log('5. 尝试重新初始化摄像机');
};

// 强制重置摄像机参数
const forceResetCamera = () => {
  console.log('🔄 === 强制重置摄像机参数 ===');

  if (!props.mainCamera || !props.mainControls) {
    console.log('❌ 主摄像机或控制器未连接');
    return;
  }

  const camera = props.mainCamera as any;
  const controls = props.mainControls;

  console.log('📷 重置前参数:');
  debugCameraState('重置前');

  // 强制设置标准参数
  camera.near = 0.1;
  camera.far = 1000;
  camera.fov = 75;
  camera.zoom = 1;

  // 重新计算aspect（从渲染器获取）
  const canvas = camera.parent?.renderer?.domElement || document.querySelector('canvas');
  if (canvas) {
    camera.aspect = canvas.clientWidth / canvas.clientHeight;
  }

  // 强制更新投影矩阵
  camera.updateProjectionMatrix();

  // 重置控制器
  controls.target.set(0, 0, 0);
  controls.update();

  // 设置一个安全的摄像机位置
  camera.position.set(10, 10, 10);
  camera.lookAt(0, 0, 0);

  console.log('📷 重置后参数:');
  debugCameraState('重置后');

  console.log('✅ 摄像机参数已强制重置');
  console.log('💡 请测试视角切换和拖拽是否正常');
};

// 组件生命周期
onMounted(() => {
  if (widgetCanvas.value) {
    initThreeJS();
  }
});

onBeforeUnmount(() => {
  // 清理全局事件监听器
  removeGlobalMouseEvents();

  // 停止所有动画
  stopDampingAnimation();
  stopViewAnimation();

  // 清理资源
  if (renderer) {
    renderer.dispose();
  }

  // 清理几何体和材质
  scene?.traverse((object) => {
    if (object instanceof Mesh) {
      if (object.geometry) {
        object.geometry.dispose();
      }
      if (object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach((material) => material.dispose());
        } else {
          object.material.dispose();
        }
      }
    }
  });

  console.log('🧭 方向控制器已清理');
});

// 监听主摄像机变化
watch(
  () => props.mainCamera,
  (newCamera) => {
    if (newCamera) {
      console.log('🧭 主摄像机已连接到方向控制器');
    }
  }
);

// 监听球体可见性变化
watch(
  () => props.showSphere,
  (newVisibility) => {
    setSphereVisibility(newVisibility);
  }
);

// 暴露一些方法供外部调用
defineExpose({
  setViewDirection,
  debugCameraState,
  diagnoseViewClipping,
  deepDiagnoseClipping,
  forceResetCamera,
  resetView: () => {
    if (props.mainCamera) {
      props.mainCamera.position.set(10, 10, 10);
      props.mainCamera.lookAt(0, 0, 0);
      if (props.mainControls?.update) {
        props.mainControls.update();
      }
    }
  },
  // 动画控制方法
  stopViewAnimation,
  isAnimating: () => isAnimating.value,
  // 外观控制方法
  setSphereVisibility,
});
</script>

<style scoped>
.orientation-widget {
  user-select: none;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(0, 0, 0, 0.2) 100%);
  transition: transform 0.2s ease;
}

.orientation-widget:hover {
  transform: translateX(-50%) scale(1.05);
}

.orientation-widget canvas {
  display: block;
  border-radius: 50%;
}
</style>
