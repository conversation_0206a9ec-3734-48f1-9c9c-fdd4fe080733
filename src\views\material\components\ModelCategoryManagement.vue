<template>
  <div class="model-category-container">
    <div class="category-content">
      <model-category-left
        ref="leftComponentRef"
        :total-count="allTotalCount"
        @select-category="handleCategorySelect"
        @category-added="handleCategoryAdded" />
      <model-category-right
        :category-id="currentCategoryId"
        :category-update-trigger="categoryUpdateTrigger"
        @update-total="handleTotalUpdate"
        @category-updated="handleCategoryUpdated" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ModelCategoryLeft from './ModelCategoryLeft.vue';
import ModelCategoryRight from './ModelCategoryRight.vue';

const currentCategoryId = ref<number | null>(null);
const totalCount = ref(0);
const allTotalCount = ref(0); // 记录所有模型的总数，不随筛选变化
const leftComponentRef = ref<InstanceType<typeof ModelCategoryLeft>>();
const categoryUpdateTrigger = ref(0); // 添加触发更新的计数器

const handleCategorySelect = (categoryId: number) => {
  currentCategoryId.value = categoryId;
};

const handleTotalUpdate = (total: number) => {
  totalCount.value = total;
  // 如果是第一次加载或当前是"所有模型"（categoryId为null），则更新allTotalCount
  if (currentCategoryId.value === null || allTotalCount.value === 0) {
    allTotalCount.value = total;
  }
};

const handleCategoryUpdated = () => {
  // 当右侧模型分类设置成功后，刷新左侧分类列表
  if (leftComponentRef.value) {
    leftComponentRef.value.refreshCategories();
  }
};

const handleCategoryAdded = () => {
  console.log('🔍 handleCategoryAdded 被调用');
  console.log('📊 当前 categoryUpdateTrigger:', categoryUpdateTrigger.value);

  // 当左侧添加新分类成功后，触发右侧更新分类选项
  categoryUpdateTrigger.value++;

  console.log('🔄 更新后 categoryUpdateTrigger:', categoryUpdateTrigger.value);
};
</script>

<style scoped lang="less">
.model-category-container {
  height: calc(100vh - 200px); // 使用视口高度减去头部和其他元素的高度
  padding: 20px;
  margin-top: 20px;

  .category-content {
    display: flex;
    gap: 10px;
    height: 100%;
    align-items: flex-start; // 确保两个组件顶部对齐
  }
}
</style>
