<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-content-title">
        <div>上传素材组合</div>
        <div class="icon iconfont icon-close" @click="changeState"></div>
      </div>
      <div class="modal-form">
        <div class="schedule">
          <div>
            <span v-if="step == 1">1</span>
            <img v-if="step == 2" src="@/assets/images/icon/completed.png" />
            <span :class="step == 2 ? 'opacity065' : ''">选择素材</span>
          </div>
          <div class="step-line"></div>
          <div :class="step == 1 ? 'opacity065' : ''">
            <span>2</span>
            <span>素材组合</span>
          </div>
        </div>
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px"
          require-asterisk-position="right">
          <div v-if="step == 1">
            <el-form-item label="素材组合名称" prop="groupName">
              <el-input class="form-input" v-model="ruleForm.groupName" placeholder="请输入素材名称" />
            </el-form-item>
            <el-form-item label="素材组合类型" prop="groupType">
              <el-select v-model="ruleForm.groupType" placeholder="请选择素材组合类型" class="select-default"
                popper-class="select-option" :suffix-icon="DropDown" style="width: 432px; height: 36px;"
                @change="changeGroup">
                <el-option v-for="(item, index) in sourceGroupList" :key="index" :label="item.name"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <div class="source-box" v-for="sourceItem in sourceGroupList" v-show="sourceItem.value == showType"
              :key="sourceItem.value">
              <div class="tips-title">
                <span>{{ sourceItem.tip_titles[0] }}</span>
                <span :class="showError[0] ? 'errors' : ''">{{ sourceItem.tips[0] }}</span>
              </div>
              <div class="source-content">
                <div v-for="(item, index) in showSource.filter((e: any) => e.materialType == sourceItem.groupIds[0])"
                  :key="item.id" class="source-pool"
                  :class="((item.id == selectedIds[0] || (selectedIds[0].includes && selectedIds[0].includes(item.id))) ? 'selected' : '') + ((item.id != selectedIds[0] && !!selectedIds[0] && sourceItem.groupIds[0] != '3') ? ' disabled' : '')"
                  :style="{ marginRight: (index + 1) % 4 == 0 ? '0' : '7px' }">
                  <div :class="hoverImage == item.id ? 'hoverSource' : ''"
                    @mousedown="handleMouseDown(item, 0, sourceItem.groupIds[0])"
                    @mousemove="handleMouseMove(item.id, 0, sourceItem.groupIds[0])" @mouseleave="handleMouseLeave">
                    <img :src="materialUrls[item.materialType][hoverImage == item.id ? 'url_a' : 'url']"
                      draggable="false" />
                  </div>
                  <div>{{ item.materialName }}</div>
                </div>
              </div>
              <div class="tips-title" v-if="sourceItem.tips[1]">
                <span>{{ sourceItem.tip_titles[1] }}</span>
                <span :class="showError[1] ? 'errors' : ''">{{ sourceItem.tips[1] }}</span>
              </div>
              <div class="source-content">
                <div v-for="(item, index) in showSource.filter((e: any) => e.materialType == sourceItem.groupIds[1])"
                  :key="item.id" class="source-pool"
                  :class="(item.id == selectedIds[1] ? 'selected' : '') + ((item.id != selectedIds[1] && selectedIds[1]) ? ' disabled' : '')"
                  :style="{ marginRight: (index + 1) % 4 == 0 ? '0' : '7px' }">
                  <div :class="hoverImage == item.id ? 'hoverSource' : ''"
                    @mousedown="handleMouseDown(item, 1, sourceItem.groupIds[0])"
                    @mousemove="handleMouseMove(item.id, 1)" @mouseleave="handleMouseLeave">
                    <img :src="materialUrls[item.materialType][hoverImage == item.id ? 'url_a' : 'url']"
                      draggable="false" />
                  </div>
                  <div>{{ item.materialName }}</div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="step == 2" class="preview-box">
            <div class="preview-text" :class="'a' + textStyleId" :title="materialText" v-if="showType != 5">
              <div>{{ materialText }}</div>
            </div>
            <div class="preview-image" v-if="showType == 4 || showType == 5">
              <div v-for="(item, index) in groupDatas" :key="item.id" class="source-pool"
                :style="{ marginRight: (index + 1) % 4 == 0 ? '0' : '7px' }">
                <div>
                  <img :src="materialUrls[item.materialType][hoverImage == item.id ? 'url_a' : 'url']" />
                </div>
                <div>{{ item.materialName }}</div>
              </div>
            </div>
            <div v-if="showType == 1" class="preview-video">
              <video class="loaded-img" :src="groupDatas[0].ossPath"></video>
            </div>
            <div v-if="showType == 2" class="preview-model">
              <canvas-preview ref="canvasRef"></canvas-preview>
            </div>
            <div v-if="showType == 3" class="preview-audio">
              <audio class="loaded-img" :src="groupDatas[0].ossPath"></audio>
            </div>
            <div class="text-position" v-if="showType != 5">
              <el-form-item label="文本位置" prop="wordPlace">
                <div class="text-position-btn" :class="ruleForm.wordPlace == 1 ? 'active' : ''"
                  @click="ruleForm.wordPlace = 1">将文字置于图片上方</div>
                <div class="text-position-btn" :class="ruleForm.wordPlace == 2 ? 'active' : ''"
                  @click="ruleForm.wordPlace = 2">将文字置于图片下方</div>
              </el-form-item>
            </div>
            <div class="text-style" v-if="showType != 5">
              <el-form-item label="文本框样式"></el-form-item>
              <div class="styles">
                <div v-for="(item, index) in  styles" :key="item">
                  <img :src="require(`@/assets/images/background/text-style-${index + 1}.png`)" />
                  <div>
                    <img :src="textStyleId == index + 1 ? radioA : radioD" @click="textStyleId = index + 1" />
                    <span>{{ item }}</span>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="picture-view" v-if="showType == 4 || showType == 5">
              <el-form-item label="图片展示模版"></el-form-item>
              <div class="styles">
                <div>
                  <img src="@/assets/images/background/tile-view.png" />
                  <img class="radio-style" :src="tileId == 1 ? radioA : radioD" @click="tileId = 1" />
                  <span>平铺展示</span>
                </div>
                <div>
                  <img src="@/assets/images/background/column-view.png" />
                  <img class="radio-style" :src="tileId == 2 ? radioA : radioD" @click="tileId = 2" />
                  <span style="margin-right: 30px;">九宫格展示</span>
                </div>
              </div>
            </div> -->
          </div>
          <el-form-item class="form-submit" v-if="step == 1">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef)">
                下一步
              </el-button>
            </div>
          </el-form-item>
          <el-form-item class="form-submit" v-if="step == 2">
            <div class="btn-default el-size3">
              <el-button @click="step = 1">上一步</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef)">
                确认
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import DropDown from '@/components/DropDown.vue'
import { sourceGroupList, materialUrls } from '@/config'
import CanvasPreview from '@/components/CanvasPreview.vue'

import { getOssAccessPath, createMaterialGroupInfo, updateMaterialGroup } from '@/api'

const props = defineProps({
  handleHide: {
    default: null,
    type: Function
  },
  sourceList: {
    default: null,
    type: Object
  },
  modalShow2: {
    default: '',
    type: String
  },
  groupMaterial: {
    default: null,
    type: Object
  }
})

const showSource: any = ref([])
const hoverImage = ref(-1) // 悬浮在照片上
const showType = ref(1)
const selectedIds: any = ref([0, 0])
const step = ref(1)
const showError = ref([0, 0])
const styles = ['样式 1', '样式 2', '样式 3', '无边框']
const textStyleId = ref(1)
const materialText = ref('')
const radioD = require('@/assets/images/icon/radio.png')
const radioA = require('@/assets/images/icon/radioA.png')
// const tileId = ref(2)
const groupDatas: any = ref([])
const canvasRef = ref()
const affiliationMap: any = reactive({})
const defaultData: any = ref({})

// 导入增强的loader配置
import { loader } from '@/config/threeJs';


interface RuleForm {
  groupType: number,
  groupName: string,
  wordPlace: number
}

const ruleFormRef = ref<FormInstance>()
const ruleForm: any = reactive<RuleForm>({
  groupType: 1,
  groupName: '',
  wordPlace: 1
})
const rules = reactive<FormRules<RuleForm>>({
  groupName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  groupType: [
    { required: true, message: '请选择素材组合类型', trigger: 'blur', }
  ],
  wordPlace: [
    { required: true, message: '请选择文本位置', trigger: 'blur' }
  ],
})
const submitForm = async (formEl: FormInstance | undefined) => {
  showError.value[0] = !selectedIds.value[0] ? 1 : 0
  showError.value[1] = !selectedIds.value[1] ? 1 : 0
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (!selectedIds.value[0] || (ruleForm.groupType != 5 && !selectedIds.value[1])) {
        return
      }
      if (step.value == 1) {
        step.value = 2
        if (ruleForm.groupType == 4 || ruleForm.groupType == 5) {
          groupDatas.value = props.sourceList.filter((e: any) => selectedIds.value[0].includes(e.id))
        } else {
          groupDatas.value = props.sourceList.filter((e: any) => selectedIds.value[0] == e.id)
          getOssAccessPath({ key: groupDatas.value[0].ossKey }).then((res1: any) => {
            groupDatas.value[0].ossPath = res1.data
          })
        }
        if (ruleForm.groupType == 2) {
          if (groupDatas.value[0].modelStorageMap.web?.ossKey) {
            getOssAccessPath({ key: groupDatas.value[0].modelStorageMap.web?.ossKey }).then((res1: any) => {
              loader[groupDatas.value[0].materialFormat].load(res1.data, function (glb: any) {
                if (glb.scene) {
                  canvasRef.value.addMesh(glb.scene)
                } else {
                  canvasRef.value.addMesh(glb)
                }
              })
            })
          }
        }
      } else {
        const parame = { ...ruleForm }
        parame.textBoxStyle = textStyleId.value

        switch (ruleForm.groupType) {
          case 1:
            parame.videoMaterial = {
              materialId: selectedIds.value[0],
              affiliationId: affiliationMap[selectedIds.value[0]]
            }
            break;
          case 2:
            parame.modelMaterial = {
              materialId: selectedIds.value[0],
              affiliationId: affiliationMap[selectedIds.value[0]]
            }
            break;
          case 3:
            parame.modelMaterial = {
              materialId: selectedIds.value[0],
              affiliationId: affiliationMap[selectedIds.value[0]]
            }
            break;
        }

        if (ruleForm.groupType != 5) {
          if (ruleForm.groupType == 4) {
            parame.wordMaterial = {
              materialId: selectedIds.value[1][0],
              affiliationId: affiliationMap[selectedIds.value[1][0]]
            }
          } else {
            parame.wordMaterial = {
              materialId: selectedIds.value[1],
              affiliationId: affiliationMap[selectedIds.value[1]]
            }
          }
        }
        if (ruleForm.groupType == 4 || ruleForm.groupType == 5) {
          const selectedSimpleIds = selectedIds.value[0].map((e: any) => ({ materialId: e, affiliationId: affiliationMap[e] }))
          parame.materialSimpleInfoComposeArr = [[], [], []]
          parame.materialSimpleInfoComposeArr[0] = selectedSimpleIds.slice(0, 3)
          parame.materialSimpleInfoComposeArr[1] = selectedSimpleIds.slice(3, 6)
          parame.materialSimpleInfoComposeArr[2] = selectedSimpleIds.slice(6, 9)
        }
        if (!defaultData.value.id) {
          createMaterialGroupInfo(parame).then((res) => {
            props.handleHide(true)
          })
        } else {
          parame.id = defaultData.value.id
          updateMaterialGroup(parame).then((res) => {
            props.handleHide(true)
          })
        }
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const changeState = () => {
  props.handleHide()
}

const changeGroup = (value: number) => {
  const obj = sourceGroupList.filter((e: any) => e.value == value)[0]
  showSource.value = []
  obj.groupIds.forEach((v: string) => {
    const sl = props.sourceList.filter((e: any) => e.materialType == v) || []
    showSource.value.push(...sl)
  })
  showType.value = value
  if (value != 5) {
    selectedIds.value = [0, 0]
  } else {
    selectedIds.value = [0]
  }
}

const handleMouseMove = (id: number, index: number, type?: string) => {
  if (type == '3' || (!selectedIds.value[index] || selectedIds.value[index] == id)) {
    hoverImage.value = id
  }
}

const handleMouseLeave = () => {
  hoverImage.value = -1
}

const handleMouseDown = (data: any, index: number, type?: string) => {
  if (type == '3') {
    if (!selectedIds.value[index]) {
      selectedIds.value[index] = [data.id]
    } else {
      selectedIds.value[index].push(data.id)
    }
  } else {
    if (selectedIds.value[index] == 0) {
      selectedIds.value[index] = data.id
    } else if (selectedIds.value[index] == data.id) {
      selectedIds.value[index] = 0
    }
  }
  if (data.materialType == '5') {
    materialText.value = data.materialWord
  }
}

onMounted(() => {
  if (+props.modalShow2) {
    defaultData.value = props.groupMaterial.filter((e: any) => e.id == +props.modalShow2)[0]
    ruleForm.groupName = defaultData.value.groupName
    ruleForm.groupType = defaultData.value.groupType
    ruleForm.wordPlace = defaultData.value.wordPlace
    const firstIds = defaultData.value.viewGroupArray.filter((e: any) => e.materialType != '5').map((m: any) => m.id)
    const lastIds = defaultData.value.viewGroupArray.filter((e: any) => e.materialType == '5').map((m: any) => m.id)
    if (defaultData.value.groupType == 4 || defaultData.value.groupType == 5) {
      selectedIds.value[0] = [...firstIds]
      selectedIds.value[1] = [...lastIds]
    } else {
      selectedIds.value[0] = firstIds[0]
      selectedIds.value[1] = lastIds[0]
    }
  }
  showType.value = ruleForm.groupType
  textStyleId.value = defaultData.value.textBoxStyle
  if (defaultData.value.wordMaterial) {
    materialText.value = defaultData.value.wordMaterial.baseMaterialDto.materialWord
  }
  sourceGroupList[ruleForm.groupType - 1 || 0].groupIds.forEach((v: string) => {
    const sl = props.sourceList.filter((e: any) => e.materialType == v) || []
    showSource.value.push(...sl)
  })
  props.sourceList.forEach((e: any) => {
    affiliationMap[e.id] = e.materialAffiliation
  })
})
</script>

<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .modal-content {
    width: 816px;
    // height: 930px;
    max-height: 90%;
    background: #EDEFF2;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    border: 1px solid #EDEFF2;
    overflow: hidden;
    overflow-y: auto;

    .modal-content-title {
      height: 76px;
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px 0 63px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;

      .icon-close {
        font-size: 26px;
        cursor: pointer;
        font-weight: 400;

        &:hover {
          color: #2E76FF;
        }
      }
    }

    .modal-form {
      position: relative;
      width: 100%;
      height: calc(100% - 76px);
      padding: 36px 95px 60px;
      box-sizing: border-box;
      overflow-y: auto;

      .schedule {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 50px;
        margin-bottom: 38px;

        &>div {
          span {
            vertical-align: middle;
          }

          img {
            vertical-align: middle;
          }
        }

        &>div>span:first-child {
          display: inline-block;
          width: 32px;
          height: 32px;
          line-height: 32px;
          background: #298BFF;
          text-align: center;
          border-radius: 50%;
          font-weight: 400;
          font-size: 14px;
          color: #FFFFFF;
        }

        &>div>span:last-child {
          font-weight: 500;
          font-size: 16px;
          margin-left: 8px;
          color: #298BFFFF;
        }

        .step-line {
          width: 147px;
          height: 1px;
          background: rgba(41, 139, 255, 0.35);
          margin: 0 8px;
        }

        .loaded-img {
          width: 100%;
          height: 100%;
          display: inline-block;
        }
      }

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-textarea {
        width: 432px;
        height: 91px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .form-submit {
        margin-top: 38px;
      }

      .source-box {
        width: 816px;
        margin-left: -95px;
        font-weight: 400;
        font-size: 12px;
        color: #0f0f0f;
        text-align: left;
        background-color: #99999914;
        padding: 16px 120px 16px 112px;
        box-sizing: border-box;
      }

      .source-box,
      .preview-box {


        .source-content {
          max-height: 208px;
          overflow: hidden;
          overflow-y: auto;
        }

        span {
          vertical-align: middle;
        }

        .tips-title {
          margin-bottom: 20px;
          display: block;

          &>span:first-child {
            font-size: 14px;
            margin-right: 10px;
          }

          &>span:last-child {
            color: #6F6F6FFF;
          }

          .errors {
            color: #f56c6c !important;
          }
        }

        &>div {
          overflow: hidden;
          overflow-x: auto;
        }

        .source-pool {
          float: left;
          width: 64px;
          height: 84px;
          margin-right: 7px;
          margin-bottom: 20px;
          text-align: center;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          padding: 4px 8px;
          border-radius: 8px;
          cursor: pointer;

          &.selected {
            background: rgba(41, 139, 255, 0.12);
          }

          &.disabled {
            opacity: 0.4;
            cursor: default;
          }

          &>div:first-child {
            position: relative;
            width: 64px;
            height: 64px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 12px;
            margin-bottom: 5px;

            .default-source {
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 14px;
              line-height: 14px;
              background-image: url(~@/assets/images/icon/default-source-icon.png);
              background-size: 100% 100%;
              font-size: 10px;
              font-weight: 600;
              color: #2166EA;
              text-align: center;
            }

            &.hoverSource {
              position: relative;
              background: #D8D8D8 linear-gradient(180deg, #FFFFFF 0%, #A8DDFF 67%, #2C8CFF 100%, #2C8CFF 100%);

              .preview {
                width: 12px;
                height: 12px;
                position: absolute;
                right: 3px;
                top: 3px;
                background-image: url(~@/assets/images/preview.png);
                background-size: 100% 100%;
                cursor: pointer;
              }

              .default-source {
                background-image: url(~@/assets/images/icon/default-source-iconA.png);
                color: #fff;
              }
            }

            img {
              width: 48px;
              height: 48px;
              margin-top: 8px;
            }
          }

          &>div:last-child {
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            color: #0f0f0f;
          }
        }
      }

      .preview-box {
        .preview-text {
          min-height: 104px;
          text-align: center;
          padding: 0 25px;
          box-sizing: border-box;
          font-weight: 600;
          font-size: 14px;
          color: #414040;
          overflow: hidden;
          cursor: default;

          &>div {
            height: 40px;
            margin-top: 28px;
            overflow: hidden;
          }

          &.a1 {
            background: url(~@/assets/images/background/text-style-1a.png);
            background-size: 100% 100%;
          }

          &.a2 {
            background: url(~@/assets/images/background/text-style-2a.png);
            background-size: 100% 100%;
          }

          &.a3 {
            background: url(~@/assets/images/background/text-style-3a.png);
            background-size: 100% 100%;
          }

          &.a4 {
            background: url(~@/assets/images/background/text-style-4a.png);
            background-size: 100% 100%;
          }
        }

        .preview-image {
          min-height: 183px;
        }

        .preview-video,
        .preview-model,
        .preview-audio {
          width: 580px;
          height: 183px;
          object-fit: cover;
          border-radius: 8px;

          video {
            width: 100%;
            height: 100%;
            object-fit: fill;
          }
        }

        .preview-audio {
          background: #D0D0D0;
          border-radius: 8px;
          background-image: url(~@/assets/images/background/audio.png);
          background-repeat: no-repeat;
          background-position: 50% 50%;
        }

        .text-position {
          margin-left: -76px;
          margin-top: 10px;

          .text-position-btn {
            width: 158px;
            height: 32px;
            border-radius: 2px;
            border: 1px solid #298BFF;
            margin-right: 20px;
            font-size: 14px;
            color: #298BFF;
            cursor: pointer;

            &.active {
              background-color: #298BFF;
              color: #FFFFFF;
            }
          }
        }

        .text-style {
          &>div:first-child {
            margin-left: -36px;
          }

          .styles {
            height: 140px;
            background: rgba(92, 92, 92, 0.16);
            border-radius: 8px;
            border: 2px solid #FFFFFF;
            margin-top: -5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 22px;
            font-weight: 600;
            font-size: 14px;
            color: #0375FF;

            &>div {
              width: 130px;
              height: 109px;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              &:last-child>img {
                width: 114px;
                margin-top: 5px;
                margin-left: 3px;
              }

              &>div img {
                margin-right: 10px;
                margin-left: -10px;
                cursor: pointer;
              }
            }
          }
        }

        .picture-view {
          margin-top: 15px;

          &>div:first-child {
            margin-left: -25px;
          }

          .styles {
            height: 140px;
            background: rgba(92, 92, 92, 0.16);
            border-radius: 8px;
            border: 2px solid #FFFFFF;
            margin-top: -5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 12px;

            &>div {
              img {
                vertical-align: middle;
              }

              .radio-style {
                margin: 0 10px;
                cursor: pointer;
              }

              span {
                vertical-align: middle;
                font-weight: 600;
                font-size: 14px;
                color: #0375FF;
              }
            }
          }
        }

        &>div {
          width: 580px;
          display: inline-block;
        }
      }
    }
  }
}

.el-size3 {
  width: 114px;
  height: 38px;
  margin-left: 12px;
}

.opacity065 {
  opacity: 0.65;
}

.el-upload__text>span {
  margin-left: 3px;
}
</style>