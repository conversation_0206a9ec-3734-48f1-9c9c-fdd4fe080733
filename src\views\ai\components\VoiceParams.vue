<template>
  <div class="voice-params">
    <div class="param-title">参数设置</div>
    <el-input
      v-model="text"
      type="textarea"
      :maxlength="1500"
      show-word-limit
      placeholder="输入想要生成的语音文本"
      class="desc-input" />
    <div class="slider-label">
      倍数选择
      <span style="float: right">x{{ rate }}</span>
    </div>
    <div class="custom-slider" ref="sliderRef" @click="onSliderClick">
      <div class="slider-track"></div>
      <div
        class="slider-active-bar"
        :style="{
          width: (rate - 1) * 33.33 + '%',
          display: rate > 1 ? 'block' : 'none',
        }"></div>
      <div
        v-for="n in 4"
        :key="n"
        class="slider-tick"
        :style="{ left: (n - 1) * 33.33 + '%' }"></div>
      <div class="slider-thumb" :style="{ left: (rate - 1) * 33.33 + '%' }"></div>
    </div>

    <div class="slider-label">音色选择</div>
    <div class="voice-select">
      <div class="voice-style">{{ selectedVoice.name }}</div>
      <div class="voice-btns">
        <div class="voice-btn" :class="{ active: selectedVoice.age === 'youth' }">青年</div>
        <div class="voice-btn" :class="{ active: selectedVoice.gender === 'female' }">女声</div>
      </div>
      <img
        src="@/assets/images/Switch22.png"
        class="switch-icon"
        style="width: 24px; height: 24px"
        @click="handleSwitchClick" />
    </div>
  </div>
  <VoiceSelectDialog
    v-model:show="showVoiceDialog"
    :voices="voices"
    :selected-idx="selectedVoiceIdx"
    @confirm="onVoiceConfirm"
    @select="onVoiceSelect" />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import VoiceSelectDialog from './VoiceSelectDialog.vue';
import { voices } from '../config/voices';
import { genAiVoice } from '@/api/modules/ai';

const text = ref('');
const rate = ref(1);
const sliderRef = ref();
const showVoiceDialog = ref(false);
const selectedVoiceIdx = ref(0);
const selectedVoice = computed(() => voices[selectedVoiceIdx.value]);

const onSliderClick = (e: MouseEvent) => {
  const slider = sliderRef.value as HTMLElement;
  const rect = slider.getBoundingClientRect();
  let percent = (e.clientX - rect.left) / rect.width;
  percent = Math.max(0, Math.min(1, percent));
  let value = Math.round(percent * 3) + 1;
  rate.value = value;
};

const handleSwitchClick = () => {
  showVoiceDialog.value = true;
};

const onVoiceConfirm = (idx: number) => {
  selectedVoiceIdx.value = idx;
  showVoiceDialog.value = false;
};
const onVoiceSelect = (idx: number) => {
  selectedVoiceIdx.value = idx;
};

const onGenerate = async () => {
  if (!text.value.trim()) {
    ElMessage.warning('请输入要生成的文本');
    return;
  }

  try {
    const result = await genAiVoice({
      voiceType: selectedVoice.value.voiceType,
      speedRatio: rate.value,
      text: text.value,
    });

    console.log('语音生成成功:', result);
    ElMessage.success('语音生成成功');

    // 这里可以处理返回的音频数据
    // 比如播放音频或保存到历史记录
  } catch (error) {
    console.error('语音生成失败:', error);
    ElMessage.error('语音生成失败');
  }
};

// 暴露给父组件的方法和属性
defineExpose({
  validateParams: () => {
    console.log('=== VoiceParams validateParams 调试 ===');
    console.log('text:', text);
    console.log('text.value:', text.value);
    console.log('text.value?.trim():', text.value?.trim());
    console.log('!!text.value?.trim():', !!text.value?.trim());
    console.log('=====================================');
    return !!text.value?.trim();
  },
  desc: text,
  ratio: rate,
  selectedVoice: selectedVoice,
  ratios: [
    { value: 1, width: 512, height: 512 },
    { value: 2, width: 1024, height: 1024 },
    { value: 3, width: 1024, height: 512 },
    { value: 4, width: 512, height: 1024 },
  ],
  onGenerate,
});
</script>

<style scoped lang="less">
.voice-params {
  width: 100%;
  .param-title {
    font-weight: bold;
    font-size: 24px;
    color: #1e1e1e;
    text-align: left;
    margin-bottom: 12px;
  }
  .desc-input {
    width: 100%;
    height: 220px;
    margin-bottom: 24px;
    :deep(.el-textarea__inner) {
      background: rgba(218, 218, 218, 0.27) !important;
      border-radius: 8px !important;
      border: none !important;
      box-shadow: none !important;
      min-height: 220px;
      height: 220px;
      resize: none;
      padding-top: 10px !important;
    }
    :deep(.el-input__count) {
      right: 12px !important;
      left: auto !important;
      bottom: 8px !important;
      background: #f5f5f5 !important;
      border-radius: 8px !important;
      color: #797979 !important;
      padding: 2px 8px !important;
      font-size: 12px !important;
      font-weight: 400 !important;
      box-shadow: none !important;
    }
  }
  .slider-label {
    font-size: 14px;
    color: #797979;
    margin-bottom: 4px;
    margin-top: 8px;
    text-align: left;
  }
  .custom-slider {
    position: relative;
    width: 100%;
    height: 24px;
    margin: 16px 0 0 0;
    user-select: none;
    .slider-track {
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 2px;
      background: #e5e5e5;
      border-radius: 2px;
      transform: translateY(-50%);
      z-index: 0;
    }
    .slider-active-bar {
      position: absolute;
      top: 50%;
      left: 0;
      height: 0px;
      border-radius: 0;
      border: 2px solid #2e76ff;
      border-bottom: none;
      border-left: none;
      border-right: none;
      background: transparent;
      transform: translateY(-50%);
      z-index: 1;
    }
    .slider-tick {
      position: absolute;
      top: 50%;
      width: 0px;
      height: 9px;
      border-radius: 0;
      border: 1.8px solid #1e1e1e;
      background: transparent;
      border-radius: 0.5px;
      transform: translate(-1px, -5px);
      z-index: 2;
    }
    .slider-thumb {
      position: absolute;
      top: 50%;
      width: 7px;
      height: 12px;
      border-radius: 2px;
      background: #2e76ff;
      border: none;
      box-shadow: none;
      transform: translate(-50%, -50%);
      cursor: pointer;
      z-index: 3;
      transition: background 0.2s;
    }
  }

  .voice-select {
    height: 50px;
    background: #f5f6f7;
    border-radius: 4px;
    padding: 16px 12px;
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    .voice-style {
      font-size: 16px;
      color: #222;
      font-weight: 500;
      margin-bottom: 6px;
    }
    .voice-btns {
      display: flex;
      gap: 8px;
      margin-right: 0;
      .voice-btn {
        width: 40px;
        height: 26px;
        background: rgba(0, 0, 0, 0.06);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #222;
        cursor: pointer;
        transition: background 0.2s, color 0.2s;
      }
      .voice-btn.active {
        background: rgba(0, 0, 0, 0.06);
        font-weight: 400;
        color: #000000;
      }
    }
    .switch-icon {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 20px;
      color: #bdbdbd;
      cursor: pointer;
    }
  }
}
</style>
