<template>
  <el-dialog
    v-model="visible"
    title="我的订单"
    width="1200px"
    :before-close="handleClose"
    class="order-dialog">
    <div class="order-content">
      <CommonTable
        :data="orderList"
        :columns="tableColumns"
        :loading="orderLoading"
        loading-text="加载中..."
        :show-pagination="true"
        :total="orderTotal"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        pagination-layout="prev, pager, next"
        empty-text="暂无内容"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, h } from 'vue';
import { ElMessage, ElTag } from 'element-plus';
import dayjs from 'dayjs';
import { getOrderEnergyList } from '@/api/modules/payment';
import CommonTable from '@/components/CommonTable.vue';

// 表格列配置接口
interface TableColumn {
  prop: string;
  label: string;
  width?: string | number;
  minWidth?: string | number;
  formatter?: any;
}

// Props
interface Props {
  show: boolean;
  columns?: TableColumn[];
}

const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
});

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean];
}>();

// 响应式数据
const orderList = ref<any[]>([]);
const orderLoading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const orderTotal = ref(0);

// 计算属性
const visible = computed({
  get: () => props.show,
  set: (value: boolean) => emit('update:show', value),
});

// 默认表格列配置
const defaultColumns: TableColumn[] = [
  {
    prop: 'orderNo',
    label: '订单ID(编号)',
  },
  {
    prop: 'energyValue',
    label: '能量值',
    formatter: (props: { value: number }) =>
      h('span', { class: 'energy-text' }, formatNumber(props.value)),
  },
  {
    prop: 'amount',
    label: '金额（¥）',
    formatter: (props: { value: number }) =>
      h('span', { class: 'amount-text' }, `¥${props.value / 100}`),
  },
  {
    prop: 'payType',
    label: '支付方式',
    formatter: (props: { value: string }) => h('span', {}, getPayTypeText(props.value)),
  },
  {
    prop: 'createTime',
    label: '订单日期',
    formatter: (props: { value: string }) => h('span', {}, formatTime(props.value)),
  },
  {
    prop: 'status',
    label: '订单状态',
    formatter: (props: { value: number }) => h('span', `已支付`),
  },
];

// 表格列配置
const tableColumns = computed(() => {
  return props.columns.length > 0 ? props.columns : defaultColumns;
});

// 格式化数字
const formatNumber = (num: number) => {
  return new Intl.NumberFormat().format(num);
};

// 获取支付方式文本
const getPayTypeText = (payType: string) => {
  const payTypeMap: Record<string, string> = {
    1: '微信支付',
    2: '支付宝',
  };
  return payTypeMap[payType] || payType || '未知';
};

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  // 使用dayjs格式化为 YYYY-MM-DD HH:mm
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm');
};

// 加载订单列表
const loadOrderList = async () => {
  orderLoading.value = true;
  try {
    const response = await getOrderEnergyList({
      pageNo: currentPage.value,
      pageSize: pageSize.value,
    });

    if (response.data) {
      // 根据实际API响应结构调整数据映射
      orderList.value = response.data.records || response.data.list || [];
      orderTotal.value = response.data.total || 0;
    }
  } catch (error) {
    console.error('加载订单列表失败：', error);
    ElMessage.error('加载订单列表失败');
    orderList.value = [];
    orderTotal.value = 0;
  } finally {
    orderLoading.value = false;
  }
};

// 分页相关方法
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadOrderList();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadOrderList();
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// 监听弹窗显示状态
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      // 重置分页
      currentPage.value = 1;
      pageSize.value = 10;
      // 加载数据
      loadOrderList();
    }
  }
);
</script>

<style scoped lang="less">
// 订单弹窗样式
:deep(.order-dialog) {
  .el-dialog__header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
    }
  }

  .el-dialog__body {
    // padding: 24px;
  }

  // 订单表格特定样式
  .order-content {
    :deep(.amount-text) {
      color: #e74c3c;
      font-weight: 500;
    }

    :deep(.energy-text) {
      color: #2e76ff;
      font-weight: 500;
    }
  }
}
</style>
