<template>
  <div class="scene-card" :class="!resultMap.latestUpdate ? 'empty-data' : ''">
    <div class="list-box" :style="{height: (heightCur + 55) + 'px'}">
      <div :class="'list-style-' + columnLength" v-for="(item, index) in resultMap.latestUpdate ? sceneList : columnLength" :key="index" :style="{marginRight: !((index + 1) % columnLength) ? '0px' : '24px'}">
        <div :class="`style-color-${item.scenePlatform}`">{{ _sceneType(item) }}</div>
        <img v-if="resultMap.latestUpdate" :src="item.scenePic" alt="" :style="{height: heightCur + 'px'}">
        <img v-else src="@/assets/images/card-default.png" :style="{height: heightCur + 'px'}"/>
        <div class="hover-mask" @click.stop="resultMap.latestUpdate ? $emit('edit', item) : null" :style="{height: (heightCur + 55) + 'px'}"></div>
        <div class="con_father">
          <div class="con_sheet">
            <div class="con_sheet_img">
              <img v-if="item.scenePlatform == 1" src="@/assets/images/home/<USER>" />
              <img v-if="item.scenePlatform == 2" src="@/assets/images/home/<USER>" />
              <img v-if="item.scenePlatform == 3" src="@/assets/images/home/<USER>" />
              <div v-if="!resultMap.latestUpdate" class="image-empty"></div>
            </div>
            <div>
              <p v-if="resultMap.latestUpdate" class="p_sceneName">{{ item.sceneName }}</p>
              <p v-if="resultMap.latestUpdate" class="p_updateTimeStr">更新时间: {{ item.updateTimeStr }}</p>
              <div class="image-empty2" v-if="!resultMap.latestUpdate"></div>
            </div>
          </div>
        </div>
        <div class="cursor-box">
          <div class="hover-card">
            <div class="share-btn" @click="$emit('share', item)" v-if="(isOperation == 1 || userInfo?.userDto?.userType == 1)">分享</div>
            <div @click="$emit('cancelTemplate', item)" v-if="item.sceneStatus == 3">取消模板</div>
            <div @click="$emit('setTemplate', item)" :class="{ isDisabled: item.sceneStatus == 3 }"
              v-if="item.sceneStatus != 3 && isOperation != 1">设为模板</div>
            <div class="move-to-style" v-if="!item.groupId">
              移动至
              <div v-if="sceneGroups.length">
                <div>
                  <div v-for="(e, i) in sceneGroups" :key="i" @click="$emit('changeGroup', `${item.id}_${e.id}`)">{{ e.groupName }}</div>
                </div>
              </div>
            </div>
            <div class="move-to-style" v-else @click="$emit('remove', item)">移出</div>
            <div class="active-delete" @click="$emit('delete', item)" v-if="item.sceneStatus != 3 && isOperation != 1">删除</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface SceneItem {
  id: number;
  sceneName: string;
  scenePic: string;
  scenePlatform: number;
  sceneType: number;
  sceneStatus: number;
  updateTimeStr: string;
  groupId?: number;
}

interface ResultMap {
  latestUpdate: boolean;
}

interface UserInfo {
  userDto?: {
    userType: number;
  }
}

interface SceneGroup {
  id: number;
  groupName: string;
}

const props = defineProps<{
  resultMap: ResultMap;
  sceneList: SceneItem[];
  columnLength: number;
  heightCur: number;
  isOperation: number;
  userInfo: UserInfo;
  sceneGroups: SceneGroup[];
}>()

const emit = defineEmits<{
  (e: 'edit', item: SceneItem): void;
  (e: 'share', item: SceneItem): void;
  (e: 'cancelTemplate', item: SceneItem): void;
  (e: 'setTemplate', item: SceneItem): void;
  (e: 'changeGroup', val: string): void;
  (e: 'remove', item: SceneItem): void;
  (e: 'delete', item: SceneItem): void;
}>()

const _sceneType = (item: SceneItem) => {
  switch(item.scenePlatform) {
    case 1:
      return '眼镜端'
    case 2:
      return '移动端'
    case 3:
      return '小程序'
    default:
      return ''
  }
}
</script>

<style scoped lang="less">
.scene-card {
  // 从ItemListView.vue中复制相关样式
}
</style> 