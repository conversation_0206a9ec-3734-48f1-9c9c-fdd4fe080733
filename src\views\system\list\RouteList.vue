<template>
  <div>
    <search-form
      :form-keys-data="keysData"
      :search-data-event="searchData"
      currentRoute="/system/route"></search-form>
  </div>
  <div>
    <table-list
      :data="tableData"
      :column-list="columnList"
      :change-page="changePage"
      :delete-content="deleteContent"
      :delete-dataEvent="deleteDataEvent"
      :data-total="pageTotal"
      :page-size="searchForm.pageSize"
      :page-no="searchForm.pageNo"
      :operation-items="operationItems"></table-list>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted, reactive } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import { useRouter } from 'vue-router';
import { getGuideRoutePage, getSceneMetaPageForWeb } from '@/api/index';
import type { OperationItems, TableRow } from '@/types/operation';

const pageTotal = ref(0); // 总计数据条数
const tableData: any = ref([]); // 表格数据
const deleteContent = {
  title: '删除路线',
  content: '是否删除此路线？',
};

const router = useRouter();

const searchForm: any = reactive({
  // 查询对象
  pageSize: 20,
  pageNo: 1,
});

// Operation items configuration
const operationItems = reactive<OperationItems>([
  {
    label: '编辑',
    key: 'edit',
    onClick: (row: TableRow) => {
      editData(row);
    },
  },
]);

const keysData: any = reactive([
  {
    key: 'sceneId',
    type: 'select',
    label: '项目名称',
    dataList: [],
  },
  {
    key: 'guideRouteName',
    type: 'input',
    label: '路线名称',
  },
]);

const editData = (data: any) => {
  router.push(`/scene_edit?sceneid=${data.sceneId}&guideRouteid=${data.id}`);
};

const deleteDataEvent = (data: any) => {};

const columnList = [
  {
    prop: 'id',
    label: '路线编号',
  },
  {
    prop: 'guideRouteName',
    label: '路线名称',
  },
  {
    prop: 'sceneName',
    label: '项目名称',
  },
  {
    prop: 'visitCount',
    label: '访问次数',
  },
  {
    prop: 'updateTimeStr',
    label: '更新日期',
  },
  {
    prop: 'operate',
    label: '操作',
    width: 120,
    type: 'operation',
  },
];

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getData();
};

const searchData = (data: any) => {
  for (const key in data) {
    searchForm[key] = data[key];
  }
  getData();
};

const getData = () => {
  getGuideRoutePage({ ...searchForm }).then((res: any) => {
    pageTotal.value = res.data.total;
    tableData.value = [...res.data.records].map((e: any) => ({
      ...e,
      hasMaterial: e.hasMaterial ? '是' : '否',
    }));
  });
};

onMounted(() => {
  getSceneMetaPageForWeb({ pageNo: 1, pageSize: 999 }).then((res: any) => {
    keysData[0].dataList = [...res.data.records].map((e: any) => ({
      name: e.sceneName,
      value: e.id,
    }));
  });
  getData();
});
</script>
