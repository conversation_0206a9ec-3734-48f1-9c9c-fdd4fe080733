<template>
  <div class="new-mask">
    <div>
      <div class="left_top">
        <div class="name">升级套餐</div>
        <div class="icon iconfont icon-close" @click="closeEvent"></div>
      </div>
      <!-- <img class="closed" src="@/assets/images/close.png" alt=""
          @click="closeEvent"> -->
      <img class="p" src="@/assets/images/wechat.jpg" alt="">
      <div class="desc">联系商务微信立即购买</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  hideAddMask: {
    default: null,
    type: Function
  }
})


const closeEvent = () => {
    props.hideAddMask()
}
</script>

<style scoped lang="less">
.new-mask {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .left_top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 24px;
      height: 24px;
    }

    .name {
      font-weight: bold;
      font-size: 18px;
      color: #1E1E1E;
      line-height: 1.5;
      transform: translateY(-1px);
    }
  }

  &>div {
    width: 335px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 16px 24px 40px;
    box-sizing: border-box;
    text-align: left;
    font-weight: 600;
    font-size: 18px;
    color: #3D566C;

    .header {
      font-weight: bold;
      font-size: 20px;
      color: #3D566C;
      margin-bottom: 14px;
      margin-top: 49px;
    }

    .p {
      width: 245px;
      height: 245px;
      margin-bottom: 12px;
      margin-top: 25px;
      margin-left: -5px;
    }

    .icon-close {
      font-size: 26px;
      cursor: pointer;
      font-weight: 400;

      &:hover {
        color: #2E76FF;
      }
    }

    .desc {
      font-weight: 400;
      font-size: 18px;
      color: #000000;
      line-height: 27px;
    }
  }
}
</style>