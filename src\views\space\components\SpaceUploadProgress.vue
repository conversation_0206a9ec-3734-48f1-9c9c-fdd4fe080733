<template>
  <div class="progress-modal-mask">
    <div class="progress-modal">
      <div class="modal-bg"></div>
      <div class="modal-gradient"></div>
      <div class="close-icon-wrap">
        <img
          src="@/assets/images/close-tips.png"
          @click="$emit('close')"
          alt="关闭"
          class="close-icon" />
      </div>
      <img :src="progressIcon" class="progress-icon" />
      <div class="progress-bar-wrap">
        <div class="progress-bar-bg"></div>
        <div
          class="progress-bar"
          :style="{ width: currentProgress + '%' }"
          :class="{ error: uploadStep === -1 }"></div>
      </div>
      <div class="progress-text" :class="{ error: uploadStep === -1, success: uploadStep === 5 }">
        {{ currentStepText }}
      </div>
      <div
        v-if="store.state.spaceUploadTask.errorMessage && uploadStep === -1"
        class="error-message">
        {{ store.state.spaceUploadTask.errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';

// 固定使用的进度图标
const progressIcon = require('@/assets/images/modelGif.gif');

interface Props {
  uploadStep: number;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  close: [];
}>();

const store = useStore();
const currentProgress = ref(0);
const targetProgress = ref(0);
const lastUpdateTime = ref(Date.now());
let progressInterval: ReturnType<typeof setInterval> | null = null;

// 进度阶段配置 - 定义每个阶段的速度和行为
const progressPhases = {
  uploading: { min: 0, max: 32, baseSpeed: 0.08, continuousSpeed: 0.02 }, // 上传阶段：慢速开始
  processing: { min: 33, max: 66, baseSpeed: 0.15, continuousSpeed: 0.03 }, // 处理阶段：中等速度
  finalizing: { min: 67, max: 98, baseSpeed: 0.12, continuousSpeed: 0.02 }, // 最终阶段：适中速度，但不到100%
  completing: { min: 99, max: 100, baseSpeed: 0.5, continuousSpeed: 0 }, // 完成阶段：只有确认后才到100%
};

// 任务阶段配置 - 基于实际WebSocket数据
const taskPhases = {
  initial: {
    text: '准备上传空间数据...',
    description: '正在初始化上传任务',
  },
  uploading: {
    text: '正在上传到服务器...',
    description: '请勿关闭此页面并保持网络畅通',
  },
  processing: {
    text: '正在解压和处理文件...',
    description: '正在解压和处理上传的文件',
  },
  finalizing: {
    text: '正在上传到 Immersal...',
    description: '正在上传空间数据到最终服务器',
  },
  completed: {
    text: '上传完成',
    description: '空间数据上传成功',
  },
  failed: {
    text: '上传失败',
    description: '空间数据上传失败，请重试',
  },
};

// 根据WebSocket数据动态计算进度
const calculateProgress = (taskProccessMap: any) => {
  if (!taskProccessMap) return 0;

  let completedTasks = 0;
  const totalTasks = 3;

  if (taskProccessMap.uploadToNjyjServer) completedTasks++;
  if (taskProccessMap.unzipAndTreat) completedTasks++;
  if (taskProccessMap.uploadToImmersal) completedTasks++;

  return Math.round((completedTasks / totalTasks) * 100);
};

// 根据任务状态确定当前阶段
const getCurrentPhase = (uploadStep: number, taskProccessMap: any) => {
  if (uploadStep === 5) return 'completed';
  if (uploadStep === -1) return 'failed';

  if (!taskProccessMap) {
    return uploadStep === 1 ? 'uploading' : 'initial';
  }

  if (taskProccessMap.uploadToImmersal) return 'finalizing';
  if (taskProccessMap.unzipAndTreat) return 'processing';
  if (taskProccessMap.uploadToNjyjServer) return 'uploading';

  return uploadStep === 1 ? 'uploading' : 'initial';
};

// 当前步骤文本 - 基于任务状态，不显示百分比
const currentStepText = computed(() => {
  // 获取当前任务进度映射
  const taskProccessMap = store.state.spaceUploadTask?.taskProccessMap || null;
  const currentPhase = getCurrentPhase(props.uploadStep, taskProccessMap);
  const phaseConfig = taskPhases[currentPhase as keyof typeof taskPhases];

  if (!phaseConfig) return '处理中...';

  // 直接返回阶段文本，不显示百分比
  return phaseConfig.text;
});

// 获取当前进度阶段
const getCurrentProgressPhase = (progress: number) => {
  if (progress <= 32) return 'uploading';
  if (progress <= 66) return 'processing';
  if (progress <= 98) return 'finalizing';
  return 'completing';
};

// 计算基于时间的持续进度增长
const calculateContinuousProgress = (currentPhase: string, timeSinceLastUpdate: number) => {
  const phase = progressPhases[currentPhase as keyof typeof progressPhases];
  if (!phase) return 0;

  // 基于时间的持续增长，但速度会随时间递减
  const timeBasedIncrement = phase.continuousSpeed * (timeSinceLastUpdate / 1000);
  const decayFactor = Math.max(0.3, 1 - timeSinceLastUpdate / 30000); // 30秒后减速到30%

  return timeBasedIncrement * decayFactor;
};

// 更新进度条 - 实现平滑的时间基础动画
const updateProgress = () => {
  const now = Date.now();
  const timeSinceLastUpdate = now - lastUpdateTime.value;

  // 如果是完成状态，直接设置为100%
  if (props.uploadStep === 5) {
    currentProgress.value = 100;
    targetProgress.value = 100;
    return;
  }

  // 如果是错误状态，停止进度
  if (props.uploadStep === -1) {
    return;
  }

  // 获取实际任务进度作为目标
  const taskProccessMap = store.state.spaceUploadTask?.taskProccessMap;
  const actualProgress = calculateProgress(taskProccessMap);

  // 更新目标进度，但确保不会超过实际完成的阶段边界
  if (actualProgress > targetProgress.value) {
    targetProgress.value = actualProgress;
    lastUpdateTime.value = now;
  }

  const current = currentProgress.value;
  const target = targetProgress.value;
  const currentPhase = getCurrentProgressPhase(current);
  const phase = progressPhases[currentPhase as keyof typeof progressPhases];

  if (!phase) return;

  // 计算进度增量
  let increment = 0;

  if (current < target) {
    // 向目标进度移动 - 使用基础速度
    const distance = target - current;
    increment = Math.min(distance, phase.baseSpeed);
  } else if (current < phase.max && timeSinceLastUpdate > 2000) {
    // 持续的视觉进度 - 即使没有新的WebSocket数据也继续缓慢增长
    increment = calculateContinuousProgress(currentPhase, timeSinceLastUpdate);

    // 确保不会超过当前阶段的最大值
    increment = Math.min(increment, phase.max - current);

    // 如果没有实际进度更新，限制持续进度的上限
    if (actualProgress === 0 && current > 15) increment = 0; // 上传阶段限制
    if (actualProgress === 33 && current > 50) increment = 0; // 处理阶段限制
    if (actualProgress === 67 && current > 85) increment = 0; // 最终阶段限制
  }

  // 应用进度增量
  if (increment > 0) {
    currentProgress.value = Math.min(phase.max, current + increment);

    // 防止在没有实际完成确认的情况下达到100%
    if (actualProgress < 100) {
      currentProgress.value = Math.min(98, currentProgress.value);
    }
  }
};

// 监听步骤变化和任务进度变化
watch(
  [() => props.uploadStep, () => store.state.spaceUploadTask?.taskProccessMap],
  ([newStep, taskProccessMap]) => {
    const now = Date.now();

    // 如果是完成状态，立即设置为100%
    if (newStep === 5) {
      currentProgress.value = 100;
      targetProgress.value = 100;
      return;
    }

    // 如果是错误状态，停止进度但不重置
    if (newStep === -1) {
      return;
    }

    // 当收到新的WebSocket数据时，更新目标进度和时间戳
    if (taskProccessMap) {
      const actualProgress = calculateProgress(taskProccessMap);
      if (actualProgress > targetProgress.value) {
        targetProgress.value = actualProgress;
        lastUpdateTime.value = now;
      }
    }

    // 如果是刚开始上传，初始化进度
    if (newStep === 1 && currentProgress.value === 0) {
      currentProgress.value = 1; // 给用户一个开始的视觉反馈
      lastUpdateTime.value = now;
    }
  },
  { immediate: true, deep: true }
);

// 监听任务完成状态
watch(
  () => store.state.spaceUploadTask.isCompleted,
  (isCompleted) => {
    if (isCompleted) {
      // 任务完成后延迟3秒自动关闭弹框
      setTimeout(() => {
        emit('close');
      }, 3000);
    }
  }
);

// 启动进度更新定时器
onMounted(() => {
  // 初始化时间戳
  lastUpdateTime.value = Date.now();
  // 使用更高频率的定时器（每200ms）以实现平滑的进度动画
  progressInterval = setInterval(updateProgress, 200);
});

// 清理定时器
onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval);
    progressInterval = null;
  }
});
</script>

<style scoped>
.progress-modal-mask {
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.35);
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-modal {
  position: relative;
  min-width: 520px;
  min-height: 300px;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px 32px 32px 32px;
  overflow: hidden;
  background: transparent;
}

.modal-bg {
  position: absolute;
  inset: 0;
  background: #fff;
  border-radius: 10px;
  z-index: 0;
}

.modal-gradient {
  position: absolute;
  inset: 0;
  border-radius: 10px;
  background: linear-gradient(180deg, rgba(46, 118, 255, 0.65) 0%, rgba(107, 211, 255, 0) 92%),
    #ffffff;
  z-index: 1;
  pointer-events: none;
}

.progress-modal > *:not(.modal-bg):not(.modal-gradient) {
  position: relative;
  z-index: 2;
}

.close-icon-wrap {
  width: 100%;
  height: 48px;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-top: 10px;
  box-sizing: border-box;
}

.close-icon {
  width: 48px;
  height: 48px;
  cursor: pointer;
  position: relative;
  left: 24px;
}

.progress-icon {
  width: 140px;
  height: 140px;
  margin-bottom: 24px;
  margin-top: 8px;
}

.progress-bar-wrap {
  width: 280px;
  height: 4px;
  position: relative;
  margin-bottom: 24px;
}

.progress-bar-bg {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border-radius: 4px;
}

.progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #3c96ff 0%, #1e7ce8 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-bar.error {
  background: linear-gradient(90deg, #ff4d4f 0%, #ff7875 100%);
}

.progress-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.progress-text.success {
  color: #52c41a;
}

.progress-text.error {
  color: #ff4d4f;
}

.error-message {
  font-size: 14px;
  color: #ff4d4f;
  text-align: center;
  margin-top: 12px;
  padding: 8px 16px;
  background: rgba(255, 77, 79, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(255, 77, 79, 0.2);
}
</style>
