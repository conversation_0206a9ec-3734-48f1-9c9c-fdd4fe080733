<template>
  <SceneEdit2D ref="sceneEdit2DRef" :isPlanStyle="isPlanStyle" :show-route="1" :add3DModel="add3DModel"
    :set3DLocation="set3DLocation" :activeSource3D="activeSource3D" :refresh3D="refresh3D" />
  <SceneEdit3D ref="sceneEdit3DRef" :isPlanStyle="isPlanStyle" :add2DModel="add2DModel" :activeSource2D="activeSource2D"
    :set2DLocation="set2DLocation" :refresh2D="refresh2D" />
  <div class="change-style-box">
    <div class="change-style1" v-if="!styleListShow" @click="styleListShow = true">
      <div :class="isPlanStyle ? 'active-1' : 'active-2'">{{ isPlanStyle ? '2D' : '3D' }}</div>
    </div>
    <div class="change-style2" v-if="styleListShow" @click="styleListShow = false">
      <div v-if="!isPlanStyle" :class="isPlanStyle ? 'active' : ''" @click="isPlanStyle = true">2D</div>
      <div v-if="!isPlanStyle" :class="!isPlanStyle ? 'active' : ''" @click="isPlanStyle = false">3D</div>

      <!-- 实现测试提的2d和3d切换后位置变化功能 -->
      <div v-if="isPlanStyle" :class="!isPlanStyle ? 'active' : ''" @click="isPlanStyle = false">3D</div>
      <div v-if="isPlanStyle" :class="isPlanStyle ? 'active' : ''" @click="isPlanStyle = true">2D</div>
    </div>
  </div>
  <div class="header-tools">
    <div class="save" @click="saveSceneData">
      <img src="@/assets/images/experience-icon/save.png" alt="">
      <span>保存</span>
    </div>
    <div class="release" @click="showShare">
      <img src="@/assets/images/experience-icon/release.png" alt="">
      <span>发布</span>
    </div>
  </div>
  <share-view v-if="showShareMask" :hide-mask="hideMask"></share-view>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import SceneEdit2D from '@/components/SceneEdit2D.vue'
import SceneEdit3D from '@/components/SceneEdit3D.vue'
import ShareView from '@/components/experience/ShareView.vue'
import { useStore } from 'vuex'
import { Vector3, Scene, Vector2, Raycaster, Group, Quaternion, Box3 } from 'three'

const store = useStore()
const isPlanStyle = ref(true) // 是否是2D平面模式
const styleListShow = ref(false) // 是否显示2D和3D的切换
const sceneEdit2DRef = ref()
const sceneEdit3DRef = ref()
const showShareMask = ref(false)

// 2D页面的生成同步到3D页面
const add3DModel = (point: any, currentSource: any, activeAreaIndex: number) => {
  if (!currentSource.groupType) {
    sceneEdit3DRef.value.addModelFrom2D(new Vector3(point.x, 0, point.z), currentSource, activeAreaIndex)
  }
}

// 通过2D设置3D位置
const set3DLocation = (point: any) => {
  sceneEdit3DRef.value.changeRightValue(point.x, 'x', 'position')
  sceneEdit3DRef.value.changeRightValue(point.z, 'z', 'position')
}

// 2D触发左侧高亮，3D响应
const activeSource3D = (i: number, index: number, isActive?: boolean) => {
  sceneEdit3DRef.value.activeSourceMaterial(i, index, isActive, true)
}

// 3D页面的生成同步到2D页面
const add2DModel = (currentSource: any, activeAreaIndex: number) => {
  sceneEdit2DRef.value.addModelFrom3D(currentSource, activeAreaIndex)
}

// 3D触发左侧高亮，2D响应
const activeSource2D = (i: number, index: number, isActive?: boolean) => {
  sceneEdit2DRef.value.activeSourceMaterial(i, index, isActive, true)
}

const hideMask = () => {
  showShareMask.value = false
}

const showShare = () => {
  showShareMask.value = true
}

// 通过3D设置2D位置
const set2DLocation = (point: any) => {
  sceneEdit2DRef.value.changeSourcePositionFrom3D(point)
}

// 2D页面的保存刷新
const refresh3D = () => {
  window.localStorage.setItem('isPlanStyle', '1')
  sceneEdit3DRef.value.getInitData(true)
  // location.reload();
}

// 3D页面的保存刷新
const refresh2D = () => {
  window.localStorage.setItem('isPlanStyle', '2')
  sceneEdit2DRef.value.getInitData(true)
  // location.reload();
}

onMounted(() => {
  if (window.history) {
    history.pushState(null, '', document.URL)
    window.addEventListener('popstate', function () {
      history.pushState(null, '', document.URL)
      const planStyle = window.localStorage.getItem('isPlanStyle') == '1'
      if (planStyle) {
        window.localStorage.setItem('isPlanStyle', '')
        sceneEdit2DRef.value && sceneEdit2DRef.value.exitEdit()
      } else {
        sceneEdit3DRef.value && sceneEdit3DRef.value.exitEdit()
      }
    })
  }
  isPlanStyle.value = true
})
</script>
<style scoped lang="less">
img {
  width: 24px;
  height: 24px;
}

.change-style-box {
  position: absolute;
  left: 355px;
  bottom: 20px;
  margin-top: 10px;
  font-weight: bold;
  font-size: 16px;
  color: #3D566C;
  line-height: 22px;
  background: #FFFFFF;
  border-radius: 8px;
}

.header-tools {
  position: absolute;
  right: 152px;
  top: -44px;
  width: 240px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  z-index: 999;

  .profile-picture {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background: rgba(54, 113, 254, 0.3);
  }

  .save {
    width: 72px;
    height: 30px;
    line-height: 30px;
    border-radius: 6px;
    border: 1px solid #3671FE;
    margin-left: 40px;
    margin-right: 20px;
    box-sizing: border-box;
    padding: 0 10px;
    font-weight: bold;
    font-size: 14px;
    color: #3D566C;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    img {
      width: 16px;
      height: 16px;
    }

    span {
      font-size: 14px;
      font-weight: 700;
    }
  }

  .release {
    width: 70px;
    height: 30px;
    line-height: 30px;
    background: #3671FE;
    border-radius: 6px;
    padding: 0 10px;
    box-sizing: border-box;
    font-weight: bold;
    font-size: 14px;
    color: #FFFFFF;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    img {
      width: 16px;
      height: 16px;
    }

    span {
      font-size: 14px;
      font-weight: 700;
      color: #FFFFFF;
    }
  }
}

.change-style1 {
  width: 126px;
  height: 42px;
  box-sizing: border-box;
  overflow: hidden;
  padding-left: 20px;

  &>div {
    position: relative;
    padding-left: 31px;
    width: 107px;
    box-sizing: border-box;
    margin-top: 10px;
    color: #3671FE;
    cursor: pointer;
    text-align: left
  }

  &>.active-1::before,
  &>.active-2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 22px;
    height: 22px;
    background: url(~@/assets/images/icon/plan-styleA.png);
    background-size: 100% 100%;
  }

  &>.active-1::after,
  &>.active-2::after {
    content: '';
    position: absolute;
    right: 20px;
    top: 4px;
    width: 9px;
    height: 14px;
    background: url(~@/assets/images/experience-icon/expand-iconA.png);
    background-size: 100% 100%;
  }

  &>.active-2::before {
    background: url(~@/assets/images/icon/cube-styleA.png);
    background-size: 100% 100%;
  }
}

.change-style2 {
  width: 126px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  box-sizing: border-box;
  overflow: hidden;
  // padding: 10px 0;

  &>div {
    position: relative;
    padding-left: 51px;
    width: 100%;
    height: 42px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    cursor: pointer;

    &:hover {
      background: rgba(54, 113, 254, 0.1);
    }
  }

  &>div:first-child:before,
  &>div:last-child:before {
    content: '';
    position: absolute;
    left: 20px;
    top: 10px;
    width: 22px;
    height: 22px;
    background: url(~@/assets/images/icon/plan-style.png);
    background-size: 100% 100%;
  }

  &>div:last-child:before {
    background: url(~@/assets/images/icon/cube-style.png);
    background-size: 100% 100%;
  }

  &>div:first-child::after,
  &>div:last-child::after {
    content: '';
    position: absolute;
    right: 20px;
    top: 14px;
    width: 9px;
    height: 14px;
    background: url(~@/assets/images/experience-icon/expand-icon.png);
    background-size: 100% 100%;
  }

  &>.active {
    color: #3671FE;
  }

  &>div:first-child.active::before {
    background: url(~@/assets/images/icon/plan-styleA.png);
    background-size: 100% 100%;
  }

  &>div:first-child.active::after {
    background: url(~@/assets/images/experience-icon/expand-iconA.png);
    background-size: 100% 100%;
  }

  &>div:last-child.active::before {
    background: url(~@/assets/images/icon/cube-styleA.png);
    background-size: 100% 100%;
  }

  &>div:last-child.active::after {
    background: url(~@/assets/images/experience-icon/expand-iconA.png);
    background-size: 100% 100%;
  }
}
</style>