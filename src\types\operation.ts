export interface TableRow {
  id?: string | number;
  userStatus?: number;
  adminUserInfo?: {
    id?: string | number;
    packageVersion?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface OperationItem<T extends TableRow = TableRow> {
  label: string | ((row: T) => string);
  key: string;
  show?: (row: T) => boolean;
  disabled?: (row: T) => boolean;
  onClick: (row: T) => void;
}

export type OperationItems<T extends TableRow = TableRow> = OperationItem<T>[];
