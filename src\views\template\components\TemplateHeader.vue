<template>
  <div class="template-header">
    <!-- 页面标题 -->
    <div class="template-header__title-wrapper">
      <div class="template-header__left-content">
        <div
          class="template-header__title"
          :style="{ color: isAdmin && !isSortMode ? '#1E1E1E' : '' }"
          @click="handleTitleClick">
          模板广场
        </div>

        <!-- 普通状态显示按钮 -->
        <button
          class="template-header__sort-button"
          v-if="isAdmin && !isSortMode"
          @click="handleSortClick">
          设置模板排序
        </button>
        <img
          v-if="isAdmin && isSortMode"
          src="@/assets/images/jiantou233.png"
          alt=""
          class="model-category-icon"
          style="width: 24px; height: 24px" />
        <!-- 排序状态显示文字 -->
        <div
          class="template-header__sort-text"
          v-if="isAdmin && isSortMode"
          @click="handleSortClick">
          设置模板排序
        </div>
      </div>
      <div class="template-header__tabs" :class="{ 'tabs-hidden': !isSortMode }">
        <TabsBar v-model="activeTab" :tabs="platformTabs" :bottom-margin="0" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import TabsBar from '@/components/TabsBar.vue';

// Props
const props = defineProps({
  isAdmin: {
    type: Boolean,
    default: false,
  },
  isSortMode: {
    type: Boolean,
    default: false,
  },
  activeTabValue: {
    type: String,
    default: 'web',
  },
  platformTabs: {
    type: Array,
    default: () => [
      { name: 'web', label: '网页端' },
      { name: 'miniprogram', label: '小程序端' },
    ],
  },
});

// Emits
const emit = defineEmits(['update:activeTabValue', 'title-click', 'sort-click']);

// Computed
const activeTab = computed({
  get: () => props.activeTabValue,
  set: (value) => emit('update:activeTabValue', value),
});

// Methods
const handleTitleClick = () => {
  emit('title-click');
};

const handleSortClick = () => {
  emit('sort-click');
};
</script>

<style lang="less" scoped>
.template-header {
  margin-bottom: 24px;

  &__title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-right: 25px;
  }

  &__left-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__title {
    font-weight: bold;
    font-size: 20px;
    color: #797979; // 修改为灰色
    text-align: left;
    cursor: pointer;
    user-select: none;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  &__sort-button {
    width: 120px;
    height: 32px;
    background: #2e76ff;
    border-radius: 4px;
    border: none;
    font-weight: bold;
    cursor: pointer;
    user-select: none;
    transition: opacity 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 12px;
    color: #ffffff;

    &:hover {
      opacity: 0.8;
    }
  }

  &__sort-text {
    font-weight: bold;
    font-size: 14px;
    color: #1e1e1e;
    cursor: pointer;
    user-select: none;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  &__tabs {
    width: 200px;
    transition: opacity 0.3s ease;

    &.tabs-hidden {
      opacity: 0;
      pointer-events: none;
    }
  }
}

.model-category-icon {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}

// 响应式设计
@media (max-width: 992px) {
  .template-header {
    &__title-wrapper {
      padding-right: 10px;
    }
  }
}

@media (max-width: 768px) {
  .template-header {
    &__title-wrapper {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      padding-right: 25px;
    }

    &__left-content {
      gap: 8px;
    }

    &__sort-button {
      width: 100px;
      height: 28px;
      font-size: 16px;
    }

    &__sort-text {
      font-size: 18px;
    }

    &__tabs {
      width: 180px;
      margin-top: 8px;
    }
  }
}

@media (max-width: 576px) {
  .template-header {
    &__title {
      font-size: 18px;
    }

    &__sort-button {
      width: 90px;
      height: 26px;
      font-size: 14px;
    }

    &__sort-text {
      font-size: 16px;
    }

    &__tabs {
      width: 160px;
    }
  }
}
</style>
