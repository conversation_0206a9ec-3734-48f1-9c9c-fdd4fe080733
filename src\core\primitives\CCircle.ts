import {
  BufferGeometry,
  Mesh,
  Intersection,
  MeshBasicMaterial,
  Raycaster,
  RepeatWrapping,
  TextureLoader,
  Vector2,
  Vector3,
  S<PERSON>pe,
  ShapeGeometry,
  Matrix4,
  Quaternion,
} from "three";
import { ACCURACY } from "@/core/config";
import { screenToWorldVector } from "@/utils";

export const ADSORPTION_RADIUS = 10; // 点吸附半径

export type CCircleAttrs = {
  position?: Vector2;
  radius?: number;
  color?: number;
  uuid?: string;
  normal?: Vector3;
  transparent?: boolean;
  url?: string;
  depthTest?: boolean;
  constraint?: any; // 约束缓存
  marks?: any; // 标注缓存
  id?: number;
  type?: string;
};
export class CCircle extends Mesh {
  geometry: ShapeGeometry;
  material: MeshBasicMaterial;
  name: string;
  userData: { [k: string]: any };
  constructor(attrs?: CCircleAttrs) {
    super();

    this.name = "ccircle";
    this.geometry = new ShapeGeometry(new Shape());
    this.material = new MeshBasicMaterial({ side: 2 });
    this.userData = {};
    this.initialize(attrs);
  }
  getType() {
    // 获取类型
    return "Point";
  }

  initData(attrs?: CCircleAttrs) {
    // 约束/标注/uuid/索引缓存
    this.userData = {
      constraint: {
        links: [],
      },
      marks: [],
      uuid: "",
      index: -1,
      radius: 0,
    };
    if (attrs && attrs.constraint !== undefined)
      this.userData.constraint = attrs.constraint; // constraint data cache
    if (attrs && attrs.marks !== undefined) this.userData.marks = attrs.marks; // marks data cache
    const normal =
    attrs && attrs.normal !== undefined
      ? attrs.normal.normalize()
      : new Vector3(0, 1, 0); // 当前planeGeometry传入的法向量
  const matrix = new Matrix4().makeRotationFromQuaternion(
    new Quaternion().setFromUnitVectors(
      new Vector3(0, 0, 1), // 初始向量
      normal // 面所在的法向量
    )
  );
  this.userData.matrix = matrix.clone();
  }

  initUI(attrs?: CCircleAttrs) {
    // 默认坐标原点
    if (attrs && attrs.radius && attrs.position) {
      this.setVertexs(attrs.radius, attrs.position);
    }

    attrs && attrs.transparent !== undefined
      ? this.setTransparent(attrs.transparent)
      : this.setTransparent(true); // 透明

    if (attrs && attrs.url !== undefined) this.loadTexture(attrs.url); // 默认纹理
    if (attrs && attrs.color !== undefined) this.setColor(attrs.color); // 颜色

    // - 所有情况下点都是 depthtest 为false
    this.setDepthTest(false);
  }

  initialize(attrs?: CCircleAttrs) {
    this.initData(attrs);
    this.initUI(attrs);
  }

  // @override 射线拾取点
  raycast(raycaster: Raycaster, intersects: Intersection[]) {
    const ori = (raycaster as any).ray.origin;
    // const oriCurrent = ori.clone().project((window as any).camera)
    // const v1 =  new Vector2((0.5 + oriCurrent.x / 2) * window.innerWidth, (0.5 - oriCurrent.y / 2) * window.innerHeight)
    // const position = new Vector3(this.geometry.attributes.position.array[0], this.geometry.attributes.position.array[1], this.geometry.attributes.position.array[2])
    // const current = position.clone().project((window as any).camera)
    // const v2 = new Vector2((0.5 + current.x / 2) * window.innerWidth, (0.5 - current.y / 2) * window.innerHeight)
    // if (v1.distanceToSquared(v2) <= ADSORPTION_RADIUS * ADSORPTION_RADIUS) {
    //     intersects.push({
    //         distance: current.clone().distanceTo((window as any).camera.position),
    //         object: this,
    //         point: position
    //     } as any)
    // }
  }

  loadTexture(url: string) {
    const t = new TextureLoader().load(url);
    t.wrapS = t.wrapT = RepeatWrapping;
    this.material.map = t;
    return this;
  }

  //
  setDepthTest(depthTest: boolean) {
    this.material.depthTest = depthTest;
    return this;
  }

  setTransparent(transparent: boolean) {
    this.material.transparent = transparent;
    this.material.opacity = 0.2;
    return this;
  }

  // 通过位置和半径生成圆
  setVertexs(radius: number, position: Vector2) {
    const x = 0,
      y = 0;
    const heartShape = new Shape();
    heartShape.moveTo(x, y);
    heartShape.arc(x, y, radius, 0, Math.PI * 2, true);
    this.geometry = new ShapeGeometry(heartShape, 32);
    this.geometry.lookAt((window as any).camera.up);
    const p = screenToWorldVector(position.x, position.y);
    this.position.set(p.x, p.y + ACCURACY, p.z);
    // 设置半径
    this.userData.radius = radius;
    return this;
  }

  // 计算面积
  getArea() {
    return Math.PI * Math.pow(this.userData.radius, 2);
  }

  getVertexs() {
    // const vertexs = []
    // if (!this.geometry.attributes.position) return []
    // const bufferData = this.geometry.attributes.position.array
    // for (let i = 0; i < bufferData.length; i += 3) {
    //     vertexs.push(new Vector3(bufferData[i], bufferData[i + 1], bufferData[i + 2]))
    // }
    // return vertexs
  }

  // 颜色
  setColor(color: number) {
    this.material.color.set(color);
    return this;
  }

  getColor() {
    return this.material.color;
  }

  // 克隆过滤一些属性后的自身信息
  cloneSelffilter(filters: any) {
    const Json = this.toJSON();
    for (const key in Json) {
      if (filters && filters.indexOf(key) > -1) Json[key] = undefined;
    }
    return new CCircle(Json);
  }

  cloneSelf() {
    return new CCircle(this.toJSON());
  }

  toJSON() {
    const json: { [k: string]: any } = {
      name: this.name,
      color: this.getColor(),
    };

    for (const key in this.userData) {
      json[key] = this.userData[key];
    }
    return json;
  }
}
