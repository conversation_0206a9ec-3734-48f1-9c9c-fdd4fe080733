import { ElMessage } from 'element-plus';
import { getAiTaskStatus } from '@/api/modules/ai';
import type { ApiResponse } from '@/types/api';

interface TaskStatusData {
  status: number;
  rank: number;
}

interface PollingOptions {
  taskId: number;
  taskType: '图片' | '语音' | '视频' | '3D模型';
  onProgress: (data: { status: number; rank: number; progress: number }) => void;
  onComplete: () => void;
  interval?: number;
  maxAttempts?: number;
}

export const startTaskPolling = ({
  taskId,
  taskType,
  onProgress,
  onComplete,
  interval = 3000,
  maxAttempts = 120,
}: PollingOptions) => {
  let pollTimer: ReturnType<typeof setTimeout> | null = null;
  let isPolling = true; // 添加标志来跟踪是否应该继续轮询
  let attempts = 0;
  let currentProgress = 0;

  // 清理轮询的函数
  const clearPolling = () => {
    isPolling = false; // 设置标志以停止新的轮询
    if (pollTimer) {
      clearTimeout(pollTimer);
      pollTimer = null;
    }
  };

  const pollStatus = async () => {
    if (!isPolling) return; // 如果已经停止轮询，直接返回

    try {
      const statusResponse: ApiResponse<TaskStatusData> = await getAiTaskStatus(taskId);
      // 如果已经停止轮询，不处理响应
      if (!isPolling) return;

      if (statusResponse.code === '200' && !statusResponse.error && statusResponse.data) {
        const { status, rank } = statusResponse.data;

        // 更新进度
        if (status !== 4 && status !== 2) {
          // 非完成和失败状态时，进度缓慢增加
          currentProgress = Math.min(80, currentProgress + 1);
        }

        // 更新状态和排队信息
        onProgress({ status, rank, progress: currentProgress });

        if (status === 4) {
          // 处理完毕
          ElMessage.success(`${taskType}生成成功`);
          onComplete();
          clearPolling();
          return;
        } else if (status === 2) {
          // 失败
          ElMessage.error(`${taskType}生成失败`);
          clearPolling();
          return;
        }

        // 继续轮询
        attempts++;
        if (attempts < maxAttempts && isPolling) {
          // 检查是否应该继续轮询
          pollTimer = setTimeout(pollStatus, interval);
        } else {
          if (attempts >= maxAttempts) {
            ElMessage.warning(`${taskType}生成超时，请稍后查看结果`);
          }
          clearPolling();
        }
      }
    } catch (error) {
      // 如果已经停止轮询，不显示错误消息
      if (!isPolling) return;

      console.error(`轮询${taskType}任务状态失败:`, error);
      ElMessage.error('获取任务状态失败');
      clearPolling();
    }
  };

  // 开始第一次轮询
  pollTimer = setTimeout(pollStatus, interval);

  // 返回清理函数
  return clearPolling;
};
