<template>
  <div class="experience-home">
    <div class="list-title" :class="!resultMap.create ? 'empty-text' : ''">快速创建</div>
    <div
      class="quickly-create"
      v-if="!resultMap.create"
      :class="!resultMap.create ? 'empty-title' : ''">
      <div class="card1 card-room has-permission"></div>
      <div class="card1 card-mobile has-permission"></div>
      <div class="card1 card-wechat has-permission"></div>
      <div class="card2">
        <div class="has-permission"></div>
        <div class="has-permission"></div>
      </div>
      <div class="card2 card3">
        <div class="has-permission"></div>
        <div class="has-permission"></div>
      </div>
      <div class="card2 card3">
        <div class="has-permission"></div>
        <div class="has-permission"></div>
      </div>
      <div class="card2 card3">
        <div class="has-permission"></div>
        <div class="has-permission"></div>
      </div>
    </div>
    <div class="quickly-create" :class="pageStyle" v-if="resultMap.create">
      <div
        class="card1 card-room"
        :class="permissionMap.glasses ? 'has-permission' : ''"
        @click="permissionMap.glasses ? addNewProject(1, 1) : null">
        <div class="glasses"></div>
        <div>创建</div>
        <div>眼镜端 空间AR</div>
      </div>
      <div
        class="card1 card-mobile"
        :class="permissionMap.mobile ? 'has-permission' : ''"
        @click="permissionMap.mobile ? addNewProject(2, 1) : null">
        <div class="mobile"></div>
        <div>创建</div>
        <div>移动端 空间AR</div>
      </div>
      <div
        class="card1 card-wechat"
        :class="permissionMap.wechat ? 'has-permission' : ''"
        @click="permissionMap.wechat ? addNewProject(3, 1) : null">
        <div class="wechat"></div>
        <div>创建</div>
        <div>微信小程序 空间AR</div>
      </div>
      <div class="card2">
        <div :class="permissionMap.upload ? 'has-permission' : ''" @click="modalShow2 = true">
          <img src="@/assets/images/home/<USER>" />
          <div>上传 空间数据</div>
        </div>
        <div :class="permissionMap.path ? 'has-permission' : ''" @click="modalShow3 = true">
          <img src="@/assets/images/home/<USER>" />
          <div>创建 路径导航</div>
        </div>
      </div>
      <div class="card2 card3">
        <div
          :class="permissionMap.plan ? 'has-permission' : ''"
          @click="permissionMap.plan ? addNewProject(3, 2) : null">
          <img src="@/assets/images/home/<USER>" />
          <div>创建 平面AR</div>
        </div>
        <div
          :class="permissionMap.singleScene ? 'has-permission' : ''"
          @click="permissionMap.singleScene ? addNewProject(3, 8) : null">
          <img src="@/assets/images/home/<USER>" />
          <div>创建 单场景AR</div>
        </div>
      </div>
      <div class="card2 card3">
        <div
          :class="permissionMap.image ? 'has-permission' : ''"
          @click="permissionMap.image ? addNewProject(3, 3) : null">
          <img src="@/assets/images/home/<USER>" />
          <div>创建 图像AR</div>
        </div>
        <div
          :class="permissionMap.body ? 'has-permission' : ''"
          @click="permissionMap.body ? addNewProject(3, 5) : null">
          <img src="@/assets/images/home/<USER>" />
          <div>创建 身体AR</div>
        </div>
      </div>
      <div class="card2 card3">
        <div
          :class="permissionMap.face ? 'has-permission' : ''"
          @click="permissionMap.face ? addNewProject(3, 6) : null">
          <img src="@/assets/images/home/<USER>" />
          <div>创建 人脸AR</div>
        </div>
        <div
          :class="permissionMap.hand ? 'has-permission' : ''"
          @click="permissionMap.hand ? addNewProject(3, 7) : null">
          <img src="@/assets/images/home/<USER>" />
          <div>创建 手势AR</div>
        </div>
      </div>
    </div>

    <!-- 模板 -->
    <div class="box__">
      <div class="head" :class="!resultMap.template ? 'empty-text' : ''">推荐模板</div>
      <div class="menu_box" v-if="resultMap.template">
        <div
          class="menu_list"
          :class="{ active_menu_list: activeMenuTab.name == menu.name }"
          v-for="(menu, index) in arMenuList"
          :key="index"
          @click="activeTab(menu)">
          {{ menu.name }}
        </div>
      </div>
      <div class="menu_box" v-if="!resultMap.template">
        <div
          style="background-color: #f7f7f9; cursor: default"
          class="menu_list"
          v-for="item in 7"></div>
      </div>
    </div>
    <div class="box_content" :class="!resultMap.template ? 'empty-data' : ''">
      <div class="list-box" :style="{ height: heightCur + 45 + 'px' }">
        <div
          :class="'list-style-' + columnLength"
          v-for="(item, i) in resultMap.template
            ? templateList.slice(0, columnLength)
            : columnLength"
          :key="item.sceneId"
          @mouseenter="resultMap.template ? mouseenterTemp(item, i) : null"
          :id="'item' + i"
          @mouseleave="resultMap.template ? mouseoverTemp(item) : null">
          <img
            v-if="resultMap.template"
            :src="item.templatePicKey.split('?')[0]"
            alt=""
            :style="{ height: heightCur + 'px' }" />
          <img
            v-else
            src="@/assets/images/card-default.png"
            :style="{ height: heightCur + 'px' }" />
          <div class="hover-mask" :style="{ height: heightCur + 45 + 'px' }">
            <div>
              <div class="learn-btn" v-if="userType == 1" @click="editTemplate(item)">修改</div>
              <div class="learn-btn" @click="startLearn(item, 'sceneId')">体验</div>
              <div class="create-btn" @click="openCreateScene(item)" v-if="item.sceneType != 1">
                创建同款
              </div>
            </div>
          </div>
          <div class="list-bottom">
            <img v-if="item.scenePlatform == 1" src="@/assets/images/home/<USER>" />
            <img v-if="item.scenePlatform == 2" src="@/assets/images/home/<USER>" />
            <img v-if="item.scenePlatform == 3" src="@/assets/images/home/<USER>" />
            <div v-if="!resultMap.template" class="image-empty"></div>
            <span :class="!resultMap.template ? 'empty-style' : ''">{{ item.templateName }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="list-title" :class="!resultMap.latestUpdate ? 'empty-text' : ''">最新更新</div>
    <div class="scene-card" :class="!resultMap.latestUpdate ? 'empty-data' : ''">
      <div class="list-box" :style="{ height: heightCur + 55 + 'px' }">
        <!-- 新页面入口卡片 -->
        <div
          :class="'list-style-' + columnLength + ' new-page-entry'"
          @click="goToNewPage"
          style="cursor: pointer">
          <div class="style-color-new">🚀 新功能</div>
          <img src="@/assets/images/card-default.png" :style="{ height: heightCur + 'px' }" />
          <div class="hover-mask new-page-hover" :style="{ height: heightCur + 55 + 'px' }">
            <div class="new-page-content">
              <div class="new-page-title">全新编辑器</div>
              <div class="new-page-desc">体验重构后的左侧边栏</div>
            </div>
          </div>
          <div class="con_father">
            <div class="con_sheet">
              <div class="con_sheet_img">
                <img src="@/assets/images/icon/add-interaction.png" />
              </div>
              <div>
                <p class="p_sceneName">空间编辑器 V2</p>
                <p class="p_updateTimeStr">
                  更新时间: {{ formatDateOnly(new Date().toISOString()) }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 原有的最新更新列表 -->
        <div
          :class="'list-style-' + columnLength"
          v-for="(item, index) in resultMap.latestUpdate
            ? sceneList.slice(0, columnLength - 1)
            : columnLength - 1"
          :key="index">
          <div :class="`style-color-${item.scenePlatform}`">{{ _sceneType(item) }}</div>
          <img
            v-if="resultMap.latestUpdate"
            :src="item.scenePic"
            alt=""
            :style="{ height: heightCur + 'px' }" />
          <img
            v-else
            src="@/assets/images/card-default.png"
            :style="{ height: heightCur + 'px' }" />
          <div
            class="hover-mask"
            @click.stop="resultMap.latestUpdate ? routerEdit(item) : null"
            :style="{ height: heightCur + 55 + 'px' }"></div>
          <div class="con_father">
            <div class="con_sheet">
              <div class="con_sheet_img">
                <img
                  v-if="item.scenePlatform == 1"
                  src="@/assets/images/home/<USER>" />
                <img
                  v-if="item.scenePlatform == 2"
                  src="@/assets/images/home/<USER>" />
                <img
                  v-if="item.scenePlatform == 3"
                  src="@/assets/images/home/<USER>" />
                <div v-if="!resultMap.latestUpdate" class="image-empty"></div>
              </div>
              <div>
                <p v-if="resultMap.latestUpdate" class="p_sceneName">{{ item.sceneName }}</p>
                <p v-if="resultMap.latestUpdate" class="p_updateTimeStr">
                  更新时间: {{ formatDateOnly(item.updateTimeStr) }}
                </p>
                <div class="image-empty2" v-if="!resultMap.latestUpdate"></div>
              </div>
            </div>
          </div>
          <div
            class="cursor-box"
            :class="{ no_handle: userType != 1, showOperation: isOperation == 1 || userType == 1 }"
            @click.stop="null"
            @mouseenter.stop="showMoreList(item.id)"
            @mouseleave.stop="hideMoreList">
            <div
              v-if="
                (showMoreListIndex == item.id && userType == 1) ||
                (showMoreListIndex == item.id && isOperation == 1)
              "
              class="hover-card">
              <div
                class="share-btn"
                @click="openShareCode(item)"
                v-if="isOperation == 1 || userType == 1">
                分享
              </div>
              <div @click="requestCancelTemplate(item)" v-if="item.sceneStatus == 3">取消模板</div>
              <div
                @click="openSetTemplate(item)"
                :class="{ isDisabled: item.sceneStatus == 3 }"
                v-if="item.sceneStatus != 3 && isOperation != 1">
                设为模板
              </div>
              <div>移动至</div>
              <div
                class="active-delete"
                @click="deleteDataEvent(item)"
                v-if="item.sceneStatus != 3 && isOperation != 1">
                删除
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <create-scene
    v-if="modalShow"
    :modal-show="modalShow"
    :handle-hide="handleHide"
    :requestCopyWxScene="requestCopyWxScene"
    :isCopyTemp="isCopyTemp"
    :hasRoomScene="hasRoomScene"
    :sceneType="sceneType"
    :hasPlaneScene="hasPlaneScene"
    :platform="createScenePlatform || sceneDataPlatform"></create-scene>
  <QrCode
    v-if="isOpenQR"
    :hide-add-mask="hideQrCode"
    :hoverCurrentTemplate="hoverCurrentTemplate"
    :codeUrl="codeUrl"
    :name="qrName"
    :sceneDataPlatform="sceneDataPlatform"
    :activeTempSceneId="activeTempSceneId"
    :sceneType="sceneType"></QrCode>
  <SetTemplate
    v-if="isOpenSetTemp"
    ref="setTemplateRef"
    :hide-add-mask="hideSetTemp"
    @confirm="confirm"
    @update="update"
    :currentScene="hoverCurrentScene"></SetTemplate>
  <RoomTempTip v-if="dialogRoomTempVisible" :hide-add-mask="hideRoomTemp"></RoomTempTip>
  <QrSceneIdCode
    v-if="isOpenShareCard"
    :hideAddMask="hideQrSceneIdCode"
    :scene="hoverCurrentScene"></QrSceneIdCode>
  <create-space v-if="modalShow2" :handle-hide="handleHide2"></create-space>
  <create-path-navigation v-if="modalShow3" :handle-hide="handleHide3"></create-path-navigation>
</template>

<script lang="ts" setup>
// @ts-nocheck
import '@/assets/media/style.css';
import { onMounted, ref, reactive, watch, markRaw, onUnmounted, nextTick, computed } from 'vue';
import CreateScene from '@/views/project/create/CreateScene2.vue';
import { useRouter } from 'vue-router';
import {
  getSceneMetaPageForWeb,
  queryTemplateScene,
  getOrgnizationStatistic,
  deleteSceneMeta,
  getUserTypeByToken,
} from '@/api/index';
import {
  getSceneTypes,
  getTemplates,
  setSceneAsTemplate,
  updateTemplateInfo,
  getWxAppQrCode,
  copyWxScene,
  cancelTemplate,
  getSceneStorage,
  getShareInfo,
  getOssAccessPath,
} from '@/api';
import { useStore } from 'vuex';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Delete } from '@element-plus/icons-vue';
import QrCode from '@/components/experience/create/QrCode.vue';
import SetTemplate from '@/components/experience/create/SetTemplate.vue';
import RoomTempTip from '@/components/experience/create/RoomTempTip.vue';
import SelfProgress from '@/components/SelfProgress.vue';
import QrSceneIdCode from '@/components/experience/create/QrSceneIdCode.vue';
import { arMenus } from '@/config';
import CreateSpace from '@/views/space/components/CreateSpace.vue';
import CreatePathNavigation from '@/views/path-navigation/components/CreatePathNavigation.vue';
import { formatDateOnly } from '@/utils';

const activeTempSceneId = ref(0);
const sceneDataPlatform = ref(3);
const sceneType = ref(1);
const isMoreDesc = ref(true);
const moreIndex = ref(4);
let isOperation = ref(1);
const domMaskIndexs = ref([]);
const isWorkAuth = ref(true);
const modalShow = ref(false);
const modalShow2 = ref(false);
const modalShow3 = ref(false);
const hasRoomScene = ref(true);
const hasPlaneScene = ref(true);
const router = useRouter();
let sceneList: any = ref([]); // 场景列表
const styleType = ref(1);
let timer: any = null;
const showMoreListIndex = ref(-1);
const pageSize = ref(12);
const store = useStore();
const pageNo = ref(1);
const searchForm: any = reactive({
  // 查询对象
  sceneName: '',
});
const userInfo = ref({});
const qrName = ref('');
const userType = store.state.userDto?.packageVersion == 'V2' ? 2 : 1;
const templateList = ref([]);
const isOpenQR = ref(false);
const isOpenSetTemp = ref(false);
const hoverCurrentScene = ref();
const hoverCurrentTemplate = ref();
const setTemplateRef = ref();
let editTemp = {};
const codeUrl = ref('');
const total = ref(0);
let tempCopyScene = {};
const isCopyTemp = ref(false);
const sceneStorage = ref({});
const dialogRoomTempVisible = ref(false);
const isOpenShareCard = ref(false);
const arMenuList = ref(arMenus);
const activeMenuTab = ref(arMenus[0]);
const pageStyle = ref('');
const permissionMap = ref({
  upload: false,
  path: false,
  plan: true,
  singleScene: false,
  image: true,
  body: false,
  face: true,
  hand: false,
  glasses: false,
  mobile: false,
  wechat: false,
});
const homeStorage: any = ref({});
const createScenePlatform = ref('');
const columnLength = ref(4);
const heightCur = ref(0);
const resultMap = ref({ create: false, template: false, latestUpdate: false });

const enterSceneCard = (item) => {
  item.isActive = true;
};

const leaveSceneCard = (item) => {
  item.isActive = false;
};

const _sceneType = computed(() => {
  return (item) => {
    switch (item.sceneType) {
      case 1:
        return '空间AR';
      case 2:
        return '平面AR';
      case 3:
        return '图像AR';
      case 5:
        return '身体AR';
      case 6:
        return '人脸AR';
      case 7:
        return '手势AR';
      case 8:
        return '单场景AR';
    }
  };
});

const itemLeftWechat = computed(() => {
  return (item) => {
    return item.isLeft && item.scenePlatform == 3;
  };
});

const itemTopWechat = computed(() => {
  return (item) => {
    return item.isTop && item.scenePlatform == 3;
  };
});

const itemLeftEye = computed(() => {
  return (item) => {
    return item.isLeft && item.sceneType == 1 && item.scenePlatform != 3;
  };
});

const itemTopEye = computed(() => {
  return (item) => {
    return item.isTop && item.sceneType == 1 && item.scenePlatform != 3;
  };
});

const openShareCode = (item) => {
  if (item.scenePlatform == 3) {
    startLearn(item, 'id', 'scene');
  } else {
    hoverCurrentScene.value = item;
    isOpenShareCard.value = true;
  }
};

const hideQrSceneIdCode = () => {
  isOpenShareCard.value = false;
};

const activeTab = (menu) => {
  for (let i = 0; i < arMenuList.value.length; i++) {
    const item = arMenuList.value[i];
    item.isActive = false;
  }
  activeMenuTab.value = menu;
  requestGetTemplateSquares();
};

const addSceneHome = () => {
  if (sceneList.value.length == sceneStorage.packageSceneNum) {
    return ElMessage({ type: 'warning', message: '项目数量已上限' });
  }
  modalShow.value = true;
};

const requestGetTemplateSquares = () => {
  const params = {
    name: '',
    sceneType: activeMenuTab.value.sceneType,
  };
  queryTemplateScene(params, true).then((res) => {
    if (res.code == '200') {
      templateList.value = res.data;
      resultMap.value.template = true;
    }
  });
};

const addNewProject = (val: number, type: number) => {
  const isV2Package = store.state.userDto?.packageVersion == 'V2';
  const v2Condition =
    isV2Package && homeStorage.value.packageSceneNum <= homeStorage.value.userdSceneNum;
  const nonV2Condition =
    !isV2Package &&
    homeStorage.value.planeArScenePackageNum + homeStorage.value.spaceArScenePackageNum <=
      homeStorage.value.planeArSceneUsedNum + homeStorage.value.spaceArSceneUsedNum;
  if (v2Condition || nonV2Condition) {
    store.state.showTips = '您的当前可创建项目数不足，请确认后再操作1';
    return;
  }
  store.state.addProjectFlag = !store.state.addProjectFlag;
  createScenePlatform.value = val;
  sceneType.value = type;
};

watch(
  () => store.state.addProjectFlag,
  () => {
    addSceneHome();
  }
);

watch(
  () => store.state.userDto,
  (nv) => {
    userInfo.value = nv;
    permissionMap.value.upload =
      ![6, 7, 0].includes(userType) && (store.state.hasSpaceAr || userInfo.packageVersion != 'V2');
    permissionMap.value.path =
      ![6, 7, 0].includes(userType) && (store.state.hasSpaceAr || userInfo.packageVersion != 'V2');
    permissionMap.value.glasses =
      ![6, 7, 0].includes(userType) && (store.state.hasSpaceAr || userInfo.packageVersion != 'V2');
    permissionMap.value.mobile =
      ![6, 7, 0].includes(userType) && (store.state.hasSpaceAr || userInfo.packageVersion != 'V2');
    permissionMap.value.wechat =
      ![6, 7, 0].includes(userType) && (store.state.hasSpaceAr || userInfo.packageVersion != 'V2');
    permissionMap.value.singleScene = !!nv?.wxSingleSceneAr;
    permissionMap.value.body = !!nv?.wxBodyAr;
    permissionMap.value.hand = !!nv?.wxHandAr;
    resultMap.value.create = true;
  },
  { immediate: true, deep: true }
);

watch(
  () => store.state.isOperation,
  (nv) => {
    isOperation.value = nv;
  },
  { immediate: true }
);

const confirm = (params) => {
  return new Promise((resolve) => {
    setSceneAsTemplate(params).then((res) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '操作成功！' });
        isOpenSetTemp.value = false;
        requestGetTemplateSquares();
        hoverCurrentScene.value.sceneStatus = 3;
      }
    });
  });
};

const hideRoomTemp = () => {
  dialogRoomTempVisible.value = false;
};

const update = (params) => {
  return new Promise((resolve) => {
    updateTemplateInfo(params).then((res) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '修改成功！' });
        isOpenSetTemp.value = false;
        requestGetTemplateSquares();
      }
    });
  });
};

const requestCancelTemplate = (item) => {
  const params = {
    sceneId: item.id,
  };
  return new Promise((resolve) => {
    cancelTemplate(params).then((res) => {
      if (res.code == 200) {
        ElMessage({ type: 'success', message: '取消成功！' });
        item.sceneStatus = 1;
        requestGetTemplateSquares();
      }
    });
  });
};

const deleteDataEvent = (data: any) => {
  ElMessageBox.confirm('是否删除此项目？', '确认', {
    type: 'warning',
    icon: markRaw(Delete),
  })
    .then(() => {
      deleteSceneMeta({ sceneId: data.id }).then((res: any) => {
        if (res.code == 200) {
          ElMessage({ type: 'success', message: '删除成功！' });
          getSceneMetaPageForWeb({ pageSize: 100, pageNo: 1 }).then((res: any) => {
            sceneList.value = [...res.data.records];
          });
        }
      });
    })
    .catch(() => {});
};

const openCreateScene = (item) => {
  const userDto = store.state.userDto;
  const packageInfoDto = store.state.packageInfoDto;
  const userBindPackageDto = store.state.userBindPackageDto;
  const storageData = store.state.storageData.home;
  if (userDto.packageVersion == 'V2') {
    if (userBindPackageDto.singleIdentify == 1) {
      // 单点服务
      if (storageData.planeArSceneUsedNum >= userBindPackageDto.sceneNum) {
        store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
        return;
      }
    } else {
      // 空间服务
      if ([2, 3, 6].includes(item.sceneType)) {
        if (storageData.planeArSceneUsedNum >= userBindPackageDto.sceneNum) {
          store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
          return;
        }
      } else {
        store.state.showTips = '创建同款失败，当前账号暂无创建该类型的项目权限';
        return;
      }
    }
  } else {
    if (storageData.planeArSceneUsedNum >= packageInfoDto.planeSceneNum) {
      store.state.showTips = '您的当前可创建项目数不足，请确认后再操作';
      return;
    }
  }
  tempCopyScene = item;
  modalShow.value = true;
  isCopyTemp.value = true;
};

const requestCopyWxScene = (sceneName) => {
  const params = {
    sourceSceneId: tempCopyScene.sceneId,
    sceneName,
  };
  copyWxScene(params).then((res) => {
    if (res.code == 200) {
      ElMessage({ type: 'success', message: '创建成功！' });
      isCopyTemp.value = false;
      router.push('/experience_edit?sceneid=' + res.data + '&sceneType=' + sceneType.value);
    }
  });
};

const editTemplate = (item) => {
  isOpenSetTemp.value = true;
  editTemp = { ...item };
  nextTick(() => {
    setTemplateRef.value.setParmasEdit(JSON.parse(JSON.stringify(editTemp)), true);
  });
};

const getWXCode = async (item, strId) => {
  qrName.value = !activeTempSceneId.value ? item.sceneName : item.templateName;
  const { data } = await getWxAppQrCode({ sceneId: item[strId], path: 'pages/njyjxr/scene' });
  if (data) {
    codeUrl.value = 'data:image/jpeg;base64,' + data;
  }
};

const openSetTemplate = (item) => {
  if (item.sceneStatus == 3) return;
  isOpenSetTemp.value = true;
  hoverCurrentScene.value = item;
  nextTick(() => {
    setTemplateRef.value.setParmasEdit(hoverCurrentScene.value);
  });
};

const hideSetTemp = () => {
  isOpenSetTemp.value = false;
};

const getRequestLocalOsskey = (ossKey) => {
  getOssAccessPath({ key: ossKey }).then((res: any) => {
    hoverCurrentTemplate.value.sceneLocationPicKey = res.data;
  });
};

const designMakeRecursion = (scene_id) => {
  getShareInfo({ sceneId: scene_id }).then(async (res: any) => {
    if (res.code == '200' && res.data) {
      if (res.data.sceneLocationPicKey) {
        getRequestLocalOsskey(res.data.sceneLocationPicKey);
      }
    }
  });
};

const startLearn = (item, strId, cardType = 'template') => {
  sceneType.value = item.sceneType;
  sceneDataPlatform.value = item.scenePlatform;
  if (cardType == 'scene') {
    activeTempSceneId.value = 0;
  }
  isOpenQR.value = true;
  if (item.sceneId) {
    designMakeRecursion(item.sceneId);
  }
  getWXCode(item, strId);
};

const hideQrCode = () => {
  isOpenQR.value = false;
};

const mouseenterTemp = (item, i) => {
  const thresholdRight = item.sceneType == 1 && item.scenePlatform != 3 ? 544 : 280;
  const thresholdTop = item.sceneType == 1 && item.scenePlatform != 3 ? 300 : 375;
  item.isHover = true;
  sceneDataPlatform.value = item.scenePlatform;
  sceneType.value = item.sceneType;
  activeTempSceneId.value = item.sceneId;
  hoverCurrentTemplate.value = item;

  const node = document.getElementById('item' + i);
  const rect = node.getBoundingClientRect();
  const distanceFromTop = rect.top + window.scrollY;
  const distanceFromRight = document.documentElement.clientWidth - rect.right;
  if (distanceFromRight < thresholdRight) {
    item.isLeft = true;
  } else {
    item.isLeft = false;
  }
  if (distanceFromTop < thresholdTop) {
    item.isTop = true;
  } else {
    item.isTop = false;
  }
};

const mouseoverTemp = (item) => {
  item.isHover = false;
};

const routerEdit = (item) => {
  store.dispatch('resetEditData');
  const path =
    item.sceneType == 1
      ? '/scene_edit?sceneid=' + item.id
      : '/experience_edit?sceneid=' + item.id + '&sceneType=' + item.sceneType;
  router.push(path);
};

// 新增方法：跳转到新页面
const goToNewPage = () => {
  // 直接跳转到使用 LeftSideV2 重构组件的空间编辑页面
  router.push('/space_edit_v2?sceneid=1319');
};

const showMoreList = (index: number) => {
  clearTimeout(timer);
  showMoreListIndex.value = index;
};

const hideMoreList = () => {
  clearTimeout(timer);
  timer = setTimeout(() => {
    showMoreListIndex.value = -1;
  }, 500);
};

const handleHide = (renew?: boolean) => {
  modalShow.value = false;
  if (isCopyTemp.value) {
    // 如果是正在 创建模板同款
    isCopyTemp.value = false;
  } else {
    if (renew) {
      // 判断是否需要重新渲染
      startRequestData();
      getSceneStorage().then((res) => {
        sceneStorage.value = { ...res.data };
      });
    }
  }
};

const handleHide2 = () => {
  modalShow2.value = false;
};

const handleHide3 = () => {
  modalShow3.value = false;
};

const requestDatalist = (isHidden) => {
  return new Promise((resolve) => {
    getSceneMetaPageForWeb({ ...searchForm, pageNo: pageNo.value, pageSize: pageSize.value }).then(
      (res: any) => {
        total.value = res.data.total;
        let tempData = isWorkAuth.value
          ? [
              ...res.data.records.map((e: any) => ({
                ...e,
                pointCloudMapId: e.spaceDto ? e.spaceDto.pointCloudMapId : '',
              })),
            ]
          : [...res.data.records];
        if (!isHidden) {
          sceneList.value.push(...tempData);
        } else {
          sceneList.value = tempData;
        }
        resultMap.value.latestUpdate = true;
        resolve();
      }
    );
  });
};

// 获取场景列表
const startRequestData = () => {
  return new Promise(async (resolve) => {
    await requestDatalist();
    nextTick(() => {
      resolve();
    });
    getSceneStorage().then((res) => {
      sceneStorage.value = { ...res.data };
      store.state.storageData.home = JSON.parse(JSON.stringify(sceneStorage.value));
    });
  });
};

onUnmounted(() => {
  store.dispatch('resetEditData');
  window.removeEventListener('resize', projectStyleCount);
});

const projectStyleCount = () => {
  const dom = document.querySelector('.experience-home');
  const itemDom = document.querySelector('.itemDiv');
  moreIndex.value = Math.floor(dom?.clientWidth / itemDom?.clientWidth);
  if (window.innerWidth <= 1024) {
    pageStyle.value = 'wl1';
  } else if (window.innerWidth > 1024 && window.innerWidth <= 1280) {
    pageStyle.value = 'wl2';
  } else if (window.innerWidth > 1280 && window.innerWidth <= 1440) {
    pageStyle.value = 'wl3';
  } else if (window.innerWidth > 1440 && window.innerWidth <= 1680) {
    pageStyle.value = 'wl4';
  } else if (window.innerWidth > 1680 && window.innerWidth <= 1920) {
    pageStyle.value = 'wl5';
  } else if (window.innerWidth > 1920 && window.innerWidth <= 2560) {
    pageStyle.value = 'wl6';
  } else if (window.innerWidth > 2560) {
    pageStyle.value = 'wl7';
  }
  if (pageStyle.value == 'wl1' || pageStyle.value == 'wl2') {
    columnLength.value = 3;
  } else if (pageStyle.value == 'wl3' || pageStyle.value == 'wl4') {
    columnLength.value = 4;
  } else if (pageStyle.value == 'wl5' || pageStyle.value == 'wl6' || pageStyle.value == 'wl7') {
    columnLength.value = 5;
  }
  setTimeout(() => {
    heightCur.value =
      (210 / 306) * document.querySelector('.list-box>div').getBoundingClientRect().width;
  }, 0);
};

watch(
  () => store.state.storageData,
  (newState) => {
    homeStorage.value = newState.home;
  },
  { deep: true }
);

onMounted(async () => {
  requestGetTemplateSquares();
  const identity = localStorage.getItem('identity');
  isWorkAuth.value = identity == 'enterprise';
  styleType.value = store.state.homeListType;
  getUserTypeByToken().then((res: any) => {});
  await startRequestData();
  projectStyleCount();
  activeMenuTab.value.isActive = true;
  window.addEventListener('resize', projectStyleCount);
});

watch(styleType, (newState) => {
  store.state.homeListType = newState;
});
</script>

<style scoped lang="less">
.green {
  background: rgba(21, 142, 100, 0.5) !important;
}

::-webkit-scrollbar {
  width: 8px;
  background-color: transparent;
}

/* 修改滚动条滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: rgba(135, 206, 235, 0.5);
}

/* 修改滚动条滑轨样式 */
::-webkit-scrollbar-track {
  background-color: transparent;
}

.list-style-3 {
  width: calc(33.33% - 12px) !important;
}

.list-style-4 {
  width: calc(25% - 13.5px) !important;
}

.list-style-5 {
  width: calc(20% - 14.4px) !important;
}

.experience-home {
  position: relative;
  text-align: left;

  .style-color-1,
  .style-color-2,
  .style-color-3 {
    width: 55px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    background: rgba(129, 95, 204, 0.5);
    position: absolute;
    bottom: 53px;
    left: 0;
    z-index: 2;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    border-radius: 0px 10px 0px 0px;
  }

  .style-color-2 {
    background: rgba(46, 118, 255, 0.5);
  }

  .style-color-3 {
    background: rgba(6, 198, 97, 0.5);
  }

  .list-title {
    font-weight: bold;
    font-size: 20px;
    color: #1e1e1e;
    line-height: 30px;
    margin-bottom: 24px;
  }

  .image-empty {
    width: 24px;
    height: 24px;
    border-radius: 7px;
    position: relative;
    margin-right: 8px;
    background-color: #f7f7f9;
  }

  .image-empty2 {
    width: 149px;
    height: 41px;
    border-radius: 4px;
    background-color: #f7f7f9;
  }

  .quickly-create {
    width: calc(100% - 34px);
    position: relative;
    top: -15px;
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: flex-start;

    &.empty-title {
      & > div {
        cursor: default;

        & > div {
          background-color: #f7f7f9;
          cursor: default;
        }
      }

      .card-room,
      .card-mobile,
      .card-wechat {
        background-image: none !important;
        background-color: #f7f7f9 !important;

        &:before {
          content: none !important;
        }
      }
    }

    & > div {
      margin-bottom: 8px;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
    }

    & > .card1 {
      position: relative;
      width: calc(16.72% - 7px);
      height: 138px;
      background: rgba(255, 255, 255, 1);
      border-radius: 10px 10px 10px 10px;
      border: 1px solid rgba(0, 0, 0, 0.06);
      box-sizing: border-box;
      padding: 23px 0 0 23px;

      .glasses,
      .mobile,
      .wechat {
        position: relative;
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
      }

      & > div {
        margin-bottom: 5px;
      }

      .glasses {
        background-image: url(~@/assets/images/home/<USER>
        background-size: 100% 100%;
      }

      .mobile {
        background-image: url(~@/assets/images/home/<USER>
        background-size: 100% 100%;
      }

      .wechat {
        background-image: url(~@/assets/images/home/<USER>
        background-size: 100% 100%;
      }

      &:hover .glasses {
        background-image: url(~@/assets/images/home/<USER>
        background-size: 100% 100%;
      }

      &:hover .mobile {
        background-image: url(~@/assets/images/home/<USER>
        background-size: 100% 100%;
      }

      &:hover .wechat {
        background-image: url(~@/assets/images/home/<USER>
        background-size: 100% 100%;
      }

      &::after {
        content: '\672A\5F00\901A';
        position: absolute;
        right: 0;
        top: 0;
        width: 49px;
        height: 18px;
        background-color: rgba(232, 241, 253, 1);
        border-top-right-radius: 9px;
        border-bottom-left-radius: 9px;
        font-weight: 400;
        font-size: 12px;
        color: #2e76ff;
        text-align: center;
        line-height: 18px;
      }

      &.has-permission::after {
        content: none;
      }

      &.card-room:hover {
        border-radius: 10px;
        border: 1px solid rgba(0, 0, 0, 0.06);
        background: linear-gradient(
            239deg,
            rgba(255, 255, 255, 0) 63.34%,
            rgba(178, 203, 255, 0.2) 97.42%
          ),
          linear-gradient(40deg, rgba(255, 255, 255, 0) 40.26%, #d7efff 81.04%), #f7fafd;

        &::after {
          content: none;
        }

        &:before {
          content: '';
          position: absolute;
          right: 0;
          top: -25px;
          width: 62.35%;
          height: 112px;
          background-image: url(~@/assets/images/home/<USER>
          background-size: 100% 100%;
        }
      }

      &.card-mobile:hover {
        border-radius: 10px;
        border: 1px solid rgba(0, 0, 0, 0.06);
        background: linear-gradient(
            239deg,
            rgba(255, 255, 255, 0) 63.34%,
            rgba(222, 178, 255, 0.2) 97.42%
          ),
          linear-gradient(40deg, rgba(255, 255, 255, 0) 40.26%, #edd7ff 81.04%), #fff;

        &::after {
          content: none;
        }

        &:before {
          content: '';
          position: absolute;
          right: 0;
          top: -25px;
          width: 62.35%;
          height: 112px;
          background-image: url(~@/assets/images/home/<USER>
          background-size: 100% 100%;
        }
      }

      &.card-wechat:hover {
        border-radius: 10px;
        border: 1px solid rgba(0, 0, 0, 0.06);
        background: linear-gradient(
            239deg,
            rgba(255, 255, 255, 0) 63.34%,
            rgba(178, 255, 183, 0.2) 97.42%
          ),
          linear-gradient(40deg, rgba(255, 255, 255, 0) 40.26%, #deffd7 81.04%), #fff;

        &::after {
          content: none;
        }

        &:before {
          content: '';
          position: absolute;
          right: 0;
          top: -25px;
          width: 62.35%;
          height: 112px;
          background-image: url(~@/assets/images/home/<USER>
          background-size: 100% 100%;
        }
      }
    }

    & > .card2 {
      position: relative;
      width: calc(12.47% - 7px);
      height: 138px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      & > div {
        position: relative;
        width: 100%;
        height: 65px;
        background: rgba(255, 255, 255, 1);
        border-radius: 10px 10px 10px 10px;
        border: 1px solid rgba(0, 0, 0, 0.06);
        box-sizing: border-box;
        padding-left: 23px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        img {
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }

        &::after {
          content: '\672A\5F00\901A';
          position: absolute;
          right: 0;
          top: 0;
          width: 49px;
          height: 18px;
          background-color: rgba(232, 241, 253, 1);
          border-top-right-radius: 9px;
          border-bottom-left-radius: 9px;
          font-weight: 400;
          font-size: 12px;
          color: #2e76ff;
          text-align: center;
          line-height: 18px;
        }

        &.has-permission::after {
          content: none;
        }
      }
    }

    &.wl1 {
      .card1 {
        width: calc(33.4% - 5px);
      }

      .card2 {
        width: calc(24.85% - 5px);
      }
    }

    &.wl2,
    &.wl3 {
      .card1,
      .card2 {
        width: calc(25.1% - 6px);
      }

      .card3 {
        width: calc(33.5% - 6px);
      }
    }

    &.wl4 {
      .card1 {
        width: calc(13.75% - 7px);
      }

      .card2 {
        width: calc(14.83% - 7px);
      }
    }

    &.wl5 {
      .card1 {
        width: calc(16.7% - 7px);
      }

      .card2 {
        width: calc(12.51% - 7px);
      }
    }

    &.wl6 {
      .card1 {
        width: calc(16.72% - 7px);
      }

      .card2 {
        width: calc(12.47% - 7px);
      }
    }
  }

  .box_content,
  .scene-card {
    margin: 24px 0 40px;

    .list-box {
      width: calc(100% - 30px);
      height: 255px;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      & > div {
        position: relative;
        width: calc(25% - 19px);
        height: 100%;
        cursor: pointer;
        border-radius: 10px;
        margin-right: 18px;
        // overflow: hidden;

        img {
          width: 100%;
          height: 210px;
          -o-object-fit: cover;
          object-fit: cover;
          vertical-align: middle;
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 10px;
        }

        &:hover {
          .hover-mask {
            display: block;
          }

          .cursor-box {
            display: block;
          }
        }

        .hover-mask {
          position: relative;
          width: 100%;
          height: 210px;
          background: rgba(0, 0, 0, 0.6);
          z-index: 9;
          position: absolute;
          top: 0;
          left: 0;
          box-sizing: border-box;
          border-radius: 10px;
          padding-top: 65px;
          padding-left: calc(50% - 47px);
          display: none;

          & > div {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            position: absolute;
            left: 50%;
            top: 55%;
            transform: translateX(-50%) translateY(-50%);
            z-index: 2;

            & > div {
              margin-bottom: 12px;
            }
          }

          &::before {
            content: '';
            width: 100%;
            height: calc(100% + 2px);
            position: absolute;
            left: 0;
            top: 0;
            z-index: 1;
            box-sizing: border-box;
            border: 2px solid #2e76ff;
            border-radius: 10px;
          }

          &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: calc(100% + 2px);
            z-index: 0;
            box-sizing: border-box;
            border: 4px solid #fff;
            border-radius: 10px;
          }

          .learn-btn {
            position: relative;
            box-sizing: border-box;
            font-weight: bold;
            font-size: 14px;
            color: #ffffff;
            line-height: 32px;
            text-align: center;
            cursor: pointer;
            width: 93px;
            height: 34px;
            background: #2e76ff;
            border-radius: 4px 4px 4px 4px;
            z-index: 2;

            &:hover {
              background: #1251cb;
            }
          }

          .desc_temp {
            width: 152px;
            font-weight: bold;
            font-size: 12px;
            color: #ffffff;
            margin-top: 16px;
            overflow: hidden;
          }

          .create-btn {
            position: relative;
            font-weight: bold;
            line-height: 32px;
            text-align: center;
            cursor: pointer;
            width: 92px;
            height: 32px;
            background: #669aff;
            border-radius: 4px 4px 4px 4px;
            font-weight: 500;
            font-size: 14px;
            color: #fff;
            z-index: 2;

            &:hover {
              color: #fff;
              background: #2e76ff;
            }
          }
        }

        .list-bottom {
          width: 100%;
          height: 45px;
          position: absolute;
          left: 0;
          bottom: 0;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-left: 10px;
          box-sizing: border-box;
          padding-bottom: 4px;
          font-weight: bold;
          font-size: 14px;
          color: #1e1e1e;

          img {
            width: 24px;
            height: 24px;
            border-radius: 7px;
            position: relative;
            margin-right: 8px;
          }

          .empty-style {
            width: 149px;
            height: 20px;
            background-color: #f7f7f9;
            border-radius: 4px;
          }
        }
      }

      & > div:last-child {
        margin-right: 0;
      }
    }
  }

  .empty-data {
    .list-box > div {
      cursor: default;
    }

    &.scene-card .list-box > div {
      cursor: default;
      border: 1px solid #dadada !important;
    }

    .hover-mask {
      display: none !important;
    }

    .cursor-box {
      display: none !important;
    }
  }

  .scene-card {
    .list-box div .hover-mask {
      z-index: 1;

      &::after {
        content: none;
      }

      &::before {
        z-index: 1;
      }
    }
    .con_father {
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 2;

      .con_sheet {
        position: relative;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0 16px;
        height: 55px;
        z-index: -1;
        font-weight: bold;
        font-size: 14px;
        color: #1e1e1e;
        // background: #fff;

        .con_sheet_img {
          position: relative;
          width: 24px;
          height: 24px;
          margin-right: 16px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .cursor-box {
      position: relative;
      position: absolute;
      right: 12px;
      bottom: 14px;
      width: 16px;
      height: 16px;
      background-image: url(~@/assets/images/hengdian.png);
      background-size: 100% 100%;
      cursor: pointer;
      z-index: 2;
      border: 6px solid #fff;
      background-color: #fff;
      border-radius: 6px;
      display: none;

      &:hover {
        border-color: #eaeaea;
        background-color: #eaeaea;
      }

      & > div {
        position: relative;
        position: absolute;
        left: -42px;
        bottom: 36px;
        width: 100px;
        background: #ffffff;
        box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
        text-align: center;
        font-weight: 400;
        font-size: 12px;
        color: #1e1e1e;
        padding: 8px 0 8px;
        border-radius: 8px;

        &::before {
          content: '';
          width: 36px;
          height: 26px;
          position: absolute;
          left: 50%;
          bottom: -18px;
          margin-left: -18px;
          background: url(~@/assets/images/experience-icon/triangle-shadow.png);
          background-size: 100% 100%;
          transform: rotate(180deg);
        }

        &::after {
          content: '';
          width: 20px;
          height: 9px;
          background-color: #fff;
          position: absolute;
          left: 50%;
          bottom: -1px;
          margin-left: -10px;
        }

        & > div {
          line-height: 26px;
          height: 26px;
        }

        & > div:hover {
          background: #e8f1fd;
        }

        & > div.active {
          color: #3671fe;
        }
      }
    }

    .list-box {
      height: 265px;

      & > div {
        border: 1px solid #dadada;
        box-sizing: border-box;

        &:hover .hover-mask {
          background-color: transparent;
        }

        img {
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
          border-bottom-right-radius: 0;
          border-bottom-left-radius: 0;
        }
      }

      & > div:hover {
        border-color: #2e76ff;
      }
    }

    .list-box:hover .hover-mask::before {
      height: calc(100% - 2px);
      z-index: 3;
      border-radius: 8px;
      border-width: 1px;
    }

    .list-box:hover .hover-mask::after {
      content: none;
    }
  }

  .p_sceneName {
    font-weight: bold;
    font-size: 14px;
    color: #1e1e1e;
  }

  .p_updateTimeStr {
    font-weight: 400;
    font-size: 12px;
    color: #797979;
  }

  .no_handle {
    background: none !important;
    cursor: default !important;
  }

  .green {
    background: rgba(21, 142, 100, 0.5) !important;
  }

  .isDisabled {
    cursor: no-drop;
  }

  * {
    padding: 0;
    margin: 0;
  }

  .box__ {
    display: flex;
    align-items: center;
    position: relative;

    .head {
      font-weight: bold;
      font-size: 20px;
      color: #1e1e1e;
      margin-right: 24px;
    }

    .menu_box {
      display: flex;
      align-items: center;

      .menu_list {
        margin-right: 12px;
        font-weight: 400;
        font-size: 14px;
        color: #737373;
        padding: 4px 12px;
        background: transparent;
        border-radius: 4px;
        cursor: pointer;
        min-width: 55px;
        min-height: 20px;
        box-sizing: border-box;

        &:hover {
          background: rgba(36, 118, 253, 0.1);
        }
      }

      .active_menu_list {
        background: rgba(36, 118, 253, 0.1);
        font-size: 14px;
        color: #2e76ff;
      }
    }
  }

  .el-form--inline .el-form-item {
    margin: 0;
  }
}

.experience-home .empty-text {
  font-size: 0px !important;
  width: 83px;
  height: 24px;
  border-radius: 4px;
  background-color: #f7f7f9;
}

/* 新页面入口卡片样式 */
.new-page-entry {
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  .style-color-new {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .new-page-hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 1;
    }

    .new-page-content {
      text-align: center;
      color: white;

      .new-page-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .new-page-desc {
        font-size: 13px;
        opacity: 0.9;
      }
    }
  }

  .con_sheet_img img {
    filter: hue-rotate(240deg) saturate(1.5);
  }

  .p_sceneName {
    color: #667eea !important;
    font-weight: 600 !important;
  }

  .p_updateTimeStr {
    color: #764ba2 !important;
  }
}
</style>
