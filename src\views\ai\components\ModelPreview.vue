<template>
  <div v-if="show" class="preview-overlay" @click="handleClose">
    <div class="preview-content" @click.stop>
      <img
        src="@/assets/images/close-tips.png"
        @click="handleClose"
        alt="关闭"
        class="close-icon" />
      <div class="model-container">
        <div id="model-preview" class="preview-model"></div>
        <div v-if="showLoading" class="loading-container">
          <template v-if="!loadError">
            <div class="loading-text">{{ loadingText }}</div>
            <div class="loading-progress">{{ loadingProgress }}%</div>
          </template>
          <template v-else>
            <div class="error-text">{{ errorMessage }}</div>
          </template>
        </div>
      </div>
      <div class="preview-actions">
        <button class="preview-btn save-btn" @click="handleSave">保存素材</button>
        <button class="preview-btn delete-btn" @click="handleDelete">删除</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch, nextTick } from 'vue';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import {
  Scene,
  WebGLRenderer,
  OrthographicCamera,
  AmbientLight,
  Vector3,
  Box3,
  MOUSE,
  TOUCH,
  Color,
  DirectionalLight,
  PCFSoftShadowMap,
  Mesh,
} from 'three';
import { initSize } from '@/utils';

interface Props {
  show: boolean;
  modelUrl: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'save'): void;
  (e: 'delete'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const showLoading = ref(true);
const loadingText = ref('正在加载模型...');
const loadingProgress = ref(0);
const loadError = ref(false);
const errorMessage = ref('');

const resetLoadingState = () => {
  showLoading.value = true;
  loadingText.value = '正在加载模型...';
  loadingProgress.value = 0;
  loadError.value = false;
  errorMessage.value = '';
};

const handleError = (error: any) => {
  console.error('模型加载错误:', error);
  loadError.value = true;
  errorMessage.value = '模型加载失败，请重试';
  loadingText.value = '加载失败';
  loadingProgress.value = 0;
  showLoading.value = true;
};

// 处理模型URL，确保它能被GLTFLoader正确加载
const processModelUrl = (url: string): Promise<string> => {
  console.log('原始模型URL:', url);

  // 如果URL已经包含.glb扩展名，则直接返回
  if (url.toLowerCase().endsWith('.glb')) {
    return Promise.resolve(url);
  }

  // 对于没有扩展名的URL，我们需要使用Fetch API获取文件并创建Blob URL
  return new Promise<string>((resolve, reject) => {
    console.log('开始获取模型文件...');
    fetch(url, {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache',
      },
      mode: 'cors',
      credentials: 'same-origin',
    })
      .then((response) => {
        console.log('获取到响应:', response);
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.blob();
      })
      .then((blob) => {
        console.log('获取到Blob:', blob);
        console.log('Blob类型:', blob.type);
        console.log('Blob大小:', blob.size);

        // 创建一个带有正确MIME类型的Blob
        const glbBlob = new Blob([blob], { type: 'model/gltf-binary' });
        const blobUrl = URL.createObjectURL(glbBlob);
        console.log('创建的Blob URL:', blobUrl);
        resolve(blobUrl);
      })
      .catch((error) => {
        console.error('获取模型文件失败:', error);
        reject(error);
      });
  });
};

let scene: Scene;
let camera: OrthographicCamera;
let renderer: WebGLRenderer;
let animationFrameId: number;

const initThree = async () => {
  console.log('=== 开始初始化Three.js ===');
  const container = document.getElementById('model-preview');
  if (!container) {
    console.error('找不到预览容器元素');
    return;
  }
  console.log('容器元素尺寸:', container.clientWidth, container.clientHeight);

  // 获取容器尺寸
  const width = container.clientWidth;
  const height = container.clientHeight;
  const k = width / height;
  const s = 200; // 增大视野范围

  // 创建场景
  scene = new Scene();
  scene.background = new Color(0x000000); // 设置黑色背景
  console.log('场景创建完成');

  // 创建相机
  camera = new OrthographicCamera(-s * k, s * k, s, -s, 1, 1000);
  camera.position.set(200, 200, 200); // 调整相机位置
  camera.lookAt(new Vector3(0, 0, 0));
  console.log('相机创建完成，位置:', camera.position);

  // 创建渲染器
  renderer = new WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  renderer.setClearColor(0x000000, 1); // 设置渲染器背景为黑色
  renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比
  renderer.shadowMap.enabled = true; // 启用阴影
  renderer.shadowMap.type = PCFSoftShadowMap; // 使用柔和阴影
  container.appendChild(renderer.domElement);
  console.log('渲染器创建完成');

  // 添加控制器
  const controls = new OrbitControls(camera, renderer.domElement);
  controls.mouseButtons = {
    LEFT: MOUSE.ROTATE,
    MIDDLE: MOUSE.DOLLY,
    RIGHT: MOUSE.PAN,
  };
  controls.touches = {
    ONE: TOUCH.ROTATE,
    TWO: TOUCH.DOLLY_PAN,
  };
  controls.enableDamping = true; // 启用阻尼效果
  controls.dampingFactor = 0.05; // 设置阻尼系数
  controls.screenSpacePanning = true; // 启用屏幕空间平移
  console.log('控制器创建完成');

  // 添加光源
  // 环境光 - 使用更自然的白光
  const ambient = new AmbientLight(0xffffff, 0.8);
  scene.add(ambient);
  console.log('环境光创建完成');

  // 主平行光 - 使用纯白光
  const mainLight = new DirectionalLight(0xffffff, 0.6);
  mainLight.position.set(10, 15, 10);
  mainLight.castShadow = true;
  mainLight.shadow.mapSize.width = 2048;
  mainLight.shadow.mapSize.height = 2048;
  mainLight.shadow.camera.near = 0.5;
  mainLight.shadow.camera.far = 500;
  scene.add(mainLight);
  console.log('主平行光创建完成');

  // 补充平行光 - 使用柔和的白光
  const fillLight = new DirectionalLight(0xffffff, 0.4);
  fillLight.position.set(-8, 8, -8);
  scene.add(fillLight);
  console.log('补充平行光创建完成');

  // 添加顶部平行光 - 使用柔和的白光
  const topLight = new DirectionalLight(0xffffff, 0.3);
  topLight.position.set(0, 20, 0);
  scene.add(topLight);
  console.log('顶部平行光创建完成');

  // 添加底部平行光 - 使用柔和的白光
  const bottomLight = new DirectionalLight(0xffffff, 0.2);
  bottomLight.position.set(0, -15, 0);
  scene.add(bottomLight);
  console.log('底部平行光创建完成');

  try {
    // 尝试两种方法加载模型
    console.log('=== 开始加载模型 ===');
    await loadModel();
    console.log('=== 模型加载完成 ===');
  } catch (error) {
    console.error('加载模型失败:', error);
    showLoading.value = false;
  }

  // 动画循环
  const animate = () => {
    animationFrameId = requestAnimationFrame(animate);
    controls.update(); // 更新控制器
    renderer.render(scene, camera);
  };
  animate();
  console.log('动画循环启动');

  // 添加窗口大小变化监听
  const handleResize = () => {
    const newWidth = container.clientWidth;
    const newHeight = container.clientHeight;
    const newK = newWidth / newHeight;

    camera.left = -s * newK;
    camera.right = s * newK;
    camera.top = s;
    camera.bottom = -s;
    camera.updateProjectionMatrix();

    renderer.setSize(newWidth, newHeight);
  };
  window.addEventListener('resize', handleResize);

  // 在组件卸载时移除监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });
};

// 修改loadModel函数中的模型处理部分
const loadModel = async () => {
  // 创建 DRACOLoader
  const dracoLoader = new DRACOLoader();
  dracoLoader.setDecoderPath('https://www.gstatic.com/draco/v1/decoders/');
  console.log('DRACOLoader 创建完成');

  // 创建 GLTFLoader 并设置 DRACOLoader
  const loader = new GLTFLoader();
  loader.setDRACOLoader(dracoLoader);
  console.log('GLTFLoader 创建完成');

  console.log('开始加载模型，URL:', props.modelUrl);
  loadingText.value = '正在加载模型...';
  loadingProgress.value = 0;

  return new Promise((resolve, reject) => {
    // 尝试直接加载原始URL
    loader.load(
      props.modelUrl,
      (gltf) => {
        console.log('模型加载成功，GLTF数据:', gltf);
        loadingText.value = '正在处理模型...';
        loadingProgress.value = 90;
        const model = gltf.scene;

        // 计算模型包围盒
        const box = new Box3().setFromObject(model);
        const center = box.getCenter(new Vector3());
        const size = box.getSize(new Vector3());

        // 计算合适的缩放比例
        const maxDim = Math.max(size.x, size.y, size.z);
        const scale = 150 / maxDim; // 根据视野范围调整
        model.scale.multiplyScalar(scale);

        // 将模型移动到容器中心
        const container = document.getElementById('model-preview');
        if (container) {
          // 获取容器的中心点
          const containerRect = container.getBoundingClientRect();
          const containerCenterX = containerRect.width / 2;
          const containerCenterY = containerRect.height / 2;

          // 调整模型位置到容器中心
          model.position.set(
            -center.x * scale, // 抵消模型中心偏移
            -center.y * scale, // 抵消模型中心偏移
            -center.z * scale // 抵消模型中心偏移
          );
        }

        // 启用阴影
        model.traverse((node) => {
          if (node instanceof Mesh) {
            node.castShadow = true;
            node.receiveShadow = true;
          }
        });

        scene.add(model);
        console.log('模型已添加到场景');

        loadingProgress.value = 100;
        loadingText.value = '加载完成';
        setTimeout(() => {
          showLoading.value = false;
        }, 500);
        resolve(gltf);
      },
      (progress) => {
        // 添加加载进度处理
        if (progress.total > 0) {
          // 将加载进度限制在0-80%之间，留出处理模型的进度空间
          const percent = Math.round((progress.loaded / progress.total) * 80);
          loadingProgress.value = percent;
          console.log(`模型加载进度: ${percent}% (${progress.loaded}/${progress.total} 字节)`);
        } else {
          console.log(`模型加载进度: 已加载 ${progress.loaded} 字节`);
        }
      },
      (error) => {
        console.error('直接加载模型失败，详细错误:', error);
        console.log('尝试使用Blob方式加载');
        loadingText.value = '正在尝试其他加载方式...';
        loadingProgress.value = 0;

        // 如果直接加载失败，尝试使用Blob方式
        processModelUrl(props.modelUrl)
          .then((blobUrl: string) => {
            console.log('Blob URL 创建成功:', blobUrl);
            loadingText.value = '正在加载模型(Blob方式)...';
            loader.load(
              blobUrl,
              (gltf) => {
                console.log('通过Blob加载模型成功:', gltf);
                loadingText.value = '正在处理模型...';
                loadingProgress.value = 90;
                const model = gltf.scene;
                console.log('Blob模型场景:', {
                  children: model.children.length,
                  animations: gltf.animations?.length || 0,
                  materials: Object.keys(model.userData || {}).join(', '),
                });

                scene.add(model);
                console.log('Blob模型已添加到场景');

                // 调整模型大小和位置
                const boxInfo = new Box3().expandByObject(model);
                console.log('Blob模型边界框:', {
                  min: boxInfo.min,
                  max: boxInfo.max,
                  size: boxInfo.getSize(new Vector3()),
                });

                initSize(boxInfo, model);
                let initScale = model.userData.initScale;
                console.log('Blob模型初始缩放比例:', initScale);

                let scale = new Vector3(1, 1, 1);
                if (initScale > 30) {
                  initScale = 10;
                  scale = model.scale.clone().multiplyScalar(initScale);
                } else {
                  scale = model.scale.clone().multiplyScalar(initScale * 10);
                }
                console.log('Blob模型最终缩放比例:', {
                  x: scale.x,
                  y: scale.y,
                  z: scale.z,
                });

                model.position.set(0, -5, 0);
                model.scale.set(scale.x, scale.y, scale.z);
                console.log('Blob模型位置和缩放已设置');

                loadingProgress.value = 100;
                loadingText.value = '加载完成';
                // 延迟关闭加载状态，让用户看到100%的进度
                setTimeout(() => {
                  showLoading.value = false;
                  console.log('Blob加载状态已关闭');
                }, 500);
                resolve(gltf);
              },
              (progress) => {
                if (progress.total > 0) {
                  // 将加载进度限制在0-80%之间，留出处理模型的进度空间
                  const percent = Math.round((progress.loaded / progress.total) * 80);
                  loadingProgress.value = percent;
                  console.log(
                    `Blob模型加载进度: ${percent}% (${progress.loaded}/${progress.total} 字节)`
                  );
                }
              },
              (error) => {
                console.error('通过Blob加载模型也失败，详细错误:', error);
                loadingText.value = '加载失败，请重试';
                loadingProgress.value = 0;
                showLoading.value = false;
                reject(error);
              }
            );
          })
          .catch((error: any) => {
            console.error('处理模型URL失败，详细错误:', error);
            loadingText.value = '加载失败，请重试';
            loadingProgress.value = 0;
            showLoading.value = false;
            reject(error);
          });
      }
    );
  });
};

onMounted(() => {
  console.log('=== ModelPreview组件挂载 ===');
  console.log('show:', props.show);
  console.log('modelUrl:', props.modelUrl);
});

// 监听show和modelUrl的变化
watch(
  () => [props.show, props.modelUrl],
  ([newShow, newModelUrl]) => {
    console.log('=== 属性变化 ===');
    console.log('show:', newShow);
    console.log('modelUrl:', newModelUrl);

    if (newShow && newModelUrl) {
      console.log('show和modelUrl都已准备好，开始初始化Three.js');
      // 确保DOM已更新
      nextTick(() => {
        initThree();
      });
    } else {
      console.log('show或modelUrl未准备好，不初始化Three.js');
    }
  },
  { immediate: true }
);

onUnmounted(() => {
  console.log('=== ModelPreview组件卸载 ===');
  if (animationFrameId) {
    console.log('取消动画帧');
    cancelAnimationFrame(animationFrameId);
  }
  if (renderer) {
    console.log('销毁渲染器');
    renderer.dispose();
  }
});

const handleClose = () => {
  emit('close');
};

const handleSave = () => emit('save');
const handleDelete = () => emit('delete');
</script>

<style scoped lang="less">
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.preview-content {
  position: relative;
}

.close-icon {
  position: absolute;
  top: 16px;
  right: -60px;
  width: 36px;
  height: 36px;
  cursor: pointer;
  z-index: 1001;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.7;
  }
}

.model-container {
  width: 800px;
  height: 600px;
  background: #000000;
  border-radius: 10px;
  position: relative;
}

.preview-model {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
  background: #000000;
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  padding: 16px 24px;
  border-radius: 4px;
  text-align: center;

  .loading-text {
    color: #fff;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .loading-progress {
    color: #fff;
    font-size: 14px;
  }

  .error-text {
    color: #ff4d4f;
    font-size: 14px;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.preview-actions {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: center;
  margin-top: 16px;

  .preview-btn {
    width: 120px;
    height: 40px;
    border-radius: 4px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &.save-btn {
      background: #2e76ff;
      color: #ffffff;
    }

    &.delete-btn {
      background: #c9453e;
      color: #ffffff;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
