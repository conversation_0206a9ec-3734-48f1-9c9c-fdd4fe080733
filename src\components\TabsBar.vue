<!-- TabsBar.vue -->
<template>
  <div class="tabs-bar" :style="{ marginBottom: `${bottomMargin}px` }">
    <div
      class="tabs-slider"
      :style="{
        left: activeTab === tabs[0].value ? '4px' : 'calc(50% + 4px)',
        width: 'calc(50% - 8px)',
      }"
    ></div>
    <div
      v-for="tab in tabs"
      :key="tab.value"
      :class="['tab-btn', { active: activeTab === tab.value }]"
      @click="handleTabClick(tab.value)"
    >
      {{ tab.label }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

interface Tab {
  label: string;
  value: string;
}

interface Props {
  tabs: Tab[];
  modelValue: string;
  bottomMargin?: number;
}

const props = withDefaults(defineProps<Props>(), {
  bottomMargin: 24,
});
const emit = defineEmits(["update:modelValue"]);

const activeTab = computed({
  get: () => props.modelValue,
  set: (value: string) => emit("update:modelValue", value),
});

const handleTabClick = (value: string) => {
  activeTab.value = value;
};
</script>

<style lang="less" scoped>
.tabs-bar {
  width: 100%;
  height: 40px;
  background: #f5f5f5;
  border-radius: 4px;
  display: flex;
  position: relative;

  .tabs-slider {
    position: absolute;
    top: 4px;
    height: calc(100% - 8px);
    background: #ffffff;
    border-radius: 4px;
    transition: all 0.3s;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  }

  .tab-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #86909c;
    cursor: pointer;
    z-index: 1;
    transition: all 0.3s;

    &.active {
      color: #1e1e1e;
      font-weight: 500;
    }
  }
}
</style>
