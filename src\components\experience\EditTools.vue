<template>
  <div class="canvas-tools-top" v-if="toolsList.length">
    <div v-for="(item, index) in toolsList" :key="index" :class="operateType == item.name ? 'active' : ''"
      @click="operateEvent(item.name)">
      <img :src="operateType == item.name ? item.url_a : item.url" alt="">
      <div>{{ item.name }}</div>
    </div>
    <div @click="operateEvent('全屏')">
      <img v-if="!has_full" src="@/assets/images/experience-icon/full-screen-icon.png" alt="">
      <img v-if="has_full" src="@/assets/images/experience-icon/small-screen-icon.png" alt="">
      <div>{{ !has_full ? '全屏' : '取消' }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import { useStore } from 'vuex'

const props = defineProps({
  changeOperateType: {
    default: null,
    type: Function
  }
})
const toolsList = ref([
  {
    name: '移动',
    url: require('@/assets/images/experience-icon/move-icon.png'),
    url_a: require('@/assets/images/experience-icon/move-iconA.png'),
  },
  {
    name: '旋转',
    url: require('@/assets/images/experience-icon/rotate-icon.png'),
    url_a: require('@/assets/images/experience-icon/rotate-iconA.png')
  },
  {
    name: '缩放',
    url: require('@/assets/images/experience-icon/scale-icon.png'),
    url_a: require('@/assets/images/experience-icon/scale-iconA.png')
  }
])

const operateType = ref('')
const has_full = ref(false)

const store = useStore()

const operateEvent = (name: string) => {
  if (name == '全屏') {
    let element = document.documentElement;     // 返回 html dom 中的root 节点 <html>
    // 判断浏览器设备类型
    if (!has_full.value) {
      element.requestFullscreen && element.requestFullscreen();
    } else {
      document.exitFullscreen && document.exitFullscreen();
    }
    return
  }

  operateType.value = name
  props.changeOperateType(name)
}

onMounted(() => {
  document.addEventListener('fullscreenchange', (e: any) => {
    if (document.fullscreenElement) {
      has_full.value = true
    } else {
      has_full.value = false
    }
  })
})

watch(store.state, (newState) => {

  if (newState.operateType != operateType.value) {
    operateType.value = newState.operateType
  }
})

defineExpose({
  operateEvent
})

</script>
<style scoped lang="less">
.canvas-tools-top {
  height: 40px;
  background: #F3F4F5;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
  border-radius: 6px;
  position: absolute;
  top: 72px;
  left: 323px;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #9B9B9B;

  &>div {
    width: 72px;
    height: 100%;
    padding: 0 10px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    img {
      width: 18px;
      height: 18px;
    }
  }

  .active {
    color: #3671FE;
    background: #FFFFFF;
  }
}

@media screen and (max-width: 1200px) {
  .canvas-tools-top {
    left: 273px;
  }
}
</style>