<template>
  <el-dialog v-model="show" title="选择音色" width="900px" :close-on-click-modal="false" center>
    <div class="voice-grid">
      <div
        v-for="(voice, idx) in voices"
        :key="idx"
        class="voice-card"
        :class="{ active: selectedIdx === idx }"
        @click="selectVoice(idx)"
      >
        <div class="voice-header">
          <div class="voice-info">
            <div class="voice-title">{{ voice.name }}</div>
            <div class="voice-btns">
              <div class="voice-btn" :class="{ active: voice.age === 'youth' }">青年</div>
              <div class="voice-btn" :class="{ active: voice.gender === 'female' }">女声</div>
            </div>
          </div>
          <div class="voice-actions">
            <img
              v-if="selectedIdx !== idx"
              src="@/assets/images/weibofang.png"
              class="action-icon"
              alt="未播放"
            />
            <el-tooltip content="试听" placement="top" v-else-if="playingIdx !== idx">
              <img
                src="@/assets/images/bofang.png"
                class="action-icon"
                alt="播放"
                @click.stop="tryVoice(idx)"
              />
            </el-tooltip>
            <el-tooltip content="暂停" placement="top" v-else>
              <img
                src="@/assets/images/bofangzhong.png"
                class="action-icon"
                alt="播放中"
                @click.stop="stopVoice(idx)"
              />
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer-right">
        <el-button class="cancel-btn" @click="show = false">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="confirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, onUnmounted } from 'vue';
import { Voice } from '../config/voices';

const props = defineProps({
  show: Boolean,
  voices: Array as () => Voice[],
  selectedIdx: Number,
});
const emit = defineEmits(['update:show', 'confirm', 'try', 'stop', 'select']);

const show = ref(props.show);
const selectedIdx = ref(props.selectedIdx ?? 0);
const playingIdx = ref<number | null>(null);

// 音频播放相关
const audioRef = ref<HTMLAudioElement | null>(null);
const currentAudioUrl = ref<string>('');

const selectVoice = (idx: number) => {
  // 如果点击的是不同的卡片，停止当前播放的音频
  if (selectedIdx.value !== idx && playingIdx.value !== null) {
    if (audioRef.value) {
      audioRef.value.pause();
      audioRef.value = null;
    }
    playingIdx.value = null;
    currentAudioUrl.value = '';
  }

  selectedIdx.value = idx;
  emit('select', idx);
};

const tryVoice = async (idx: number) => {
  // 如果当前有音频在播放，先停止
  if (audioRef.value) {
    audioRef.value.pause();
    audioRef.value = null;
  }

  // 停止之前播放的音频
  if (playingIdx.value !== null) {
    playingIdx.value = null;
  }

  try {
    // 这里应该调用实际的音频生成API获取音频URL
    // 暂时使用模拟的音频URL
    const mockAudioUrl = `https://m701.music.126.net/20250708120833/54bf4a3c7b8b595a6fb96685de4ae4dc/jdymusic/obj/w5zDlMODwrDDiGjCn8Ky/1993849192/b96d/5608/940b/8cdc6dc4ee4bc09e81a5afe9ee4ce35a.mp3?vuutv=BwtLQyXnwQF3xg07FFKLsnLCUqWGgR1Ei8kNPAnH+XeNlrJ1usOgdLj/mrM86RFRBwKXflXFvCOLel200YTgQSWPbvkTr5AHyQFcq/qb5QVculJbL7usBl2ncxSAbkep8fwITYnqAjaQkFtzE13d6A==`;

    // 创建新的Audio对象
    audioRef.value = new Audio(mockAudioUrl);

    // 设置音频事件监听
    audioRef.value.addEventListener('play', () => {
      playingIdx.value = idx;
      console.log(`开始播放音频 ${idx}`);
    });

    audioRef.value.addEventListener('ended', () => {
      playingIdx.value = null;
      audioRef.value = null;
      console.log(`音频 ${idx} 播放结束`);
    });

    audioRef.value.addEventListener('error', (error) => {
      console.error(`音频 ${idx} 播放失败:`, error);
      playingIdx.value = null;
      audioRef.value = null;
    });

    // 开始播放
    await audioRef.value.play();
    currentAudioUrl.value = mockAudioUrl;

    emit('try', idx);
  } catch (error) {
    console.error('音频播放失败:', error);
    playingIdx.value = null;
    audioRef.value = null;
  }
};

const stopVoice = (idx: number) => {
  if (audioRef.value) {
    audioRef.value.pause();
    audioRef.value = null;
  }
  playingIdx.value = null;
  currentAudioUrl.value = '';
  emit('stop', idx);
};

const confirm = () => {
  // 确认时停止当前播放的音频
  if (audioRef.value) {
    audioRef.value.pause();
    audioRef.value = null;
  }
  playingIdx.value = null;
  emit('confirm', selectedIdx.value);
};

// 组件卸载时清理音频
const cleanup = () => {
  if (audioRef.value) {
    audioRef.value.pause();
    audioRef.value = null;
  }
  playingIdx.value = null;
  currentAudioUrl.value = '';
};

watch(
  () => props.show,
  (v) => {
    show.value = v;
    if (!v) {
      // 弹窗关闭时清理音频
      cleanup();
    }
  }
);
watch(
  () => show.value,
  (v) => emit('update:show', v)
);

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>

<style scoped>
.voice-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 18px;
  margin: 24px 0;
}
.voice-card {
  min-width: 0;
  height: 82px;
  background: #f5f6f7;
  border-radius: 4px;
  padding: 12px 16px;
  cursor: pointer;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-sizing: border-box;
}
.voice-card.active {
  border: 2px solid #2e76ff;
  background: #eaf2ff;
}
.voice-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.voice-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.voice-title {
  font-weight: 500;
  margin-bottom: 8px;
}
.voice-btns {
  display: flex;
  gap: 8px;
  margin-bottom: 0px;
}
.voice-btn {
  width: 40px;
  height: 26px;
  background: rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #222;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.voice-btn.active {
  background: rgba(0, 0, 0, 0.06);
  font-weight: 400;
  color: #000000;
}
.voice-actions {
  margin-top: 8px;
}
.action-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: opacity 0.2s;
}
.action-icon:hover {
  opacity: 0.8;
}
.dialog-footer-right {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  width: 100%;
}
.dialog-footer-right .confirm-btn {
  width: 92px;
  height: 32px;
  background: #2e76ff;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  font-weight: 400;
}
.dialog-footer-right .cancel-btn {
  width: 92px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #dadada;
  font-size: 14px;
  font-weight: 400;
  background: #fff;
}
</style>
