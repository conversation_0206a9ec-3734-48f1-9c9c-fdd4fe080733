import request from '../request';
import { ApiResponse } from '@/types/api';

// AI任务状态接口返回类型
interface AiTaskStatus {
  id: number;
  status: number;
  rank: number;
}

// AI图片生成
export const genAiPic = (params: { prompt: string; size: string }): Promise<ApiResponse> => {
  return request({
    url: '/ai/genAiPic',
    method: 'POST',
    params,
  });
};

// 获取用户AI任务列表
export const getUserAiTask = (params: {
  pageNo: number;
  pageSize: number;
  taskType: number;
}): Promise<ApiResponse> => {
  return request({
    url: '/ai/getUserAiTask',
    method: 'GET',
    params,
  });
};

// 语音生成接口
export const genAiVoice = (data: {
  voiceType: string;
  speedRatio: number;
  text: string;
}): Promise<ApiResponse<number>> => {
  return request({
    url: `/ai/genAiVoice?voiceType=${data.voiceType}&speedRatio=${data.speedRatio}`,
    method: 'POST',
    data: JSON.stringify(data.text),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// 视频生成接口参数类型定义
export interface GenAiVideoQueryParams {
  VideoQuality: number;
  duration: number;
  ratio: number;
}

export interface GenAiVideoBodyParams {
  prompt: string;
  pic: string;
}

export interface GenAiVideoParams {
  queryParams: GenAiVideoQueryParams;
  bodyParams: GenAiVideoBodyParams;
}

// 视频生成接口
export const genAiVideo = async (params: GenAiVideoParams): Promise<ApiResponse<number>> => {
  // 构建 URL 查询参数
  const urlParams = new URLSearchParams({
    VideoQuality: params.queryParams.VideoQuality.toString(),
    duration: params.queryParams.duration.toString(),
    // ratio: params.queryParams.ratio.toString(),
    ratio: '9:16',
  });

  return request({
    url: `/ai/genAiVideo?${urlParams.toString()}`,
    method: 'POST',
    data: params.bodyParams,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// 生成AI模型
export const genAiModel = async (params: {
  pic: File | string;
  prompt: string;
  modelStyle: string;
}): Promise<ApiResponse<number>> => {
  // 构建 FormData
  const formData = new FormData();
  if (params.pic instanceof File) {
    formData.append('pic', params.pic);
  } else {
    formData.append('pic', params.pic);
  }

  // 构建 URL 参数
  const urlParams = new URLSearchParams({
    Prompt: params.prompt,
    modelStyle: params.modelStyle,
  });

  return request({
    url: `/ai/genAiModel?${urlParams.toString()}`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// 获取AI任务状态
export const getAiTaskStatus = (id: number): Promise<ApiResponse<AiTaskStatus>> => {
  return request({
    url: `/ai/getAiTaskStatus?id=${id}`,
    method: 'GET',
  });
};

// 删除AI任务
export const deleteAiTask = (id: number): Promise<ApiResponse> => {
  return request({
    url: `/ai/deleteAiTask?id=${id}`,
    method: 'POST',
  });
};

// 取消AI生成任务
export const cancelAiTask = (id: number) => {
  return request({
    url: `/ai/cancelTask`,
    method: 'POST',
    params: { id },
  });
};

// 保存AI素材
export const saveAiMaterial = (data: { id: number; name: string }): Promise<ApiResponse> => {
  return request({
    url: `/ai/NjyjAiMaterialUpload?id=${data.id}&materialName=${data.name}`,
    method: 'POST',
  });
};
