{"name": "mixedspace", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "serve": "vue-cli-service serve", "build": "vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/crypto-js": "^4.2.2", "axios": "^1.5.0", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "dat.gui": "^0.7.9", "dayjs": "^1.11.13", "element-plus": "^2.8.8", "html2canvas": "^1.4.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "qrcode.vue": "^3.5.1", "three": "^0.149.0", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuedraggable": "^4.1.0", "vuex": "^4.0.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@types/node": "^20.8.5", "@types/three": "^0.158.2", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "autoprefixer": "^10.4.20", "browserslist": "^4.23.3", "caniuse-lite": "^1.0.30001660", "eslint-plugin-vue": "^8.0.3", "less": "^4.0.0", "less-loader": "^8.0.0", "typescript": "^5.5.4"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:none", "@vue/typescript/recommended"], "parserOptions": {"ecmaVersion": 2020}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}