export interface RatioItem {
  label: string;
  value: string;
  width: number;
  height: number;
}

export const COMMON_RATIOS: RatioItem[] = [
  { label: '1:1', value: '1:1', width: 1024, height: 1024 },
  { label: '3:4', value: '3:4', width: 864, height: 1152 },
  { label: '4:3', value: '4:3', width: 1152, height: 864 },
  { label: '16:9', value: '16:9', width: 1280, height: 720 },
  { label: '9:16', value: '9:16', width: 720, height: 1280 },
  { label: '3:2', value: '3:2', width: 1248, height: 832 },
  { label: '2:3', value: '2:3', width: 832, height: 1248 },
  { label: '21:9', value: '21:9', width: 1512, height: 648 },
];
