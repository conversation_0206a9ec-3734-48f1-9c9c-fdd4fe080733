<template>
  <div id="experience_edit"></div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, onBeforeUnmount, onUnmounted } from 'vue';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import {
  Vector3,
  Scene,
  OrthographicCamera,
  WebGLRenderer,
  BoxGeometry,
  AmbientLight,
  Euler,
  MeshStandardMaterial,
  Mesh,
  Vector2,
  Raycaster,
  PointLight,
  Matrix4,
  Quaternion,
  Group,
  Plane,
  PlaneGeometry,
  MeshBasicMaterial,
  Box3,
  Box3Helper,
  Line,
  TextureLoader,
  VideoTexture,
  MOUSE,
  TOUCH,
  BackSide,
} from 'three';
import { axisPlan, loader } from '@/config/threeJs';
import { CLine } from '@/core/primitives';
import {
  radiansToAngle,
  angleToRadians,
  createBox,
  initSize,
  createGroundLine,
  searchMaterialFromUuid,
} from '@/utils';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { updateWxIdentifyAttachInfo } from '@/api';
import { ElMessage } from 'element-plus';

const props = defineProps({
  handleMouseMove: {
    default: null,
    type: Function,
  },
  setLocation: {
    default: null,
    type: Function,
  },
  setRotation: {
    default: null,
    type: Function,
  },
  setScale: {
    default: null,
    type: Function,
  },
  isPlanStyle: {
    default: false,
    type: Boolean,
  },
});

let canvas: any = null;
let camera: any = null;
let controls: any = null;
let renderer: any = null;
const scene = new Scene();
let dragObjectName = '';
const operateType = ref('移动');
const object3DNames: any = ref([]);
const activeModelName = ref('');
const rotatePlan = ref('');
let cube: any = null;
let activeObject: any = ref([]);
let width: number, height: number;
let countLoaderNum = 0; // 计数cube里的箭头加载了几个
const router = useRouter();
let sceneType = 0;
let animationFrameId: any = null;

const store = useStore();

onMounted(() => {
  const pageQuery: any = router.currentRoute.value.query;
  sceneType = +pageQuery.sceneType || 0;

  canvas = document.getElementById('experience_edit');
  width = window.innerWidth || 0;
  height = window.innerHeight || 0;
  let k = window.innerWidth / window.innerHeight;
  let s = 6;
  camera = new OrthographicCamera(-s * k, s * k, s, -s, 1, 1000);
  camera.position.set(172, 426, 593);
  camera.lookAt(new Vector3(0, 0, 0));
  renderer = new WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  renderer.setClearColor(0xc3c7cb, 1);
  canvas?.appendChild(renderer.domElement);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.render(scene, camera);
  (window as any).renderer2 = renderer;

  controls = new OrbitControls(camera, renderer.domElement);
  controls.mouseButtons = {
    LEFT: MOUSE.ROTATE,
    MIDDLE: MOUSE.DOLLY,
    RIGHT: MOUSE.PAN,
  };
  controls.touches = {
    ONE: TOUCH.ROTATE,
    TWO: TOUCH.DOLLY_PAN,
  };
  controls.maxZoom = 10;
  controls.minZoom = 0.05;
  // controls.maxPolarAngle = 0.45 * Math.PI;
  controls.enableDamping = false;
  (window as any).controls2 = controls;

  // 环境光
  const ambient = new AmbientLight(0xffffff, 1);
  ambient.name = 'ambient-init';
  scene.add(ambient);
  (window as any).scene2 = scene;
  (window as any).camera2 = camera;

  // 辅助线
  // const axesHelper = new AxesHelper(2);
  // axesHelper.name = 'axesHelper-init'
  // scene.add(axesHelper);

  // 点光源
  const light = new PointLight(0xffffff, 1, 100);
  light.position.set(10, 10, 10);
  light.name = 'point-init';
  light.castShadow = true; // default false
  scene.add(light);

  const geometry = new BoxGeometry(0.5, 0.5, 0.5);
  const material = new MeshStandardMaterial({
    color: 0x00ff00,
    transparent: true,
    opacity: 0,
    depthTest: false,
    depthWrite: false,
  });
  cube = new Mesh(geometry, material);
  cube.name = 'cube-init';

  // xy面，黄色
  const geometry1 = new PlaneGeometry(50, 50);
  const material1 = new MeshBasicMaterial({
    color: 0xffff00,
    transparent: true,
    opacity: 0,
    depthTest: false,
    depthWrite: false,
    side: 2,
  });
  const plane = new Mesh(geometry1, material1);
  plane.name = 'xy-plan';
  cube.add(plane);

  // yz面，蓝绿色
  const geometry2 = new PlaneGeometry(50, 50);
  const material2 = new MeshBasicMaterial({
    color: 0x00ffff,
    transparent: true,
    opacity: 0,
    depthTest: false,
    depthWrite: false,
    side: 2,
  });
  const plane2 = new Mesh(geometry2, material2);
  plane2.name = 'yz-plan';
  const matrix2 = new Matrix4().makeRotationFromQuaternion(
    new Quaternion().setFromUnitVectors(
      new Vector3(0, 0, 1), // 初始向量
      new Vector3(1, 0, 0) // 面所在的法向量
    )
  );
  // 应用矩阵到平面
  plane2.applyMatrix4(matrix2);
  cube.add(plane2);

  // xz面，紫色
  const geometry3 = new PlaneGeometry(50, 50);
  const material3 = new MeshBasicMaterial({
    color: 0xff00ff,
    transparent: true,
    opacity: 0,
    depthTest: false,
    depthWrite: false,
    side: 2,
  });
  const plane3 = new Mesh(geometry3, material3);
  plane3.name = 'xz-plan';
  const matrix3 = new Matrix4().makeRotationFromQuaternion(
    new Quaternion().setFromUnitVectors(
      new Vector3(0, 0, 1), // 初始向量
      new Vector3(0, 1, 0) // 面所在的法向量
    )
  );
  // 应用矩阵到平面
  plane3.applyMatrix4(matrix3);
  cube.add(plane3);
  cube.material.depthTest = false;
  cube.visible = false;
  scene.add(cube);

  loader.glb.load(`glb/quarter-circle.glb`, function (glb2: any) {
    glb2.scene.children[0].material.depthTest = false;
    glb2.scene.children[0].renderOrder = 10;
    glb2.scene.rotateY(-Math.PI / 2);
    glb2.scene.children[0].userData.planName = 'yz-plan';
    cube.add(glb2.scene);
  });

  loader.glb.load(`glb/quarter-circle.glb`, function (glb2: any) {
    glb2.scene.children[0].material.depthTest = false;
    glb2.scene.children[0].renderOrder = 10;
    glb2.scene.children[0].userData.planName = 'xy-plan';
    cube.add(glb2.scene);
  });

  loader.glb.load(`glb/quarter-circle.glb`, function (glb2: any) {
    glb2.scene.children[0].material.depthTest = false;
    glb2.scene.children[0].renderOrder = 10;
    glb2.scene.rotateX(Math.PI / 2);
    glb2.scene.children[0].userData.planName = 'xz-plan';
    cube.add(glb2.scene);
  });

  const geometryx = new PlaneGeometry(200, 200);
  const materialx = new MeshBasicMaterial({
    side: 1,
    transparent: true,
    opacity: 1,
  });
  const t = new TextureLoader().load('sourceType/ground.png');
  materialx.map = t;
  const ground = new Mesh(geometryx, materialx);
  ground.name = 'ground-init';
  ground.lookAt(0, 1, 0);
  createGroundLine(ground, 100);
  scene.add(ground);

  animate();
  function animate() {
    if (!props.isPlanStyle) {
      renderer.render(scene, camera);
      if (renderer.domElement.style.width == '0px') {
        resizeCanvas();
      }
    }
    // 使用 requestAnimationFrame 执行动画
    animationFrameId = requestAnimationFrame(animate);
  }

  canvas?.addEventListener('mousedown', (e: any) => {
    if (!props.isPlanStyle) {
      documentMouseDown(e, controls);
    }
  });

  document?.addEventListener('mouseup', (e: any) => {
    const activeModel: any = scene.getObjectByName(activeModelName.value);
    dragObjectName = '';
    if (rotatePlan.value) {
      activeModel.userData.oldRotate = new Vector3(0, 0, 0);
      rotatePlan.value = '';
    }
    controls.enabled = true; // 取消页面禁止转动

    if ([5, 6, 7].includes(+sceneType) && activeModelName.value) {
      const hk_track: any = scene.getObjectByName('renti_' + sceneType);
      const editSceneData = { ...store.state.editSceneData };
      let minDis = 999;
      let lastPoint: any = null;
      hk_track?.children[0].children
        .filter((d: any) => d.type != 'Mesh')
        .forEach((item: any) => {
          const dis = item.position.distanceTo(activeModel.position);
          if (dis < 0.6) {
            if (minDis < dis) return;
            editSceneData.outerMaterialMetaDtoList = editSceneData.outerMaterialMetaDtoList.map(
              (e: any, i: number) => {
                if (e.uuid == activeModelName.value) {
                  e.location.x = item.position.x;
                  e.location.y = item.position.y;
                  e.location.z = item.position.z;
                  if (editSceneData.identifyPoints) {
                    editSceneData.identifyPoints[activeModelName.value] = {
                      materialId: e.materialId || e.materialDto.id,
                      materialAffiliation:
                        e.materialAffiliation || e.materialDto.materialAffiliation,
                      pointIndex: +item.name.split('_tracker_')[1],
                      materialMetaId: e.id,
                    };
                  } else {
                    editSceneData.identifyPoints = {
                      [activeModelName.value]: {
                        materialId: e.materialId || e.materialDto.id,
                        materialAffiliation:
                          e.materialAffiliation || e.materialDto.materialAffiliation,
                        pointIndex: +item.name.split('_tracker_')[1],
                        materialMetaId: e.id,
                      },
                    };
                  }
                  lastPoint = JSON.parse(JSON.stringify(e));
                }
                return e;
              }
            );
            minDis = dis;
          }
        });
      console.log(minDis, 999888);
      if (
        editSceneData.identifyPoints &&
        editSceneData.identifyPoints[activeModelName.value] &&
        minDis == 999
      ) {
        delete editSceneData.identifyPoints[activeModelName.value];
      }
      if (minDis != 999 && lastPoint) {
        const obj: any = scene.getObjectByName(activeModelName.value);
        obj.position.set(lastPoint.location.x, lastPoint.location.y, lastPoint.location.z);
        cube.position.set(lastPoint.location.x, lastPoint.location.y, lastPoint.location.z);
      }
      store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
    }
  });

  document?.addEventListener('mousemove', (e: any) => {
    documentMove(e, cube);
  });

  canvas?.addEventListener('mousemove', mousemoveCanvas, false);
  controls.addEventListener('change', onChange);
  window.addEventListener('resize', resizeCanvas, false);
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas);
  cancelAnimationFrame(animationFrameId);
});

const resizeCanvas = () => {
  if (renderer) {
    let canvas = document.getElementById('experience_edit');
    width = canvas?.getBoundingClientRect().width || 0;
    height = canvas?.getBoundingClientRect().height || 0;

    renderer.setSize(width, height);
    // 重置相机投影的相关参数
    const k = width / height; //窗口宽高比
    const s = 5;
    camera.left = -s * k;
    camera.right = s * k;
    camera.top = s;
    camera.bottom = -s;
    camera.updateProjectionMatrix();
  }
};

const onChange = () => {
  const reverseScale = 1 / (window as any).controls2.object.zoom;
  cube.scale.set(reverseScale, reverseScale, reverseScale);

  const angleDot = (window as any).controls2.object.position
    .clone()
    .sub(new Vector3(0, 0, 0))
    .normalize()
    .clone()
    .dot(new Vector3(0, 1, 0));
  const groundObj: any = scene.getObjectByName('ground-init');
  // console.log(angleDot)
  if (Math.abs(angleDot) < 0.5) {
    groundObj.children.forEach((childLine: any) => {
      childLine.setOpacity(1 - (0.5 - Math.abs(angleDot)) * 2);
    });
  }
};

const loaderVideo = (url: any, data: any) => {
  const materialSize = {
    width: data.materialMediaInfoDto?.width || 1000,
    height: data.materialMediaInfoDto?.height || 1000,
  };
  const width = materialSize.width / 1000;
  const height = materialSize.height / 1000;
  // 创建视频元素
  const video = document.createElement('video');
  video.src = url; // 视频文件路径
  video.load();
  video.crossOrigin = 'anonymous';
  video.loop = true;

  // 创建视频纹理
  const videoTexture = new VideoTexture(video);

  // 创建材质并将视频纹理应用上去
  const videoMaterial = new MeshBasicMaterial({ map: videoTexture, side: 2 });

  // 创建平面几何体
  const geometry = new PlaneGeometry(width, height);

  // 创建网格并添加到场景
  const mesh = new Mesh(geometry, videoMaterial);
  return mesh;
};

const loaderImage = (url: any, data: any) => {
  const materialType = data.materialType;
  let materialParam: any = { side: 2 };
  if (url.toLowerCase().includes('.png') || materialType == '5') {
    materialParam.transparent = true;
  }
  const materialSize = {
    width: data.materialMediaInfoDto?.width || 1000,
    height: data.materialMediaInfoDto?.height || 1000,
  };
  const width = materialSize.width / 1000;
  const height = materialSize.height / 1000;
  const geometry = new PlaneGeometry(width, height);
  const material = new MeshBasicMaterial(materialParam);
  const t = new TextureLoader().load(url);
  material.map = t;
  const mesh = new Mesh(geometry, material);
  return mesh;
};

const addModel = (url: string, data: any, callback: any, noplay?: boolean, sceneType?: any) => {
  const materialFormat = data.materialFormat;
  const materialType = data.materialType;
  // 生成图元
  if (materialType == '1') {
    if (noplay) {
      const obj = loaderImage(url, data);
      setModel(obj, callback, sceneType);
    } else {
      const obj = loaderVideo(url, data);
      setModel(obj, callback, sceneType);
    }
  } else if (materialType == '3' || materialType == '5') {
    const obj = loaderImage(url, data);
    setModel(obj, callback, sceneType);
  } else if (materialType == '4' && loader[materialFormat]) {
    loader[materialFormat].load(url, function (glb: any) {
      const obj: any = glb.scene || glb;
      setModel(obj, callback, sceneType);
    });
  } else if (materialType == '2') {
    loader.glb.load('glb/sound-model.glb', function (glb: any) {
      const obj: any = glb.scene || glb;
      setModel(obj, callback, sceneType);
    });
  }
};

const checkImageSize = (url: string) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = function () {
      console.log(`图片宽度: ${img.width}, 图片高度: ${img.height}`);
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = function () {
      console.error('图片加载失败');
    };
    img.src = url; // 设置图片 URL
  });
};

// 加载小程序识别底图
const addModelBase = async (url: string, sceneType: string) => {
  if (sceneType == '3' || sceneType == '8') {
    const geometry = new PlaneGeometry(1, 1);
    const material = new MeshBasicMaterial({ side: 2 });
    const t = new TextureLoader().load(url);
    material.map = t;
    const mesh = new Mesh(geometry, material);
    const imageInfo: any = await checkImageSize(url);
    const ratio = 6 / imageInfo.width;
    mesh.scale.set(6, imageInfo.height * ratio, 1);
    if (sceneType == '3') {
      mesh.rotation.x = -Math.PI / 2;
      mesh.position.y = 0.01;
    } else {
      (scene.getObjectByName('ground-init') as any).position.y = (-imageInfo.height * ratio) / 2;
    }
    mesh.name = 'renti_mark_img';
    scene.add(mesh);
  } else {
    loader.glb.load(url, function (glb: any) {
      const obj: any = glb.scene || glb;
      obj.name = 'renti_' + sceneType;
      scene.add(obj);
    });
  }
};

const setModel = (obj: any, callback: any, sceneType?: any) => {
  obj.name = obj.uuid;
  scene.add(obj);
  object3DNames.value.push(obj.uuid);
  obj.userData.oldRotate = new Vector3(0, 0, 0);
  const boxInfo = new Box3().expandByObject(obj);
  initSize(boxInfo, obj);
  // 生成线条框
  const helper = createBox(boxInfo, 0xffca57, 2);
  helper.name = 'box-helper';
  obj.add(helper);
  obj.userData.boxInfos = { ...boxInfo, initScale: new Vector3(1, 1, 1) };
  obj.getObjectByName('box-helper').visible = false;
  store.state.isFinishModel = !store.state.isFinishModel;
  console.log(boxInfo, 'boxInfo');
  callback(obj);
};

const mousemoveCanvas = (e: any) => {
  props.handleMouseMove(new Vector2(e.clientX, e.clientY));
};

const documentMouseDown = async (e: any, controls: any) => {
  const pointer = new Vector2();
  pointer.x = (e.clientX / width) * 2 - 1;
  pointer.y = -(e.clientY / height) * 2 + 1;

  const raycaster = new Raycaster();
  // 通过摄像机和鼠标位置更新射线
  raycaster.setFromCamera(pointer, camera);
  // 计算物体和射线的焦点
  const intersects = raycaster.intersectObjects(scene.children);

  // 旋转
  const quarterCircle = intersects.filter((e: any) => e.object.name == 'System_QuarterCircle')[0];
  if (quarterCircle && operateType.value == '旋转') {
    controls.enabled = false; // 页面禁止转动
    rotatePlan.value = quarterCircle.object.userData.planName;
    const targetPlan = intersects.filter((p: any) => p.object.name == rotatePlan.value)[0];
    if (targetPlan) {
      targetPlan.object.userData.rotateStartPoint = targetPlan.point;
    }
    return;
  }
  // 移动和缩放
  if (intersects.length) {
    const arrowArrays = intersects.filter((e: any) =>
      ['RedCube', 'GreenCube', 'BlueCube'].includes(e.object.name)
    )[0];
    if (arrowArrays) {
      dragObjectName = arrowArrays.object.name || '';

      // 缩放需要重置初始值
      if (operateType.value == '缩放') {
        const activeModel: any = scene.getObjectByName(activeModelName.value);
        const boxInfos = activeModel.userData.boxInfos;
        boxInfos.initScale = new Vector3(boxInfos.scale.x, boxInfos.scale.y, boxInfos.scale.z);
      }
      const planName = axisPlan[dragObjectName];
      if (planName) {
        controls.enabled = false; // 页面禁止转动
      }
      return;
    }

    const intersectObject = intersects
      .map((item: any, index: number) => {
        const obj = searchModel(item.object);
        return obj;
      })
      .filter((obj: any) => object3DNames.value.includes(obj.name))[0];
    if (!intersectObject) return;
    if (store.state.isDragLoading || store.state.isRequesting) {
      // 射线拾取到了模型再判断是否保存好
      return ElMessage({ message: '数据加载中，请勿频繁操作', type: 'warning' });
    }
    if (activeModelName.value !== intersectObject.name) {
      const activeModel: any = scene.getObjectByName(intersectObject.name);
      resetCube(activeModel.position);
    }

    if (intersectObject.name) {
      cube.visible = true;
      const activeModel: any = scene.getObjectByName(activeModelName.value);
      if (activeModel) {
        if (!operateType.value) {
          store.state.operateType = '移动';
        }
        activeModel.getObjectByName('box-helper').visible = true;
        if (cube && cube.children.length <= 6) {
          await addEdit([
            [new Vector3(0, 0, 1), 0x0000ff, 'blue-arrow'],
            [new Vector3(0, 1, 0), 0x00ff00, 'green-arrow'],
            [new Vector3(1, 0, 0), 0xff0000, 'red-arrow'],
          ]);
          store.state.activeMaterial = intersectObject.name;
          return;
        }
        store.state.activeMaterial = intersectObject.name;
      }
    }
  }
};

// 点击左侧高亮模型
const activeCube = async (name: string) => {
  const activeModel: any = scene.getObjectByName(name);
  resetCube(activeModel.position);
  activeModelName.value = name;
  cube.visible = true;
  activeModel.getObjectByName('box-helper').visible = true;

  if (!operateType.value) {
    store.state.operateType = '移动';
  }
  if (cube && cube.children.length <= 6) {
    await addEdit([
      [new Vector3(0, 0, 1), 0x0000ff, 'blue-arrow'],
      [new Vector3(0, 1, 0), 0x00ff00, 'green-arrow'],
      [new Vector3(1, 0, 0), 0xff0000, 'red-arrow'],
    ]);
    modelDepthAdaptive(name);
    return;
  }
  modelDepthAdaptive(name);
};

// 自适应图片和模型的深度问题
const modelDepthAdaptive = (name: string) => {
  const materialData = searchMaterialFromUuid({ ...store.state.editSceneData }, name);
  const curMaterialType = materialData.ele.materialDto.materialType;
  if (cube.children) {
    cube.children.forEach((e: any) => {
      if (e.material) {
        if (e.name != 'TransformArrow') {
          e.material.depthTest = curMaterialType != '4' && curMaterialType != '2';
        } else {
          e.material.depthTest = false;
        }
      } else {
        e.traverse((c: any) => {
          if (
            c.material &&
            !['System_QuarterCircle', 'BlueArrow', 'GreenArrow', 'RedArrow'].includes(c.name)
          ) {
            c.material.depthTest = curMaterialType != '4' && curMaterialType != '2';
          }
        });
      }
    });
  }
};

const hideCube = (name: string) => {
  const activeModel: any = scene.getObjectByName(name);
  cube.visible = false;
  activeModelName.value = '';
  if (activeModel && activeModel.getObjectByName('box-helper')) {
    activeModel.getObjectByName('box-helper').visible = false;
  }
};

const resetCube = (position?: any) => {
  cube?.position.set(position?.x || 0, position?.y || 0, position?.z || 0);
  const activeModel: any = scene.getObjectByName(activeModelName.value);
  if (activeModel && activeModel.getObjectByName('box-helper')) {
    activeModel.getObjectByName('box-helper').visible = false;
  }
};

const rotateAxis = (
  type: string,
  activeModel: any,
  radians: number,
  directionVector: any,
  normal: Vector3,
  angle?: number
) => {
  const newAngle = (activeModel.userData.oldRotate[type] || 0) - radians;
  const sign = directionVector[type] / Math.abs(directionVector[type]) || 0;
  activeModel.rotateOnWorldAxis(normal, newAngle * sign);
  activeModel.userData.oldRotate[type] = radians;
  const quaternion = activeModel.quaternion.clone();
  let metaInfo = `${quaternion.x},${quaternion.y},${quaternion.z},${quaternion.w},${activeModel.userData.initScale}`;
  const editSceneData = { ...store.state.editSceneData };
  const activeIndex = editSceneData.outerMaterialMetaDtoList.findIndex(
    (e: any) => e.uuid == activeModelName.value
  );
  editSceneData.outerMaterialMetaDtoList[activeIndex].metaInfo = metaInfo;
  const { x, y, z } = activeModel.rotation;
  editSceneData.outerMaterialMetaDtoList[activeIndex].rotation = {
    x: -x,
    y: -y,
    z: -z,
  };
  if (editSceneData.outerMaterialMetaDtoList[activeIndex].flag != 'add') {
    editSceneData.outerMaterialMetaDtoList[activeIndex].flag = 'update';
  }
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
};

const searchModel: any = (object: any) => {
  if (object?.parent?.type == 'Scene' || !object?.parent || object.name == 'box-helper') {
    return object;
  } else {
    return searchModel(object?.parent);
  }
};
// 用于计算箭头的拖拽
const documentMove = (e: any, cube: any) => {
  const pointer = new Vector2();
  pointer.x = (e.clientX / width) * 2 - 1;
  pointer.y = -(e.clientY / height) * 2 + 1;

  const raycaster = new Raycaster();

  // 通过摄像机和鼠标位置更新射线
  raycaster.setFromCamera(pointer, camera);

  // 计算物体和射线的焦点
  const intersects = raycaster.intersectObjects(scene.children);

  // 旋转
  if (rotatePlan.value && operateType.value == '旋转') {
    const targetPlan = intersects.filter((p: any) => p.object.name == rotatePlan.value)[0];
    const activeModel: any = scene.getObjectByName(activeModelName.value);
    if (targetPlan) {
      const v1 = targetPlan.object.userData.rotateStartPoint.clone().sub(activeModel.position);
      const v2 = targetPlan.point.clone().sub(activeModel.position);
      const angles = v1.clone().angleTo(v2);
      const directionVector = activeModel.position
        .clone()
        .sub(targetPlan.object.userData.rotateStartPoint || new Vector3(0, 0, 0))
        .clone()
        .cross(v2);
      if (rotatePlan.value == 'yz-plan') {
        // X
        rotateAxis('x', activeModel, angles, directionVector, new Vector3(1, 0, 0));
      } else if (rotatePlan.value == 'xz-plan') {
        // Y
        rotateAxis('y', activeModel, angles, directionVector, new Vector3(0, 1, 0));
      } else if (rotatePlan.value == 'xy-plan') {
        // Z
        rotateAxis('z', activeModel, angles, directionVector, new Vector3(0, 0, 1));
      }
    }
    return;
  } else if (operateType.value == '旋转') {
    const quarterCircle: any = intersects.filter(
      (e: any) => e.object.name == 'System_QuarterCircle'
    )[0];
    if (quarterCircle) {
      if (!activeObject.value.length) {
        activeObject.value = [
          {
            id: quarterCircle.object.id,
            oldColor: JSON.stringify(quarterCircle.object.material.color),
          },
        ];
        quarterCircle.object.material.color.setRGB(1, 1, 0);
      }
    } else {
      if (activeObject.value.length) {
        const obj: any = scene.getObjectById(activeObject.value[0].id);
        obj.material.color.set(JSON.parse(activeObject.value[0].oldColor));
        activeObject.value = [];
      }
    }
  }
  if (dragObjectName) {
    // 移动和缩放
    if (intersects.length) {
      let planInfo: any = null;
      planInfo = axisPlan[dragObjectName].p1;
      let targetPlan = intersects.filter((p: any) => p.object.name == planInfo.name);
      let activeModel: any = scene.getObjectByName(activeModelName.value);
      if (!targetPlan.length) {
        planInfo = axisPlan[dragObjectName].p2;
        targetPlan = intersects.filter((p: any) => p.object.name == planInfo.name);
        activeModel = scene.getObjectByName(activeModelName.value);
      }
      if (!targetPlan.length) return;
      var normal = planInfo.normal; // 表示平面的法向量
      normal.clone().applyQuaternion(activeModel.quaternion);
      var pointOnPlane = cube.position.clone(); // 平面上的一点
      // 创建平面对象
      var plane = new Plane();
      plane.setFromNormalAndCoplanarPoint(normal, pointOnPlane);

      var distance = plane.distanceToPoint(targetPlan[0].point);
      let ll = targetPlan[0].point.clone().distanceTo(cube.position);
      const l = Math.sqrt(Math.pow(ll, 2) - Math.pow(distance, 2));
      if (dragObjectName == 'RedCube') {
        const flag =
          targetPlan[0].point.clone().sub(pointOnPlane).clone().dot(new Vector3(1, 0, 0)) > 0;
        transform3D('x', flag, l);
      } else if (dragObjectName == 'GreenCube') {
        const flag =
          targetPlan[0].point.clone().sub(pointOnPlane).clone().dot(new Vector3(0, 1, 0)) > 0;
        transform3D('y', flag, l);
      } else if (dragObjectName == 'BlueCube') {
        const flag =
          targetPlan[0].point.clone().sub(pointOnPlane).clone().dot(new Vector3(0, 0, 1)) > 0;
        transform3D('z', flag, l);
      }
    }
  } else {
    if (operateType.value == '移动' || operateType.value == '缩放') {
      let targetPlan: any = intersects.filter((p: any) => axisPlan[p.object.name])[0];
      targetPlan = targetPlan?.object;
      if (targetPlan) {
        if (!activeObject.value.length) {
          scene.traverse((e: any) => {
            if (e.userData.glbType == targetPlan.userData.glbType) {
              activeObject.value.push({
                id: e.id,
                oldColor: JSON.stringify(e.material.color),
              });
              e.material.color.setRGB(1, 1, 0);
            }
          });
        }
      } else {
        if (activeObject.value.length) {
          activeObject.value.forEach((e: any) => {
            const obj: any = scene.getObjectById(e.id);
            obj.material.color.set(JSON.parse(e.oldColor));
          });
          activeObject.value = [];
        }
      }
    }
  }
};

const transform3D = (type: string, flag: boolean, l: number) => {
  if (operateType.value == '移动') {
    setPosition(type, l, flag);
  } else if (operateType.value == '缩放') {
    setScale(type, l);
  }
};

const setScale = (type: string, diff: number) => {
  const zoom = (window as any).controls2.object.zoom;
  const activeModel: any = scene.getObjectByName(activeModelName.value);
  const boxInfos = activeModel.userData.boxInfos;
  let newScale = diff * zoom * boxInfos.initScale[type];
  newScale = Math.max(newScale, 0.01); // 最小缩小系数限制为0.01
  if (type == 'x') {
    activeModel.scale.setX(newScale * activeModel.userData.initScale);
  } else if (type == 'y') {
    activeModel.scale.setY(newScale * activeModel.userData.initScale);
  } else if (type == 'z') {
    activeModel.scale.setZ(newScale * activeModel.userData.initScale);
  }
  activeModel.userData.boxInfos.scale[type] = newScale;

  const editSceneData = { ...store.state.editSceneData };
  editSceneData.outerMaterialMetaDtoList = editSceneData.outerMaterialMetaDtoList.map((e: any) => {
    if (e.uuid == activeModelName.value) {
      e.scale[type] = newScale.toFixed(2);
      if (e.flag != 'add') {
        e.flag = 'update';
      }
    }
    return e;
  });
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
};

const setPosition = (type: string, diff: number, flag: boolean) => {
  const zoom = (window as any).controls2.object.zoom;
  const activeModel: any = scene.getObjectByName(activeModelName.value);
  const tag = flag ? 1 : -1;
  const point = cube.position.clone();
  point[type] += diff * tag - 1 / zoom;
  cube.position.set(point.x, point.y, point.z);
  activeModel.position.set(point.x, point.y, point.z);

  const editSceneData = { ...store.state.editSceneData };
  editSceneData.outerMaterialMetaDtoList = editSceneData.outerMaterialMetaDtoList.map((e: any) => {
    if (e.uuid == activeModelName.value) {
      e.location[type] = point[type];
      if (e.flag != 'add') {
        e.flag = 'update';
      }
    }
    return e;
  });
  store.state.editSceneData = JSON.parse(JSON.stringify(editSceneData));
};

const addEdit = (values: any) => {
  return new Promise((resolve) => {
    values.forEach((data: any) => {
      const cline = new CLine({
        vertexs: [new Vector3(0, 0, 0), data[0]],
        color: data[1],
        lineWidth: 6, // 从3改为6，加粗线条
        reversalDepthTest: true,
        transparent: true,
      });
      cline.name = 'TransformArrow';
      cline.userData.glbType = data[2];
      const object3D = new Group();
      cube.add(cline);
      loaderArrow([new Vector3(0, 0, 0), data[0]], data[2], cube, object3D, resolve);
    });
  });
};

const loaderArrow = (points: any, modelName: string, obj: any, object3D: any, resolve: any) => {
  loader.glb.load(`glb/${modelName}.glb`, function (glb: any) {
    object3D.name = 'TransformArrow';
    object3D.position.set(points[1].x, points[1].y, points[1].z);
    object3D.add(glb.scene);
    glb.scene.scale.set(5.0, 5.0, 5.0); // 从3.9改为5.0，让箭头更大
    const matrix = new Matrix4().makeRotationFromQuaternion(
      new Quaternion().setFromUnitVectors(
        new Vector3(0, 1, 0), // 初始向量
        new Vector3(
          modelName.includes('red') ? 1 : 0,
          modelName.includes('green') ? 1 : 0,
          modelName.includes('blue') ? 1 : 0
        ) // 面所在的法向量
      )
    );
    // 应用矩阵到立方体
    glb.scene.applyMatrix4(matrix);
    glb.scene.children[0].userData.glbType = modelName.split('-')[0] + '-arrow';
    glb.scene.children[0].material.depthTest = false;
    glb.scene.renderOrder = 10;
    glb.scene.visible =
      (operateType.value == '缩放' && modelName.includes('cube')) ||
      ((operateType.value == '移动' || operateType.value == '旋转') && modelName.includes('arrow'));
    if (object3D.children.length != 2) {
      loaderArrow(points, modelName.split('-')[0] + '-cube', obj, object3D, resolve);
    } else {
      obj.add(object3D);
      countLoaderNum += 1;
      if (countLoaderNum == 3) {
        resolve();
      }
    }
  });
};

const changeOperateType = (value: string) => {
  operateType.value = value;
  const arrowGroups = cube.children.filter(
    (item: any) => item.name == 'TransformArrow' && item.type == 'Group'
  );
  arrowGroups.forEach((group: any) => {
    group.children[0].visible = value == '移动' || value == '旋转';
    group.children[1].visible = value == '缩放';
  });
};

watch(
  () => store.state.activeMaterial,
  (newState) => {
    if (newState) {
      activeCube(newState);
      if (!operateType.value) {
        store.state.operateType = '移动';
      }
    } else {
      if (store.state.operateType) {
        hideCube(activeModelName.value);
        store.state.operateType = '';
      }
    }
  }
);

watch(
  () => store.state.operateType,
  (newState) => {
    if (newState != operateType.value) {
      changeOperateType(newState);
    }
  }
);

onBeforeUnmount(() => {
  for (let i = 0; i < scene.children.length; i++) {
    // 删除文字标记
    scene.children[i].traverse((e: any) => {
      if (e.element) {
        e.parent?.remove(e);
      }
    });
    scene.remove(scene.children[i]);
    i--;
  }
});

defineExpose({
  addModel,
  activeCube,
  addModelBase,
  // setTransform,
  // hideCube
});
</script>
<style scoped lang="less">
#experience_edit {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
}

.rr {
  position: fixed;
  width: 60px;
  height: 60px;
  line-height: 60px;
  left: 30%;
  top: 30%;
  background-color: red;
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
}
</style>
