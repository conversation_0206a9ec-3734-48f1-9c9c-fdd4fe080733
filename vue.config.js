const { defineConfig } = require('@vue/cli-service');
const path = require('path');
module.exports = defineConfig({
  lintOnSave: false,
  transpileDependencies: true,
  chainWebpack: (config) => {
    config.resolve.alias.set('@', path.resolve('src'));
  },
  configureWebpack: {
    devServer: {
      client: {
        overlay: false,
      },
      host: '0.0.0.0',
      port: 8080,
      proxy: {
        '/api1': {
          target: process.env.VUE_APP_BASE_API,
          changeOrigin: true,
          pathRewrite: {
            '^/api1': '',
          },
        },
        '/api2': {
          target: process.env.VUE_APP_BASE_API__SPACE,
          changeOrigin: true,
          pathRewrite: {
            '^/api2': '',
          },
        },
        '/api': {
          target: process.env.VUE_APP_BASE_API__,
          changeOrigin: true,
          pathRewrite: {
            '^/api': '',
          },
        },
      },
    },
  },
});
