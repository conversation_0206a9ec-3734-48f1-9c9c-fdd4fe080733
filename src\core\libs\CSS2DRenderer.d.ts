import {
	Object3D,
	Scene,
	Camera,
	Intersection,
	Raycaster,
} from 'three';

export class CSS2DObject extends Object3D {
	constructor( element: HTMLElement );
	element: HTMLElement;
	// @override 重写 点射线拾取
    raycast(raycaster: Raycaster, intersects: Intersection[]): void
}

export class CSS2DRenderer {

	constructor();
	domElement: HTMLElement;

	getSize(): {width: number, height: number};
	setSize( width: number, height: number ): void;
	render( scene: Scene, camera: Camera ): void;

}
