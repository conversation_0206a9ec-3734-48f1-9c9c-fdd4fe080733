<template>
  <div class="scene-edit" v-show="isPlanStyle">
    <canvas-left :source-pool-mourse-down="sourcePoolMourseDown" :render-scene-data="renderSceneData"
      :handle-active-area="handleActiveArea" :handle-change-area="handleChangeArea"
      :handle-active-route="handleActiveRoute" :handle-change-route="handleChangeRoute"
      :change-active-index="changeActiveIndex" :public-material-list="materialMetaDtoList" :delete-data="deleteData"
      :delete-source="deleteSource" :active-source="activeSource" :active-source-material="activeSourceMaterial"
      :scene-info="currentScene" :interaction-id="interactionId" :guideRoute-id="guideRouteId"
      :hover-source-name="hoverSourceName" :active-source-name="activeSourceName" :show-route="showRoute"
      :isPlanStyle="isPlanStyle"></canvas-left>
    <canvas-view ref="canvasRef" canvas-id="canvas-edit" :handle-click="handleClickCanvas"
      :active-area-index="activeAreaIndex" :handle-mouse-up="handleMouseUpCanvas"
      :handleMouseDown="handleMouseDownCanvas" :handle-mouse-move="handleMouseMoveCanvas"
      :handleDblClick="handleDblClickCanvas" :handle-mouse-leave="handleMouseLeaveCanvas" :isPlanStyle="isPlanStyle" />
    <canvas-right v-if="currentScene.id" :scene-info="currentScene" :operate-index="operateIndex"
      :spatial-data="spatialData" :change-router-style="changeRouterStyle" :change-scene-value="changeSceneValue"
      :current-active-source="currentActiveSource?.name || ''" :change-right-value="changeRightValue"
      :exit-edit="exitEdit"></canvas-right>
    <div class="zoom-operation">
      <img src="@/assets/images/icon/plus.png" @click="scalePlus" />
      <img src="@/assets/images/icon/minus.png" @click="scaleMinus" />
      <div class="scene-scale" @click="resetScale">{{ (sceneScale * 100).toFixed(0) }}%</div>
    </div>
    <div class="canvas-tool">
      <img src="@/assets/images/icon/scenex.png" />
      <span class="scene-name">{{ currentScene.sceneName }}</span>
      <div class="canvas-tool-icon-box">
        <div class="canvas-tool-icon revoke" @click="handlePrev"></div>
        <div class="canvas-tool-icon revoke-r" @click="handleNext"></div>
        <div @click="handleFullScreen" class="canvas-tool-icon" :class="has_full ? 'full-no-screen' : 'full-screen'">
        </div>
        <div class="canvas-tool-icon save-scene" @click="saveSceneInfo"></div>
      </div>
    </div>
  </div>
  <div class="show-icon" :style="{ backgroundImage: `url(${dragingImg})` }" @mousemove="handleIconMove"
    @mouseup="handleIconUp">
  </div>
</template>

<script lang="ts" setup>
import { reactive, onMounted, ref, onUnmounted, watch } from 'vue'
import CanvasView from '@/components/CanvasModule.vue'
import CanvasLeft from '@/components/CanvasLeft.vue'
import CanvasRight from '@/components/CanvasRight.vue'
import { useRouter } from 'vue-router'
import { Vector3, Scene, Vector2, Raycaster, Group, Quaternion, Box3 } from 'three'
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { MouseStyle, updateMouseStyle } from '@/core/mouse'
import { CPoint, CPlan, CSprite } from '@/core/primitives'
import { screenToWorldVector, radiansToAngle, worldToScreenVector, getAngle, pointRotate, adsorption, angleToRadians, loadZipFileForJSZip } from "@/utils";
import { getSceneMetaPageForWeb, saveScene, getWebScene, modifySceneOpenStatus, updateSceneMeta } from '@/api/index'
import { materialUrls } from '@/config'
import { useStore } from 'vuex'
import { ElMessage } from "element-plus";

const props = defineProps({
  showRoute: {
    default: 0,
    type: Number
  },
  isPlanStyle: {
    default: false,
    type: Boolean
  },
  add3DModel: {
    default: null,
    type: Function
  },
  set3DLocation: {
    default: null,
    type: Function
  },
  activeSource3D: {
    default: null,
    type: Function
  },
  refresh3D: {
    default: null,
    type: Function
  }
})

const loader = new GLTFLoader();
const canvasRef = ref();
const router = useRouter()
const store = useStore()
let controls: any = reactive({});
const on_off = ref(true)
const has_full = ref(false)
let scene: Scene;
let Icondom: any = null
let interactionId = ref('') // 互动区域id
let guideRouteId = ref('') // 路线id
const dragingImg = ref('') // 当前拖拽的资源缩略图
let areaList: any = [] // 所有互动区域列表
let routeList: any = [] // 所有路线列表
const activeAreaIndex = ref(-99) // 当前高亮的互动区域的index
const activeRouteIndex = ref(-1) // 当前高亮的路线的index
let dragingObject: any = null // 拖拽位置中的互动区域
const initSize = new Vector2(1, 1) // 原始的互动区域的二维尺寸
let controlPoint: any = null // 当前拖拽缩放的控制点
let areaRotate = false // 是否处于旋转状态
let currentScene: any = ref({}) // 当前场景信息
const operateIndex = ref(0) // 当前操作的索引，0是互动区域，1是路线
let beforeBack: any = null // 前进后退时保存数据
let materialMetaDtoList: any = reactive([])  // 公共素材
let currentSource: any = null // 当前的资源
const submitData: any = {} // 要提交的数据
let sourceObject: any = null // 被拖拽的资源
const spatialData: any = ref({}) // 存放移动旋转缩放信息，和右侧菜单交互
let publicSourceGroup: any = new Group() // 存放公共互动区域
let areaPlanGroup: any = new Group() // 存放互动区域group
let routerLineGroup: any = new Group() // 存放线路的group
let rotateObject: any = null // 被旋转的对象
let startRotatePoint: any = null; // 开始旋转的点位
let currentAngle = 0; // 辅助线的旋转角度
const hoverSourceName = ref('') // 当前悬浮高亮的资源名字
const activeSourceName: any = reactive({}) // 当前被选中的素材名
let hoverSourceObject: any = null // 当前悬浮高亮的资源对象
const waitdrawing: any = ref(false) // 暂停等待进入绘画的状态
const currentActiveSource: any = ref(null) // 当前点击高亮的素材
const sceneScale = ref(1) // 场景视角缩放系数
const saveloading = ref(false) // 场景保存是否在请求中
const renderSceneData = ref({ reRender: false }) // 重新渲染场景数据

const exitEdit = () => {
  // 更新场景之前先截图保存
  const renderer = (window as any).renderer;
  const camera = (window as any).camera;
  renderer.render(scene, camera)
  let imgData = renderer.domElement.toDataURL("image/jpeg")
  saveScene({ sceneId: submitData.sceneId, base64Pic: imgData }).then((res: any) => {
    router.push('home');
  })
}
// 初始化场景信息
const getInitData = (noReload?: boolean) => {
  const pageQuery: any = router.currentRoute.value.query
  const sceneId = pageQuery.sceneid || ''
  interactionId.value = pageQuery.interactionid
  guideRouteId.value = pageQuery.guideRouteid

  materialMetaDtoList = reactive([]);
  routeList = [];
  areaList = [];
  waitdrawing.value = false
  saveloading.value = false
  renderSceneData.value.reRender = false

  for (let i = 0; i < scene.children.length; i++) {
    if (scene.children[i].name.indexOf('-init') === -1) {

      // 删除文字标记
      scene.children[i].traverse((e: any) => {
        if (e.element) {
          e.parent?.remove(e)
        }
      })
      scene.remove(scene.children[i])
      i--
    }
  }
  publicSourceGroup = new Group()
  publicSourceGroup.name = 'publicSourceGroup'
  areaPlanGroup = new Group()
  areaPlanGroup.name = 'areaPlanGroup'
  routerLineGroup = new Group()
  routerLineGroup.name = 'routerLineGroup'
  scene.add(publicSourceGroup)
  scene.add(areaPlanGroup)
  scene.add(routerLineGroup)

  clearActiveSource()

  getSceneMetaPageForWeb({ pageNo: 1, pageSize: 999 }).then((res: any) => {
    currentScene.value = res.data.records.filter((e: any) => e.id == sceneId)[0] || {}
    submitData.sceneId = sceneId
    on_off.value = currentScene.value.sceneStatus != 0

    if (noReload) {
      getSceneData(sceneId)
      return
    }
    loadZipFileForJSZip(currentScene.value.spaceDto.roomStructurePath, (glb: any) => {
      glb.scene.name = 'model-init'
      // glb.scene.traverse((e: any) => {
      //   if (e.material?.side == 0) {
      //     e.material.side = 2;
      //   }
      // })
      scene.add(glb.scene);
      const boxInfo = new Box3().expandByObject(glb.scene)
      if (scene.getObjectByName('ground-init')) {
        (scene.getObjectByName('ground-init') as any).position.y = boxInfo.min.y - 0.1
      }
      getSceneData(sceneId)
    })
  })
}

const getSceneData = (sceneId: string) => {
  getWebScene({ id: sceneId }).then((res: any) => {
    currentScene.value = { ...currentScene.value, ...res.data }
    const outerMaterialMetaDtoList = [...(res.data.outerMaterialMetaDtoList || [])].map((e: any) => ({ ...e.materialDto, point: new Vector3(e.location.x, e.location.y, e.location.z), id: e.id, materialId: e.materialId, materialAffiliation: e.materialAffiliation }))
    outerMaterialMetaDtoList.forEach((e: any) => {
      if (materialUrls[e.materialType]?.url_a) {
        const sourcePoint = new CSprite({ vertex: e.point, scale: new Vector3(1.2, 1.2, 1.2), url: materialUrls[e.materialType].url_a, type: 'source', marks: e.materialName })
        sourcePoint.name = e.id + '' // 这里区分名字便于后面的删除
        publicSourceGroup.add(sourcePoint)
      }
    })
    materialMetaDtoList.push(...outerMaterialMetaDtoList)

    const outerMaterialGroupDtoList = [...(res.data.outerMaterialGroupDtoList || [])].map((e: any) => ({ ...e, point: new Vector3(e.location.x, e.location.y, e.location.z), id: e.id, materialId: e.id, materialName: e.groupName }))
    outerMaterialGroupDtoList.forEach((e: any) => {
      if (materialUrls.groups?.url_a) {
        const sourcePoint = new CSprite({ vertex: e.point, scale: new Vector3(1.2, 1.2, 1.2), url: materialUrls.groups.url_a, type: 'source', marks: e.groupName })
        sourcePoint.name = e.id + '' // 这里区分名字便于后面的删除
        publicSourceGroup.add(sourcePoint)
      }
    })
    materialMetaDtoList.push(...outerMaterialGroupDtoList)
    renderSceneData.value.reRender = true
  })
}

// ============================= 互动区域相关 ===========================
// 生成互动区域和切换互动区域触发
const handleActiveArea = (data: any, index: number, isCreate?: boolean) => {
  if (index == -99) {
    // 互动区域取消高亮
    if (activeAreaIndex.value != -99 && activeAreaIndex.value != -1) {
      areaCancleHeight(areaList[activeAreaIndex.value].object)
    }

    activeAreaIndex.value = index
    publicSourceGroup.children.forEach((s: any) => {
      s.children[0].visible = true
    })
    clearActiveSource()
    return
  }

  activeAreaIndex.value = index
  // 创建互动区域分两种, 一种的点击创建,flag为空,一种是初始化生成创建,flag为update
  if (isCreate) {
    const currentData: any = { ...data[index] }
    currentData.scale = currentData.scale || new Vector2(1, 1) // 缩放
    currentData.rotate = currentData.rotate || 0 // 旋转
    currentData.object = new CPlan({ width: initSize.x, height: initSize.y, url: "sourceType/interactive-area.png", areaIndex: index, sizeScale: data[index].scale }) // 生成对象
    areaPlanGroup.add(currentData.object) // 加入互动区域组
    // 用来装对应的资源
    const group = new Group()
    group.name = currentData.object.uuid;
    areaPlanGroup.add(group)

    if (!currentData.flag) {
      // 从互动区域列表跳进该页面
      if (interactionId.value != currentData.id) {
        activeAreaIndex.value = -99
      }
      // 生成资源
      currentData.materialDto.forEach((e: any) => {
        let sourcePoint: any = null;
        if (materialUrls[e.materialType]?.url_a) {
          sourcePoint = new CSprite({ vertex: e.point, scale: new Vector3(1.2, 1.2, 1.2), url: materialUrls[e.materialType].url_a, type: 'source', marks: e.materialName })
        } else if (e.groupType) {
          sourcePoint = new CSprite({ vertex: e.point, scale: new Vector3(1.2, 1.2, 1.2), url: materialUrls.groups.url_a, type: 'source', marks: e.materialName })
        }
        sourcePoint.name = e.id + '' // 这里区分名字便于后面的删除
        group.add(sourcePoint)
      })
    }
    areaList[index] = currentData
  } else {
    initSpatialData()
  }
  const currentData: any = { ...areaList[index] }
  const location = currentData.location;
  // 为互动区域加变换
  const rotate = currentData.rotate
  if (rotate && !currentData.object.userData.rotate) {
    currentData.object.rotateY(-rotate)
    currentData.object.userData.rotate = rotate;
    const csprite = currentData.object.getObjectByName('csprite')
    if (csprite) {
      csprite.setRotate(-rotate)
    }

  }
  // 设置互动区域位置更新旋转时候的旋转贴图角度问题
  location && currentData.object.setVertexs(initSize.x * currentData.scale.x, initSize.y * currentData.scale.y, worldToScreenVector(new Vector3(location.x, location.y, location.z)))

  // 互动区域选中效果生成
  areaList.filter((d: any) => d.flag != 'delete').forEach((e: any, i: number) => {
    if (activeAreaIndex.value != i) {
      areaCancleHeight(e.object)
    } else {
      areaHeight(e.object)
      publicSourceGroup.children.forEach((s: any) => {
        s.children[0].visible = false
      })
    }
  });
}

// 互动区域高亮
const areaHeight = (object: any) => {
  object.children[0].setHeightStyle()
  object.children[1].children[0].visible = true // 旋转提示图片
  const currentGroup = areaPlanGroup.getObjectByName(object.uuid)
  currentGroup.children.forEach((s: any) => {
    s.children[0].visible = true
  })
}

// 互动区域取消高亮
const areaCancleHeight = (object: any) => {
  object.children[0].setBaseStyle()
  object.children[1].children[0].visible = false // 旋转提示图片
  const currentGroup = areaPlanGroup.getObjectByName(object.uuid)
  currentGroup.children.forEach((s: any) => {
    s.children[0].visible = false
  })
}

// 修改互动区域名字触发
const handleChangeArea = (title: string, index: number) => {
  areaList[index].title = title
  changeFlag(areaList, index, 'update')
}

// 更新flag
const changeFlag = (objectList: any, index: number, type: string) => {
  const flag = objectList[index].flag

  // 更新
  if (type == 'update') {
    if (flag != 'add') {
      objectList[index].flag = 'update'
    }
  }

  // 删除
  if (type == 'delete') {
    if (flag == 'add') {
      objectList.splice(index, 1)
    } else {
      objectList[index].flag = 'delete'
    }
  }
}

// 删除互动区域或路线
const deleteData = (dataIndex: number, operateIndex: number) => {
  if (operateIndex == 0) {
    // 删除挂载的资源
    const currentPlan: any = areaPlanGroup.getObjectByName(areaList[dataIndex].object.uuid)
    // 删除文字标记
    currentPlan.traverse((e: any) => {
      if (e.element) {
        e.parent?.remove(e)
      }
    })
    areaPlanGroup.remove(currentPlan)

    // 删除对应的互动区域
    areaPlanGroup.remove(areaList[dataIndex].object)
    changeFlag(areaList, dataIndex, 'delete')
    activeAreaIndex.value = -1
    initSpatialData()
  } else if (operateIndex == 1) {
    clearAuxiliaryLine()
    routerLineGroup.remove(routeList[dataIndex].object)
    changeFlag(routeList, dataIndex, 'delete')
    activeRouteIndex.value = -1
  }
}

// 删除素材触发
const deleteSource = (i: number, index: number) => {
  if (index == -99) {
    const currentSource = publicSourceGroup.getObjectByName(materialMetaDtoList[i].id + '')
    changeFlag(materialMetaDtoList, i, 'delete')
    currentSource?.remove(currentSource.children[0])
    publicSourceGroup.remove(currentSource)
  } else {
    const currentPlan = scene.getObjectByName(areaList[index].object.uuid)
    const currentSource: any = currentPlan?.getObjectByName(areaList[index].materialDto[i].id + '')
    changeFlag(areaList[index].materialDto, i, 'delete')
    currentSource?.remove(currentSource.children[0])
    currentPlan?.remove(currentSource)
  }
  if (currentActiveSource.value) {
    initSpatialData()
  }
}

// 显示素材名字
const activeSource = (i: number, index: number, isActive?: boolean) => {
  if (index == activeAreaIndex.value) return
  let currentSource: any = null;
  if (index == -99) {
    currentSource = publicSourceGroup.getObjectByName(materialMetaDtoList[i].id + '')
  } else {
    const currentPlan = scene.getObjectByName(areaList[index].object.uuid)
    currentSource = currentPlan?.getObjectByName(areaList[index].materialDto[i].id + '')
  }

  if (currentSource) {
    currentSource.children[0].visible = isActive
  }
}

// 清除资源选中状态
const clearActiveSource = () => {
  if (currentActiveSource.value) {
    const baseUrl = currentActiveSource.value.userData.baseUrl;
    const baseUrlArr = baseUrl.split('.')
    currentActiveSource.value.loadTexture(baseUrlArr[0] + '_a.' + baseUrlArr[1])
    currentActiveSource.value = null
  }
  spatialData.value.position = null
  spatialData.value.rotate = null
  spatialData.value.scale = null
  activeSourceName.text = ''
}

// 选中素材高亮交互
const activeSourceMaterial = (i: number, index: number, isActive?: boolean, once?: boolean) => {
  if (!once) {
    props.activeSource3D(i, index, isActive)
  }
  let currentSource: any = null;
  if (index == -99) {
    currentSource = publicSourceGroup.getObjectByName(materialMetaDtoList[i].id + '')
  } else {
    const currentPlan = scene.getObjectByName(areaList[index].object.uuid)
    currentSource = currentPlan?.getObjectByName(areaList[index].materialDto[i].id + '')
  }

  const activeUuid = currentActiveSource.value?.uuid;
  clearActiveSource()
  // 左侧二次点击取消高亮
  if (activeUuid === currentSource?.uuid && !once) {
    if (activeAreaIndex.value != -99) {
      initSpatialData()
    }
    return
  }
  if (currentSource) {
    const baseUrl = currentSource.userData.baseUrl;
    if (baseUrl) {
      const baseUrlArr = baseUrl.split('.')
      currentSource.loadTexture(baseUrlArr[0] + (isActive ? '_h.' : '_a.') + baseUrlArr[1])
      currentActiveSource.value = isActive ? currentSource : null;
      if (isActive) {
        initSpatialData(currentSource.position.clone())
        activeSourceName.text = currentSource.name
      }
    }
    if (index >= 0) {
      currentSource.userData.aIndex = index
    }
    currentSource.userData.sIndex = i
  }
}

const changeSourcePosition = (point: Vector3, object?: any) => {
  object.userData.point = point.clone()
  if (object.userData.aIndex !== undefined) {
    areaList[object.userData.aIndex].materialDto[object.userData.sIndex].point = point.clone()
    areaList[object.userData.aIndex].flag = 'update'
    changeFlag(areaList[object.userData.aIndex].materialDto, object.userData.sIndex, 'update')
  } else if (object.userData.sIndex !== undefined) {
    materialMetaDtoList[object.userData.sIndex].point = point.clone()
    changeFlag(materialMetaDtoList, object.userData.sIndex, 'update')
  }
}

const changeSourcePositionFrom3D = (point: any) => {
  currentActiveSource.value.setVertex(point.clone())
  changeSourcePosition(point.clone(), currentActiveSource.value)
  initSpatialData(point.clone())
}


// ============================= 导览路线相关 ===========================
// 路线回撤到上一步
const handlePrev = () => {
  const currentRouteInfo = routeList[activeRouteIndex.value];
  if (!currentRouteInfo || !currentRouteInfo.vertexs) return
  if (!beforeBack) {
    beforeBack = [...currentRouteInfo.vertexs];
  }
  currentRouteInfo.vertexs.pop()
  handleDblClickCanvas(null, true)

  // 当只剩一个点时候，清除该路线
  if (currentRouteInfo.vertexs.length == 1) {
    clearAuxiliaryLine()
    routerLineGroup.remove(currentRouteInfo.object)
    currentRouteInfo.state = 'none'
    currentRouteInfo.vertexs = []
    waitdrawing.value = false
  }
}

// 路线取消回撤一步
const handleNext = () => {
  const currentRouteInfo = routeList[activeRouteIndex.value];
  if (!currentRouteInfo || !currentRouteInfo.vertexs.length) return
  if (beforeBack && beforeBack.length > currentRouteInfo.vertexs.length) {
    currentRouteInfo.vertexs.push(beforeBack[currentRouteInfo.vertexs.length])
  }
  handleDblClickCanvas(null, true)
}

// 选择切换路线样式
const changeRouterStyle = (styles: any, hideStyle: boolean) => {
  if (activeRouteIndex.value == -1) return
  if (hideStyle != undefined) {
    routeList[activeRouteIndex.value].visible = hideStyle ? 0 : 1
    spatialData.value.visible = hideStyle ? 0 : 1
    return
  }
  routeList[activeRouteIndex.value].routeStyle = styles[0] + ''
  routeList[activeRouteIndex.value].guideStyle = styles[1] + ''
  changeFlag(routeList, activeRouteIndex.value, 'update')
}

// 新生成路线或者切换路线触发
const handleActiveRoute = (data: any, index: number, isCreate?: boolean) => {
  if (isCreate) {
    if (data[index].id) {
      const { guideRouteName, guideStyle, routeStyle, id, visible } = data[index];
      const d: any = { vertexs: data[index].routePoint.map((f: any) => new Vector3(f.x, f.y, f.z)), state: 'complete', guideRouteName, guideStyle, routeStyle, id, visible }
      spatialData.value.visible = d.visible
      if (d.vertexs.length) {
        d.object = canvasRef.value.drawDashLine({ vertexs: [...d.vertexs] }, 'line')
        const firstPiont = new CPoint({ vertex: d.vertexs[0], size: 20, url: 'png/first-point.png' })
        const lastPoint = new CSprite({ vertex: d.vertexs[d.vertexs.length - 1], scale: new Vector3(0.7, 0.7, 0.7), url: 'png/last-point.png', noraycast: true })
        routerLineGroup.add(d.object)
        d.object.add(firstPiont)
        d.object.add(lastPoint)
        d.firstPiont = firstPiont
        d.lastPoint = lastPoint
        if (d.id == guideRouteId.value) {
          spatialData.value.routeStyle = d.routeStyle
          spatialData.value.guideStyle = d.guideStyle
          d.state = 'drawing'
          d.flag = 'update'
          activeRouteIndex.value = index
        }
        routeList.push(d)

        // 计算箭头的角度
        const lastPoints = d.vertexs.slice(-2)
        const p1 = worldToScreenVector(lastPoints[0].clone().add(new Vector3(1, 0, 0)))
        const p2 = worldToScreenVector(lastPoints[0].clone())
        const p3 = worldToScreenVector(lastPoints[1].clone())
        const angle = getAngle(p3.clone().sub(p2), p1.clone().sub(p2))
        d.lastPoint.setRotate(angle)
      } else {
        d.object = canvasRef.value.drawDashLine({ vertexs: [new Vector3(), new Vector3()] }, 'line')
        routerLineGroup.add(d.object)
        d.firstPiont = null
        d.lastPoint = null
        if (d.id == guideRouteId.value) {
          spatialData.value.routeStyle = d.routeStyle
          spatialData.value.guideStyle = d.guideStyle
          d.flag = 'update'
          activeRouteIndex.value = index
        }
        routeList.push(d)
      }
    } else {
      routeList[index] = { guideRouteName: data[index].title, vertexs: [], state: 'none', flag: 'add', routeStyle: '0', guideStyle: '1', visible: 1 }
      spatialData.value.routeStyle = '0'
      spatialData.value.guideStyle = '1'
      activeRouteIndex.value = index
      spatialData.value.visible = 1
    }
  } else {
    handleDblClickCanvas(null, true)
    spatialData.value.routeStyle = routeList[index].routeStyle
    spatialData.value.guideStyle = routeList[index].guideStyle
    spatialData.value.visible = routeList[index].visible
    activeRouteIndex.value = index
    changeFlag(routeList, index, 'update')
    if (routeList[index].vertexs.length) {
      routeList[index].state = 'drawing'
    } else {
      routeList[index].state = 'none'
    }
  }
}

// 修改路线名字触发
const handleChangeRoute = (title: string, index: number) => {
  routeList[index].guideRouteName = title
  changeFlag(routeList, index, 'update')
}

// 清除虚线
const clearAuxiliaryLine = () => {
  const currentRouteInfo = routeList[activeRouteIndex.value];
  if (!currentRouteInfo) return
  routerLineGroup.remove(currentRouteInfo.dashObject)
  routerLineGroup.remove(currentRouteInfo.auxiliaryLine)
  currentRouteInfo.dashObject = null
  currentRouteInfo.auxiliaryLine = null
}


const changeRightValue = (value: any, key: string, type: string) => {
  if (currentActiveSource.value) {
    // 选中资源
    const point = currentActiveSource.value.userData.point.clone()
    point[key] = value;
    currentActiveSource.value.position.set(point.x, point.y, point.z)
    changeSourcePosition(point, currentActiveSource.value)
    props.set3DLocation(point.clone())
  } else if (activeAreaIndex.value >= 0) {
    changeFlag(areaList, activeAreaIndex.value, 'update')
    const areaObjectInfo = areaList[activeAreaIndex.value]
    if (type == 'position') {
      const point = areaObjectInfo.object.position.clone()
      point[key] = value;
      const point2D = worldToScreenVector(point)
      areaObjectInfo.object.setVertexs(initSize.x * areaObjectInfo.scale.x, initSize.y * areaObjectInfo.scale.y, point2D)
    } else if (type == 'rotate') {
      const oldRotate = areaList[activeAreaIndex.value].rotate
      const newRotate = angleToRadians(value)
      areaObjectInfo.object.rotateY(oldRotate - newRotate)
      areaList[activeAreaIndex.value].rotate = newRotate

      const currentRotate = areaObjectInfo.object.children[1].children[0] // 旋转图标
      currentRotate.userData.oldRotate = oldRotate
      currentRotate.setRotate(-newRotate)
    } else if (type == 'scale') {
      const scale = areaList[activeAreaIndex.value].scale
      if (value < 0.6) return
      scale[key == 'z' ? 'y' : key] = value
      areaList[activeAreaIndex.value].scale = scale.clone()

      const position = areaObjectInfo.object.userData.center.clone()
      areaObjectInfo.object.setVertexs(initSize.x * scale.x, initSize.y * scale.y, position);
      areaObjectInfo.object.setUV(scale.x, scale.y)
    }
  }
}

// 在互动区域和导览路线之间相互切换
const changeActiveIndex = (index: number) => {
  operateIndex.value = index
  areaList.filter((d: any) => d.flag != 'delete').forEach((e: any, i: number) => {
    if (index == 0 && activeAreaIndex.value == i) {
      e.object.children[0].setHeightStyle()
      e.object.children[1].children[0].visible = true
    } else {
      e.object.children[0].setBaseStyle()
      e.object.children[1].children[0].visible = false
    }
  });

  if (activeAreaIndex.value == -99) {
    publicSourceGroup.children.forEach((s: any) => {
      s.children[0].visible = (index == 0)
    })
  }

  clearActiveSource();
}

// 设置右侧边框初始显示
const initSpatialData = (position?: Vector3) => {
  if (!position) {
    // 取消当前高亮素材
    clearActiveSource()
    const areaInfo = areaList[activeAreaIndex.value]
    if (areaInfo && areaInfo.object) {
      spatialData.value.position = areaInfo.object.position.clone()
      spatialData.value.rotate = areaInfo.rotate
      spatialData.value.scale = areaInfo.scale
    }
  } else {
    spatialData.value.position = position.clone()
    spatialData.value.rotate = null
    spatialData.value.scale = null
  }
}

// 保存场景信息
const saveSceneInfo = () => {
  // 保存时候防抖
  if (saveloading.value) return
  submitData.guideRouteDtoList = routeList.map((e: any) => {
    const d = {
      // guideStyle: e.guideStyle || '0',
      visible: e.visible,
      routeStyle: e.routeStyle || '1',
      sceneId: submitData.sceneId,
      routePoint: e.vertexs.length > 1 ? [...e.vertexs] : [],
      flag: e.flag,
      guideRouteName: e.guideRouteName,
      id: e.id
    }
    if (!e.flag) delete d.flag
    return d
  })
  submitData.interactionDtoList = areaList.map((e: any) => {
    const n = (e.object.userData.linePoints[1].clone().sub(e.object.position)).normalize();
    const quaternion = new Quaternion();
    quaternion.setFromAxisAngle(new Vector3(0, 1, 0), -e.object.userData.rotate);
    const v = n.clone().applyQuaternion(quaternion);
    v.x = +(v.x.toFixed(4))
    v.y = +(v.y.toFixed(4))
    v.z = +(v.z.toFixed(4))
    const d = {
      flag: e.flag,
      interactionName: e.title,
      sceneId: submitData.sceneId,
      location: e.object.position.clone(),
      scale: new Vector3(e.scale.x, 1, e.scale.y),
      rotation: new Vector3(0, radiansToAngle(e.rotate), 0),
      interactionOrientation: v,
      id: e.id,
      materialMetaDtoList: e.materialDto.filter((a: any) => !a.groupName).map((f: any) => {
        const s: any = {
          location: f.point,
          materialId: f.materialId,
          sceneId: submitData.sceneId,
          materialAffiliation: f.materialAffiliation,
          id: f.id
        }
        if (f.flag) {
          s.flag = f.flag
        }
        if (!f.flag || f.flag == 'add') {
          s.rotation = new Vector3(0, 0, 0)
          s.scale = new Vector3(1, 1, 1)
        }
        if (typeof s.id == 'string') delete s.id
        return s
      }),
      materialGroupDtoList: e.materialDto.filter((a: any) => a.groupName).map((f: any) => {
        const s: any = {
          location: f.point,
          interactionId: e.id,
          flag: 'update',
          id: f.materialId,
          sceneId: submitData.sceneId,
        }
        if (f.flag) {
          s.flag = f.flag
        }
        if (typeof s.interactionId == 'string') delete s.interactionId
        return s
      })
    }
    if (!e.flag) delete d.flag
    if (typeof e.id == 'string') delete d.id
    return d
  })
  submitData.outerMaterialMetaDtoList = materialMetaDtoList.filter((a: any) => !a.groupName).map((f: any) => {
    const s: any = {
      location: f.point,
      materialId: f.materialId,
      sceneId: submitData.sceneId,
      materialAffiliation: f.materialAffiliation,
      id: f.id,
    }
    if (f.flag) {
      s.flag = f.flag
    }
    if (!f.flag || f.flag == 'add') {
      s.rotation = new Vector3(0, 0, 0)
      s.scale = new Vector3(1, 1, 1)
    }
    if (typeof s.id == 'string') delete s.id
    return s
  })
  submitData.outerMaterialGroupDtoList = materialMetaDtoList.filter((a: any) => a.groupName).map((f: any) => {
    const s: any = {
      location: f.point,
      flag: 'update',
      id: f.materialId,
      sceneId: submitData.sceneId,
    }
    if (f.flag) {
      s.flag = f.flag
    }
    return s
  })

  saveloading.value = true
  // 更新场景之前先截图保存
  const renderer = (window as any).renderer;
  const camera = (window as any).camera;
  renderer.render(scene, camera)
  let imgData = renderer.domElement.toDataURL("image/jpeg")
  saveScene({ ...submitData, base64Pic: imgData }).then((res: any) => {
    ElMessage.success('项目保存成功！')
    setTimeout(() => {
      operateIndex.value = 0
      getInitData()
      props.refresh3D()
    }, 1000)
    const { sceneName, stadiumName, spaceDescribe } = currentScene.value;
    updateSceneMeta({ sceneName, stadiumName, spaceDescribe, id: submitData.sceneId }).then((res: any) => {
      // ElMessage.success('项目保存成功！')
    })
  })
}

// 修改场景元信息
const changeSceneValue = (val: any, key: any) => {
  currentScene.value[key] = val;
}

// 鼠标拖动停止事件
const handleIconUp = (e: any) => {
  if (dragingImg.value) {
    const canvasLeftWidth = (document.querySelector('.canvas-left') as any).getBoundingClientRect().width
    updateMouseStyle(MouseStyle.default, true)

    if (canvasLeftWidth < e.clientX) {
      const point = screenToWorldVector(e.clientX, e.clientY)
      const sourcePoint = new CSprite({ vertex: point, scale: new Vector3(1.2, 1.2, 1.2), url: dragingImg.value, type: 'source', marks: currentSource.materialName })
      sourcePoint.name = new Date().getTime() + '' // 这里区分名字便于后面的删除
      if (activeAreaIndex.value >= 0) {
        const currentPlan = scene.getObjectByName(areaList[activeAreaIndex.value].object.uuid)
        currentPlan?.add(sourcePoint)
        areaList[activeAreaIndex.value].materialDto.push({ ...currentSource, point, flag: 'add', id: sourcePoint.name, materialId: currentSource.id })
      } else {
        materialMetaDtoList.push({ ...currentSource, point, flag: 'add', id: sourcePoint.name, materialId: currentSource.id })
        publicSourceGroup.add(sourcePoint)
      }

      props.add3DModel(point, currentSource, activeAreaIndex.value)
      currentSource = null
    }
    hideDragImg()
  }
}

const addModelFrom3D = (currentSource: any, activeAreaIndex: number, callback?: any) => {
  dragingImg.value = materialUrls[currentSource.materialType].url_a
  const point = new Vector3(0, 0, 0)
  const sourcePoint = new CSprite({ vertex: point, scale: new Vector3(1.2, 1.2, 1.2), url: dragingImg.value, type: 'source', marks: currentSource.materialName })
  sourcePoint.name = new Date().getTime() + '' // 这里区分名字便于后面的删除
  if (activeAreaIndex >= 0) {
    const currentPlan = scene.getObjectByName(areaList[activeAreaIndex].object.uuid)
    currentPlan?.add(sourcePoint)
    areaList[activeAreaIndex].materialDto.push({ ...currentSource, point, flag: 'add', id: sourcePoint.name, materialId: currentSource.id })
  } else {
    materialMetaDtoList.push({ ...currentSource, point, flag: 'add', id: sourcePoint.name, materialId: currentSource.id })
    publicSourceGroup.add(sourcePoint)
  }
  dragingImg.value = ''
}


// ===================== canvas交互相关 =================
// 点击canvas交互事件
const handleClickCanvas = (point: Vector3) => {
  if (operateIndex.value === 1) {
    const currentRouteInfo = routeList[activeRouteIndex.value];
    if (!currentRouteInfo) return
    if (currentRouteInfo.state !== 'complete') {
      beforeBack = null;
      currentRouteInfo.vertexs.push(point)
      if (currentRouteInfo.state === 'none') { // 生成线
        currentRouteInfo.object = canvasRef.value.drawDashLine({ vertexs: [...currentRouteInfo.vertexs] }, 'line')

        // 起点
        const firstPiont = new CPoint({ vertex: point, size: 20, url: 'png/first-point.png' })
        currentRouteInfo.firstPiont = firstPiont

        // 终点
        const lastPoint = new CSprite({ vertex: new Vector3(1000, 1000, 1000), scale: new Vector3(0.7, 0.7, 0.7), url: 'png/last-point.png', noraycast: true })
        currentRouteInfo.lastPoint = lastPoint

        currentRouteInfo.object.add(firstPiont)
        currentRouteInfo.object.add(lastPoint)
        routerLineGroup.add(currentRouteInfo.object)

        currentRouteInfo.state = 'drawing'
      } else { // 更新线
        clearAuxiliaryLine()
        currentRouteInfo.vertexs = adsorption([...currentRouteInfo.vertexs], currentAngle, currentRouteInfo.lastPoint)
        currentRouteInfo.object.setVertexs([...currentRouteInfo.vertexs])
      }
    }
  }
}

// canvas鼠标抬起事件
const handleMouseUpCanvas = (point: Vector3) => {
  if (operateIndex.value === 0) {
    if (dragingObject) { // 拖拽互动区域
      dragingObject = null
      updateMouseStyle(MouseStyle.default, true)
    }

    if (controlPoint) { // 取消控制点
      controlPoint = null
      updateMouseStyle(MouseStyle.default, true)
    }

    // 取消旋转
    if (rotateObject && rotateObject.userData.state == 'view') {
      changeRotateState('clear')
      areaRotate = false
      updateMouseStyle(MouseStyle.default, true)
    }

    if (sourceObject) { // 素材二次拖拽
      changeSourcePosition(point.clone(), sourceObject)
      activeSourceName.text = sourceObject.name
      sourceObject = null
      updateMouseStyle(MouseStyle.default, true)
      props.set3DLocation(point.clone())
    }
  }
}

// canvas双击事件，画线时候双击确认
const handleDblClickCanvas = (e: any, deleteLastPoint?: boolean, waitdDrawing?: boolean) => {
  const currentRouteInfo = routeList[activeRouteIndex.value];
  if (!currentRouteInfo) {
    return
  } else if (currentRouteInfo.vertexs > 1) {
    const routerLength = currentRouteInfo.vertexs.length;
    const lastDistance = currentRouteInfo.vertexs[routerLength - 1].distanceTo(currentRouteInfo.vertexs[routerLength - 2]);
    if (lastDistance && !deleteLastPoint && !waitdDrawing) return // 两击之间有位移不定义为双击事件
  }

  // 关闭绘图状态
  currentRouteInfo.state = 'complete'

  // 清除多余元素
  clearAuxiliaryLine()

  if (!deleteLastPoint) {
    // 更新实线段,因为双击点了两次，最后一次去掉
    currentRouteInfo.vertexs = currentRouteInfo.vertexs.slice(0, -1)
  }

  if (currentRouteInfo.vertexs.length) {
    if (!deleteLastPoint) {
      // 这里调用方法做导览路线的吸附
      currentRouteInfo.vertexs = adsorption([...currentRouteInfo.vertexs], currentAngle, currentRouteInfo.lastPoint)
    } else {
      // 切换路线时候的箭头方向和位置的重置
      const lastPoints = [...currentRouteInfo.vertexs].slice(-2)
      if (lastPoints.length == 2) {
        const p2 = worldToScreenVector(lastPoints[0])
        const p3 = worldToScreenVector(lastPoints[1])
        const p4 = worldToScreenVector(lastPoints[0].clone().add(new Vector3(1, 0, 0)))
        const angle2 = getAngle(p3.clone().sub(p2), p4.clone().sub(p2))
        currentRouteInfo.lastPoint.setRotate(angle2)
        currentRouteInfo.lastPoint.setVertex(lastPoints[1])
      }
    }

    currentRouteInfo.object.setVertexs(currentRouteInfo.vertexs)

    if (waitdDrawing) {
      waitdrawing.value = waitdDrawing;
    }
  }
}

// 鼠标按下事件
const handleMouseDownCanvas = (point: Vector3) => {
  if (operateIndex.value === 0) {
    let ray = new Raycaster()
    ray.set(point, new Vector3(0, 1, 0))
    const r1 = ray.intersectObjects(scene.children, true);
    if (r1.length) {
      const pointObject: any = r1.filter((target) => target.object.type === 'Points')[0]
      if (activeAreaIndex.value !== -1 && activeAreaIndex.value !== -99) {
        const rayPlan: any = r1.filter((target) => target.object.uuid === areaList[activeAreaIndex.value].object.uuid)[0]
        if (pointObject && pointObject.object?.userData.areaIndex == activeAreaIndex.value) { // 互动区域缩放
          controlPoint = { object: pointObject.object, point: pointObject.point }
          if ([0, 3, 4, 7].includes(controlPoint.object.userData.index)) {
            updateMouseStyle(MouseStyle.mouseScaleLR, true)
          }
          if ([1, 2, 5, 6].includes(controlPoint.object.userData.index)) {
            updateMouseStyle(MouseStyle.mouseScaleTB, true)
          }
        } else if (rotateObject && rotateObject.userData.state == 'active') { // 互动区域旋转
          changeRotateState('view')
          areaRotate = true
          startRotatePoint = point.clone()
        } else if (rayPlan && rayPlan.object?.userData.areaIndex == activeAreaIndex.value) { // 互动区域移动
          dragingObject = { ...areaList[activeAreaIndex.value] }
          updateMouseStyle(MouseStyle.mouseDrag, true)
        }
        initSpatialData()
        changeFlag(areaList, activeAreaIndex.value, 'update')
      }

      const sourcePoint: any = r1.filter((target) => target.object.type === 'Sprite')[0]
      if (sourcePoint && sourcePoint.object?.userData.type == 'source') {
        sourceObject = sourcePoint.object
        const areaIndex = areaList.findIndex((e: any) => e.object.uuid === sourceObject.parent.name)
        if (areaIndex !== -1) {
          sourceObject.userData.aIndex = areaIndex; // 当前拖动资源的区域index
          const sourceIndex = areaList[areaIndex].materialDto.findIndex((e: any) => e.id == sourceObject.name)
          sourceObject.userData.sIndex = sourceIndex
          props.activeSource3D(sourceIndex, areaIndex, true)
        } else {
          const sourceIndex = materialMetaDtoList.findIndex((e: any) => e.id == sourceObject.name)
          sourceObject.userData.sIndex = sourceIndex
          props.activeSource3D(sourceIndex, -99, true)
        }
        updateMouseStyle(MouseStyle.mouseDrag, true)


        // 取消当前高亮素材
        if (currentActiveSource.value) {
          const baseUrl = currentActiveSource.value.userData.baseUrl;
          const baseUrlArr = baseUrl.split('.')
          currentActiveSource.value.loadTexture(baseUrlArr[0] + '_a.' + baseUrlArr[1])
        }
        // 被拖拽的素材添加高亮
        const baseUrl = sourcePoint.object.userData.baseUrl;
        const baseUrlArr = baseUrl.split('.')
        initSpatialData(sourcePoint.object.position.clone())
        sourcePoint.object.loadTexture(baseUrlArr[0] + '_h.' + baseUrlArr[1])
        currentActiveSource.value = sourcePoint.object;
        activeSourceName.text = sourcePoint.object.name
      }
    }
  }
}

// 鼠标移出canvas事件
const handleMouseLeaveCanvas = () => {
  const currentRouteInfo = routeList[activeRouteIndex.value];
  if (currentRouteInfo?.state == "drawing") {
    handleDblClickCanvas(null, true, true)
  }
}

// 在canvas上悬浮时修改交互图片位置
const handleMouseMoveCanvas = (point: Vector3, point2D: Vector2) => {
  // 判断是否是画路线的暂停状态
  if (waitdrawing.value) {
    routeList[activeRouteIndex.value].state = 'drawing'
    waitdrawing.value = false
  }

  let ray = new Raycaster()
  ray.set(point, new Vector3(0, 1, 0))
  const r1 = ray.intersectObjects(scene.children, true);

  // 判断是否拾取资源
  let hoverSource: any = r1.filter((target) => target.object.userData.type === 'source')[0]
  if (hoverSource) {
    hoverSourceName.value = hoverSource.object.name
    if (!hoverSource.object.children[0].visible) {
      hoverSourceObject = hoverSource.object
      hoverSourceObject.children[0].visible = true
    }
  } else {
    hoverSourceName.value = ''
    if (hoverSourceObject) {
      hoverSourceObject.children[0].visible = false
      hoverSourceObject = null
    }
  }

  // 拾取旋转图标
  if (r1.length) {
    const pointObject: any = r1.filter((target) => target.object.name === 'csprite')[0]
    if (pointObject && pointObject.object?.userData.type == 'rotatePoint' && pointObject.object.parent.parent.userData.areaIndex == activeAreaIndex.value) {
      if (pointObject.object.userData.state == 'default') {
        rotateObject = pointObject.object
        rotateObject.loadTexture(require("@/assets/images/background/rotate-active.png"))
        rotateObject.userData.state = 'active'
        rotateObject.userData.startRotate = areaList[activeAreaIndex.value].rotate
      }
      if (!areaRotate) return
    } else if (rotateObject && rotateObject.userData.state == 'active') {
      changeRotateState('clear')
    }
  } else if (rotateObject && rotateObject.userData.state == 'active') {
    changeRotateState('clear')
  }
  if (operateIndex.value === 0) {
    if (dragingObject) { // 互动区域拖拽
      dragingObject.object.setVertexs(initSize.x * dragingObject.scale.x, initSize.y * dragingObject.scale.y, point2D)
      spatialData.value.position = point.clone()
      return
    }

    if (dragingImg.value) { // 素材缩略图拖拽
      handleIconMove({ clientX: point2D.x, clientY: point2D.y })
      return
    }

    if (sourceObject) { // 素材二次拖拽
      sourceObject.setVertex(point)
      initSpatialData(point.clone())
    }

    if (areaRotate) {
      const oldRotate = areaList[activeAreaIndex.value].rotate
      const startRotate = rotateObject.userData.startRotate || 0
      const currentRotate = areaList[activeAreaIndex.value].object.rotatePoint(point, oldRotate, startRotatePoint.clone(), startRotate)
      areaList[activeAreaIndex.value].rotate = currentRotate
      spatialData.value.rotate = currentRotate
      rotateObject.userData.oldRotate = oldRotate
      rotateObject.setRotate(-currentRotate)
      return
    }

    // 拖拽控制点
    if (controlPoint) {
      const oldScale = areaList[activeAreaIndex.value].scale;
      if ([0, 2, 4, 6, 8].includes(controlPoint.object.userData.index)) {
        const scale = areaList[activeAreaIndex.value].object.dragPoint('vertex', point, oldScale, controlPoint.object.userData.index)
        areaList[activeAreaIndex.value].scale = new Vector2(oldScale.x * scale, oldScale.y * scale)
      } else {
        const oldScale = areaList[activeAreaIndex.value].scale;
        const scale = areaList[activeAreaIndex.value].object.dragPoint('vertexCenter', point, oldScale, controlPoint.object.userData.index)
        areaList[activeAreaIndex.value].scale = scale.clone()
      }

      // 缩放之后重置位置
      const center2D = areaList[activeAreaIndex.value].object.userData?.center;
      const centerFor3D = screenToWorldVector(center2D.x, center2D.y);
      spatialData.value.position = centerFor3D.clone()
      spatialData.value.scale = areaList[activeAreaIndex.value].scale
    }
  } else {
    const currentRouteInfo = routeList[activeRouteIndex.value];
    if (!currentRouteInfo) return
    if (currentRouteInfo.state !== 'complete' && currentRouteInfo.vertexs.length) {
      const pointStart = currentRouteInfo.vertexs.slice(-1)[0];
      const pointEnd = point.clone();
      if (!currentRouteInfo.dashObject) {
        currentRouteInfo.dashObject = canvasRef.value.drawDashLine({ vertexs: [pointStart, pointEnd] }, 'linedash')
        routerLineGroup.add(currentRouteInfo.dashObject)
      } else {
        currentRouteInfo.dashObject.setVertexs([pointStart, pointEnd])
        currentRouteInfo.lastPoint.setVertex(pointEnd)

        const p1 = worldToScreenVector(pointStart.clone().add(new Vector3(1, 0, 0)))
        const p2 = worldToScreenVector(pointStart.clone())
        const p3 = worldToScreenVector(pointEnd.clone())
        const angle = getAngle(p1.clone().sub(p2), p3.clone().sub(p2))
        currentRouteInfo.lastPoint.setRotate(-angle)
      }


      // 做辅助线
      const lastPoints = currentRouteInfo.vertexs.slice(-1);
      if (lastPoints?.length) {
        const pz = lastPoints[0].clone().add(new Vector3(0, 0, -1))
        const p1 = worldToScreenVector(pz)
        const p2 = worldToScreenVector(lastPoints[0])
        const p3 = worldToScreenVector(point.clone())
        const v1 = p1.clone().sub(p2) // 延左后一个点向上做辅助线
        const v2 = p3.clone().sub(p2) // 当前变化的虚线
        const angle = getAngle(v1, v2);
        currentAngle = Math.round(angle / Math.PI * 4) * (Math.PI / 4)
        const targetPoint = pointRotate(pz, lastPoints[0], currentAngle)
        const finalPoint = lastPoints[0].clone().add((targetPoint.clone().sub(lastPoints[0])).normalize().setLength(10))
        let auxiliaryLine = routerLineGroup.getObjectByName('auxiliaryLine')
        if (auxiliaryLine) {
          auxiliaryLine.setVertexs([lastPoints[0], finalPoint])
        } else {
          auxiliaryLine = canvasRef.value.drawDashLine({ vertexs: [lastPoints[0], finalPoint] }, 'linedash', 1)
          auxiliaryLine.name = 'auxiliaryLine'
          routerLineGroup.add(auxiliaryLine)
          currentRouteInfo.auxiliaryLine = auxiliaryLine
        }
      }
    }
  }
}

// 切换旋转图片状态
const changeRotateState = (state: string) => {
  if (state == 'view') {
    rotateObject.loadTexture(require("@/assets/images/background/rotate-view.png"))
    rotateObject.userData.state = 'view'
  } else {
    rotateObject.loadTexture(require("@/assets/images/background/rotate-default.png"))
    rotateObject.userData.state = 'default'
    rotateObject = null
  }
}

// 点击图片时候修改交互图片的位置
const sourcePoolMourseDown = (e: any, url: string, data: any) => {
  if (operateIndex.value === 0) {
    updateMouseStyle(MouseStyle.mouseDrag, true)
    dragingImg.value = url
    handleIconMove(e)
    currentSource = { ...data }
  }
}

const scalePlus = () => { // 点击放大场景
  if (controls) {
    const zoom = controls.object.zoom;
    controls.object.zoom = Math.min(zoom + 0.1, controls.maxZoom);
    controls.object.updateProjectionMatrix();
    controls.update()
    sceneScale.value = controls.object.zoom
  }
}

const scaleMinus = () => { // 点击缩小场景
  if (controls) {
    const zoom = controls.object.zoom || 1;
    controls.object.zoom = Math.max(zoom - 0.1, controls.minZoom);
    controls.object.updateProjectionMatrix();
    controls.update();
    sceneScale.value = controls.object.zoom
  }
}

const resetScale = () => {
  controls.object.zoom = 1;
  controls.object.updateProjectionMatrix();
  controls.update();
  sceneScale.value = 1
}

const handleFullScreen = () => {
  let element = document.documentElement;     // 返回 html dom 中的root 节点 <html>
  // 判断浏览器设备类型
  if (!has_full.value) {
    element.requestFullscreen && element.requestFullscreen();
  } else {
    document.exitFullscreen && document.exitFullscreen();
  }
}

// 拖动素材时候悬浮交互
const handleIconMove = (e: any) => {
  if (dragingImg.value) {
    Icondom.style.left = e.clientX - 25 + 'px'
    Icondom.style.top = e.clientY - 25 + 'px'
  }
  initSpatialData(screenToWorldVector(e.clientX, e.clientY))
}

// 清空素材交互
const hideDragImg = () => {
  dragingImg.value = ''
  Icondom.style.left = '-50%'
  Icondom.style.top = "-50%"
}

onMounted(() => {
  // 初始化控制器
  controls = (window as any).controls;
  scene = (window as any).scene;

  getInitData()

  Icondom = (document.querySelector('.show-icon') as any)

  document.querySelector('.source-pool-box')?.addEventListener('mouseup', () => {
    updateMouseStyle(MouseStyle.default, true)
    hideDragImg()
  })
  document.querySelector('.source-pool-box')?.addEventListener('mousemove', handleIconMove)

  document.addEventListener('fullscreenchange', (e: any) => {
    if (!has_full.value) {
      has_full.value = true
    } else {
      has_full.value = false
    }
  })

  controls.addEventListener("change", changeControls);
})

const changeControls = () => {
  const zoom = controls.object.zoom;
  sceneScale.value = zoom
}

onUnmounted(() => {
  document.querySelector('.source-pool-box')?.removeEventListener('mousemove', handleIconMove)
  controls.removeEventListener("change", changeControls);
  for (let i = 0; i < scene.children.length; i++) {
    // 删除文字标记
    scene.children[i].traverse((e: any) => {
      if (e.element) {
        e.parent?.remove(e)
      }
    })
    scene.remove(scene.children[i])
    i--
  }
})

watch(on_off, (newState) => {
  modifySceneOpenStatus({ status: newState ? '1' : '0', sceneId: submitData.sceneId }).then((res: any) => {
  })
})

watch(activeRouteIndex, (newState) => {
  waitdrawing.value = false
})


defineExpose({
  addModelFrom3D,
  activeSourceMaterial,
  changeSourcePositionFrom3D,
  exitEdit,
  getInitData
})
</script>

<style scoped lang="less">
.scene-edit {
  width: 100%;
  height: 100%;
  background-color: #fff;
  user-select: none;
}

.zoom-operation {
  position: relative;
  position: absolute;
  right: 347px;
  padding: 0 4px;
  bottom: 28px;

  img {
    display: block;
    margin-top: 6px;
    cursor: pointer;
  }

  .scene-scale {
    position: absolute;
    top: -15px;
    cursor: pointer;
  }
}

.canvas-tool {
  width: 354px;
  height: 48px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 13px;
  position: fixed;
  top: 118px;
  left: 50%;
  margin-left: -210px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 20px;

  span {
    margin-right: 10px;
    width: calc(100% - 190px);
    max-width: 165px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;
    text-align: left;
  }

  .canvas-tool-icon-box {
    width: 152px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .canvas-tool-icon {
    width: 32px;
    height: 32px;
    cursor: pointer;
    border-radius: 8px;
  }

  .revoke {
    background: url(~@/assets/images/icon/revoke.png);
    background-size: 100% 100%;
    position: relative;

    &:hover {
      background: url(~@/assets/images/icon/revokeA.png);

      &::before {
        content: '\64a4\9500';
        position: absolute;
        left: -3px;
        top: 39px;
        font-size: 12px;
        width: 38px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        background-color: #333537;
        letter-spacing: 1px;
        border-radius: 4px;
        color: #fff;
      }

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 27px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #333537;
      }
    }
  }

  .save-scene {
    background: url(~@/assets/images/icon/save_scene.png);
    background-size: 100% 100%;
    position: relative;

    &:hover {
      background: url(~@/assets/images/icon/save_sceneA.png);
      background-size: 100% 100%;

      &::before {
        content: '\4fdd\5b58';
        position: absolute;
        left: -3px;
        top: 39px;
        font-size: 12px;
        width: 38px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        background-color: #333537;
        letter-spacing: 1px;
        border-radius: 4px;
        color: #fff;
      }

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 27px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #333537;
      }
    }
  }

  .revoke-r {
    background: url(~@/assets/images/icon/revoke-r.png);
    background-size: 100% 100%;
    position: relative;

    &:hover {
      background: url(~@/assets/images/icon/revoke-rA.png);

      &::before {
        content: '\56de\64a4';
        position: absolute;
        left: -3px;
        top: 39px;
        font-size: 12px;
        width: 38px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        background-color: #333537;
        letter-spacing: 1px;
        border-radius: 4px;
        color: #fff;
      }

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 27px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #333537;
      }
    }
  }

  .full-screen,
  .full-no-screen {
    background: url(~@/assets/images/icon/full-screen.png);
    background-size: 100% 100%;
    position: relative;
  }

  .full-screen {
    &:hover {
      background: url(~@/assets/images/icon/full-screenA.png);

      &::before {
        content: '\5168\5c4f';
        position: absolute;
        left: -3px;
        top: 39px;
        font-size: 12px;
        width: 38px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        background-color: #333537;
        letter-spacing: 1px;
        border-radius: 4px;
        color: #fff;
      }

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 27px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #333537;
      }
    }
  }

  .full-no-screen {
    &:hover {
      background: url(~@/assets/images/icon/full-screenA.png);

      &::before {
        content: '\53d6\6d88\5168\5c4f';
        position: absolute;
        left: -17px;
        top: 39px;
        font-size: 12px;
        width: 66px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        background-color: #333537;
        letter-spacing: 1px;
        border-radius: 4px;
        color: #fff;
      }

      &::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 27px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-bottom-color: #333537;
      }
    }
  }
}

.show-icon {
  width: 50px;
  height: 50px;
  position: fixed;
  left: -50%;
  top: -50%;
  background-size: 100% 100%;
  z-index: 11;
}

@media screen and (max-width: 1200px) {
  .canvas-tool {
    width: 320px;
    height: 38px;
    border-radius: 10px;
    position: fixed;
    top: 108px;
    margin-left: -160px;
    padding: 0 10px;
    font-size: 15px;

    span {
      margin-right: 26px;
    }

    .canvas-tool-icon {
      width: 24px;
      height: 24px;
    }

    img {
      width: 24px;
    }

    img:first-child {
      width: 30px;
    }
  }

  .zoom-operation {
    right: 288px;
    bottom: 18px;
  }

  .canvas-tool {
    span {
      width: calc(100% - 160px);
    }

    .canvas-tool-icon-box {
      width: 122px;
    }
  }
}
</style>