import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import HomeView from '@/views/home/<USER>';
import LoginView2 from '@/views/auth/LoginView2.vue';
import InteractiveArea from '@/views/interactive/list/index.vue';
import RouteList from '@/views/system/list/RouteList.vue';
import UserList from '@/views/user/UserList.vue';
import DeviceList from '@/views/device/DeviceList.vue';
import SourceMaterial from '@/views/material/list/SourceMaterial.vue';
import DiaryList from '@/views/system/list/DiaryList.vue';
import SceneList from '@/views/scene/list/index.vue';
import Package from '@/views/package/list/index.vue';
import RoleList from '@/views/system/list/RoleList.vue';
import SceneEdit from '@/views/scene/edit/index.vue';
import SpaceEditV2 from '@/views/space/edit-v2/index.vue';
import UploadSource from '@/views/material/upload/UploadSource.vue';
import CustomerList from '@/views/user/customer/CustomerList.vue';
import SpaceList from '@/views/space/list/SpaceList.vue';
import DefaultMaterial from '@/views/material/list/DefaultMaterial.vue';
import ExperienceHome from '@/views/experience/home/<USER>';
import ExperienceEdit from '@/views/experience/edit/index.vue';
import ExperienceMaterial from '@/views/material/list/ExperienceMaterial.vue';
import SpaceEdit from '@/views/space/edit/index.vue';
import TemplateSquare from '@/views/template/list/TemplateSquare.vue';
import TemplateManage from '@/views/template/list/TemplateManage.vue';
import PathNavigation from '@/views/path-navigation/list/index.vue'; // 路径导航
import PathNavigationEdit from '@/views/path-navigation/edit/index.vue'; // 路径导航编辑
import ItemListView from '@/views/project/list/ItemListView.vue'; // 我的项目
import AIGenerate from '@/views/ai/AIGenerate.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/path_navigation', // 空间管理
    name: 'path_navigation',
    component: PathNavigation,
  },
  {
    path: '/path_navigation_edit', // 空间管理
    name: 'path_navigation_edit',
    component: PathNavigationEdit,
    meta: {
      hideSide: true,
    },
  },
  {
    path: '/experience_edit', // 编辑页
    name: 'experience_edit',
    component: ExperienceEdit,
    meta: {
      hideSide: true,
    },
  },
  {
    path: '/experience_home', // 首页
    name: 'experience_home',
    component: ExperienceHome,
  },
  {
    path: '/experience_material', // 体验用户素材
    name: 'experience_material',
    component: ExperienceMaterial,
  },
  {
    path: '/home', // 首页
    name: 'home',
    component: HomeView,
  },
  {
    path: '/login', // 登录
    name: 'login',
    alias: '/',
    component: LoginView2,
    meta: {
      hideSide: true,
    },
  },
  {
    path: '/login2', // 登录
    name: 'login2',
    alias: '/',
    component: LoginView2,
    meta: {
      hideSide: true,
    },
  },
  {
    path: '/scenelist', // 场景统计
    name: 'scenelist',
    component: SceneList,
  },
  {
    path: '/arealist', // 互动区域统计
    name: 'arealist',
    component: InteractiveArea,
  },
  {
    path: '/routelist', // 路线统计
    name: 'routelist',
    component: RouteList,
  },
  {
    path: '/customer', // 客户管理
    name: 'customer',
    component: CustomerList,
  },
  {
    path: '/spacelist', // 空间管理
    name: 'spacelist',
    component: SpaceList,
  },
  {
    path: '/package', // 空间管理
    name: 'package',
    component: Package,
  },
  {
    path: '/userlist', // 客户管理
    name: 'userlist',
    component: UserList,
  },
  {
    path: '/rolelist', // 角色管理
    name: 'rolelist',
    component: RoleList,
  },
  {
    path: '/devicelist', // 设备管理
    name: 'devicelist',
    component: DeviceList,
  },
  {
    path: '/square', // 模板管理
    name: 'square',
    component: TemplateSquare,
  },
  {
    path: '/template_manage',
    name: 'template_manage',
    component: TemplateManage,
  },
  {
    path: '/source_material', // 素材管理
    name: 'source_material',
    component: SourceMaterial,
  },
  {
    path: '/default_material', // 运营管理
    name: 'default_material',
    component: DefaultMaterial,
  },
  {
    path: '/diarylist', // 操作日志
    name: 'diarylist',
    component: DiaryList,
  },
  {
    path: '/scene_edit',
    name: 'scene_edit',
    component: SpaceEdit,
    meta: {
      hideSide: true,
    },
  },

  {
    path: '/space_edit',
    name: 'space_edit',
    component: SceneEdit,
    meta: {
      hideSide: true,
    },
  },
  {
    path: '/space_edit_v2',
    name: 'space_edit_v2',
    component: SpaceEditV2,
    meta: {
      hideSide: true,
      hideHeader: true,
    },
  },
  {
    path: '/item_list', // 我的项目
    name: 'item_list',
    component: ItemListView,
  },
  {
    path: '/ai_generate',
    name: 'ai_generate',
    component: AIGenerate,
    meta: {
      hideSide: true,
      hideHeader: true,
    },
  },
];

const router = createRouter({
  history: createWebHashHistory(process.env.BASE_URL),
  routes,
});

export default router;
