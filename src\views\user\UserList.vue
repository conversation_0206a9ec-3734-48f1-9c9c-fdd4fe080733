<template>
  <div>
    <search-form
      :form-keys-data="keysData"
      :search-data-event="searchData"
      currentRoute="/user/list"></search-form>
  </div>
  <div>
    <table-list
      :data="tableData"
      :column-list="columnList"
      create-list="新建用户"
      :change-page="changePage"
      :delete-content="deleteContent"
      :data-total="pageTotal"
      :page-size="searchForm.pageSize"
      :page-no="searchForm.pageNo"
      :operation-items="operationItems"></table-list>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted, reactive } from 'vue';
import TableList from '@/components/TableList.vue';
import SearchForm from '@/components/SearchForm.vue';
import { getOrgnizationUsers, closeUser, reopenUser } from '@/api';
import { userStatus, userTypes } from '@/config';
import type { OperationItems, TableRow } from '@/types/operation';

const pageTotal = ref(0); // 总计数据条数
const tableData: any = ref([]); // 表格数据
const deleteContent = {
  title: '删除用户',
  content: '是否删除此用户？',
};

const searchForm: any = reactive({
  // 查询对象
  pageSize: 10,
  pageNo: 1,
});

const keysData = reactive([
  {
    key: 'userName',
    type: 'input',
    label: '用户名称',
  },
  {
    key: 'userStatus',
    type: 'select',
    label: '用户状态',
    dataList: [
      {
        name: '启用',
        value: 1,
      },
      {
        name: '停用',
        value: 2,
      },
    ],
  },
]);

const editData = (data: any) => {
  if (data.userStatus == '启用') {
    closeUser({ userId: data.adminUserInfo.id }).then((res: any) => {
      getData();
    });
  } else {
    reopenUser({ userId: data.adminUserInfo.id }).then((res: any) => {
      getData();
    });
  }
};

// Operation items configuration
const operationItems = reactive<OperationItems>([
  {
    label: (row: TableRow) => (row.userStatus === '启用' ? '停用' : '启用'),
    key: 'toggle',
    onClick: (row: TableRow) => {
      editData(row);
    },
  },
]);

const columnList = [
  {
    prop: 'id',
    label: '用户id',
  },
  {
    prop: 'username',
    label: '账号',
  },
  {
    prop: 'userType',
    label: '角色',
  },
  {
    prop: 'userStatus',
    label: '状态',
  },
  {
    prop: 'operate',
    label: '操作',
    width: 180,
    type: 'operation',
  },
];

const searchData = (data: any) => {
  for (const key in data) {
    searchForm[key] = data[key];
  }
  getData();
};

const changePage = (cur: any) => {
  searchForm.pageNo = cur;
  getData();
};

const getData = () => {
  getOrgnizationUsers({ ...searchForm }).then((res: any) => {
    pageTotal.value = res.data.total;
    tableData.value = [...res.data.records].map((e: any) => ({
      ...e,
      userStatus: userStatus[e.userStatus],
      userType: userTypes[e.userType],
    }));
  });
};

onMounted(() => {
  getData();
});
</script>
