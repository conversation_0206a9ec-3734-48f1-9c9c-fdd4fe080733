<template>
  <div v-if="show" class="preview-overlay" @click="handleClose">
    <div class="preview-content" @click.stop>
      <img
        src="@/assets/images/close-tips.png"
        @click="handleClose"
        alt="关闭"
        style="width: 36px; height: 36px"
        class="close-icon" />
      <div class="video-container">
        <video
          ref="videoRef"
          class="preview-video"
          :src="videoUrl"
          @timeupdate="onTimeUpdate"
          @loadedmetadata="onLoadedMetadata"
          @ended="onVideoEnded"></video>
        <div class="video-controls">
          <img
            :src="
              playing
                ? require('@/assets/images/pause22.png')
                : require('@/assets/images/play11.png')
            "
            @click="togglePlay"
            class="play-icon"
            alt="播放/暂停" />
          <div class="progress-bar-container" @click="handleProgressClick">
            <div class="progress-bar-bg"></div>
            <div class="progress-bar-fill" :style="{ width: progressPercentage + '%' }"></div>
            <div class="progress-bar-thumb" :style="{ left: progressPercentage + '%' }"></div>
          </div>
          <span class="video-time">{{ formatTime(currentTime) }}/{{ formatTime(duration) }}</span>
        </div>
      </div>
      <div class="preview-actions">
        <button class="preview-btn save-btn" @click="handleSave">保存素材</button>
        <button class="preview-btn delete-btn" @click="handleDelete">删除</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue';

interface Props {
  show: boolean;
  videoUrl: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'save'): void;
  (e: 'delete'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const videoRef = ref<HTMLVideoElement | null>(null);
const playing = ref(false);
const currentTime = ref(0);
const duration = ref(0);

const progressPercentage = computed(() => {
  if (!duration.value) return 0;
  return (currentTime.value / duration.value) * 100;
});

const togglePlay = async () => {
  if (!videoRef.value) return;
  try {
    if (playing.value) {
      await videoRef.value.pause();
      playing.value = false;
    } else {
      await videoRef.value.play();
      playing.value = true;
    }
  } catch (error) {
    console.error('Error toggling play state:', error);
    playing.value = false;
  }
};

const handleProgressClick = (event: MouseEvent) => {
  const container = event.currentTarget as HTMLElement;
  const rect = container.getBoundingClientRect();
  const clickPosition = event.clientX - rect.left;
  const percentage = clickPosition / rect.width;

  if (videoRef.value && duration.value) {
    const newTime = percentage * duration.value;
    videoRef.value.currentTime = Math.max(0, Math.min(newTime, duration.value));
    currentTime.value = videoRef.value.currentTime;
  }
};

const onTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;
  }
};

const onLoadedMetadata = () => {
  if (videoRef.value) {
    duration.value = videoRef.value.duration;
  }
};

const onVideoEnded = () => {
  if (videoRef.value) {
    videoRef.value.currentTime = 0;
    playing.value = false;
  }
};

const handleClose = () => {
  if (videoRef.value) {
    videoRef.value.pause();
    playing.value = false;
  }
  emit('close');
};

const handleSave = () => emit('save');
const handleDelete = () => emit('delete');

const formatTime = (s: number) => {
  const m = Math.floor(s / 60);
  const ss = Math.floor(s % 60);
  return `${m}:${ss.toString().padStart(2, '0')}`;
};

onUnmounted(() => {
  if (videoRef.value) {
    videoRef.value.pause();
  }
});
</script>

<style scoped lang="less">
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.preview-content {
  max-width: 80%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  position: relative;
}

.close-icon {
  position: absolute;
  top: 0px;
  right: -60px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 1001;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.7;
  }
}

.video-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  background: #000;
  display: flex;
  flex-direction: column;
  min-height: 400px; /* 设置最小高度 */
}

.preview-video {
  width: 100%;
  flex: 1;
  min-height: 0; /* 允许flex子项收缩 */
  max-height: calc(70vh - 58px); /* 减去控制栏高度 */
  object-fit: contain;
  background: #000;
}

.video-controls {
  position: relative;
  bottom: 0;
  left: 0;
  right: 0;
  height: 58px;
  min-height: 58px; /* 确保最小高度不被压缩 */
  flex-shrink: 0; /* 防止被压缩 */
  padding: 0 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  gap: 16px;
  border-radius: 0px 0px 0px 0px;
}

.play-icon {
  width: 40px;
  height: 40px;
  cursor: pointer;
  margin-right: 0px;
  transition: opacity 0.2s;

  &:hover {
    opacity: 0.8;
  }
}

.progress-bar-container {
  flex: 1;
  margin: 0 12px;
  height: 8px;
  position: relative;
  cursor: pointer;
  border-radius: 33px;
  overflow: visible;
  width: 262px;
}

.progress-bar-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  box-shadow: inset 0px 0px 5px 0px rgba(0, 0, 0, 0.25);
  border-radius: 33px;
}

.progress-bar-fill {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background: #2973ff;
  border-radius: 33px;
  transition: width 0.1s linear;
}

.progress-bar-thumb {
  position: absolute;
  width: 6px;
  height: 18px;
  background: #2e76ff;
  border-radius: 27px;
  top: 50%;
  transform: translate(-50%, -50%);
  left: calc(v-bind(progressPercentage) * 1%);
  pointer-events: none;
  transition: left 0.1s linear;
}

.video-time {
  min-width: 70px;
  text-align: right;
  color: #333333;
  font-size: 15px;
}

.preview-actions {
  display: flex;
  gap: 16px;
  align-items: center;

  .preview-btn {
    width: 120px;
    height: 40px;
    border-radius: 4px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &.save-btn {
      background: #2e76ff;
      color: #ffffff;
    }

    &.delete-btn {
      background: #c9453e;
      color: #ffffff;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
