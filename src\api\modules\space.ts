import request from '../request';

// 获取空间列表
export function getSpacePage(data: any) {
  return request({
    url: `/space/getSpacePage?pageNo=${data.pageNo}&pageSize=${data.pageSize}&descriptionName=${
      data.descriptionName || ''
    }&sceneName=${data.sceneName || ''}&status=${data.status || ''}`,
    method: 'get',
  });
}

// 获取空间信息
export function getSpace(data: any) {
  return request({
    url: '/space/getSpace?id=' + data.id,
    method: 'get',
  });
}

// 获取空间绑定的场景
export function getSpaceBindScene(data: any) {
  return request({
    url: `/space/getSpaceBindScene?spaceId=${data.spaceId || ''}`,
    method: 'get',
  });
}

// 删除空间
export function deleteSpace(data: any) {
  return request({
    url: `/space/deleteSpace?spaceId=${data.spaceId || ''}`,
    method: 'post',
  });
}

// 更新空间名称
export function updateSpaceName(data: any) {
  let url = `/space/updateSpaceName?spaceId=${data.spaceId || ''}&spaceName=${data.spaceName}`;
  if (data.spacePic) {
    url = `/space/updateSpaceName?spaceId=${data.spaceId || ''}&spaceName=${
      data.spaceName
    }&spacePic=${data.spacePic}`;
  }
  return request({
    url: url,
    method: 'get',
  });
}

// 通过id获取场景
export function getTaskInfo(data: any) {
  return request({
    url: '/space/getTaskInfo?taskId=' + data.taskId,
    method: 'get',
  });
}

// 根据ID获取空间
export function getSpaceById(data: any) {
  return request({
    url: '/space/getSpaceById?spaceId=' + data.spaceId,
    method: 'get',
  });
}
