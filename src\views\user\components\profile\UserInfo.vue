<template>
  <div class="modal">
    <div class="modal-content" :class="{ smallModal: [6, 7].includes(userType) }">
      <div class="modal-content-title">
        <div>用户信息</div>
        <div class="icon iconfont icon-close" @click="changeState"></div>
      </div>
      <div class="modal-form">
        <div class="user-info-box">
          <div class="user-info">
            <div>
              <div class="user-info-img">
                <upload-template
                  class-style="avatar-uploader"
                  :baseURL="baseURL"
                  :beforeUpload="beforeUpload"
                  :handleAvatarSuccess="handleAvatarSuccess">
                  <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                  <div class="avatar-icon"></div>
                </upload-template>
              </div>
              <el-tooltip :content="packageInfo.base.packageName" placement="bottom" effect="dark">
                <!-- <div class="version">{{ packageInfo.base.packageName }}</div> -->
              </el-tooltip>
            </div>
            <div>
              <div class="corporate-name">{{ packageInfo.base.packageName }}</div>
              <div class="package-time" v-if="![6, 7].includes(props.userType)">
                <span>有效期：</span>
                <span style="color: #1e1e1e">
                  {{
                    packageInfo.packageStartDate
                      ? getDayTime(packageInfo.packageStartDate).split('-').join('.')
                      : ''
                  }}
                  ~
                </span>
                <span style="color: #1e1e1e">
                  {{
                    packageInfo.packageEndDate
                      ? getDayTime(packageInfo.packageEndDate).split('-').join('.')
                      : ''
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="logout">
            <div @click="router.push('/login')">退出登录</div>
          </div>
        </div>
        <div class="information">
          <div class="title" v-if="![6, 7].includes(userType)">企业信息</div>
          <div class="information-info">
            <div>
              <div v-if="![6, 7].includes(userType)" class="item-info">
                <span class="information-info-list-name">企业名称</span>
                <span class="bold-text">{{ packageInfo.orgnizationInfo.orgnizationName }}</span>
              </div>
              <div class="item-info">
                <span class="information-info-list-name">账号</span>
                <span class="bold-text">{{ compute_ }}</span>
              </div>
              <div class="item-info">
                <span class="information-info-list-name">密码</span>
                <span class="bold-text">
                  <span v-if="!isView">******</span>
                  <span v-if="isView">{{ pass_ }}</span>
                  <span class="password-view" @click="isView = !isView">
                    <img v-if="isView" src="@/assets/images/icon/view.png" />
                    <img v-if="!isView" src="@/assets/images/icon/view_off.png" />
                  </span>
                  <span class="update-passWord" @click="updatePassWord">更新密码</span>
                </span>
              </div>
            </div>
            <div v-if="![6, 7].includes(userType)" style="transform: translateX(-19px)">
              <div class="item-info">
                <span class="information-info-list-name right samllWidth">姓名</span>
                <span class="bold-text">{{ packageInfo.orgnizationInfo.contactName }}</span>
              </div>
              <div class="item-info">
                <span class="information-info-list-name right samllWidth">电话</span>
                <span class="bold-text">{{ packageInfo.orgnizationInfo.contactPhone }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="service-pack" :class="{ njyjxrPack: ![6, 7].includes(userType) }">
          <div class="title-header">
            <div class="title">产品服务信息</div>
            <div class="close-btn" @click="handleClose">
              <img src="@/assets/images/icon/close-icon.png" alt="关闭" />
            </div>
          </div>
          <el-table
            align="center"
            :data="tableData"
            border
            style="width: 100%; margin-top: 14px; height: 442px"
            :header-cell-style="headerStyle"
            row-class-name="rowStyle">
            <el-table-column center prop="service" label="服务项" width="188" />
            <el-table-column
              center
              :label="`【${packageInfo.base.packageName}】标准服务包`"
              width="232">
              <template #default="{ row }">
                <!-- 项目数量 -->
                <template v-if="row.prop == 'itemCount'">
                  <div v-if="![6, 7].includes(userType)" class="item-count">
                    <header>小程序AR数量: {{ packageInfo.base.planeSceneNum || '--' }}</header>
                    <header>大空间AR数量: {{ packageInfo.base.arSceneNum || '--' }}</header>
                  </div>
                  <div v-else class="cell-center">小程序AR数量: 10个</div>
                </template>
                <!-- 空间限制 -->
                <template v-if="row.prop == 'roomLimit'">
                  <div
                    v-if="packageInfo.base.spacePicSingleNum || packageInfo.base.spacePicTotalNum"
                    style="
                      height: 60px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      flex-direction: column;
                    ">
                    <div class="sing" v-if="packageInfo.base.spacePicSingleNum">
                      单次 {{ packageInfo.base.spacePicSingleNum }} 张
                    </div>
                    <div class="sing" v-if="packageInfo.base.spacePicTotalNum">
                      总数 {{ packageInfo.base.spacePicTotalNum }}
                      张
                    </div>
                  </div>
                  <div v-else>--</div>
                </template>
                <!-- 素材空间             -->
                <template v-if="row.prop == 'assetsRoom'">
                  <div v-html="materialUploadSize" class="cell-center"></div>
                </template>
                <!-- 单个素材上传上限      -->
                <template v-if="row.prop == 'onlyAssetLimit'">
                  <div v-html="calculateSize(singleUploadSize)" class="cell-center"></div>
                </template>
                <!-- 套餐时长 -->
                <template v-if="row.prop == 'packageTime'">
                  <div class="cell-center">
                    {{ expireDay == 99999 ? '不限' : expireDay + '天' }}
                  </div>
                </template>
                <!-- 大空间定位平台支持 -->
                <template v-if="row.prop == 'roomPlatform'">
                  <div v-if="packageInfo.base.packagePlatformList">
                    <div
                      class="cell-center"
                      v-for="line in packageInfo.base.packagePlatformList"
                      :key="line">
                      {{ line == 1 ? '眼镜端' : line == 2 ? '移动端' : '微信小程序' }}
                    </div>
                  </div>
                  <div v-else class="cell-center">--</div>
                </template>
                <!-- 空间数量 -->
                <template v-if="row.prop == 'roomCount'">
                  <div v-if="packageInfo.base.spaceNum == null">
                    <div class="cell-center">--</div>
                  </div>
                  <div v-else>
                    <div class="cell-center" v-if="packageInfo.base.spaceNum">
                      限制 {{ packageInfo.base.spaceNum }} 个
                    </div>
                    <div v-else class="cell-center">不限-照片用完为止</div>
                  </div>
                </template>
              </template>
            </el-table-column>
            <el-table-column center label="扩展服务包" width="200">
              <template #default="{ row }">
                <template v-if="row.prop == 'itemCount'">
                  <main class="item-count">
                    <header>
                      小程序AR数量: {{ packageInfo.resultExpand.planeSceneNum || '--' }}
                    </header>
                    <header>大空间AR数量: {{ packageInfo.resultExpand.arSceneNum || '--' }}</header>
                  </main>
                </template>
                <template v-if="row.prop == 'roomLimit'">
                  <div class="cell-center">
                    {{
                      packageInfo.resultExpand.spacePicTotalNum
                        ? packageInfo.resultExpand.spacePicTotalNum + '张'
                        : '--'
                    }}
                  </div>
                </template>
                <template v-if="row.prop == 'assetsRoom'">
                  <div
                    class="cell-center"
                    v-html="calculateSize(packageInfo.resultExpand.materialUploadSize)"></div>
                </template>
                <template v-if="row.prop == 'onlyAssetLimit'">
                  <div class="cell-center">--</div>
                </template>
                <template v-if="row.prop == 'packageTime'">
                  <div class="cell-center">--</div>
                </template>
                <template v-if="row.prop == 'roomPlatform'">
                  <div class="cell-center">--</div>
                </template>
                <template v-if="row.prop == 'roomCount'">
                  <div class="cell-center">--</div>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="view-agree" :class="{ newView: [6, 7].includes(userType) }">
        <span @click="showAgree = 1">《服务协议》</span>
        、
        <span @click="showAgree = 2">《隐私政策》</span>
      </div>
    </div>
  </div>
  <consent-agreement
    v-if="showAgree"
    :show-type="showAgree"
    :handle-hide="() => (showAgree = 0)"
    :hide-mask="true"></consent-agreement>
  <tips-view></tips-view>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import TipsView from '@/components/TipsView.vue';
import { getOrgnizationPackage } from '@/api';
import { useRouter } from 'vue-router';
import ConsentAgreement from '@/components/ConsentAgreement.vue';
import { calculateSize, getDayTime, decrypt } from '@/utils';
import { truncate } from 'fs/promises';
import type { UploadProps } from 'element-plus';
import { getOssAccessPath } from '@/api';
import { useStore } from 'vuex';
import { before } from 'lodash';
import UploadTemplate from '@/views/template/components/UploadTemplate.vue';

const router = useRouter();
const showAgree = ref(0);
const isView = ref(false);
const packageInfo: any = ref({ base: {}, expands: [], orgnizationInfo: {} });
const baseURL =
  (process.env.NODE_ENV === 'production' ? '/api' : '/api1') + '/authority/addprofilePicture'; // 基础url
const materialUploadSize = ref('');
const expireDay = ref('');
const singleUploadSize = ref('');
const pass_ = ref('');

const props = defineProps({
  changeEvent: {
    default: null,
    type: Function,
  },
  data: {
    default: null,
    type: Object,
  },
  userType: {
    default: 6,
  },
});

const headerStyle = {
  height: '32px',
  background: 'rgba(230,237,247,0.3)',
  border: '1px solid #E6EDF7',
  textAlign: 'center',
};

const tableData = ref([
  {
    prop: 'itemCount',
    service: '项目数量',
    all: true,
  },
  {
    prop: 'roomLimit',
    service: '空间限制',
  },
  {
    prop: 'assetsRoom',
    service: '素材空间',
    all: true,
  },
  {
    prop: 'onlyAssetLimit',
    service: '单个素材上传上限',
    all: true,
  },
  {
    prop: 'packageTime',
    service: '套餐时长',
  },
  {
    prop: 'roomPlatform',
    service: '大空间定位平台支持',
  },
  {
    prop: 'roomCount',
    service: '空间数量',
  },
]);

const imageUrl = ref('');

const store = useStore();

const beforeUpload = (uploadFile) => {
  if (uploadFile.size / 1024 / 1024 > 5) {
    store.state.showTips = '大小不超过5MB';
    return false;
  }
};

const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  imageUrl.value = URL.createObjectURL(uploadFile.raw!);
  store.state.profilePic = imageUrl.value;
  store.state.profilePicture = response.data;
};
const changeState = () => {
  props.changeEvent('');
};

const compute_ = computed(() => {
  return [6, 7].includes(props.userType)
    ? localStorage.getItem('phoneNo')
    : localStorage.getItem('userName');
});

const updatePassWord = () => {
  props.changeEvent('password');
};

onMounted(() => {
  packageInfo.value.base = props.data.packageInfoDto;
  packageInfo.value.expands = props.data.packageExtensionDtos;
  packageInfo.value.orgnizationInfo =
    props.data.orgnizationDto || JSON.parse(sessionStorage.getItem('packageInfoDto') || '{}');
  packageInfo.value.packageStartDate = props.data.packageStartDate;
  packageInfo.value.packageEndDate = props.data.packageEndDate;
  if (props.data.userDto?.profilePicture) {
    getOssAccessPath({ key: props.data.userDto.profilePicture }).then((res: any) => {
      imageUrl.value = res.data;
    });
  } else {
    imageUrl.value = require('@/assets/images/ailongmask.png');
  }
  materialUploadSize.value = packageInfo.value.base.materialUploadSize;
  singleUploadSize.value = packageInfo.value.base.singleUploadSize;
  expireDay.value = packageInfo.value.base.expireDay;

  packageInfo.value.resultExpand = {
    arSceneNum: 0,
    planeSceneNum: 0,
    materialUploadSize: 0,
    spacePicTotalNum: 0,
  };
  packageInfo.value.expands.forEach((item) => {
    if (item.arSceneNum) {
      packageInfo.value.resultExpand.arSceneNum += item.arSceneNum;
    }
    if (item.planeSceneNum) {
      packageInfo.value.resultExpand.planeSceneNum += item.planeSceneNum;
    }
    if (item.materialUploadSize) {
      packageInfo.value.resultExpand.materialUploadSize += item.materialUploadSize;
    }
    if (item.spacePicTotalNum) {
      packageInfo.value.resultExpand.spacePicTotalNum += item.spacePicTotalNum;
    }
  });

  if ([6, 7].includes(props.userType)) {
    tableData.value = tableData.value.filter((item) => item.all);
  }
  pass_.value = props.data.userDto.password || '';
});

watch(
  () => props.data,
  () => {
    packageInfo.value.base = props.data.packageInfoDto;
    packageInfo.value.expands = props.data.packageExtensionDtos;
    packageInfo.value.orgnizationInfo = props.data.orgnizationDto || {};
  },
  { deep: true }
);
</script>
<style scoped lang="less">
:deep(.el-table__cell) {
  border-right: none !important;
}

.newView {
  transform: translateY(-7px);
}

:deep(.rowStyle) {
  height: 60px !important;
  border-left: 1px solid #e6edf7;
  border-right: 1px solid #e6edf7;
  border-bottom: 1px solid #e6edf7;
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;
}

.samllWidth {
  min-width: 32px !important;
  width: 32px !important;
}

.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 11;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .njyjxrPack {
    height: 436px !important;
  }

  .cell-center {
    text-align: center;
  }

  .item-count {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .tiyanMain {
    height: 40px;
  }

  .height120 {
    height: 120px !important;
    line-height: 120px;
  }

  .maxHeight {
    height: 60px !important;
    line-height: 60px !important;
  }

  .newLine {
    line-height: 40px !important;
  }

  .modal-content {
    width: 664px;
    height: 855px;
    max-height: 90%;
    background: #fff;
    box-shadow: 0px 10px 20px 0px rgba(62, 85, 132, 0.3);
    border-radius: 8px;
    border: 1px solid #edeff2;
    overflow: hidden;

    .modal-content-title {
      height: 76px;
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;

      & > div:first-child {
        font-weight: bold;
        font-size: 18px;
        color: #1e1e1e;
      }

      .icon-close {
        font-size: 26px;
        cursor: pointer;
        font-weight: 400;

        &:hover {
          color: #2e76ff;
        }
      }
    }

    .modal-form {
      position: relative;
      width: 100%;
      height: calc(100% - 126px);
      box-sizing: border-box;
      overflow-y: auto;
      padding: 0 20px;

      .user-info-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .logout {
          position: relative;
          font-weight: 500;
          font-size: 14px;
          color: #2e76ff;
          cursor: pointer;

          &::before {
            content: '';
            width: 16px;
            height: 16px;
            position: absolute;
            left: -20px;
            top: 2px;
            background-image: url(~@/assets/images/icon/logout-1.png);
            background-size: 100% 100%;
          }

          &:hover {
            color: rgba(18, 81, 200, 1);

            &::before {
              background-image: url(~@/assets/images/icon/logout-1A.png);
              background-size: 100% 100%;
            }
          }
        }

        .user-info {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 12px;

          & > div:first-child {
            position: relative;
            margin-right: 20px;
          }

          .user-info-img {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;

            div {
              width: 65px;
              height: 65px;
            }

            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
          }

          .avatar-uploader .avatar-icon {
            position: absolute;
            width: 24px;
            height: 24px;
            left: 47px;
            top: 46px;
          }

          .avatar-icon {
            background-image: url(~@/assets/images/icon/renew.png);
            background-size: 100% 100%;

            &:hover {
              background-image: url(~@/assets/images/icon/renewA.png);
              background-size: 100% 100%;
            }
          }

          .version {
            position: absolute;
            left: 50%;
            top: 73px;
            width: 69px;
            height: 25px;
            line-height: 24px;
            text-align: center;
            background: linear-gradient(225deg, #0375ff 0%, #3c96ff 100%);
            box-shadow: inset -1px -1px 0px 0px rgba(255, 255, 255, 0.2),
              inset 1px 1px 0px 0px rgba(11, 91, 225, 0.4);
            border-radius: 13px;
            margin-left: -35px;
            font-size: 13px;
            font-weight: 400;
            color: #ffffff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .corporate-name {
            font-weight: bold;
            font-size: 14px;
            color: #1e1e1e;
          }

          .package-time {
            font-weight: 500;
            font-size: 14px;
            color: #797979;
            margin-top: 4px;
          }

          .corporate-email {
            font-size: 20px;
            font-weight: 400;
            color: #000000;
          }

          & > div {
            text-align: left;
          }
        }
      }

      .information,
      .service-pack {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
        border: 2px solid #ffffff;
        box-sizing: border-box;

        .title-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .title {
            position: relative;
            font-weight: bold;
            font-size: 14px;
            color: #1e1e1e;
            text-align: left;
            line-height: 1;
          }

          .close-btn {
            width: 20px;
            height: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }

            &:hover {
              opacity: 0.7;
            }
          }
        }

        & > .title {
          position: relative;
          font-weight: bold;
          font-size: 14px;
          color: #1e1e1e;
          text-align: left;
          line-height: 1;
        }

        .information-info {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          text-align: left;
          font-size: 16px;
          font-weight: 400;
          color: #333333;

          .item-info {
            margin-top: 12px;

            & > span:first-child {
              min-width: 56px;
              text-align: left;
              margin-right: 8px;
            }
          }

          .information-info-list-name {
            display: inline-block;
            font-weight: 500;
            font-size: 14px;
            color: #797979;
          }

          .bold-text {
            font-weight: 500;
            font-size: 14px;
            color: #1e1e1e;

            & > span {
              vertical-align: middle;
            }
          }

          .password-view {
            margin-left: 5px;
            cursor: pointer;
            font-size: 0;
            height: 16px;
            display: inline-block;

            img {
              width: 16px;
              height: 16px;
            }
          }

          .right {
            width: 62px;
          }

          .update-passWord {
            position: relative;
            width: 96px;
            height: 24px;
            line-height: 24px;
            background: rgba(46, 118, 255, 0.1);
            border-radius: 12px;
            display: inline-block;
            font-size: 14px;
            font-weight: 400;
            color: #1e84ff;
            text-align: center;
            margin-left: 10px;
            padding-left: 19px;
            box-sizing: border-box;
            cursor: pointer;

            &::before {
              content: '';
              position: absolute;
              left: 11px;
              top: 5px;
              width: 14px;
              height: 14px;
              background-image: url(~@/assets/images/icon/modify.png);
              background-size: 100% 100%;
            }

            &:hover {
              background-color: rgba(46, 118, 255, 0.27);
            }
          }
        }

        .service-pack-info {
          margin-top: 12px;
          // font-size: 16px;
          font-weight: 400;
          color: #333333;
          width: 652px;
          overflow: hidden;
          overflow-x: auto;
          font-size: 0;

          .table-header {
            height: 32px;
            background: rgba(230, 237, 247, 0.3);
            font-weight: 400;
            font-size: 12px !important;
            color: #797979;
            line-height: 32px;
          }

          .table-line {
            & > div,
            & > main {
              border-bottom: 1px solid #e6edf7;
            }
          }

          & > div {
            text-align: center;
            white-space: nowrap;

            & > div {
              width: 216px;
              line-height: 40px;
              display: inline-block;
              box-sizing: border-box;
              font-size: 16px;
            }
          }

          & > div:first-child {
            font-size: 16px;
            font-weight: 500;
            color: #6f6f6f;
            margin-bottom: 5px;
          }

          & > div:last-child {
            height: 280px;
            overflow-x: hidden;
            overflow-y: scroll;
            display: flex;

            & > div:last-child {
              border: none;
            }
          }
        }
      }

      .information {
        margin-top: 25px;
      }

      .service-pack {
        margin-top: 30px;
      }
    }

    .view-agree {
      padding-top: 13px;
      text-align: left;
      padding-left: 20px;
      z-index: 999;
      font-weight: 400;
      font-size: 14px;
      color: #2e76ff;
      text-align: left;
      position: relative;

      & > span {
        cursor: pointer;
        text-decoration-line: underline;
      }
    }
  }

  .smallModal {
    height: 610px;
  }
}
</style>
