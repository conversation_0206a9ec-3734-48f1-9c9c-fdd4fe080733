<template>
  <div class="model-params">
    <TabsBar
      v-model="activeTab"
      :tabs="[
        { label: '图片生成3D模型', value: 'image' },
        { label: '文本生成3D模型', value: 'text' },
      ]" />

    <div v-if="activeTab === 'image'" class="image-to-model">
      <div class="upload-box" @click="handleUploadClick">
        <input
          ref="fileInput"
          type="file"
          accept="image/jpeg,image/png"
          style="display: none"
          @change="handleFileChange" />
        <div v-if="!imageUrl" class="upload-placeholder">
          <img src="@/assets/images/starimg1.png" class="upload-icon" />
          <div class="upload-tip">点击上传图片到此处</div>
          <div class="upload-desc">
            仅支持JPG/PNG格式，文件大小不超过10MB
            <br />
            分辨率最低要求128*128
          </div>
        </div>
        <img v-else :src="imageUrl" class="uploaded-image" />
      </div>

      <div class="tips-container">
        <div class="section-label">图片上传建议</div>
        <div class="tips-content-container">
          <div class="reference-images">
            <div class="reference-image">
              <img src="@/assets/images/testimg1.png" alt="参考图片" />
            </div>
          </div>
          <div class="tips-content">
            <div class="tip-item">
              <span class="tip-dot">•</span>
              <span style="width: 100%; text-align: left">背景简单（纯色或透明）</span>
            </div>
            <div class="tip-item">
              <span class="tip-dot">•</span>
              <span style="width: 100%; text-align: left">不包含文字</span>
            </div>
            <div class="tip-item">
              <span class="tip-dot">•</span>
              <span style="width: 100%; text-align: left">单个主体</span>
            </div>
            <div class="tip-item">
              <span class="tip-dot">•</span>
              <span style="width: 100%; text-align: left">主体不要过小</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="activeTab === 'text'" style="width: 100%">
      <el-input
        v-model="desc"
        type="textarea"
        :maxlength="150"
        show-word-limit
        placeholder="输入想要生成的3D模型描述"
        class="desc-input2" />

      <!-- 风格选择区域 -->
      <div class="section-label">风格选择</div>
      <div class="style-btns">
        <div
          v-for="style in modelStyles"
          :key="style.value"
          :class="['style-btn', { active: selectedStyle === style.value }]"
          @click="selectedStyle = style.value">
          {{ style.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import TabsBar from '@/components/TabsBar.vue';

// 标签切换
const activeTab = ref('image');
// 文本生成3D模型参数
const desc = ref('');
const selectedStyle = ref('general');

// 风格选项
const modelStyles = [
  { label: '通用', value: 'general' },
  { label: '卡通', value: 'cartoon' },
  { label: '中国风', value: 'chinese' },
  { label: '青花瓷', value: 'bluewhite' },
  { label: '石雕', value: 'stone' },
  { label: '赛博朋克', value: 'cyberpunk' },
];

// 图片生成3D模型参数
const fileInput = ref<HTMLInputElement | null>(null);
const imageFile = ref<File | null>(null);
const imageUrl = ref('');

// 处理上传点击
const handleUploadClick = () => {
  fileInput.value?.click();
};

// 处理文件选择
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  if (!files || !files[0]) return;

  const file = files[0];

  // 检查文件类型
  if (!['image/jpeg', 'image/png'].includes(file.type)) {
    ElMessage.error('仅支持JPG/PNG格式');
    return;
  }

  // 检查文件大小 (10MB = 10 * 1024 * 1024 bytes)
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB');
    return;
  }

  // 保存文件并创建预览
  imageFile.value = file;
  const reader = new FileReader();
  reader.onload = (e) => {
    imageUrl.value = e.target?.result as string;

    // 检查图片分辨率
    const img = new Image();
    img.onload = () => {
      if (img.width < 128 || img.height < 128) {
        ElMessage.error('图片分辨率不能小于128*128');
        imageFile.value = null;
        imageUrl.value = '';
      }
    };
    img.src = imageUrl.value;
  };
  reader.readAsDataURL(file);
};

// 验证参数方法（不显示错误消息）
const validateParams = () => {
  if (activeTab.value === 'text') {
    return !!desc.value?.trim();
  } else {
    return !!imageFile.value;
  }
};

// 获取参数方法，供父组件调用（显示错误消息）
const getParams = () => {
  if (activeTab.value === 'text') {
    if (!desc.value?.trim()) {
      ElMessage.warning('请输入模型描述');
      return null;
    }
    return {
      type: 'text',
      desc: desc.value,
      style: selectedStyle.value,
      ratio: '1:1', // 默认比例
    };
  } else {
    if (!imageFile.value) {
      ElMessage.warning('请上传图片');
      return null;
    }
    return {
      type: 'image',
      pic: imageFile.value,
    };
  }
};

// 暴露方法给父组件
defineExpose({
  validateParams,
  getParams,
  activeTab,
  desc,
  imageFile,
});
</script>

<style scoped lang="less">
.model-params {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  overflow: visible;

  .param-title {
    font-weight: bold;
    font-size: 24px;
    color: #1e1e1e;
    margin-bottom: 12px;
  }

  .model-tabs-bar {
    position: relative;
    width: 100%;
    height: 36px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    margin-bottom: 18px;

    .model-tabs-slider {
      position: absolute;
      top: 3.5px;
      left: 4px;
      width: calc(50% - 8px);
      height: 29px;
      background: #fff;
      border-radius: 3px;
      z-index: 1;
      transition: left 0.3s, width 0.3s;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    }

    .model-tab-btn {
      position: relative;
      z-index: 2;
      flex: 1;
      height: 29px;
      line-height: 29px;
      text-align: center;
      font-weight: 500;
      font-size: 14px;
      color: #797979;
      cursor: pointer;
      user-select: none;
      margin: 0 4px;
      background: transparent;
      transition: color 0.2s;

      &.active {
        font-weight: 500;
        font-size: 14px;
        color: #1e1e1e;
      }
    }
  }

  .desc-input2 {
    width: 100% !important;
    min-width: 0;
    display: block;
    box-sizing: border-box;
    margin-bottom: 12px;

    :deep(.el-textarea),
    :deep(.el-textarea__inner) {
      width: 100% !important;
      min-width: 0;
      box-sizing: border-box;
      background: #f5f6f7 !important;
      border-radius: 8px !important;
      border: none !important;
      min-height: 300px;
      height: 300px;
      resize: none;
      box-shadow: none !important;
      padding-top: 10px !important;
    }

    :deep(.el-input__count) {
      right: 12px !important;
      left: auto !important;
      bottom: 8px !important;
      background: #f5f5f5 !important;
      border-radius: 8px !important;
      color: #797979 !important;
      padding: 2px 8px !important;
      font-size: 12px !important;
      font-weight: 400 !important;
      box-shadow: none !important;
    }
  }
  .image-to-model {
    width: 100%;
  }
  .upload-box {
    width: 100% !important;
    min-width: 0;
    box-sizing: border-box;
    height: 220px;
    background: #f5f6f7;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-bottom: 18px;
    position: relative;

    .upload-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #bfc6d1;

      .upload-icon {
        width: 24px;
        height: 24px;
        margin-bottom: 8px;
      }

      .upload-tip {
        margin-bottom: 8px;
        font-weight: 400;
        font-size: 14px;
        color: #1e1e1e;
      }

      .upload-desc {
        font-weight: 400;
        font-size: 14px;
        color: #797979;
        line-height: 24px;
        text-align: center;
      }
    }

    .uploaded-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 8px;
    }
  }

  .tips-container {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 12px;

    .section-label {
      font-size: 14px;
      color: #797979;
      margin-bottom: 10px;
      text-align: left;
    }
    .tips-content-container {
      width: 100%;
      display: flex;
      align-items: center;

      gap: 20px;
    }
    > div {
      display: flex;
      align-items: flex-start;
      gap: 20px;
    }

    .tips-content {
      width: 100%;
      flex: 1;
      display: flex;
      justify-self: start;
      flex-direction: column;

      .tip-item {
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        text-align: left;

        .tip-dot {
          color: #797979;
          margin-right: 5px;
          font-size: 14px;
        }

        span {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .reference-image {
      width: 150px;
      height: 150px;
      border-radius: 7px;
      overflow: hidden;
      background: #0072a6;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .section-label {
    font-size: 14px;
    color: #797979;
    margin-bottom: 6px;
    margin-top: 8px;
    text-align: left;
    width: 100%;
  }

  .style-btns {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 11px;
    width: 100%;
    margin-bottom: 12px;

    .style-btn {
      height: 33px;
      background: #f5f5f5;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #797979;
      cursor: pointer;
      transition: all 0.2s;

      &.active {
        background: rgba(46, 118, 255, 0.1);
        color: #2e76ff;
        font-weight: 500;
      }

      &:hover {
        background: rgba(46, 118, 255, 0.05);
      }
    }
  }
}
</style>
