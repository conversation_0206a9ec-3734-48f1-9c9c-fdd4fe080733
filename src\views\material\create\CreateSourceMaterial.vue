<template>
  <div class="modal">
    <div class="modal-content" v-show="!showFormat">
      <div class="modal-content-title">
        <div>上传素材</div>
      </div>
      <div class="modal-form">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="80px"
          class="demo-ruleForm">
          <el-form-item label="素材名称" prop="materialName">
            <el-input
              class="form-input"
              v-model="ruleForm.materialName"
              placeholder="请输入素材名称"
              maxlength="20" />
          </el-form-item>
          <el-form-item label="素材类型" prop="materialType">
            <el-select
              v-model="ruleForm.materialType"
              placeholder="请选择资源类型"
              class="select-default"
              popper-class="select-option"
              :suffix-icon="DropDown"
              style="width: 432px; height: 36px"
              @change="changeMaterialType">
              <el-option
                v-for="(item, index) in materialType"
                :key="index"
                :label="item.name"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            label="模型分类"
            prop="typeIdStr"
            v-if="ruleForm.materialType === '4' && props.materialAffiliation === 2">
            <el-select
              v-model="ruleForm.typeIdStr"
              placeholder="请选择模型分类"
              class="select-default"
              popper-class="select-option"
              :suffix-icon="DropDown"
              style="width: 432px; height: 36px"
              multiple
              collapse-tags
              collapse-tags-tooltip
              :loading="categoryLoading"
              @change="handleCategoryChange">
              <el-option key="all" label="全部" value="all" />
              <el-option
                v-for="category in modelCategories"
                :key="category.value"
                :label="category.label"
                :value="category.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="!['4', '5'].includes(ruleForm.materialType)"
            label="上传素材"
            prop="ossKey"
            class="upload-box-style">
            <div class="upload-material-tips">每个文件大小不超过{{ singleUploadSize }}MB</div>
            <div
              class="upload-material-style"
              :class="{ 'format-error': formatError && ruleForm.materialType !== '4' }">
              <div class="upload-tips" v-if="uploadTips[ruleForm.materialType]">
                {{ uploadTips[ruleForm.materialType] }}
              </div>
              <el-upload
                class="upload-btn"
                :before-upload="beforeUpload"
                :action="uploadURL"
                :on-change="handleChange"
                :headers="headerObj"
                :show-file-list="false"
                :on-success="uploaded"
                :on-error="errorUpload"
                :disabled="uploadStatus[ruleForm.materialType] == 'uploading'">
                <el-button :class="uploadStatus[ruleForm.materialType]">选择</el-button>
              </el-upload>
              <div :class="uploadStatus[ruleForm.materialType] + '-style'">
                <template
                  v-if="showUploadSuccess && uploadStatus[ruleForm.materialType] === 'uploaded'">
                  <span>
                    <i class="el-icon-success" style="color: #2e76ff; margin-right: 4px"></i>
                    {{ uploadStatusMap[uploadStatus[ruleForm.materialType]] }}
                  </span>
                </template>
                <template v-else>
                  {{ uploadStatusMap[uploadStatus[ruleForm.materialType]] }}
                </template>
              </div>
            </div>
          </el-form-item>
          <div v-if="ruleForm.materialType == '4' && showDeviceToUpload.length">
            <el-form-item label="上传素材" prop="ossKey" class="upload-box-style">
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: flex-start;
                  flex-direction: row;
                ">
                <div class="upload-tips" v-if="uploadTips[ruleForm.materialType]">
                  <div class="upload-material-tips" style="display: flex; align-items: center">
                    每个文件大小不超过{{ singleUploadSize }}MB
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      content="Right Top prompts info"
                      placement="right-start">
                      <template #content>
                        <span style="font-weight: bold; font-size: 14px; color: #ffffff">
                          素材支持项目类型
                        </span>
                        <br />
                        glb：支持全平台项目
                        <br />
                        fbx：支持移动端/眼镜端项目
                      </template>
                      <img
                        src="@/assets/images/help222.png"
                        style="width: 16px; height: 16px"
                        alt="" />
                    </el-tooltip>
                  </div>
                </div>
              </div>

              <div class="upload-model-box">
                <div
                  class="upload-material-style"
                  :class="[
                    'styleFor' + showDeviceToUpload.length,
                    { 'format-error': formatError && loadMap[item].type === 'web' },
                  ]"
                  v-for="(item, index) in showDeviceToUpload"
                  :key="index">
                  <div class="upload-tips" v-if="uploadTips[ruleForm.materialType]">
                    <template
                      v-if="
                        uploadStatus[ruleForm.materialType][loadMap[item].type] === 'uploaded' &&
                        uploadedModelName &&
                        loadMap[item].type === 'web'
                      ">
                      <span
                        style="
                          display: block;
                          text-align: center;
                          width: 100%;
                          font-weight: 400;
                          font-size: 14px;
                          color: #1e1e1e;
                        ">
                        {{ uploadedModelName }}
                      </span>
                    </template>
                    <template v-else>
                      {{ loadMap[item].tag }}
                    </template>
                  </div>
                  <div v-if="uploadTips[ruleForm.materialType]">
                    {{
                      ruleForm.equipType.includes('3') && item == 2
                        ? loadMap[item].rule2
                        : loadMap[item].rule
                    }}
                  </div>
                  <el-upload
                    class="upload-btn"
                    :before-upload="beforeUpload"
                    :action="uploadURL"
                    :on-change="handleChange"
                    :headers="headerObj"
                    :show-file-list="false"
                    :on-success="(res: any) => { uploaded(res, loadMap[item].type); }"
                    @click.stop="updateType = loadMap[item].type"
                    :on-error="errorUpload"
                    :disabled="
                      uploadStatus[ruleForm.materialType][loadMap[item].type] == 'uploading'
                    ">
                    <el-button :class="uploadStatus[ruleForm.materialType][loadMap[item].type]">
                      {{
                        uploadStatus[ruleForm.materialType][loadMap[item].type] === 'uploaded'
                          ? '重新上传'
                          : '选择'
                      }}
                    </el-button>
                  </el-upload>
                  <div
                    :class="[
                      showUploadSuccess &&
                      uploadStatus[ruleForm.materialType][loadMap[item].type] === 'uploaded'
                        ? 'uploaded-style'
                        : '',
                      uploadStatus[ruleForm.materialType][loadMap[item].type] + '-style',
                    ]">
                    <template
                      v-if="
                        showUploadSuccess &&
                        uploadStatus[ruleForm.materialType][loadMap[item].type] === 'uploaded'
                      ">
                      <span
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          position: relative;
                        ">
                        <span>上传成功</span>
                        <span class="complete-icon"></span>
                      </span>
                    </template>
                    <template
                      v-else-if="
                        uploadStatus[ruleForm.materialType][loadMap[item].type] === 'uploaded'
                      ">
                      <span
                        style="display: flex; align-items: center; gap: 12px; position: relative">
                        <span style="position: relative; padding-right: 8px">
                          眼镜端
                          <span
                            class="platform-icon"
                            :class="{ fail: platformIconType('glasses') !== 'success' }"></span>
                        </span>
                        <span style="position: relative; padding-right: 8px">
                          移动端
                          <span
                            class="platform-icon"
                            :class="{ fail: platformIconType('mobile') !== 'success' }"></span>
                        </span>
                        <span style="position: relative; padding-right: 8px">
                          微信小程序端
                          <span
                            class="platform-icon"
                            :class="{ fail: platformIconType('wechat') !== 'success' }"></span>
                        </span>
                      </span>
                    </template>
                    <template v-else>
                      {{ uploadStatusMap[uploadStatus[ruleForm.materialType][loadMap[item].type]] }}
                    </template>
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
          <el-form-item
            v-if="ruleForm.materialType === '5'"
            label="上传素材"
            prop="materialWord"
            class="upload-box-style">
            <div class="upload-material-tips">每个文本字数不超过200字</div>
            <el-input
              class="form-input"
              type="textarea"
              style="height: 124px"
              resize="none"
              v-model="ruleForm.materialWord"
              placeholder="请输入文字"
              @input="changeText"
              maxlength="200" />
          </el-form-item>
          <el-form-item
            label="模型预览"
            :style="{ marginTop: '40px' }"
            v-if="ruleForm.materialType == '4' && uploadStatus[4].web == 'uploaded'">
            <div class="preview">
              <canvas-preview ref="canvasRef" :handle-mouse-up="handleMouseUp"></canvas-preview>
            </div>
            <div class="tips-text">
              <div>可滑动鼠标查看模型</div>
              <div>同步刷新素材封面</div>
            </div>
          </el-form-item>
          <el-form-item label="素材封面" prop="thumbnail">
            <div class="thumbnail-show">
              <div>
                <el-upload
                  v-if="ruleForm.materialType != 1"
                  :before-upload="beforeUploadThumbnail"
                  :action="uploadThumbnail"
                  :show-file-list="false"
                  :on-change="handleChangeThumbnail"
                  :headers="headerObj"
                  :on-success="uploadedThumbnail">
                  <div class="thumbnail-icon-upload">
                    <img
                      v-if="!loadedThumbnail"
                      :src="materialUrls[ruleForm.materialType].url_upload"
                      style="width: 24px; height: 24px" />
                    <img v-if="loadedThumbnail" :src="loadedThumbnail" />
                  </div>
                </el-upload>
                <div class="thumbnail-icon-upload" v-if="ruleForm.materialType == 1">
                  <img
                    v-if="!loadedThumbnail"
                    :src="materialUrls[ruleForm.materialType].url_upload"
                    style="width: 24px; height: 24px" />
                  <img v-if="loadedThumbnail" :src="loadedThumbnail" />
                </div>
              </div>
              <div class="thumbnail-text" v-if="ruleForm.materialType != 1">
                <div>点击图片可更换封面</div>
                <div>支持512KB的.png或.jpg图片</div>
              </div>
            </div>
          </el-form-item>
          <!-- <el-form-item label="备注" prop="materialDescribe">
            <el-input
              class="form-input"
              v-model="ruleForm.materialDescribe"
              placeholder="请输入描述信息"
              maxlength="60" />
          </el-form-item> -->
          <el-form-item class="form-submit">
            <div class="btn-default el-size3">
              <el-button @click="changeState">取消</el-button>
            </div>
            <div class="btn-primary el-size3">
              <el-button @click="submitForm(ruleFormRef)">确认</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <text-canvas ref="textCanvasRef"></text-canvas>
    <tips-view v-if="!showTemporaryText"></tips-view>
    <tips-view
      v-if="showTemporaryText"
      show-btn="知道了"
      tips-title="取消上传素材"
      :sure-event="deleteTemporary"
      :cancle-event="() => (showTemporaryText = false)"></tips-view>
    <div class="pic-model">
      <div>
        <img :src="loadedThumbnail" />
      </div>
    </div>

    <!-- 模型上传进度弹框 -->
    <ModelUploadProgressModal
      v-if="showProgressModal"
      :icon="progressIcon"
      :progress="modelUploadProgress"
      :status="modelUploadStatus"
      @close="handleCloseProgressModal" />
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import TipsView from '@/components/TipsView.vue';
import DropDown from '@/components/DropDown.vue';
import CanvasPreview from '@/components/CanvasPreview.vue';
import ModelUploadProgressModal from './components/ModelUploadProgressModal.vue';
import {
  addMaterial,
  addModelMaterial,
  addDefaultMaterialMeta,
  updateMaterial,
  getOrgnizationPackage,
  getOssAccessPath,
  updateDefaultMaterial,
  getUserStorage,
  deleteTmpMaterial,
  getMaterialGenStatus,
} from '@/api';
import { getModeType } from '@/api/modules/material';
import { materialType, materialUrls } from '@/config';
import { convertToLowerCase, createUuid, desensitizte } from '@/utils/index';
import axios from 'axios';
import { useStore } from 'vuex';
import TextCanvas from '@/components/TextCanvas.vue';

const store = useStore();

// 导入增强的loader配置
import { loader } from '@/config/threeJs';

const token = window.localStorage.getItem('token');
const uploadURL = ref(''); // 上传素材接口地址
const uploadThumbnail = ref(''); // 上传缩略图接口地址
const baseURL = process.env.NODE_ENV === 'production' ? '/api' : '/api1'; // 基础url
const loadedURL = ref(''); // 预览素材照片地址
const loadedThumbnail = ref(''); // 显示上传的缩略图地址
const thumbnailkey = ref(' '); // 缩略图上传radio的值
const defaultKey = ref(materialUrls[1].url_a); // 默认缩略图的key
const headerObj: any = ref({
  token: token || '',
});
const previewType = ref('video'); // 预览的类型
const singleUploadSize = ref(0); // 单次可以上传的大小
const updateType = ref('');
const showFormat = ref(false); // 是否显示格式下拉框
const materialAnimations = ref([]);
const njyj_version = window.localStorage.getItem('njyj-version');
const platforms: any = {
  web: 2,
};
const loadMap: any = ref({
  2: {
    type: 'web',
    tag: '3D模型',
    rule: '支持GLB/GLTF/FBX格式',
    rule2: '支持GLB/GLTF/FBX格式',
    value: 2,
  },
});
const uploadTips: any = {
  1: '支持MP4格式',
  2: '支持MP3格式',
  3: '支持png/jpg格式',
  4: '支持FBX、OBJ、GLB、GLTF格式模型',
};
const packageInfoDto: any = ref({});
const canvasRef = ref();
const headers: any = {
  'Content-Type': 'text/plain',
  token: token,
};
if (njyj_version) {
  headers['njyj-version'] = njyj_version;
  headerObj.value['njyj-version'] = njyj_version;
}
const canUsed = ref(0);
const textCanvasRef = ref();
const temporaryUuidArr: any = []; //存放上传时的uuid
const showTemporaryText = ref(false);
const showError = ref(false);
// 1. 新增一个变量保存上传的文件名
const uploadedModelName = ref('');
const lastUploadFileName = ref('');
const showUploadSuccess = ref(false);
let uploadSuccessTimer: any = null;
// 添加格式错误状态控制
const formatError = ref(false);

// 模型上传进度弹框相关状态
const showProgressModal = ref(false);
const modelUploadProgress = ref(0);
const modelUploadStatus = ref<'uploading' | 'processing' | 'completed' | 'error'>('uploading');
const progressIcon = require('@/assets/images/modelGif.gif');

// 轮询相关状态
const pollingInterval = ref<ReturnType<typeof setInterval> | null>(null);
const materialId = ref<number | null>(null);
const pollingRetryCount = ref(0);
const maxRetryCount = 15;

// 进度条动画相关状态
const progressAnimationInterval = ref<ReturnType<typeof setInterval> | null>(null);

// 模型分类相关数据
const modelCategories = ref<Array<{ label: string; value: string }>>([]);
const categoryLoading = ref(false);

// 上传素材的状态
const uploadStatus: any = ref({
  1: 'wait',
  2: 'wait',
  3: 'wait',
  4: {
    web: 'wait',
  },
  5: 'wait',
});

// 上传的状态用来区分样式
const uploadStatusMap: any = ref({
  wait: '未上传',
  uploading: '上传中',
  uploaded: '上传成功',
});

const showDeviceToUpload: any = ref([]); // 选择的设备对应需要上传的包

const props = defineProps({
  handleHide: {
    default: null,
    type: Function,
  },
  defaultValue: {
    default: null,
    type: Object,
  },
  materialAffiliation: {
    default: 1,
    type: Number,
  },
});

interface RuleForm {
  materialName: string;
  materialType: string;
  materialDescribe: string;
  ossKey: string;
  thumbnail: string;
  storageSize: string;
  materialFormat: string;
  equipType: string;
  materialWord: string;
  typeIdStr: string[];
}

const ruleFormRef = ref<FormInstance>();
const ruleForm: any = reactive<RuleForm>({
  materialName: '',
  materialType: '1',
  materialDescribe: '',
  ossKey: '',
  thumbnail: '',
  storageSize: '',
  materialFormat: '',
  equipType: '',
  materialWord: '',
  typeIdStr: [],
});

const rules = reactive<FormRules<RuleForm>>({
  materialName: [
    { required: true, message: '请输入素材名称', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value && value.trim() === '') {
          callback(new Error('素材名称不能全部为空格'));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
  materialType: [{ required: true, message: '请选择素材类型', trigger: 'blur' }],
  ossKey: [{ required: true, message: '请选择或上传素材', trigger: 'blur' }],
  equipType: [{ required: true, message: '请选择设备端', trigger: 'blur' }],
  materialWord: [{ required: true, message: '请输入文字', trigger: 'blur' }],
  typeIdStr: [
    {
      required: true,
      validator: (_rule: any, value: any, callback: any) => {
        // 只有当素材类型为模型且为公共素材时才验证
        if (ruleForm.materialType === '4' && props.materialAffiliation === 2) {
          if (!value || (Array.isArray(value) && value.length === 0)) {
            callback(new Error('公共素材必须选择模型分类'));
          } else if (Array.isArray(value) && value.length > 0) {
            // 检查是否只选择了"全部"或者选择了具体分类
            const hasValidSelection = value.includes('all') || value.some((v) => v !== 'all');
            if (!hasValidSelection) {
              callback(new Error('请选择有效的模型分类'));
            } else {
              callback();
            }
          } else {
            callback();
          }
        } else {
          // 私有素材不需要强制选择分类
          callback();
        }
      },
      trigger: ['change', 'blur'],
    },
  ],
});

const changeText = (val: string) => {
  textCanvasRef.value.canvasText(val, async (formData: any, dataURLBase64: string) => {
    const uuid = createUuid();
    const ossBack = await axios.post(
      `${baseURL}/material/uploadMaterial?fileName=${new Date().getTime()}&materialType=${
        ruleForm.materialType
      }&uuid=${uuid}`,
      formData,
      {
        headers,
      }
    );
    ruleForm.materialFormat = 'text';
    ruleForm.ossKey = ossBack.data.data.materialOssKey;
    temporaryUuidArr.push(uuid);

    const thumbnailBack = await axios.post(
      `${baseURL}/material/uploadThumbnail?fileName=${new Date().getTime()}`,
      dataURLBase64,
      {
        headers,
      }
    );
    thumbnailkey.value = thumbnailBack.data.data;
    loadedThumbnail.value = dataURLBase64;
  });
};

const errorUpload = (err: any, e2: any) => {
  if (e2.status == 'fail' && !String(err).includes('<!DOCTYPE html>') && !showError.value) {
    store.state.showTips = '上传失败，请检查格式或命名是否正确';
    if (ruleForm.materialType === '4') {
      uploadStatus.value[ruleForm.materialType][updateType.value] = 'wait';
    }
  }
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      // 提交时二次校验：公共素材的模型分类必须填写
      if (ruleForm.materialType === '4' && props.materialAffiliation === 2) {
        if (
          !ruleForm.typeIdStr ||
          (Array.isArray(ruleForm.typeIdStr) && ruleForm.typeIdStr.length === 0)
        ) {
          ElMessage.error({
            message: '公共素材必须选择模型分类，请选择至少一个分类后再提交',
            type: 'error',
            duration: 3000,
          });
          return;
        }

        // 检查是否选择了有效的分类
        const hasValidCategory =
          ruleForm.typeIdStr.includes('all') ||
          ruleForm.typeIdStr.some(
            (id: string) => id !== 'all' && modelCategories.value.some((cat) => cat.value === id)
          );

        if (!hasValidCategory) {
          ElMessage.error({
            message: '请选择有效的模型分类',
            type: 'error',
            duration: 3000,
          });
          return;
        }
      }

      if (!ruleForm.storageSize) {
        delete ruleForm.storageSize;
      }

      // 处理模型分类数据：将数组转换为逗号分隔的字符串
      if (ruleForm.materialType === '4') {
        if (ruleForm.typeIdStr && Array.isArray(ruleForm.typeIdStr)) {
          // 如果选择了"全部"，则不提交 typeIdStr 字段
          if (ruleForm.typeIdStr.includes('all')) {
            delete ruleForm.typeIdStr;
          } else {
            ruleForm.typeIdStr = ruleForm.typeIdStr.length > 0 ? ruleForm.typeIdStr.join(',') : '';
          }
        }
      } else {
        // 非模型类型时清空分类数据
        ruleForm.typeIdStr = '';
      }

      const hasSensitiveWords1 = await desensitizte(
        ruleForm.materialName,
        '素材名称不可包含敏感词汇！'
      );
      if (hasSensitiveWords1) return;
      // 对于3D模型，确保ossKey指向web端的模型文件
      if (ruleForm.materialType === '4') {
        // 简化逻辑：只需要web模型
        if (ruleForm.webModelStorageOssKey) {
          ruleForm.ossKey = ruleForm.webModelStorageOssKey;
        } else {
          console.warn('提交表单时发现web模型未上传');
        }
      }
      if (ruleForm.materialType == '5') {
        const hasSensitiveWords2 = await desensitizte(
          ruleForm.materialWord,
          '文字素材名称不可包含敏感词汇！'
        );
        if (hasSensitiveWords2) return;
      }
      // 选择默认不传路径，默认的是在本地
      if (ruleForm.thumbnail == defaultKey.value) {
        ruleForm.thumbnail = '';
      }
      let animationInfoDto: any = {
        animationCount: materialAnimations.value.length,
        animationList: materialAnimations.value,
        materialAffiliation: props.materialAffiliation,
      };
      if (props.defaultValue?.id) {
        animationInfoDto.materialId = props.defaultValue.id;
      }
      if (ruleForm.materialType != '4') {
        animationInfoDto = {};
        showDeviceToUpload.value = [];
      } else {
        ruleForm.platformV2 = showDeviceToUpload.value.join(',');
      }

      if (props.defaultValue?.id) {
        if (props.materialAffiliation == 2) {
          updateDefaultMaterial({ ...ruleForm, id: props.defaultValue.id, animationInfoDto }).then(
            (res: any) => {
              props.handleHide(true);
            }
          );
        } else {
          updateMaterial({ ...ruleForm, id: props.defaultValue.id, animationInfoDto }).then(
            (res: any) => {
              // 显示素材更新完成提示
              ElMessage({
                message: '素材更新完成',
                type: 'success',
              });

              props.handleHide(true);
            }
          );
        }
      } else {
        if (props.materialAffiliation == 2) {
          addDefaultMaterialMeta({ ...ruleForm, animationInfoDto }).then((res: any) => {
            // 显示默认素材添加完成提示
            ElMessage({
              message: '素材上传完成',
              type: 'success',
            });

            props.handleHide(true);
          });
        } else {
          console.log('提交前的ruleForm数据:', {
            materialType: ruleForm.materialType,
            ossKey: ruleForm.ossKey,
            webModelStorageOssKey: ruleForm.webModelStorageOssKey,
          });

          // 根据素材类型选择正确的API接口
          if (ruleForm.materialType === '4') {
            // 3D模型：根据格式自动设置 equipType
            const modelFormat = ruleForm.materialFormat?.toLowerCase();
            if (modelFormat === 'glb' || modelFormat === 'gltf') {
              // GLB/GLTF 支持所有平台
              ruleForm.equipType = '1,2,3';
              console.log('GLB/GLTF模型，设置equipType为全平台:', ruleForm.equipType);
            } else if (modelFormat === 'fbx') {
              // FBX 只支持眼镜端和移动端
              ruleForm.equipType = '1,2';
              console.log('FBX模型，设置equipType为眼镜端和移动端:', ruleForm.equipType);
            } else {
              // 默认情况，使用用户选择的设备端
              console.log('未知格式，使用用户选择的equipType:', ruleForm.equipType);
            }

            // 3D模型使用 addModelMaterial 接口
            // 显示进度弹框
            showProgressModal.value = true;
            modelUploadStatus.value = 'processing';
            modelUploadProgress.value = 0;

            addModelMaterial({ ...ruleForm, animationInfoDto, ossKey: ruleForm.ossKey })
              .then((res: any) => {
                console.log('addModelMaterial 提交成功:', res);

                // 获取返回的材料ID并开始轮询
                if (res.data) {
                  console.log('开始轮询，材料ID:', res.data);
                  startPolling(res.data);
                } else {
                  // 如果没有返回ID，显示错误
                  modelUploadStatus.value = 'error';
                  setTimeout(() => {
                    handleCloseProgressModal();
                  }, 2000);
                }
              })
              .catch((error: any) => {
                console.error('addModelMaterial 提交失败:', error);

                // 更新进度状态为错误
                modelUploadStatus.value = 'error';

                // 延迟关闭弹框
                setTimeout(() => {
                  handleCloseProgressModal();
                }, 2000);
              });
          } else {
            // 其他素材类型（图片、视频、音频等）：设置为全平台支持
            if (!ruleForm.equipType) {
              ruleForm.equipType = '1,2,3';
              console.log('非3D模型素材，设置equipType为全平台:', ruleForm.equipType);
            }

            // 其他素材类型（图片、视频、音频等）使用 addMaterial 接口
            addMaterial({ ...ruleForm, animationInfoDto })
              .then((res: any) => {
                console.log('addMaterial 提交成功:', res);

                // 显示素材上传完成提示
                ElMessage({
                  message: '素材上传完成',
                  type: 'success',
                });

                props.handleHide(true, ruleForm.materialType);
              })
              .catch((error: any) => {
                console.error('addMaterial 提交失败:', error);
              });
          }
        }
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

const changeState = () => {
  if (temporaryUuidArr.length) {
    showTemporaryText.value = true;
    store.state.showTips = '已上传的素材取消保存';
  } else {
    props.handleHide();
  }
};

const deleteTemporary = () => {
  deleteTmpMaterial(temporaryUuidArr).then((res) => {
    store.state.showTips = '';
    props.handleHide();
    showTemporaryText.value = false;
  });
};

// 开始进度条动画
const startProgressAnimation = () => {
  // 清除之前的动画
  stopProgressAnimation();

  progressAnimationInterval.value = setInterval(() => {
    if (modelUploadProgress.value < 90) {
      // 缓慢增长，每次增加1-3%
      const increment = Math.random() * 2 + 1;
      modelUploadProgress.value = Math.min(90, modelUploadProgress.value + increment);
    }
  }, 500); // 每500ms更新一次
};

// 停止进度条动画
const stopProgressAnimation = () => {
  if (progressAnimationInterval.value) {
    clearInterval(progressAnimationInterval.value);
    progressAnimationInterval.value = null;
  }
};

// 关闭进度弹框
const handleCloseProgressModal = () => {
  // 停止轮询和动画
  stopPolling();
  stopProgressAnimation();

  // 重置状态
  showProgressModal.value = false;
  modelUploadProgress.value = 0;
  modelUploadStatus.value = 'uploading';
  materialId.value = null;
  pollingRetryCount.value = 0;
};

// 开始轮询获取模型处理状态
const startPolling = (id: number) => {
  // 存储材料ID
  materialId.value = id;

  // 停止之前的轮询（如果有）
  stopPolling();

  // 开始进度条动画
  startProgressAnimation();

  // 立即执行第一次状态查询
  pollMaterialStatus(id);

  // 设置轮询间隔为2秒
  pollingInterval.value = setInterval(() => {
    if (materialId.value) {
      pollMaterialStatus(materialId.value);
    }
  }, 2000);
};

// 停止轮询
const stopPolling = () => {
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value);
    pollingInterval.value = null;
  }
  // 同时停止进度动画
  stopProgressAnimation();
};

// 轮询获取模型处理状态
const pollMaterialStatus = async (id: number) => {
  try {
    const res: any = await getMaterialGenStatus(id);

    if (res.code === '2000' || res.code === '200') {
      // 重置重试计数
      pollingRetryCount.value = 0;

      // 获取处理状态
      const status = res.data;

      // 根据状态更新进度
      if (status === 1) {
        // 添加生成 - 停止动画，设置固定进度
        stopProgressAnimation();
        modelUploadProgress.value = 20;
        modelUploadStatus.value = 'processing';
      } else if (status === 2) {
        // 等待生成 - 停止动画，设置固定进度
        stopProgressAnimation();
        modelUploadProgress.value = 50;
        modelUploadStatus.value = 'processing';
      } else if (status === 3) {
        // 生成错误 - 显示错误状态
        stopPolling();
        modelUploadStatus.value = 'error';

        // 延迟关闭弹框
        setTimeout(() => {
          handleCloseProgressModal();
        }, 2000);
      } else if (status === 4) {
        // 生成成功 - 立即设置100%并停止轮询
        stopPolling();
        modelUploadProgress.value = 100;
        modelUploadStatus.value = 'completed';

        // 延迟关闭弹框并显示成功提示
        setTimeout(() => {
          handleCloseProgressModal();

          // 显示成功提示
          ElMessage({
            message: '模型处理完成',
            type: 'success',
          });

          // 刷新页面或重新加载数据
          props.handleHide(true, ruleForm.materialType);
        }, 1000);
      }
    } else {
      handlePollingError();
    }
  } catch (error) {
    handlePollingError();
  }
};

// 处理轮询错误
const handlePollingError = () => {
  pollingRetryCount.value++;

  // 如果重试次数超过最大值，停止轮询并显示错误
  if (pollingRetryCount.value > maxRetryCount) {
    modelUploadStatus.value = 'error';
    stopPolling();

    // 延迟关闭弹框
    setTimeout(() => {
      handleCloseProgressModal();
    }, 2000);
  }
};

const changeMaterialType = async (v: string) => {
  defaultKey.value = materialUrls[v].url_a;
  previewType.value = materialUrls[v].type;
  // 重置格式错误状态
  formatError.value = false;
  if (ruleForm.materialType === '4') {
    uploadStatus.value[ruleForm.materialType].web = 'wait';
    // 简化：只需要 web 端
    showDeviceToUpload.value = [2];
    // 当切换到模型类型时，加载分类数据
    await loadModelCategories();
    // 如果没有选择分类，默认选择"全部"
    if (!ruleForm.typeIdStr || ruleForm.typeIdStr.length === 0) {
      ruleForm.typeIdStr = ['all'];
    }
  } else {
    showDeviceToUpload.value = [];
    uploadStatus.value[ruleForm.materialType] = 'wait';
    // 清空分类选择
    ruleForm.typeIdStr = [];
  }
  ruleForm.ossKey = '';
  loadedURL.value = '';
  loadedThumbnail.value = '';
  await getCurrentUserStorage();
};

// 加载模型分类数据
const loadModelCategories = async () => {
  try {
    categoryLoading.value = true;
    const response = await getModeType();

    if (response && response.data && Array.isArray(response.data)) {
      modelCategories.value = response.data.map((item: any) => ({
        label: item.typeName || item.name,
        value: String(item.id),
      }));
    }
  } catch (error) {
    console.error('获取模型分类失败:', error);
  } finally {
    categoryLoading.value = false;
  }
};

// 处理分类选择变化
const handleCategoryChange = (selectedValues: string[]) => {
  console.log('选中的分类:', selectedValues);

  // 如果选择了"全部"，清空其他选择
  if (selectedValues.includes('all')) {
    // 如果之前没有选择"全部"，现在选择了，则只保留"全部"
    if (selectedValues.length > 1) {
      ruleForm.typeIdStr = ['all'];
    }
  } else {
    // 如果没有选择"全部"，移除"全部"选项（如果存在）
    ruleForm.typeIdStr = selectedValues.filter((value) => value !== 'all');
  }
};

const handleChange = (file: any) => {
  if (!uploadURL.value) {
    loadedURL.value = '';
    return;
  }
  loadedURL.value = URL.createObjectURL(file.raw);
};

const handleChangeThumbnail = (file: any) => {
  if (uploadThumbnail.value) {
    loadedThumbnail.value = URL.createObjectURL(file.raw);
  } else {
    loadedThumbnail.value = '';
  }
};

// 检查 FBX 文件是否有效
const validateFBXFile = (file: File): Promise<boolean> => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer;
        if (!arrayBuffer) {
          resolve(false);
          return;
        }

        // 检查 FBX 文件头
        const view = new DataView(arrayBuffer);
        const header = new Uint8Array(arrayBuffer, 0, Math.min(23, arrayBuffer.byteLength));
        const headerString = String.fromCharCode.apply(null, Array.from(header));

        // FBX 文件应该以 "Kaydara FBX Binary" 开头（二进制格式）
        // 或者包含 "FBX" 字符串（ASCII 格式）
        if (headerString.includes('Kaydara FBX Binary') || headerString.includes('FBX')) {
          resolve(true);
        } else {
          resolve(false);
        }
      } catch (error) {
        console.error('FBX 文件验证失败:', error);
        resolve(false);
      }
    };
    reader.onerror = () => resolve(false);
    reader.readAsArrayBuffer(file.slice(0, 1024)); // 只读取前1KB用于验证
  });
};

// 报错处理
const errorEvent = (msg: string, type?: number) => {
  store.state.showTips = msg;
  if (type) return;
  uploadURL.value = '';
  if (ruleForm.materialType === '4') {
    uploadStatus.value[ruleForm.materialType][updateType.value] = 'wait';
  } else {
    uploadStatus.value[ruleForm.materialType] = 'wait';
  }
};

const beforeUpload = async (rawFile: any) => {
  lastUploadFileName.value = rawFile.name;
  const uuid = createUuid();
  await getCurrentUserStorage();
  if (rawFile.size / 1024 / 1024 > singleUploadSize.value) {
    errorEvent('您上传的资源大于您当前套餐单次可上传资源大小');
    return false;
  }
  console.log(
    rawFile.size / 1024 / 1024 > canUsed.value,
    rawFile.size / 1024 / 1024,
    canUsed.value
  );
  if (rawFile.size / 1024 / 1024 > canUsed.value) {
    errorEvent('您上传的资源大于您当前套餐可上传资源总量');
    return false;
  }

  const expectFormat = materialUrls[ruleForm.materialType].format;
  let sourceFormat = rawFile.name.split('.')[1];
  if (sourceFormat) {
    sourceFormat = sourceFormat.toUpperCase().toLowerCase();
  }

  // 检查模型格式，如果不是 glb 或 fbx，设置格式错误状态
  if (ruleForm.materialType === '4' && updateType.value === 'web') {
    if (sourceFormat !== 'glb' && sourceFormat !== 'fbx') {
      formatError.value = true;
    } else {
      formatError.value = false;

      // 如果是 FBX 文件，进行额外的文件有效性检查
      if (sourceFormat === 'fbx') {
        const isValidFBX = await validateFBXFile(rawFile);
        if (!isValidFBX) {
          errorEvent('上传的 FBX 文件格式不正确或文件已损坏，请检查文件是否为有效的 FBX 格式');
          formatError.value = true;
          return false;
        }
      }
    }
  } else {
    formatError.value = false;
  }
  if (!expectFormat.includes(sourceFormat)) {
    errorEvent('您上传的素材格式或命名不正确，请上传与要求相符的素材。');
    return false;
  }
  if (ruleForm.materialType === '4') {
    uploadStatus.value[ruleForm.materialType][updateType.value] = 'uploading';
    if (updateType.value == 'web') {
      // 修复：允许所有支持的3D格式，包括FBX
      if (sourceFormat != 'glb' && sourceFormat != 'gltf' && sourceFormat != 'fbx') {
        errorEvent(
          '您上传的素材格式或命名不正确，请上传与要求相符的素材。支持格式：GLB、GLTF、FBX'
        );
        return false;
      }

      // 如果是眼镜端且使用FBX格式，给出提示但不阻止上传
      if (ruleForm.equipType.includes('3') && sourceFormat === 'fbx') {
        console.warn('注意：眼镜端推荐使用GLB或GLTF格式以获得更好的性能');
      }
      ruleForm.materialFormat = rawFile.name?.split('.').slice(-1)[0];
      uploadURL.value = `${baseURL}/material/uploadMaterial?fileName=${rawFile.name}&materialType=${
        ruleForm.materialType
      }&platformV2=${loadMap.value[platforms[updateType.value]].value}&modelFormat=${
        ruleForm.materialFormat
      }&uuid=${uuid}`;
      temporaryUuidArr.push(uuid);
      ruleForm.storageSize = rawFile.size;
    }
    // 移除了非 web 端的处理逻辑，因为现在只支持 web 端
  } else {
    uploadURL.value = `${baseURL}/material/uploadMaterial?fileName=${rawFile.name}&materialType=${ruleForm.materialType}&uuid=${uuid}`;
    temporaryUuidArr.push(uuid);
    uploadStatus.value[ruleForm.materialType] = 'uploading';
    ruleForm.materialFormat = rawFile.name?.split('.').slice(-1)[0];
    ruleForm.storageSize = rawFile.size;
  }
  loadedURL.value = '';
};

const beforeUploadThumbnail = (rawFile: any) => {
  let sourceFormat = rawFile.name.split('.')[1];
  if (sourceFormat) {
    sourceFormat = sourceFormat.toUpperCase().toLowerCase();
  }
  if (!['png', 'jpg'].includes(sourceFormat)) {
    errorEvent('您上传的素材格式或命名不正确，请上传与要求相符的素材。', 1);
    uploadThumbnail.value = '';
    return;
  }
  if (rawFile.size / 1024 > 512) {
    errorEvent('您上传的素材大小超出了限制，请检查后再上传。', 1);
    uploadThumbnail.value = '';
    return;
  }
  uploadThumbnail.value = `${baseURL}/material/uploadThumbnail?fileName=${rawFile.name}`;
};

const uploaded = async (res: any, type?: string) => {
  console.log('上传返回数据:', res.data);
  await getCurrentUserStorage();
  // 上传成功后重置格式错误状态
  formatError.value = false;
  if (
    (uploadStatus.value[ruleForm.materialType] == 'wait' && ruleForm.materialType != '4') ||
    (type &&
      uploadStatus.value[ruleForm.materialType][type] == 'wait' &&
      ruleForm.materialType == '4')
  )
    return;
  if (ruleForm.materialType === '4') {
    ruleForm[type + 'ModelStorageOssKey'] = res.data.materialOssKey;
    if (type) {
      uploadStatus.value[ruleForm.materialType][type] = 'uploaded';
      // 只对 web 类型记录文件名
      if (type === 'web') {
        uploadedModelName.value = lastUploadFileName.value;
        showUploadSuccess.value = true;
        if (uploadSuccessTimer) clearTimeout(uploadSuccessTimer);
        uploadSuccessTimer = setTimeout(() => {
          showUploadSuccess.value = false;
        }, 3000);
      }
    }
    if (type == 'web') {
      ruleForm.ossKey = ruleForm['webModelStorageOssKey'];

      // 根据模型格式自动设置 equipType
      const modelFormat = ruleForm.materialFormat?.toLowerCase();
      if (modelFormat === 'glb' || modelFormat === 'gltf') {
        // GLB/GLTF 支持所有平台
        ruleForm.equipType = '1,2,3';
        console.log('上传GLB/GLTF模型，自动设置equipType为全平台:', ruleForm.equipType);
      } else if (modelFormat === 'fbx') {
        // FBX 只支持眼镜端和移动端
        ruleForm.equipType = '1,2';
        console.log('上传FBX模型，自动设置equipType为眼镜端和移动端:', ruleForm.equipType);
      }

      getOssAccessPath({ key: res.data.materialOssKey }).then((res1: any) => {
        console.log('开始加载3D模型:', ruleForm.materialFormat, res1.data);
        loader[ruleForm.materialFormat].load(
          res1.data,
          function (object: any) {
            console.log('3D模型加载成功:', object);

            try {
              // 处理动画
              const animations = object.animations || [];
              console.log('模型动画数量:', animations.length);
              if (animations.length) {
                materialAnimations.value = animations.map((e: any) => ({ name: e.name }));
              } else {
                materialAnimations.value = [];
              }

              // 确定要添加到场景的对象
              const modelToAdd = object.scene || object;
              console.log('准备添加到场景的对象:', modelToAdd);

              // 检查模型是否有几何体
              let hasGeometry = false;
              let meshCount = 0;
              modelToAdd.traverse((child: any) => {
                if (child.isMesh) {
                  meshCount++;
                  if (child.geometry) {
                    hasGeometry = true;
                    console.log('发现网格:', child.name || 'unnamed mesh');
                  }
                }
              });

              console.log(`模型包含 ${meshCount} 个网格对象，${hasGeometry ? '有' : '没有'}几何体`);

              if (!hasGeometry) {
                console.warn('警告：模型中没有发现几何体，可能导致渲染问题');
              }

              // 添加到场景
              if (canvasRef.value && canvasRef.value.addMesh) {
                canvasRef.value.addMesh(modelToAdd, getImg);
              } else {
                console.error('canvasRef.value 或 addMesh 方法不存在');
              }

              // 添加调试信息
              setTimeout(() => {
                if (canvasRef.value && canvasRef.value.debugScene) {
                  canvasRef.value.debugScene();
                }
              }, 500);

              function getImg() {
                console.log('模型添加完成，准备截图');

                setTimeout(() => {
                  handleMouseUp();
                }, 200);
              }
            } catch (error) {
              console.error('处理模型时发生错误:', error);
            }
          },
          function (progress: any) {
            // 加载进度回调
            console.log('模型加载进度:', progress);
          },
          function (error: any) {
            // 错误处理回调
            console.error('模型加载失败:', error);
            let errorMessage = '模型文件加载失败，请检查文件格式是否正确或文件是否损坏';

            // 针对 FBX 文件的特殊错误提示
            if (ruleForm.materialFormat === 'fbx' && error.message) {
              if (error.message.includes('version number')) {
                errorMessage =
                  'FBX 文件版本不支持或文件已损坏。建议：1) 使用较新版本的建模软件重新导出 FBX；2) 尝试导出为 GLB 格式；3) 检查文件是否完整下载';
              } else if (error.message.includes('skinning weights')) {
                errorMessage =
                  'FBX 模型的骨骼权重超出了 WebGL 的限制。建议：1) 在建模软件中减少每个顶点的骨骼权重数量；2) 尝试导出为 GLB 格式';
              }
            }

            store.state.showTips = errorMessage;
            // 重置上传状态
            uploadStatus.value[ruleForm.materialType][type] = 'wait';
            formatError.value = true;
          }
        );
      });
    }
    // 简化逻辑：只需要 web 模型，直接设置 ossKey
    if (ruleForm['webModelStorageOssKey']) {
      ruleForm.ossKey = ruleForm['webModelStorageOssKey'];
      console.log('设置 ossKey 为 web 模型:', ruleForm.ossKey);
    } else {
      console.warn('警告: web 模型未上传，ossKey 将为空');
      ruleForm.ossKey = '';
    }
  } else {
    ruleForm.ossKey = res.data.materialOssKey;

    // 非3D模型素材：设置为全平台支持
    if (!ruleForm.equipType) {
      ruleForm.equipType = '1,2,3';
      console.log('非3D模型素材上传成功，设置equipType为全平台:', ruleForm.equipType);
    }

    if (ruleForm.materialType === '1') {
      thumbnailkey.value = res.data.vedioFrameOssKey;
      getOssAccessPath({ key: res.data.vedioFrameOssKey }).then((res1: any) => {
        loadedThumbnail.value = res1.data;
      });
    }
    if (ruleForm.materialType === '3') {
      thumbnailkey.value = ruleForm.ossKey;
      getOssAccessPath({ key: ruleForm.ossKey }).then((res1: any) => {
        loadedThumbnail.value = res1.data;
      });
    }
    uploadStatus.value[ruleForm.materialType] = 'uploaded';
  }
};

const handleMouseUp = async () => {
  const renderer = canvasRef.value.getRenderer();
  const camera = canvasRef.value.getCamera();
  const scene = canvasRef.value.getScene();
  renderer.render(scene, camera);
  let imgData = renderer.domElement.toDataURL('image/png');
  loadedThumbnail.value = imgData;

  const { data } = await axios.post(
    `${baseURL}/material/uploadThumbnail?fileName=${new Date().getTime()}`,
    imgData,
    {
      headers,
    }
  );
  thumbnailkey.value = data.data;
};

const uploadedThumbnail = (res: any) => {
  thumbnailkey.value = res.data;
};

const getCurrentUserStorage = async () => {
  await getUserStorage().then((res: any) => {
    if (res.code == 200) {
      canUsed.value = res.data.userPackageStorage - res.data.userUsedStorage;
    }
  });
};

// 判断平台icon类型
function platformIconType(platform: string) {
  const name = uploadedModelName.value || '';
  const ext = name.split('.').pop()?.toLowerCase();
  if (ext === 'glb') return 'success';
  if (ext === 'fbx') {
    if (platform === 'wechat') return 'fail';
    return 'success';
  }
  // 其它格式默认都显示勾
  return 'success';
}

onMounted(async () => {
  uploadURL.value = '';
  uploadThumbnail.value = `${baseURL}/material/uploadThumbnail`;

  packageInfoDto.value = JSON.parse(window.sessionStorage.getItem('packageInfoDto') || '{}');

  // 在组件挂载时就加载模型分类数据
  await loadModelCategories();

  // 处理defaultValue中的materialType（新建和编辑模式都支持）
  if (props.defaultValue?.materialType) {
    ruleForm.materialType = props.defaultValue.materialType;
  }

  // 编辑页初始数据
  if (props.defaultValue?.id) {
    if (props.defaultValue.ossKey && !props.defaultValue.modelStorageMap) {
      await getOssAccessPath({ key: props.defaultValue.ossKey }).then((res1: any) => {
        loadedURL.value = res1.data;
      });
    }
    ruleForm.materialName = props.defaultValue.materialName || '';
    ruleForm.materialWord = props.defaultValue.materialWord || '';
    ruleForm.materialDescribe = props.defaultValue.materialDescribe || '';
    // ruleForm.storageSize = props.defaultValue.storageSize || ''
    ruleForm.materialFormat = props.defaultValue.materialFormat || '';
    ruleForm.ossKey = props.defaultValue.ossKey || '';

    // 处理模型分类数据回显
    if (ruleForm.materialType === '4') {
      await loadModelCategories();
      // 如果有已选中的分类数据，进行回显
      if (
        props.defaultValue.modelTypeDtoList &&
        Array.isArray(props.defaultValue.modelTypeDtoList)
      ) {
        ruleForm.typeIdStr = props.defaultValue.modelTypeDtoList.map((item: any) =>
          String(item.id)
        );
      } else if (props.defaultValue.typeIdStr) {
        // 如果是字符串格式，转换为数组
        ruleForm.typeIdStr = props.defaultValue.typeIdStr
          .split(',')
          .filter((id: string) => id.trim());
      } else {
        // 如果没有分类数据，默认选择"全部"
        ruleForm.typeIdStr = ['all'];
      }
    }
    loadedThumbnail.value = props.defaultValue.thumbnailOssAccessUrl || '';
    if (props.defaultValue.thumbnail) {
      await getOssAccessPath({ key: props.defaultValue.thumbnail }).then((res1: any) => {
        loadedThumbnail.value = res1.data;
      });
    }

    if (ruleForm.materialType != 4) {
      uploadStatus.value[ruleForm.materialType] = 'uploaded';
      // 非3D模型：设置为全平台支持（如果没有设置的话）
      if (!ruleForm.equipType && props.defaultValue.equipType) {
        ruleForm.equipType = props.defaultValue.equipType;
      } else if (!ruleForm.equipType) {
        ruleForm.equipType = '1,2,3';
        console.log('编辑模式：非3D模型设置equipType为全平台:', ruleForm.equipType);
      }
    }
    thumbnailkey.value = props.defaultValue.thumbnail;
    if (loadedURL.value) {
      previewType.value = materialUrls[ruleForm.materialType].type;
    }

    if (props.defaultValue.modelStorageMap) {
      const { web } = props.defaultValue.modelStorageMap;
      // 简化：只处理 web 端模型
      ruleForm.webModelStorageOssKey = web.ossKey;
      ruleForm.ossKey = web.ossKey;

      // 只设置 web 端为已上传状态
      uploadStatus.value[ruleForm.materialType].web = 'uploaded';

      if (props.defaultValue.equipType) {
        ruleForm.equipType = props.defaultValue.equipType;
      }
      getOssAccessPath({ key: props.defaultValue.modelStorageMap.web.ossKey }).then((res1: any) => {
        loader[ruleForm.materialFormat].load(
          res1.data,
          function (object: any) {
            const animations = object.animations;
            if (animations.length) {
              materialAnimations.value = animations.map((e: any) => ({ name: e.name }));
            } else {
              materialAnimations.value = [];
            }
            if (object.scene) {
              canvasRef.value.addMesh(object.scene);
            } else {
              canvasRef.value.addMesh(object);
            }
          },
          function (progress: any) {
            // 加载进度回调（可选）
            console.log('Loading progress:', progress);
          },
          function (error: any) {
            // 错误处理回调
            console.error('编辑模式模型加载失败:', error);
            let errorMessage = '模型文件加载失败，请检查文件格式是否正确或文件是否损坏';

            // 针对 FBX 文件的特殊错误提示
            if (
              ruleForm.materialFormat === 'fbx' &&
              error.message &&
              error.message.includes('version number')
            ) {
              errorMessage =
                'FBX 文件版本不支持或文件已损坏。建议重新上传有效的 FBX 文件或使用 GLB 格式';
            }

            store.state.showTips = errorMessage;
          }
        );
      });
    }
  }

  getOrgnizationPackage({}).then((res: any) => {
    const packageInfoDto =
      res.data.userDto.packageVersion == 'V2'
        ? res.data.userBindPackageDto
        : res.data.packageInfoDto;
    const packageExtensionDtos =
      res.data.userDto.packageVersion == 'V2'
        ? res.data.userBindPackageExtensionDtos
        : res.data.packageExtensionDtos;
    singleUploadSize.value += packageInfoDto.singleUploadSize || 100;
    packageExtensionDtos.forEach((e: any) => {
      singleUploadSize.value += e.singleUploadSize;
    });
  });
  await getCurrentUserStorage();
  if (ruleForm.materialType === '4') {
    // 简化：只需要 web 端（值为2）
    showDeviceToUpload.value = [2];
  } else {
    showDeviceToUpload.value = [];
  }
});

// 组件卸载时清理轮询定时器
onUnmounted(() => {
  stopPolling();
});

watch(thumbnailkey, (newState) => {
  if (newState != defaultKey.value && newState) {
    ruleForm.thumbnail = newState;
  }
});
watch(loadedThumbnail, (newState) => {
  if (newState) {
    setTimeout(() => {
      const imgInfo = document.querySelector('.pic-model>div>img')?.getBoundingClientRect();
      if (ruleForm.materialType !== '1') {
        ruleForm.materialMediaInfoDto = {
          width: imgInfo?.width,
          height: imgInfo?.height,
        };
      }
    }, 200);
  } else {
    delete ruleForm.materialMediaInfoDto;
  }
});

watch(
  () => store.state.showTips,
  (newState) => {
    showError.value = !!newState;
  }
);
</script>
<style scoped lang="less">
.modal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;
  -webkit-user-select: none;
  /* Chrome, Safari, Opera */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;

  .modal-content {
    width: 453px;
    max-height: 94%;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #edeff2;
    overflow: hidden;
    overflow-y: auto;
    color: #1e1e1e;

    .modal-content-title {
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 18px;
      color: #1e1e1e;
      padding: 16px 0 8px 24px;
    }

    .modal-form {
      width: 100%;
      height: calc(100% - 76px);
      padding: 16px 24px;
      box-sizing: border-box;
      overflow: hidden;
      overflow-y: auto;

      .upload-tips {
        font-size: 14px;
        text-align: left;
        padding-left: 52px;
        margin-top: -5px;
        font-weight: bold;
        color: rgba(61, 86, 108, 0.3);
      }

      .upload-tips-text {
        position: absolute;
        top: 42px;
        left: 10px;
        font-size: 12px;
        color: #333;
        width: 100%;
        text-align: center;
      }

      .tips-text {
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        line-height: 14px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        margin-left: 12px;

        & > div:last-child {
          margin-top: 8px;
        }
      }

      .form-input {
        width: 432px;
        height: 36px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .loaded-img {
        width: 100%;
        height: 100%;
        display: inline-block;
      }

      .preview {
        position: relative;
        width: 146px;
        height: 146px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #dadada;
      }

      .preview-audio {
        background-color: #d0d0d0;
        background-image: url(~@/assets/images/background/audio.png);
        background-repeat: no-repeat;
        background-position: 50% 50%;
        border-radius: 8px;
      }

      .upload-box-style {
        position: relative;
        display: flex;
        flex-direction: column;

        .upload-material-tips {
          position: absolute;
          left: 78px;
          top: -31px;
          font-weight: 400;
          font-size: 12px;
          color: #797979;
        }
      }

      .upload-material-style {
        position: relative;
        width: 100%;
        height: 124px;
        border-radius: 10px;
        border: 1px dashed #dadada;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 12px;
        color: #797979;
        box-sizing: border-box;
        padding-top: 2px;

        &:hover {
          border-color: #2e76ff;

          button {
            background-color: #2e76ff;
            color: #fff;
          }
        }

        & > div {
          line-height: 1.5;
        }

        .upload-btn {
          margin: 8px 0;
        }

        button {
          position: relative;
          border: none;
          background-color: rgba(46, 118, 255, 0.2);
          font-weight: 400;
          font-size: 12px;
          color: #2e76ff;
          padding: 0;
          width: 60px;
          height: 18px;
          text-align: center;

          &.uploading {
            color: #797979;
            background-color: rgba(0, 0, 0, 0.1);
            cursor: not-allowed;
          }
        }

        .upload-tips {
          padding-left: 0;
          color: #1e1e1e;
        }

        .uploading-style,
        .uploaded-style {
          position: relative;
        }
        .complete-icon {
          display: inline-block;
          width: 14px;
          height: 14px;
          margin-left: 6px;
          background-image: url(~@/assets/images/icon/complete-icon.png);
          background-size: 100% 100%;
          vertical-align: middle;
        }
        .uploading-style::before {
          content: '';
          width: 14px;
          height: 14px;
          background-image: url(~@/assets/images/icon/loading-icon.png);
          background-size: 100% 100%;
          position: absolute;
          left: 0;
          top: 2px;
          animation: rotate 1.5s linear infinite;
        }
        /* 删除.uploaded-style::after，避免重复icon */
        .platform-icon {
          display: inline-block;
          width: 14px;
          height: 14px;
          margin-left: 6px;
          background-image: url(~@/assets/images/icon/complete-icon.png);
          background-size: 100% 100%;
          vertical-align: middle;
        }
        .platform-icon.fail {
          background-image: url(~@/assets/images/icon/fail-icon.png);
        }

        /* 格式错误时的样式 */
        &.format-error {
          border-color: #f2994a !important;

          &:hover {
            border-color: #f2994a !important;
          }
        }
      }

      .thumbnail-show {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        & > div:first-child {
          width: 92px;
          height: 92px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #dadada;

          .thumbnail-icon-upload {
            width: 92px;
            height: 92px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }

        .thumbnail-text {
          font-weight: 400;
          font-size: 12px;
          color: #797979;
          line-height: 14px;
          margin-left: 12px;
          text-align: left;

          & > div:last-child {
            margin-top: 8px;
          }
        }
      }

      .upload-model-box {
        width: 100%;
        height: 124px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        & > .upload-material-style.styleFor3 {
          width: 124px;
        }

        & > .upload-material-style.styleFor2 {
          width: 196px;
        }

        & > .upload-material-style.styleFor1 {
          width: 100%;
        }
      }
    }

    ::v-deep(.modal-form .el-form > div) {
      &.form-submit {
        margin: 24px 0 0 !important;
      }
    }

    ::v-deep(.el-form-item__label) {
      color: #797979;
    }

    ::v-deep(.el-input__wrapper .el-input__inner) {
      color: #1e1e1e;
    }
  }

  .pic-model {
    // position: relative;
    position: fixed;
    left: 0;
    top: 0;
    font-size: 0;
    z-index: -1;
    opacity: 0;

    img {
      display: block;
    }
  }
}

.thumbnail-icon {
  width: 49px;
  height: 49px;
  background-image: url(~@/assets/images/icon/source-bg.png);

  img {
    width: 100%;
    height: 100%;
  }
}

.uploadMask {
  position: absolute;
  left: 0;
  top: 0;
  width: 105px;
  height: 36px;
  background-color: transparent;
  z-index: 10;
  cursor: pointer;
}

.el-size3 {
  width: 92px;
  height: 32px;
  margin-left: 12px;
}

.el-size {
  width: 102px;
  height: 36px;
  box-sizing: border-box;
  margin-right: 95px;

  .el-button {
    background: rgba(46, 118, 255, 0.1) !important;
  }
}

.upload-demo {
  position: relative;
  display: inline-block;

  .progress {
    position: absolute;
    left: 0px;
    top: -18px;
    width: 105px;
  }

  .complete-icon {
    width: 16px;
    position: absolute;
    left: 115px;
    height: 16px;
    line-height: 16px;
    top: -2px;
  }
}

.el-upload__text > span {
  margin-left: 3px;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.model-category-tip {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.platform-icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-left: 6px;
  background-image: url(~@/assets/images/icon/complete-icon.png);
  background-size: 100% 100%;
  vertical-align: middle;
}
.platform-icon.fail {
  background-image: url(~@/assets/images/icon/fail-icon.png);
}

// 模型分类提示样式
.model-category-tip {
  margin-bottom: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}
</style>
