<template>
  <!-- 新增顶部Header区域 -->
  <div class="page-header">
    <div class="menu-back" @click="exitEdit">
      <el-button link class="back-btn" @click="$router.back()">
        <el-icon><ArrowLeft /></el-icon>

        <span style="margin-left: 4px; margin-right: 6px; color: #797979">返回</span>
      </el-button>
      <span class="linkName">{{ graphInfo.graphName || '路径导航名称' }}</span>
    </div>
    <div class="header-actions">
      <div class="btn preview-btn" @click="showPreview = true">预览</div>
      <div class="btn save-btn" @click="handleSave">保存</div>
    </div>
  </div>

  <!-- 主要内容区域 -->
  <div class="main-content">
    <!-- 左侧栏 -->
    <div class="left-sidebar">
      <div class="position-point">
        <span @click="backToPathSettings">设置路径</span>
        <span
          class="point-icon"
          @click="canAddNode ? (addPointState = !addPointState) : handleMaxNodeReached()"
          :class="[addPointState ? 'add-point' : '', !canAddNode ? 'disabled' : '']"></span>
      </div>

      <!-- 路径点数量显示区域 -->
      <div class="point-count-display">
        <span style="margin-right: 15px">导航点</span>
        <span style="font-weight: bold; font-size: 12px; color: #1e1e1e">
          {{ String(navigationPointsCount).padStart(2, '0') }}/30
        </span>
        <span style="margin-left: 15px">个</span>
      </div>

      <div class="point-box">
        <div
          v-for="(item, index) in graphPoints"
          :key="index"
          @click.stop="activePointEvent(index)"
          :class="activePointIndex == index ? 'active' : ''">
          <div style="padding-left: 4px" v-if="editIndex != index">
            <span
              class="point-child-icon"
              :class="item.nodeType == 3 ? 'point-child-icon2' : ''"></span>
            <span class="node-name">{{ item.nodeName }}</span>
          </div>
          <div v-if="editIndex != index">
            <div class="delete-icon" @click.stop="deletePointSure(index)"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间编辑器区域 -->
    <div class="center-editor">
      <div id="canvas-path" class="canvas-view"></div>
      <div
        class="point-follow"
        :style="{
          width: pointInitSize + 'px',
          height: pointInitSize + 'px',
        }"
        @mousemove="handleIconMove"
        @mouseup="handleIconUp"
        @mousedown="mousedownCanvas"></div>
    </div>

    <!-- 右侧栏 -->
    <div class="right-sidebar">
      <!-- 动态标题 -->
      <div class="sidebar-title">
        <span>{{ activePointIndex === -1 ? '路径信息' : '点位信息' }}</span>
      </div>

      <div class="sidebar-content">
        <!-- 默认状态：路径信息编辑 -->
        <template v-if="activePointIndex === -1">
          <div class="info-item">
            <label>路径名称：</label>
            <el-input
              v-model="graphInfo.graphName"
              placeholder="请输入路径名称"
              class="custom-input" />
          </div>
          <div class="info-item">
            <label>路径描述：</label>
            <div class="textarea-wrapper">
              <el-input
                v-model="graphInfo.graphInfo"
                type="textarea"
                placeholder="请输入路径描述"
                :rows="3"
                show-word-limit="true"
                maxlength="60"
                class="custom-textarea"
                style="height: auto; min-height: 80px; padding-bottom: 25px" />
              <div class="word-count">{{ (graphInfo.graphInfo || '').length }}/60</div>
            </div>
          </div>
        </template>

        <!-- 点位选中状态：点位信息编辑 -->
        <template v-else-if="activePointIndex >= 0 && graphPoints[activePointIndex]">
          <div class="info-item">
            <label>点位名称：</label>
            <el-input
              v-model="graphPoints[activePointIndex].nodeName"
              @blur="changeNodeName"
              maxlength="20"
              placeholder="请输入点位名称"
              class="custom-input" />
          </div>
          <div class="info-item">
            <label>点位状态：</label>
            <div style="height: auto; overflow: visible; text-align: left; padding: 8px 0">
              <div class="is-operate-change">
                <div
                  v-for="(item, index) in pointTypes"
                  :class="[
                    'point-type-btn',
                    graphPoints[activePointIndex].nodeType == item.value ? 'active' : '',
                  ]"
                  :key="index"
                  @click="changeNodeType(item.value)">
                  {{ item.label }}
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>

  <tips-view
    show-btn="确定"
    tips-title="删除定位点"
    :sure-event="deletePoint"
    v-if="isDeleteTips"></tips-view>
  <graph-preview
    titleName="路径效果预览"
    v-if="showPreview"
    :spaceInfo="currentSpace"
    :hideModel="hidePreview"
    :resData="[...childNodeArray, ...graphPoints]"></graph-preview>

  <div class="point-follow2" :style="{ height: pointInitSize + 'px' }">
    <div></div>
    <img
      :style="{ width: pointInitSize + 'px', height: pointInitSize + 'px' }"
      src="/images/invalid-default.png" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, onUnmounted, nextTick, computed } from 'vue';
import { ArrowLeft } from '@element-plus/icons-vue';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import {
  Vector3,
  Scene,
  OrthographicCamera,
  WebGLRenderer,
  PlaneGeometry,
  AmbientLight,
  RepeatWrapping,
  MOUSE,
  TOUCH,
  MeshBasicMaterial,
  TextureLoader,
  Mesh,
  Raycaster,
  Group,
  Box3,
} from 'three';
import { loadZipFileForJSZip, createUuid, worldToScreenVector } from '@/utils';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { CPoint, CLine } from '@/core/primitives';
import { CPointAttrs } from '@/core/primitives/CPoint';
import {
  saveNjyjNodes,
  getNjyjNodes,
  getNaviRoute,
  getNjyjGraphById,
  updateNjyjGraph,
  getSpaceById,
  getOssAccessPath,
  getNaviRouteByLocalize,
} from '@/api/index';
import { useRouter } from 'vue-router';
import { MouseStyle, updateMouseStyle } from '@/core/mouse';
import { ElMessage, ElMessageBox } from 'element-plus';
import TipsView from '@/components/TipsView.vue';
import { useStore } from 'vuex';
import GraphPreview from '@/components/preview/GraphPreview.vue';
import { base64ToBlob } from '@/utils/index';
import axios from 'axios';

let camera: OrthographicCamera;
let width: number, height: number;
let scene: Scene;
let renderer: any;
const router = useRouter();
const graphId = ref(0);
const graphPoints: any = ref([]);
let drawLine: any = [];
const lineArrs = [];
const currentId = ref('');
const isActiveRouter = ref(false); // 是否是路径规划模式
const activePointIndex = ref(-1); // 左侧高亮路线index
const editIndex = ref(-1); // 编辑的线路index
const addPointState = ref(false); // 打点状态
let Icondom: any = null; // 跟随鼠标移动的点
let hoveStyleDom: any = null; // 悬浮时候显示的箭头等点位
const activeObjectName = ref(''); // 被选中的点的名称
let activeObject: any = null; // 被选中的点的对象
const unitDistance = 3; // 默认两点之间距离
let hoverActivePoint: any = null; // 被悬浮高亮的点位
let previewNode: any = null; // 透明预览节点
const isDrag = ref(false); // 是否拖拽高亮点
const isDragPath = ref(false);
let dynamicLine: any = null;
let dragStart: any = null;
const graphInfo: any = ref({});
const controlsScale = ref(1);
const unitDis = 0.5; // 小的子节点一份的数量
const maxNodes = 30; // 最大节点数量限制
const childNodeArray: any = ref([]); // 线中间的点位集合

// 计算导航点数量（只统计nodeType为3的点）
const navigationPointsCount = computed(() => {
  return graphPoints.value.filter((point: any) => point.nodeType === 3).length;
});

// 计算是否可以添加新节点
const canAddNode = computed(() => {
  return graphPoints.value.length < maxNodes;
});
const isDeleteTips = ref(false);
const showPreview = ref(false);
let currentSpace: any = ref({});
const pointInitSize = 66; // 节点图标的固定屏幕像素大小，不随3D场景缩放变化
let animationFrameId: any = null;
const pointTypes = [
  {
    label: '路径点',
    value: 1,
  },
  {
    label: '导航点',
    value: 3,
  },
];
const token = window.localStorage.getItem('token');
const njyj_version = window.localStorage.getItem('njyj-version');
const baseURL = process.env.NODE_ENV === 'production' ? '/api' : '/api1'; // 基础url
const headers: any = {
  'Content-Type': 'text/plain',
  token: token,
};
if (njyj_version) {
  headers['njyj-version'] = njyj_version;
}

// 无效高亮texture
const acTexture = new TextureLoader().load('/images/invalid-active.png');
acTexture.wrapS = acTexture.wrapT = RepeatWrapping;

// 无效默认texture
const deTexture = new TextureLoader().load('/images/invalid-default.png');
deTexture.wrapS = deTexture.wrapT = RepeatWrapping;

// 有效高亮texture
const validActive = new TextureLoader().load('/images/valid-active.png');
validActive.wrapS = validActive.wrapT = RepeatWrapping;

// 有效默认texture
const validDefault = new TextureLoader().load('/images/valid-default.png');
validDefault.wrapS = validDefault.wrapT = RepeatWrapping;

// 无效高亮texture
const acTexture2 = new TextureLoader().load('/images/invalid-active2.png');
acTexture2.wrapS = acTexture2.wrapT = RepeatWrapping;

// 无效默认texture
const deTexture2 = new TextureLoader().load('/images/invalid-default2.png');
deTexture2.wrapS = deTexture2.wrapT = RepeatWrapping;

// 有效高亮texture
const validActive2 = new TextureLoader().load('/images/valid-active2.png');
validActive2.wrapS = validActive2.wrapT = RepeatWrapping;

// 有效默认texture
const validDefault2 = new TextureLoader().load('/images/valid-default2.png');
validDefault2.wrapS = validDefault2.wrapT = RepeatWrapping;

// 大圆texture
const circleBigTexture = new TextureLoader().load('/images/circle-big.png');
circleBigTexture.wrapS = circleBigTexture.wrapT = RepeatWrapping;

// 小圆texture
const circleSmallTexture = new TextureLoader().load('/images/circle-small.png');
circleSmallTexture.wrapS = circleSmallTexture.wrapT = RepeatWrapping;

// 下一个点texture
const circleNextTextureL = new TextureLoader().load('/images/circle-next-left.png');
circleNextTextureL.wrapS = circleNextTextureL.wrapT = RepeatWrapping;

// 下一个点texture
const circleNextTextureR = new TextureLoader().load('/images/circle-next-right.png');
circleNextTextureR.wrapS = circleNextTextureR.wrapT = RepeatWrapping;

// 下一个点texture
const circleNextTextureU = new TextureLoader().load('/images/circle-next-up.png');
circleNextTextureU.wrapS = circleNextTextureU.wrapT = RepeatWrapping;

// 下一个点texture
const circleNextTextureD = new TextureLoader().load('/images/circle-next-down.png');
circleNextTextureD.wrapS = circleNextTextureD.wrapT = RepeatWrapping;

const store = useStore();

onMounted(() => {
  // 确保DOM完全渲染后再初始化
  nextTick(() => {
    const pageQuery: any = router.currentRoute.value.query;
    graphId.value = +pageQuery.graphId || 0;
    scene = new Scene();
    let canvas = document.getElementById('canvas-path');

    if (!canvas) {
      console.error('Canvas element not found!');
      return;
    }

    // 修复：使用canvas实际尺寸而不是window尺寸
    const rect = canvas.getBoundingClientRect();
    width = rect.width || 0;
    height = rect.height || 0;

    console.log('Canvas initialization - Width:', width, 'Height:', height);
    console.log('Canvas rect:', rect);

    let k = width / height;
    let s = 10;

    camera = new OrthographicCamera(-s * k, s * k, s, -s, 0.1, 1000);
    camera.position.set(0, 50, 0);
    camera.lookAt(new Vector3(0, 0, 0));
    (window as any).camera = camera;

    renderer = new WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.setClearColor(0xf4f4f4, 0);
    canvas?.appendChild(renderer.domElement);
    renderer.render(scene, camera);

    const geometryx = new PlaneGeometry(200, 200);
    const materialx = new MeshBasicMaterial({ side: 1, transparent: true });
    // const t = new TextureLoader().load('sourceType/ground.png');
    // materialx.map = t;
    const ground = new Mesh(geometryx, materialx);
    ground.position.set(0, -0.2, 0);
    ground.name = 'ground-init';
    ground.lookAt(0, 1, 0);
    // createGroundLine(ground, 100)
    scene.add(ground);

    // 添加控制器
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.mouseButtons = {
      LEFT: MOUSE.ROTATE,
      MIDDLE: MOUSE.DOLLY,
      RIGHT: MOUSE.PAN,
    };
    controls.touches = {
      ONE: TOUCH.ROTATE,
      TWO: TOUCH.DOLLY_PAN,
    };
    // 使用控制器
    controls.enableDamping = false;
    controls.autoRotate = false;
    controls.enableRotate = false;
    (window as any).controls = controls;

    // 环境光
    const ambient = new AmbientLight(0xffffff, 3);
    scene.add(ambient);

    animate();
    function animate() {
      // 使用 requestAnimationFrame 执行动画
      animationFrameId = requestAnimationFrame(animate);

      renderer.render(scene, camera);
    }

    // 双击选中
    // canvas?.addEventListener('mouseup', () => {
    //   props.handleMouseUp && props.handleMouseUp()
    // }, false);

    // 鼠标落下
    canvas?.addEventListener('mousedown', mousedownCanvas);

    // 鼠标抬起
    canvas?.addEventListener('mouseup', mouseupCanvas);

    // 滑动悬浮部分逻辑
    canvas?.addEventListener('mousemove', mousemoveCanvas, false);

    window.addEventListener('resize', resizeCanvas, false);

    controls.addEventListener('change', onChange);

    getData();

    Icondom = document.querySelector('.point-follow') as any;
    hoveStyleDom = document.querySelector('.point-follow2') as any;

    // 生成一条动态线
    dynamicLine = new CLine({
      vertexs: [new Vector3(0, 0, 0), new Vector3(0, 0, 0)],
      color: 0x333333,
      lineWidth: 4,
      transparent: true,
    });
    scene.add(dynamicLine);

    getNjyjGraphById({ graphId: graphId.value }).then((res: any) => {
      if (res.code == 200) {
        graphInfo.value = res.data;
        getSpaceById({ spaceId: res.data.spaceId }).then((res2: any) => {
          currentSpace.value = { ...res2.data };
          getOssAccessPath({ key: res2.data.roomStructurePath }).then((pathData: any) => {
            loadZipFileForJSZip(pathData.data, (glb: any) => {
              scene.add(glb.scene.clone());
              const boxInfo = new Box3().expandByObject(glb.scene);
              (scene.getObjectByName('ground-init') as any).position.y = boxInfo.min.y - 0.1;
              const { id, planeHeight } = graphInfo.value;
              if (!planeHeight) {
                updateNjyjGraph({ planeHeight: boxInfo.min.y, id });
              }
            });
          });
        });
      }
    });
  }); // 关闭nextTick
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas);
  cancelAnimationFrame(animationFrameId);
  // 清理预览节点
  if (previewNode) {
    scene.remove(previewNode);
    previewNode = null;
  }
});

const exitEdit = async () => {
  renderer.render(scene, camera);
  let imgData = renderer.domElement.toDataURL('image/jpeg');
  const formData = base64ToBlob(imgData, 'pic');
  const ossBack = await axios.post(`${baseURL}/navi/uploadGraphPic`, formData, {
    headers,
  });
  if (ossBack.data.code == '200') {
    const { graphName, id } = graphInfo.value;
    updateNjyjGraph({
      graphName,
      graphInfo: graphInfo.value.graphInfo,
      id,
      graphPicKey: ossBack.data.data,
    }).then((res: any) => {
      if (res.code == 200) {
        router.push('path_navigation');
      }
    });
  }
};

const changeNodeType = (val: any) => {
  const navPoints = graphPoints.value.filter((e: any) => e.nodeType == 3);
  if (navPoints.length == 30 && val == 3) {
    store.state.showTips = '最多可以设置30个导航点位';
    return;
  }
  const valid = activeObject.userData.valid;
  activeObject.material.map = valid
    ? val == 1
      ? validActive
      : validActive2
    : val == 1
    ? acTexture
    : acTexture2;
  graphPoints.value[activePointIndex.value].nodeType = val;
  activeObject.userData.data = graphPoints.value[activePointIndex.value];
};

const hidePreview = () => {
  showPreview.value = false;
};

const editGraph = (index: number) => {
  editIndex.value = index;
  if (activePointIndex.value != index) {
    activePointEvent(index);
  }
  setTimeout(() => {
    (document.querySelector('.point-box input') as any).focus();
  });
};

const deletePointSure = (index: number) => {
  store.state.showTips = '确定要删除当前路径点，删除后不可恢复';
  isDeleteTips.value = true;
  if (activePointIndex.value != index) {
    activePointEvent(index);
  }
};

const onChange = () => {
  controlsScale.value = (window as any).controls.object.zoom;

  const sceneChild: any = scene.children.filter((e) => e.userData.nodeId);
  if (sceneChild.length) {
    sceneChild.forEach((e: any) => {
      // 保持节点图标在屏幕上的固定像素大小（66x66像素）
      e.setSize(pointInitSize);

      // 更新小圆点控制器的位置
      if (e.children && e.children[0] && e.children[0].children) {
        const nodePosition = e.getVertexs()[0];
        const camera = (window as any).camera;
        const distance = camera.position.distanceTo(nodePosition);
        const worldOffset = (pointInitSize * 0.3 * distance) / (camera.zoom * 1000);

        const controlGroup = e.children[0];
        const [sm_left, sm_right, sm_up, sm_down] = controlGroup.children;

        // 更新外部小圆点位置
        if (sm_left) {
          sm_left.setVertex(
            new Vector3(nodePosition.x - worldOffset, nodePosition.y, nodePosition.z)
          );
          if (sm_left.children[0]) {
            sm_left.children[0].setVertex(
              new Vector3(nodePosition.x - worldOffset, nodePosition.y + 0.0001, nodePosition.z)
            );
            sm_left.children[0].userData.point = new Vector3(
              nodePosition.x - worldOffset,
              nodePosition.y + 0.0001,
              nodePosition.z
            );
          }
        }
        if (sm_right) {
          sm_right.setVertex(
            new Vector3(nodePosition.x + worldOffset, nodePosition.y, nodePosition.z)
          );
          if (sm_right.children[0]) {
            sm_right.children[0].setVertex(
              new Vector3(nodePosition.x + worldOffset, nodePosition.y + 0.0001, nodePosition.z)
            );
            sm_right.children[0].userData.point = new Vector3(
              nodePosition.x + worldOffset,
              nodePosition.y + 0.0001,
              nodePosition.z
            );
          }
        }
        if (sm_up) {
          sm_up.setVertex(
            new Vector3(nodePosition.x, nodePosition.y, nodePosition.z - worldOffset)
          );
          if (sm_up.children[0]) {
            sm_up.children[0].setVertex(
              new Vector3(nodePosition.x, nodePosition.y + 0.0001, nodePosition.z - worldOffset)
            );
            sm_up.children[0].userData.point = new Vector3(
              nodePosition.x,
              nodePosition.y + 0.0001,
              nodePosition.z - worldOffset
            );
          }
        }
        if (sm_down) {
          sm_down.setVertex(
            new Vector3(nodePosition.x, nodePosition.y, nodePosition.z + worldOffset)
          );
          if (sm_down.children[0]) {
            sm_down.children[0].setVertex(
              new Vector3(nodePosition.x, nodePosition.y + 0.0001, nodePosition.z + worldOffset)
            );
            sm_down.children[0].userData.point = new Vector3(
              nodePosition.x,
              nodePosition.y + 0.0001,
              nodePosition.z + worldOffset
            );
          }
        }
      }
    });
  }
  const angleDot = (window as any).controls.object.position
    .clone()
    .sub(new Vector3(0, 0, 0))
    .normalize()
    .clone()
    .dot(new Vector3(0, 1, 0));
  const groundObj: any = scene.getObjectByName('ground-init');
  if (Math.abs(angleDot) < 0.5) {
    groundObj.children.forEach((childLine: any) => {
      childLine.setOpacity(1 - (0.5 - Math.abs(angleDot)) * 2);
    });
  }
};

const changeData = (even: any) => {
  const { graphName, id } = graphInfo.value;
  updateNjyjGraph({ graphName, graphInfo: graphInfo.value.graphInfo, id });
};

const activePointEvent = (index: number) => {
  if (activePointIndex.value != index) {
    activePointIndex.value = index;
  } else {
    activePointIndex.value = -1;
  }
};

const deletePoint = () => {
  const data = graphPoints.value[activePointIndex.value];
  graphPoints.value = graphPoints.value
    .filter((e: any) => e.nodeId != data.nodeId)
    .map((d: any) => {
      let deleteChildNodeId = [];
      if (d.relatedNodeIdList.includes(data.nodeId)) {
        const parentNodeId = [data.nodeId, d.nodeId];
        d.relatedNodeIdList = d.relatedNodeIdList.filter((s: any) => s != data.nodeId);
        deleteChildNodeId = childNodeArray.value
          .filter(
            (e: any) =>
              e.relatedNodeIdList.includes(parentNodeId[0]) &&
              e.relatedNodeIdList.includes(parentNodeId[1])
          )
          .map((d: any) => d.nodeId);
        const path1 = scene.getObjectByName(data.nodeId + d.nodeId);
        const path2 = scene.getObjectByName(d.nodeId + data.nodeId);
        path1 && scene.remove(path1);
        path2 && scene.remove(path2);
        if (d.relatedNodeIdList.length == 0) {
          const point: any = scene.getObjectByName(d.nodeId);
          point.material.map = d.nodeType == 1 ? deTexture : deTexture2;
        }
        d.flag = 'delete';
        childNodeArray.value = childNodeArray.value.filter(
          (e: any) =>
            !(
              e.relatedNodeIdList.includes(parentNodeId[0]) &&
              e.relatedNodeIdList.includes(parentNodeId[1])
            )
        );
      }
      if (deleteChildNodeId.length) {
        deleteChildNodeId.forEach((e: any) => {
          if (d.neighborsOriginWeightMap[e]) {
            delete d.neighborsOriginWeightMap[e];
          }
        });
      }
      return d;
    });
  const obj: any = scene.getObjectByName(data.nodeId);
  scene.remove(obj);
  activePointIndex.value = -1;
  store.state.showTips = '';
  isDeleteTips.value = false;

  // 显示删除点位完成提示
  ElMessage({
    message: '删除点位完成',
    type: 'success',
  });
};

const changeNodeName = () => {
  graphPoints.value[activePointIndex.value].flag = 'update';
};

const handleSave = () => {
  // 验证路径名称是否为空
  if (!graphInfo.value.graphName || graphInfo.value.graphName.trim() === '') {
    ElMessage({
      message: '路径名称不能为空',
      type: 'warning',
    });
    return;
  }

  const lineMap: any = {};
  childNodeArray.value.forEach((e: any) => {
    if (
      lineMap[e.relatedNodeIdList[0] + e.relatedNodeIdList[1]] &&
      !lineMap[e.relatedNodeIdList[1] + e.relatedNodeIdList[0]]
    ) {
      lineMap[e.relatedNodeIdList[0] + e.relatedNodeIdList[1]].lineNodeIds.push(e.nodeId);
    } else if (
      !lineMap[e.relatedNodeIdList[0] + e.relatedNodeIdList[1]] &&
      !lineMap[e.relatedNodeIdList[1] + e.relatedNodeIdList[0]]
    ) {
      lineMap[e.relatedNodeIdList[0] + e.relatedNodeIdList[1]] = {
        lineNodeIds: [e.nodeId],
      };
    }
    if (lineMap[e.relatedNodeIdList[0] + e.relatedNodeIdList[1]]) {
      lineMap[e.relatedNodeIdList[0] + e.relatedNodeIdList[1]].startNodeId = e.relatedNodeIdList[0];
      lineMap[e.relatedNodeIdList[0] + e.relatedNodeIdList[1]].endNodeId = e.relatedNodeIdList[1];
      lineMap[e.relatedNodeIdList[0] + e.relatedNodeIdList[1]].lineType = 1;
      lineMap[e.relatedNodeIdList[0] + e.relatedNodeIdList[1]].graphId = graphId.value;
    }
  });
  saveNjyjNodes({
    njyjNodeList: [...(graphPoints.value || []), ...(childNodeArray.value || [])],
    graphId: graphId.value,
    updateVersion: new Date().getTime(),
    njyjLineList: Object.values(lineMap),
  }).then((res: any) => {
    if (res.code == 200) {
      ElMessage.success('路径保存成功！');
    }
  });
};

// 拖动素材时候悬浮交互
const handleIconMove = (e: any) => {
  if (addPointState.value) {
    // 获取canvas容器的位置信息
    const canvas = document.getElementById('canvas-path');
    const rect = canvas?.getBoundingClientRect();

    if (rect) {
      // 计算相对于canvas容器的坐标
      const relativeX = e.clientX - rect.left;
      const relativeY = e.clientY - rect.top;

      // 设置透明节点位置，减去节点尺寸的一半以实现居中
      // 使用固定大小，不随缩放变化
      const nodeSize = pointInitSize;
      Icondom.style.left = relativeX - nodeSize / 2 + 12 + 'px';
      Icondom.style.top = relativeY - nodeSize / 2 + 12 + 'px';

      // 调试信息（可选）
      // console.log('Mouse follow - clientX:', e.clientX, 'clientY:', e.clientY);
      // console.log('Canvas rect:', rect);
      // console.log('Relative coords:', relativeX, relativeY);
      // console.log('Node position:', Icondom.style.left, Icondom.style.top);
    }
  }
};

// 清空素材交互
const hideDragImg = () => {
  addPointState.value = false;
  // 隐藏透明节点到屏幕外
  Icondom.style.left = '-9999px';
  Icondom.style.top = '-9999px';
};

// 显示透明预览节点
const showPreviewNode = (position: Vector3) => {
  if (!previewNode) {
    // 创建透明预览节点
    previewNode = new CPoint({
      vertex: position,
      size: pointInitSize, // 使用固定的屏幕像素大小
      depthTest: false,
      transparent: true,
    });
    previewNode.material.map = deTexture; // 使用默认的路径点纹理
    previewNode.material.opacity = 0.6; // 设置透明度
    previewNode.material.transparent = true; // 确保透明度生效
    previewNode.name = 'preview-node';
    scene.add(previewNode);
  } else {
    // 更新现有预览节点的位置
    previewNode.setVertex(position);
    previewNode.visible = true;
    // 确保透明度设置正确
    previewNode.material.opacity = 0.6;
    // 确保大小保持固定的屏幕像素大小
    previewNode.setSize(pointInitSize);
  }
};

// 隐藏透明预览节点
const hidePreviewNode = () => {
  if (previewNode) {
    previewNode.visible = false;
  }
};

// 回到路径设置信息
const backToPathSettings = () => {
  // 取消当前选中的点位，回到路径信息编辑状态
  activePointIndex.value = -1;
  activeObjectName.value = '';
};

// 检查节点数量限制
const checkNodeLimit = (): boolean => {
  const currentCount = graphPoints.value.length;

  if (currentCount >= maxNodes) {
    ElMessageBox.confirm('最多可以设置30个导航点', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    });
    return false; // 超过限制，不创建节点
  }

  return true; // 可以创建节点
};

// 处理小圆点点击创建节点
const handleControlPointClick = () => {
  if (!hoverActivePoint) return;

  // 检查节点数量限制
  const canCreate = checkNodeLimit();
  if (!canCreate) {
    return; // 超过限制，不创建节点
  }

  hoveStyleDom.style.left = '-50%';
  hoveStyleDom.style.top = '-50%';
  // 隐藏透明预览节点
  hidePreviewNode();
  const { x, y, z } = hoverActivePoint.userData.point;
  const pointArray = [x, y, z];
  pointArray[0] += hoverActivePoint.userData.dis.x || 0;
  pointArray[2] += hoverActivePoint.userData.dis.z || 0;
  const hoverNodeId = hoverActivePoint.parent?.parent?.parent?.name;
  createPoint((nodeId: string, nodeName: string) => {
    const worldVector = new Vector3(...pointArray);
    drawPoint({ vertex: worldVector }, nodeId, 1);
    graphPoints.value.push({
      graphId: graphId.value,
      x: worldVector.x,
      z: worldVector.z,
      neighborsOriginWeightMap: {},
      relatedNodeIdList: [],
      nodeId,
      nodeName,
      flag: 'add',
      nodeType: 1,
    });
    drawLine = graphPoints.value
      .filter((e: any) => [hoverNodeId, nodeId].includes(e.nodeId))
      .map((d: any) => ({ ...d, flag: 'update' }));
    if (drawLine.length == 2) {
      createLine();
    }
  });
};

// 处理达到最大节点数时的点击
const handleMaxNodeReached = () => {
  checkNodeLimit();
};

// 鼠标拖动停止事件
const handleIconUp = (e: any) => {
  if (addPointState.value) {
    updateMouseStyle(MouseStyle.default, true);
    hideDragImg();
  }
};

const getData = () => {
  getNjyjNodes({ graphId: graphId.value }).then((res) => {
    graphPoints.value = [...res.data].filter((e: any) => e.nodeType == 1 || e.nodeType == 3);
    childNodeArray.value = [...res.data].filter((e: any) => e.nodeType == 2);
    initPoint();
  });
};

const initPoint = () => {
  const sceneChild = scene.children.filter((e) => e.userData.nodeId);
  sceneChild.forEach((e) => {
    scene.remove(e);
  });
  const pointObj: any = {};
  graphPoints.value.forEach((e: any) => {
    pointObj[e.nodeId] = e;
  });
  graphPoints.value.forEach((e: any) => {
    // 这里判断是有效点位还是无效点位
    if (e.relatedNodeIdList.length) {
      drawPoint({ vertex: new Vector3(e.x, 0, e.z) }, e.nodeId, e.nodeType, true);
      e.relatedNodeIdList.forEach((k: any) => {
        const oldPath = scene.getObjectByName(pointObj[k].nodeId + e.nodeId);
        if (!oldPath) {
          drawDashLine(
            {
              vertexs: [
                [e.x, 0, e.z],
                [pointObj[k].x, 0, pointObj[k].z],
              ],
            },
            [e.nodeId, pointObj[k].nodeId]
          );
        }
      });
    } else {
      drawPoint({ vertex: new Vector3(e.x, 0, e.z) }, e.nodeId, e.nodeType);
    }
  });
};

// 鼠标移动事件
const mousemoveCanvas = (e: any) => {
  // 判断是否是打点状态
  const worldVector = screenToWorldVector(e.clientX, e.clientY);
  if (isDrag.value && activeObjectName.value) {
    // 拖拽时隐藏预览节点
    hidePreviewNode();
    activeObject.children[0].visible = false;
    activeObject.setVertex(worldVector);
    const relatedNodeIdList = activeObject.userData.data.relatedNodeIdList;
    relatedNodeIdList.forEach((e: any) => {
      const line1: any = scene.getObjectByName(activeObjectName.value + e);
      const line2: any = scene.getObjectByName(e + activeObjectName.value);
      let curLine: any = null;
      // 修改端点位置
      if (line1) {
        const v = line1.getVertexs();
        v[0] = worldVector.clone();
        line1.setVertexs(v);
        curLine = line1;
      } else if (line2) {
        const v = line2.getVertexs();
        v[1] = worldVector.clone();
        line2.setVertexs(v);
        curLine = line2;
      }
      // 隐藏线上的点
      if (curLine && curLine.children[0].visible) {
        curLine.children.forEach((e: any) => {
          e.visible = false;
        });
      }
    });
    // 修改数据上端点位置
    graphPoints.value = graphPoints.value.map((item: any) => {
      if (item.nodeId == activeObjectName.value) {
        item.x = worldVector.x;
        item.z = worldVector.z;
        item.flag = 'update';
      }
      return item;
    });
    return;
  }
  if (isDragPath.value && activeObjectName.value) {
    const data = activeObject.userData.data;
    dynamicLine && dynamicLine.setVertexs([new Vector3(data.x, 0, data.z), worldVector]);
    return;
  }
  let ray = new Raycaster();
  ray.set(worldVector, new Vector3(0, 1, 0));
  const r1 = ray.intersectObjects(scene.children, true);
  const activePoint0 = r1.filter((e) => e.object.type == 'Points')[0];
  const activePoint = r1.filter(
    (e) =>
      e.object.type == 'Points' &&
      e.object.parent?.parent?.name == activeObjectName.value &&
      e.object.visible
  )[0];
  const activePoint2 = r1.filter(
    (e) =>
      e.object.type == 'Points' &&
      e.object.parent?.parent?.parent?.name == activeObjectName.value &&
      e.object.visible
  )[0];
  // 右上角的显示
  if (activePoint0) {
    currentId.value = activePoint0.object.userData.curId;
  }

  if (!activePoint && !activePoint2 && hoverActivePoint) {
    hoverActivePoint.visible = false;
    hoverActivePoint = null;
  }

  if (activePoint && !hoverActivePoint && activePoint.object.parent?.type != 'Line2') {
    activePoint.object.children[0].visible = true;
    hoverActivePoint = activePoint.object.children[0];
    const hoverPoint = hoverActivePoint.getVertexs();
    const point2D = worldToScreenVector(hoverPoint[0]);
    hoveStyleDom.style.left = point2D.x + hoverActivePoint.userData.dis.x * 4 + 'px';
    hoveStyleDom.style.top = point2D.y - 33 + 'px';

    // 显示透明预览节点
    if (hoverActivePoint.userData.point && hoverActivePoint.userData.dis) {
      // 使用与真实节点创建相同的位置计算逻辑
      const { x, y, z } = hoverActivePoint.userData.point;
      const previewPosition = new Vector3(
        x + (hoverActivePoint.userData.dis.x || 0),
        y,
        z + (hoverActivePoint.userData.dis.z || 0)
      );
      showPreviewNode(previewPosition);

      // 调试信息（可选）
      // console.log('Preview position:', previewPosition);
      // console.log('Base point:', { x, y, z });
      // console.log('Direction offset:', hoverActivePoint.userData.dis);
    }

    if (hoverActivePoint.userData.dis.x < 0) {
      hoveStyleDom.style.transform = 'rotate(180deg)';
      hoveStyleDom.childNodes[1].style.transform = 'rotate(-180deg)';
    } else {
      hoveStyleDom.style.transform = 'rotate(0deg)';
      hoveStyleDom.childNodes[1].style.transform = 'rotate(0deg)';
    }

    if (hoverActivePoint.userData.dis.z < 0) {
      hoveStyleDom.style.transform = 'rotate(-90deg)';
      hoveStyleDom.childNodes[1].style.transform = 'rotate(90deg)';
      hoveStyleDom.style.top = point2D.y - 39 + 'px';
    } else if (hoverActivePoint.userData.dis.z > 0) {
      hoveStyleDom.style.transform = 'rotate(90deg)';
      hoveStyleDom.childNodes[1].style.transform = 'rotate(-90deg)';
      hoveStyleDom.style.top = point2D.y - 100 + 'px';
    }
  } else if (!hoverActivePoint) {
    hoveStyleDom.style.left = '-50%';
    hoveStyleDom.style.top = '-50%';
    // 隐藏透明预览节点
    hidePreviewNode();
  }
  if (addPointState.value) {
    handleIconMove(e);
  }
};
// 修复的屏幕坐标转世界坐标函数
const screenToWorldVector = (evX: number, evY: number) => {
  // 获取canvas元素的实际尺寸和位置
  const canvas = document.getElementById('canvas-path');
  const rect = canvas?.getBoundingClientRect();

  if (!rect) {
    console.warn('Canvas element not found');
    return new Vector3(0, 0, 0);
  }

  // 计算相对于canvas的坐标
  const x = evX - rect.left;
  const y = evY - rect.top;

  // 使用canvas的实际尺寸进行坐标转换
  const canvasWidth = rect.width;
  const canvasHeight = rect.height;

  // 调试信息
  if (isDrag.value) {
    console.log('Mouse coords:', { evX, evY });
    console.log('Canvas rect:', {
      left: rect.left,
      top: rect.top,
      width: canvasWidth,
      height: canvasHeight,
    });
    console.log('Relative coords:', { x, y });
  }

  const x1 = (x / canvasWidth) * 2 - 1;
  const y1 = -(y / canvasHeight) * 2 + 1;
  const stdVector = new Vector3(x1, y1, 0.5);
  const worldVector = stdVector.unproject((window as any).camera);
  worldVector.x = +worldVector.x.toFixed(4);
  worldVector.y = 0;
  worldVector.z = +worldVector.z.toFixed(4);

  if (isDrag.value) {
    console.log('World vector:', worldVector);
  }

  return worldVector;
};

let pathPlanning: any = [];
// 鼠标点下事件
const mousedownCanvas = (e: any) => {
  const worldVector = screenToWorldVector(e.clientX, e.clientY);
  let ray = new Raycaster();
  ray.set(worldVector, new Vector3(0, 1, 0));
  const r1 = ray.intersectObjects(scene.children, true);
  const activePoint: any = r1.filter(
    (e) => e.object.type == 'Points' && e.object.userData.nodeId
  )[0];
  const activePoint2 = r1.filter(
    (e) =>
      e.object.type == 'Points' &&
      e.object.parent?.parent?.parent?.name == activeObjectName.value &&
      e.object.visible
  )[0];
  if (isActiveRouter.value) {
    if (pathPlanning.length == 0) {
      pathPlanning.push(worldVector);
    } else if (activePoint) {
      getNaviRouteByLocalize({
        graphId: graphId.value,
        localizeX: pathPlanning[0].x,
        localizeZ: pathPlanning[0].z,
        endNodeId: activePoint.object.userData.nodeId,
      }).then((res) => {
        const pointRs = res.data.map((e: any) => new Vector3(e.x, 0, e.z));
        pointRs.unshift(pathPlanning[0]);
        if (pointRs.length > 1) {
          const line = new CLine({
            vertexs: pointRs,
            color: 0xff0000,
            lineWidth: 4,
            dashed: true,
            dashSize: 0.5,
            gapSize: 0.5,
            transparent: true,
          });
          scene.add(line);
          drawLine = [];
        }
        pathPlanning = [];
      });
    }
  }
  if (activePoint) {
    isDrag.value = true;
    // 选中并高亮点位
    const oldActiveName = activeObjectName.value;
    activeObjectName.value = activePoint.object.name;
    activeObject = activePoint.object;
    const d1 = graphPoints.value.filter((e: any) => e.nodeId == activePoint.object.name)[0];
    activePoint.object.material.map = activePoint.object.userData.valid
      ? d1.nodeType == 1
        ? validActive
        : validActive2
      : d1.nodeType == 1
      ? acTexture
      : acTexture2;
    activeObject.userData.data = d1;
    activePoint.object.children[0].visible = true;
    activePointIndex.value = graphPoints.value.findIndex(
      (e: any) => e.nodeId == activeObjectName.value
    );
    if (oldActiveName != activeObjectName.value && oldActiveName) {
      const oldObject: any = scene.getObjectByName(oldActiveName);
      const oldNodeType = oldObject.userData.data.nodeType;
      oldObject.material.map = oldObject.userData.valid
        ? oldNodeType == 1
          ? validDefault
          : validDefault2
        : oldNodeType == 1
        ? deTexture
        : deTexture2;
      oldObject.children[0].visible = false;
    }
    // 点击已经选中的点，不取消选中
    // if (oldActiveName == activeObjectName.value) {
    //   activeObjectName.value = '';
    //   activeObject = null;
    //   activePointIndex.value = -1;
    // }
    dragStart = worldVector;

    if (drawLine.length < 2) {
      // 过滤出选中的点
      const f1 = graphPoints.value.filter((e: any) => e.nodeId == activePoint.object.name)[0];
      drawLine.push({ ...f1, point: [f1.x, 0, f1.z], flag: 'update' });
    }
    if (drawLine.length == 2) {
      if (isActiveRouter.value) {
        getNaviRoute({
          graphId: graphId.value,
          startNodeId: drawLine[1].nodeId,
          endNodeId: drawLine[0].nodeId,
        }).then((res: any) => {
          const pointRs = res.data.map((e: any) => new Vector3(e.x, 0, e.z));
          if (pointRs.length > 1) {
            const line = new CLine({
              vertexs: pointRs,
              color: 0xff0000,
              lineWidth: 4,
              dashed: true,
              dashSize: 0.5,
              gapSize: 0.5,
              transparent: true,
            });
            scene.add(line);
            drawLine = [];
          }
        });
        return;
      }
    }
  } else if (activePoint2) {
    isDragPath.value = true;
    dragStart = worldVector;
  } else if (addPointState.value) {
    // 检查节点数量限制
    const canCreate = checkNodeLimit();
    if (!canCreate) {
      return; // 超过限制，不创建节点
    }

    const oldActiveName = activeObjectName.value;
    const pointVector = worldVector;
    pointVector.x += 0.55;
    pointVector.z += 0.55;
    createPoint((nodeId: string, nodeName: string) => {
      const d1 = {
        graphId: graphId.value,
        x: pointVector.x,
        z: pointVector.z,
        neighborsOriginWeightMap: {},
        relatedNodeIdList: [],
        nodeId,
        nodeName,
        flag: 'add',
        nodeType: 1,
      };
      activeObject = drawPoint({ vertex: pointVector }, nodeId, 1);
      activeObjectName.value = activeObject.name;
      activeObject.material.map = acTexture;
      activeObject.userData.data = d1;
      activeObject.children[0].visible = true;
      graphPoints.value.push(d1);
      activePointIndex.value = graphPoints.value.length - 1;

      if (oldActiveName) {
        const oldObject: any = scene.getObjectByName(oldActiveName);
        const oldNodeType = oldObject.userData.data.nodeType;
        oldObject.material.map = oldObject.userData.valid
          ? oldNodeType == 1
            ? validDefault
            : validDefault2
          : oldNodeType == 1
          ? deTexture
          : deTexture2;
        oldObject.children[0].visible = false;
      }
    });
  } else {
    activePointEvent(activePointIndex.value);
  }
};

// 鼠标抬起事件
const mouseupCanvas = (e: any) => {
  const currentPoint = screenToWorldVector(e.clientX, e.clientY);
  const dragDis = dragStart
    ? Math.abs(dragStart.x - currentPoint.x) > 0.001 ||
      Math.abs(dragStart.z - currentPoint.z) > 0.01
    : 0;
  if (isDragPath.value) {
    dynamicLine.setVertexs([new Vector3(0, 0, 0), new Vector3(0, 0, 0)]);
  }
  // 拖拽连接两点
  if (dragDis && activeObject && isDragPath.value) {
    let ray = new Raycaster();
    ray.set(currentPoint, new Vector3(0, 1, 0));
    const r1 = ray.intersectObjects(scene.children, true);
    const activePoint: any = r1.filter(
      (e) => e.object.type == 'Points' && e.object.userData.nodeId
    )[0];
    if (activePoint) {
      const data = activeObject.userData.data;
      drawLine = graphPoints.value
        .filter((e: any) => [data.nodeId, activePoint.object.name].includes(e.nodeId))
        .map((d: any) => ({ ...d, flag: 'update' }));
      createLine();
    }
    isDragPath.value = false;
    return;
  }
  // 拖拽移动位置
  if (dragDis && activeObject && isDrag.value) {
    // 区分是点击还是拖拽
    const data = activeObject.userData.data;
    const updateLine: any = [];
    // 删除线上的点
    graphPoints.value = graphPoints.value.map((d: any) => {
      if (d.nodeId != data.nodeId) {
        let deleteChildNodeId = [];
        if (d.relatedNodeIdList.includes(data.nodeId)) {
          const parentNodeId = [data.nodeId, d.nodeId];
          d.relatedNodeIdList = d.relatedNodeIdList.filter((s: any) => s != data.nodeId);
          deleteChildNodeId = childNodeArray.value
            .filter(
              (e: any) =>
                e.relatedNodeIdList.includes(parentNodeId[0]) &&
                e.relatedNodeIdList.includes(parentNodeId[1])
            )
            .map((d: any) => d.nodeId);
          const path1 = scene.getObjectByName(data.nodeId + d.nodeId);
          const path2 = scene.getObjectByName(d.nodeId + data.nodeId);
          path1 && scene.remove(path1);
          path2 && scene.remove(path2);
          if (d.relatedNodeIdList.length == 0) {
            const point: any = scene.getObjectByName(d.nodeId);
            point.material.map = d.nodeType == 1 ? deTexture : deTexture2;
          }
          d.flag = 'delete';
          childNodeArray.value = childNodeArray.value.filter(
            (e: any) =>
              !(
                e.relatedNodeIdList.includes(parentNodeId[0]) &&
                e.relatedNodeIdList.includes(parentNodeId[1])
              )
          );
          updateLine.push(parentNodeId);
        }
        if (deleteChildNodeId.length) {
          deleteChildNodeId.forEach((e: any) => {
            if (d.neighborsOriginWeightMap[e]) {
              delete d.neighborsOriginWeightMap[e];
            }
          });
        }
      } else {
        d.neighborsOriginWeightMap = {};
        d.relatedNodeIdList = [];
      }
      return d;
    });
    // 生成新的线和点
    updateLine.forEach((nodeIds: any) => {
      drawLine = graphPoints.value
        .filter((e: any) => nodeIds.includes(e.nodeId))
        .map((d: any) => ({ ...d, flag: 'update' }));
      if (drawLine.length == 2) {
        createLine(true);
      }
    });

    // 删除旧的点生成新的点
    scene.remove(activeObject);
    activeObject = drawPoint(
      { vertex: new Vector3(data.x, 0, data.z) },
      data.nodeId,
      data.nodeType,
      !!data.relatedNodeIdList.length
    );
    activeObject.userData.data = data;
    activeObjectName.value = activeObject.name;
    activeObject.children[0].visible = true;
  }

  isDrag.value = false;
  isDragPath.value = false;

  // 点击生成下一个点位
  if (hoverActivePoint) {
    handleControlPointClick();
    return;
  }
  handleIconUp(e);
};

const createLine = (isDefault?: boolean) => {
  drawDashLine(
    {
      vertexs: [
        [drawLine[0].x, 0, drawLine[0].z],
        [drawLine[1].x, 0, drawLine[1].z],
      ],
    },
    [drawLine[0].nodeId, drawLine[1].nodeId],
    true
  );
  const p1: any = scene.getObjectByName(drawLine[0].nodeId);
  const p2: any = scene.getObjectByName(drawLine[1].nodeId);
  p1.material.map = isDefault
    ? drawLine[0].nodeType == 1
      ? validDefault
      : validDefault2
    : drawLine[0].nodeType == 1
    ? validActive
    : validActive2;
  p2.material.map = drawLine[1].nodeType == 1 ? validDefault : validDefault2;
  p1.userData.valid = true;
  p2.userData.valid = true;
  graphPoints.value = graphPoints.value.map((e: any) => {
    if (e.nodeId == drawLine[0].nodeId) {
      e.relatedNodeIdList.push(drawLine[1].nodeId);
    }
    if (e.nodeId == drawLine[1].nodeId) {
      e.relatedNodeIdList.push(drawLine[0].nodeId);
    }
    return e;
  });
  drawLine = [];
};

// 生成点位
const createPoint = (callback: any) => {
  // 获取所有现有的有效路径点（排除已删除的）
  const existingPoints = graphPoints.value.filter(
    (e: any) => e && e.nodeName && e.flag !== 'delete'
  );

  console.log(
    '🔍 当前所有路径点:',
    existingPoints.map((p) => ({ name: p.nodeName, flag: p.flag }))
  );

  // 基于现有路径点的数量来生成新的路径点名称
  const currentCount = existingPoints.length;
  const nodeName = '路径点' + (currentCount + 1);

  console.log('🔍 生成新路径点:', {
    existingPointNames: existingPoints.map((p) => p.nodeName),
    currentCount,
    newNodeName: nodeName,
  });

  const nodeId = graphId.value + '_' + createUuid();
  callback(nodeId, nodeName);
};

const resizeCanvas = () => {
  if (renderer) {
    let canvas = document.getElementById('canvas-path');
    width = canvas?.getBoundingClientRect().width || 0;
    height = canvas?.getBoundingClientRect().height || 0;

    console.log('Canvas resize - Width:', width, 'Height:', height);

    renderer.setSize(width, height);
    // 重置相机投影的相关参数
    const k = width / height; //窗口宽高比
    const s = 10;
    camera.left = -s * k;
    camera.right = s * k;
    camera.top = s;
    camera.bottom = -s;
    camera.updateProjectionMatrix();
  }
};
// 画线
const drawDashLine = (data: any, nodeIds: any, isCreate?: boolean) => {
  const vertexs = data.vertexs;
  const reNodeIds = [...nodeIds].reverse();
  let points = [];
  let lineNodes: any = childNodeArray.value.filter(
    (e: any) =>
      nodeIds.join('') == e.relatedNodeIdList.join('') ||
      reNodeIds.join('') == e.relatedNodeIdList.join('')
  );

  const dis = new Vector3(...vertexs[0]).distanceTo(new Vector3(...vertexs[1]));
  const pointNum = dis / unitDis;
  if (pointNum > 1) {
    const unitX = (vertexs[1][0] - vertexs[0][0]) / pointNum; // 单位x
    const unitZ = (vertexs[1][2] - vertexs[0][2]) / pointNum; // 单位z
    points = [];
    for (let i = 0; i <= Math.floor(pointNum); i++) {
      points.push([vertexs[0][0] + i * unitX, 0, vertexs[0][2] + i * unitZ]);
    }

    if (Math.floor(pointNum) != pointNum) {
      points.push(vertexs[1]);
    }
  } else {
    points = [...data.vertexs];
  }

  // 生成直线时候
  if (isCreate) {
    childNodeArray.value = childNodeArray.value.filter(
      (e: any) => nodeIds.join('') != e.relatedNodeIdList.join('')
    );
    lineNodes = points.slice(1, -1).map((e) => ({
      x: e[0],
      z: e[2],
      nodeType: 2,
      nodeId: graphId.value + '_' + createUuid(),
      relatedNodeIdList: [...nodeIds],
      neighborsOriginWeightMap: {},
      flag: 'add',
      graphId: graphId.value,
    }));
    let startnode: any = null;
    let endnode: any = null;
    graphPoints.value = graphPoints.value.map((e: any) => {
      if (e.nodeId == nodeIds[0]) {
        const dis = new Vector3(e.x, 0, e.z).distanceTo(
          new Vector3(lineNodes[0].x, 0, lineNodes[0].z)
        );
        e.neighborsOriginWeightMap[lineNodes[0].nodeId] = dis;
        startnode = { ...e };
      }
      if (e.nodeId == nodeIds[1]) {
        const dis = new Vector3(e.x, 0, e.z).distanceTo(
          new Vector3(lineNodes[1].x, 0, lineNodes[1].z)
        );
        e.neighborsOriginWeightMap[lineNodes.slice(-1)[0].nodeId] = dis;
        endnode = { ...e };
      }
      return e;
    });
    lineNodes = lineNodes.map((e: any, i: number) => {
      if (i == 0) {
        const dis1 = new Vector3(e.x, 0, e.z).distanceTo(new Vector3(startnode.x, 0, startnode.z));
        const dis2 = new Vector3(e.x, 0, e.z).distanceTo(
          new Vector3(lineNodes[1].x, 0, lineNodes[1].z)
        );
        e.neighborsOriginWeightMap[startnode.nodeId] = dis1;
        e.neighborsOriginWeightMap[lineNodes[1].nodeId] = dis2;
      } else if (i == lineNodes.length - 1) {
        const dis1 = new Vector3(e.x, 0, e.z).distanceTo(new Vector3(endnode.x, 0, endnode.z));
        const dis2 = new Vector3(e.x, 0, e.z).distanceTo(
          new Vector3(lineNodes[i - 1].x, 0, lineNodes[i - 1].z)
        );
        e.neighborsOriginWeightMap[endnode.nodeId] = dis1;
        e.neighborsOriginWeightMap[lineNodes[i - 1].nodeId] = dis2;
      } else {
        const dis1 = new Vector3(e.x, 0, e.z).distanceTo(
          new Vector3(lineNodes[i - 1].x, 0, lineNodes[i - 1].z)
        );
        const dis2 = new Vector3(e.x, 0, e.z).distanceTo(
          new Vector3(lineNodes[i + 1].x, 0, lineNodes[i + 1].z)
        );
        e.neighborsOriginWeightMap[lineNodes[i - 1].nodeId] = dis1;
        e.neighborsOriginWeightMap[lineNodes[i + 1].nodeId] = dis2;
      }
      return e;
    });
    childNodeArray.value.push(...lineNodes);
  }

  const currentLine = scene.getObjectByName(nodeIds[1] + nodeIds[0]);
  if (currentLine) return;
  const cline = new CLine({
    ...data,
    color: 0x333333,
    lineWidth: 4,
    depthTest: false,
    transparent: true,
  });
  cline.userData.points = [new Vector3(...data.vertexs[0]), new Vector3(...data.vertexs[1])];
  cline.name = nodeIds.join('');
  for (let i = 1; i <= lineNodes.length - 1; i++) {
    const pt = new CPoint({
      vertex: new Vector3(lineNodes[i].x, 0, lineNodes[i].z),
      size: 6,
      depthTest: false,
      transparent: true,
    });
    pt.material.map = circleSmallTexture;
    pt.userData.curId = lineNodes[i].nodeId;
    pt.visible = false;
    cline.add(pt);
  }
  scene.add(cline);
  return cline;
};

// 画点
const drawPoint = (data: any, nodeId: string, nodeType: number, isValid?: boolean) => {
  const cpoint = new CPoint({
    ...data,
    size: pointInitSize, // 使用固定的屏幕像素大小
    depthTest: false,
    transparent: true,
  });
  cpoint.material.map = isValid
    ? nodeType == 1
      ? validDefault
      : validDefault2
    : nodeType == 1
    ? deTexture
    : deTexture2;
  cpoint.userData.valid = !!isValid;
  cpoint.name = nodeId;
  cpoint.userData.nodeId = nodeId;
  cpoint.userData.curId = nodeId;
  const group1 = new Group();
  // 计算小圆点相对于节点的偏移距离
  // 基于节点的固定屏幕大小计算世界坐标偏移
  const nodeScreenSize = pointInitSize; // 节点的屏幕像素大小
  const camera = (window as any).camera;
  const distance = camera.position.distanceTo(data.vertex);
  const worldOffset = (nodeScreenSize * 0.3 * distance) / (camera.zoom * 1000); // 调整系数，减小距离

  const sm_left = new CPoint({
    vertex: new Vector3(data.vertex.x - worldOffset, data.vertex.y, data.vertex.z),
    size: 15,
    depthTest: false,
    transparent: true,
  });
  sm_left.name = nodeId + '#sm_left';
  const sm_right = new CPoint({
    vertex: new Vector3(data.vertex.x + worldOffset, data.vertex.y, data.vertex.z),
    size: 15,
    depthTest: false,
    transparent: true,
  });
  sm_right.name = nodeId + '#sm_right';
  const sm_up = new CPoint({
    vertex: new Vector3(data.vertex.x, data.vertex.y, data.vertex.z - worldOffset),
    size: 15,
    depthTest: false,
    transparent: true,
  });
  sm_up.name = nodeId + '#sm_up';
  const sm_down = new CPoint({
    vertex: new Vector3(data.vertex.x, data.vertex.y, data.vertex.z + worldOffset),
    size: 15,
    depthTest: false,
    transparent: true,
  });
  sm_down.name = nodeId + '#sm_down';
  sm_left.material.map = circleSmallTexture;
  sm_right.material.map = circleSmallTexture;
  sm_up.material.map = circleSmallTexture;
  sm_down.material.map = circleSmallTexture;
  const cn_left = new CPoint({
    vertex: new Vector3(data.vertex.x - worldOffset, data.vertex.y + 0.0001, data.vertex.z),
    size: 20,
    depthTest: false,
    transparent: true,
  });
  const cn_right = new CPoint({
    vertex: new Vector3(data.vertex.x + worldOffset, data.vertex.y + 0.0001, data.vertex.z),
    size: 20,
    depthTest: false,
    transparent: true,
  });
  const cn_up = new CPoint({
    vertex: new Vector3(data.vertex.x, data.vertex.y + 0.0001, data.vertex.z - worldOffset),
    size: 20,
    depthTest: false,
    transparent: true,
  });
  const cn_down = new CPoint({
    vertex: new Vector3(data.vertex.x, data.vertex.y + 0.0001, data.vertex.z + worldOffset),
    size: 20,
    depthTest: false,
    transparent: true,
  });
  cn_left.visible = false;
  cn_right.visible = false;
  cn_up.visible = false;
  cn_down.visible = false;
  cn_left.material.map = circleNextTextureL;
  cn_right.material.map = circleNextTextureR;
  cn_up.material.map = circleNextTextureU;
  cn_down.material.map = circleNextTextureD;
  cn_left.userData.dis = { x: -unitDistance, y: 0, z: 0 };
  cn_right.userData.dis = { x: unitDistance, y: 0, z: 0 };
  cn_up.userData.dis = { x: 0, y: 0, z: -unitDistance };
  cn_down.userData.dis = { x: 0, y: 0, z: unitDistance };
  cn_left.userData.point = new Vector3(
    data.vertex.x - worldOffset,
    data.vertex.y + 0.0001,
    data.vertex.z
  );
  cn_right.userData.point = new Vector3(
    data.vertex.x + worldOffset,
    data.vertex.y + 0.0001,
    data.vertex.z
  );
  cn_up.userData.point = new Vector3(
    data.vertex.x,
    data.vertex.y + 0.0001,
    data.vertex.z - worldOffset
  );
  cn_down.userData.point = new Vector3(
    data.vertex.x,
    data.vertex.y + 0.0001,
    data.vertex.z + worldOffset
  );
  sm_left.add(cn_left);
  sm_right.add(cn_right);
  sm_down.add(cn_down);
  sm_up.add(cn_up);
  group1.add(sm_left, sm_right, sm_up, sm_down);
  group1.visible = false;
  cpoint.add(group1);
  scene.add(cpoint);
  return cpoint;
};

watch(
  () => addPointState.value,
  (newState) => {
    if (newState) {
      updateMouseStyle(MouseStyle.mouseAdd, true);
    } else {
      updateMouseStyle(MouseStyle.default, true);
    }
  }
);

watch(
  () => activePointIndex.value,
  (newState) => {
    const oldData = activeObject?.userData.data;
    if (oldData) {
      activeObject.material.map = !!oldData.relatedNodeIdList.length
        ? oldData.nodeType == 1
          ? validDefault
          : validDefault2
        : oldData.nodeType == 1
        ? deTexture
        : deTexture2;
      activeObject.children[0].visible = false;
    }
    if (newState != -1) {
      const data = graphPoints.value[newState];
      activeObjectName.value = data.nodeId;
      activeObject = scene.getObjectByName(data.nodeId);
      activeObject.userData.data = data;
      activeObject.material.map = !!data.relatedNodeIdList.length
        ? data.nodeType == 1
          ? validActive
          : validActive2
        : data.nodeType == 1
        ? acTexture
        : acTexture2;
      activeObject.children[0].visible = true;
    }
  }
);

// 监听添加点位状态变化
watch(
  () => addPointState.value,
  (newVal) => {
    if (newVal) {
      // 激活添加状态时，初始化透明节点位置到屏幕外
      if (Icondom) {
        Icondom.style.left = '-9999px';
        Icondom.style.top = '-9999px';
      }
      updateMouseStyle(MouseStyle.crosshair, true);
    } else {
      // 取消添加状态时，隐藏透明节点
      hideDragImg();
    }
  }
);
</script>
<style scoped lang="less">
.id-box {
  width: 500px;
  position: fixed;
  right: 10px;
  top: 80px;
  color: rgb(0, 4, 255);
  padding: 10px 20px 20px;
  background-color: #fff;

  & > div {
    text-align: left;
    margin-top: 10px;
  }

  .active-option {
    color: #000;
    cursor: pointer;
    background-color: #ccc;
    display: inline-block;
    padding: 10px;
    border-radius: 10px;

    &.active {
      color: rgb(0, 4, 255);
    }
  }
}

.preview-box {
  width: 224px;
  height: 100%;
  background: #ffffff;
  border: 1px solid #dadada;
  border-radius: 0px 0px 0px 0px;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  padding: 12px;
  box-sizing: border-box;
  overflow-y: auto;

  .save-btn:hover {
    background-color: #1251c8 !important;
  }

  .is-operate-change {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  & > div:first-child {
    display: flex;
    justify-content: center;
    align-items: center;

    .btn {
      cursor: pointer;
      width: 96px;
      background: #f3f3f3;
      border-radius: 4px 4px 4px 4px;
      line-height: 32px;
      text-align: center;
      margin: 0 4px;
    }

    & > .btn:last-child {
      background-color: rgba(46, 118, 255, 1);
      color: rgba(255, 255, 255, 1);
    }
  }

  .menu-title {
    font-size: 14px;
    color: #1e1e1e;
    height: 32px;
    line-height: 32px;
    margin: 12px 0 0;
    text-align: left;
    font-weight: 600;
  }

  & > div:last-child {
    padding: 0 12px;
    border-top: 1px solid #e8e8e8;
    margin-top: 12px;
  }

  .remark {
    height: 132px;
    background-color: #f3f3f3;
    overflow: hidden;
    border-radius: 4px;
    margin-top: 11px;

    ::v-deep(.el-textarea__inner) {
      background-color: transparent;
      box-shadow: none;
    }
  }

  .input-graphName {
    width: 100%;
    height: 32px;
    box-sizing: border-box;
    background-color: #f3f3f3;
    border: none;
    outline: none;
    border-radius: 4px;
    padding-left: 4px;
    line-height: 30px;
    border: 1px solid #f3f3f3;

    &:focus {
      border: 1px solid #3c96ff;
    }
  }
}

.canvas-view {
  width: 100%;
  height: 100%;
  background-color: #f8f8f8;
  background-image: linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.point-follow {
  position: absolute;
  left: -9999px; /* 初始隐藏在屏幕外 */
  top: -9999px;
  background-size: 100% 100%;
  z-index: 11;
  background-image: url(~@/assets/images/icon/invalid-default.png);
  background-size: 100% 100%;
  opacity: 0.5;
  pointer-events: none; /* 防止干扰鼠标事件 */
}

.point-follow2 {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  display: none;
  transform-origin: 0 0;

  img {
    position: absolute;
    left: 0;
    top: 0;
  }
}

/* 新增顶部Header样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
  background: #ffffff;
  border-radius: 0px 0px 0px 0px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 0 16px;
  box-sizing: border-box;
}

.back-btn {
  height: 32px;
  margin-right: 18px;
  padding: 0 8px;
  font-size: 14px;
  color: #797979;
  &:hover {
    color: #409eff;
  }

  .el-icon {
    font-size: 16px;
  }
}

.lineBlack {
  width: 1px;
  height: 36px;
  background: #dadada;
  margin-right: 18px;
}

.linkName {
  font-size: 16px;
  color: #333333;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-left: auto;

  .btn {
    cursor: pointer;
    width: 96px;
    background: #f3f3f3;
    border-radius: 4px;
    line-height: 32px;
    text-align: center;
    margin: 0 4px;
  }
  .preview-btn {
    width: 68px;
    height: 34px;
    background: #d5e4ff;
    border-radius: 4px 4px 4px 4px;
    font-weight: 500;
    font-size: 14px;
    color: #2e76ff;
    &:hover {
      background: rgba(0, 96, 255, 0.06) !important;
      border-color: rgba(218, 218, 218, 1) !important;
    }
  }
  .save-btn {
    width: 68px;
    height: 34px;
    background-color: rgba(46, 118, 255, 1);
    color: #fff;
    font-weight: 500;
    font-size: 14px;
    color: #ffffff;
  }
  .save-btn:hover {
    background-color: #1251c8 !important;
  }
}

/* 主要内容区域 */
.main-content {
  display: flex;
  height: calc(100vh - 72px);
  margin-top: 72px;
}

/* 左侧栏样式 */
.left-sidebar {
  width: 224px;
  min-width: 224px; /* 确保最小宽度，防止被挤压 */
  max-width: 224px; /* 确保最大宽度，防止过宽 */
  background: #ffffff;
  border-radius: 0px 0px 0px 0px;
  border-top: 1px solid #dadada;
  padding: 12px;
  box-sizing: border-box;
  text-align: left;
  font-weight: bold;
  font-size: 14px;
  color: #1e1e1e;
  z-index: 99;
  flex-shrink: 0; /* 防止flex收缩 */
}

/* 中间编辑器区域 */
.center-editor {
  flex: 1;
  position: relative;
  min-width: 0; /* 确保flex项目可以收缩 */
}

/* 右侧栏样式 */
.right-sidebar {
  width: 224px;
  min-width: 224px; /* 确保最小宽度，防止被挤压 */
  max-width: 224px; /* 确保最大宽度，防止过宽 */
  background: #ffffff;
  border-top: 1px solid #dadada;
  border-radius: 0px 0px 0px 0px;
  box-sizing: border-box;
  text-align: left;
  font-weight: bold;
  font-size: 14px;
  color: #1e1e1e;
  overflow-y: auto;
  flex-shrink: 0; /* 防止flex收缩 */

  .sidebar-title {
    padding: 12px 0 0px 12px;

    height: 32px;
    font-weight: bold;
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 12px;
  }

  .sidebar-content {
    padding: 0 0 0 12px;
    .info-item {
      margin-bottom: 16px;
      display: flex;
      label {
        display: block;
        margin-bottom: 6px;
        font-weight: 400;
        font-size: 12px;
        color: #797979;

        .required {
          color: #ff4d4f;
          margin-right: 4px;
        }
      }
      // Element Plus 组件样式覆盖
      .custom-input {
        width: 125px;

        :deep(.el-input__wrapper) {
          border: none !important;
          box-shadow: none !important;
          background-color: rgb(244, 244, 245) !important;
          border-radius: 4px;
          padding: 8px;
        }

        :deep(.el-input__inner) {
          border: none !important;
          background-color: transparent !important;
          font-size: 14px;
          color: #333;
          box-shadow: none !important;
        }

        // 确保所有状态下都无边框
        :deep(.el-input__wrapper:hover),
        :deep(.el-input__wrapper.is-focus) {
          border: none !important;
          box-shadow: none !important;
        }
      }

      .textarea-wrapper {
        position: relative;
        width: 125px;
      }

      .custom-textarea {
        width: 100%;
        :deep(.el-textarea__inner) {
          border: none;
          box-shadow: none;
          background-color: rgb(244, 244, 245) !important;
          border-radius: 4px;
          padding: 8px;
          padding-bottom: 25px; // 为字数统计留出空间
          font-size: 14px;
          color: #333;
          resize: none;
          height: auto; // 改为auto，让高度自适应内容
          min-height: 60px; // 设置最小高度
        }
      }

      .word-count {
        position: absolute;
        bottom: 26px; // 调整位置到文本框内部右下角
        right: 8px;
        font-size: 12px;
        font-weight: 400;
        color: #909399;
        line-height: 1;
        background: rgba(244, 244, 245, 0.9); // 增加背景色透明度
        padding: 2px 6px;
        border-radius: 3px;
        z-index: 1; // 确保显示在文本框内容之上
      }
    }
  }
}

.point-box {
  font-weight: 400;
  font-size: 14px;
  color: #1e1e1e;
  height: calc(100vh - 200px);
  overflow: hidden;
  overflow-y: auto;

  .point-child-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url(@/assets/images/icon/jiedian.png);
    background-size: 100% 100%;
  }

  .point-child-icon2 {
    background-image: url(@/assets/images/icon/danhang.png);
    background-size: 100% 100%;
  }

  & > div {
    position: relative;
    width: 100%;
    height: 29px;
    background: #fff;
    border-radius: 4px 4px 4px 4px;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    padding-right: 4px;
    margin-bottom: 6px;

    span {
      vertical-align: middle;
      margin-right: 8px;
    }

    & > div:first-child {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    & > div:last-child {
      position: absolute;
      right: 0;
      top: 4px;
    }

    .node-name {
      display: inline-block;
      // width: calc(100% - 32px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 70%;
    }

    input {
      height: 100%;
      width: 100%;
      border: none !important;
      background-color: transparent;
      box-shadow: none !important;
      outline: none;
      box-sizing: border-box;
      border-radius: 4px 4px 4px 4px;
      padding-left: 4px;

      &:focus {
        border: 1px solid #3c96ff !important;
      }
    }

    &:hover {
      background-color: #f3f3f3;

      .delete-icon {
        display: inline-block;
        background-color: transparent;
      }
    }

    & > div {
      height: 20px;
      line-height: 20px;
    }

    &.active {
      background-color: rgba(46, 118, 255, 0.13);

      &:hover {
        .delete-icon {
          display: inline-block;
          background-color: transparent;
        }
      }
    }

    .delete-icon {
      width: 20px;
      height: 20px;
      display: inline-block;
      margin-left: 6px;
      margin-right: 4px;
      cursor: pointer;
      border-radius: 4px;
      display: none;
      background-image: url(@/assets/images/icon/path-delete.png);
      background-size: 100% 100%;

      &:hover {
        background-image: url(@/assets/images/icon/path-deleteA.png);
        background-size: 100% 100%;
      }
    }
  }
}

.menu-back {
  font-size: 16px;
  color: #333333;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .back-icon {
    width: 24px;
    height: 24px;
    background-image: url(@/assets/images/icon/path-back.png);
    background-size: 100% 100%;
    margin-right: 8px;

    &:hover {
      background-image: url(@/assets/images/icon/path-backA.png);
      background-size: 100% 100%;
    }
  }
}

.position-point {
  cursor: pointer;
  height: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  width: 100%;

  span:first-child {
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
      color: #3c96ff;
    }
  }

  .add-point {
    background-image: url(@/assets/images/icon/point-iconA.png);
    background-size: 100% 100%;
  }
}

.point-count-display {
  height: 26px;
  background: #f9f9f9;
  border-radius: 4px 4px 4px 4px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  margin-bottom: 8px;
}

.point-num {
  font-weight: 400;
  font-size: 14px;
  color: #797979;
  margin-left: 12px;
}

.point-icon {
  width: 24px;
  height: 24px;
  background-image: url(@/assets/images/icon/point-icon.png);
  background-size: 100% 100%;
  cursor: pointer;

  &:hover:not(.disabled) {
    background-image: url(@/assets/images/icon/point-iconA.png);
    background-size: 100% 100%;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background-image: url(@/assets/images/icon/point-icon.png);
    }
  }
}

::v-deep(.el-textarea .el-input__count) {
  display: none; /* 隐藏默认的字数显示，使用自定义的 */
}

::v-deep(.el-radio__input) {
  position: relative;
  top: 2px;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.point-type-btn {
  width: 125px;
  height: 26px;
  background: #f5f5f5;
  border-radius: 4px;
  margin-right: 12px;
  text-align: center;
  line-height: 26px;
  font-size: 14px;
  color: #333333;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-bottom: 8px;

  &.active {
    width: 125px;
    height: 26px;
    background: #eaf1ff;
    border-radius: 4px;
    color: #333333;
  }
}
</style>
