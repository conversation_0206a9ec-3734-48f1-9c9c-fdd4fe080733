// 设备状态映射
export const DeviceStatus = {
  0: '待确认',
};

// 用户类型映射
export const UserType = {
  0: '管理员',
};

// 客户/组织类型映射
export const orgnizationType: any = {
  1: '文娱',
  2: '工业',
  3: '教育',
  4: '其他',
};

// 场景状态映射
export const sceneStatus = {
  1: '开启',
  2: '关闭',
};

// 素材类型配置 - 用于下拉框选择
export const materialType = [
  {
    name: '视频',
    value: '1',
    desc: 'video',
  },
  {
    name: '音频',
    value: '2',
    desc: 'audio',
  },
  {
    name: '图片',
    value: '3',
    desc: 'image',
  },
  {
    name: '模型',
    value: '4',
    desc: 'model',
  },
  // {
  //   name: "文字",
  //   value: "5",
  //   desc: "text",
  // },
];

// 行为条件映射 - 定义不同素材类型的触发和效果条件
export const conditeMap = {
  model: {
    // 模型触发条件
    triggerCondites: [
      {
        label: '被点击',
        value: '0',
      },
      {
        label: '停止时',
        value: '1',
      },
    ],
    // 模型效果条件
    effectCondites: [
      {
        label: '显示',
        value: '0',
      },
      {
        label: '隐藏',
        value: '1',
      },
      {
        label: '显示 / 隐藏',
        value: '2',
        isMutex: true,
      },
      {
        label: '播放',
        value: '3',
      },
      {
        label: '播放（动画复位）',
        value: '6',
        isMutex: true,
      },
      {
        label: '停止',
        value: '4',
      },
      {
        label: '播放 / 停止',
        value: '5',
        isMutex: true,
      },
    ],
  },
  image: {
    // 图片触发条件
    triggerCondites: [
      {
        label: '被点击',
        value: '0',
      },
    ],
    // 图片效果条件
    effectCondites: [
      {
        label: '显示',
        value: '0',
      },
      {
        label: '隐藏',
        value: '1',
      },
      {
        label: '显示 / 隐藏',
        value: '2',
        isMutex: true,
      },
    ],
  },
  text: {
    // 文字触发条件
    triggerCondites: [
      {
        label: '被点击',
        value: '0',
      },
    ],
    // 文字效果条件
    effectCondites: [
      {
        label: '显示',
        value: '0',
      },
      {
        label: '隐藏',
        value: '1',
      },
      {
        label: '显示 / 隐藏',
        value: '2',
        isMutex: true,
      },
    ],
  },
  audio: {
    // 音频触发条件
    triggerCondites: [
      {
        label: '停止时',
        value: '1',
      },
    ],
    // 音频效果条件
    effectCondites: [
      {
        label: '播放',
        value: '0',
      },
      {
        label: '暂停',
        value: '1',
      },
      {
        label: '停止',
        value: '2',
      },
      {
        label: '播放 / 暂停',
        value: '3',
        isMutex: true,
      },
    ],
  },
  video: {
    // 视频触发条件
    triggerCondites: [
      {
        label: '被点击',
        value: '0',
      },
      {
        label: '停止时',
        value: '1',
      },
    ],
    // 视频效果条件
    effectCondites: [
      {
        label: '播放',
        value: '0',
      },
      {
        label: '暂停',
        value: '1',
      },
      {
        label: '停止',
        value: '2',
      },
      {
        label: '播放 / 暂停',
        value: '3',
        isMutex: true,
      },
      {
        label: '显示',
        value: '4',
      },
      {
        label: '隐藏',
        value: '5',
      },
      {
        label: '显示 / 隐藏',
        value: '6',
        isMutex: true,
      },
    ],
  },
};

// 素材图标配置 - 用于场景编辑器中的素材显示
export const materialUrls: any = {
  1: {
    name: '视频',
    url: 'sourceType/video.png', // 默认图标
    url_a: 'sourceType/video_a.png', // 激活状态图标
    url_h: 'sourceType/video_h.png', // 悬停状态图标
    url_upload: 'sourceType/video-icon.png', // 上传时图标
    type: 'video',
    format: ['mp4'], // 支持的文件格式
  },
  2: {
    name: '音频',
    url: 'sourceType/audio.png',
    url_a: 'sourceType/audio_a.png',
    url_h: 'sourceType/audio_h.png',
    url_upload: 'sourceType/audio-icon.png',
    type: 'audio',
    format: ['mp3'],
  },
  3: {
    name: '图片',
    url: 'sourceType/picture.png',
    url_a: 'sourceType/picture_a.png',
    url_h: 'sourceType/picture_h.png',
    url_upload: 'sourceType/picture-icon.png',
    type: 'picture',
    format: ['png', 'jpg'],
  },
  4: {
    name: '模型',
    url: 'sourceType/model.png',
    url_a: 'sourceType/model_a.png',
    url_h: 'sourceType/model_h.png',
    url_upload: 'sourceType/model-icon.png',
    type: 'model',
    format: ['glb', 'gltf', 'obj', 'fbx'],
  },
  5: {
    name: '文字',
    url: 'sourceType/text.png',
    url_a: 'sourceType/text_a.png',
    url_h: 'sourceType/text_h.png',
    url_upload: 'sourceType/text-icon.png',
    type: 'text',
  },
  // 6: {
  //   name: "模型(有动画)",
  //   url: 'sourceType/model.png',
  //   url_a: 'sourceType/model_a.png',
  //   url_h: 'sourceType/model_h.png',
  //   type: 'animate_model'
  // },
  groups: {
    name: '素材组合',
    url_a: 'sourceType/group_a.png',
    url_h: 'sourceType/group_h.png',
    type: 'group',
  },
};

// 用户账号状态映射
export const userStatus: any = {
  1: '启用',
  2: '停用',
};

// 用户角色类型映射
export const userTypes: any = {
  1: '超级管理员',
  2: '眼镜',
  3: '普通管理员',
  4: '移动端',
  5: '普通用户',
};

// 客户组织类型选项 - 用于下拉框
export const orgnizationTypes = [
  {
    name: '文娱',
    value: 1,
  },
  {
    name: '工业',
    value: 2,
  },
  {
    name: '教育',
    value: 3,
  },
  {
    name: '其他',
    value: 4,
  },
];

// 素材组合类型配置 - 定义不同素材的组合方式
export const sourceGroupList: any = [
  {
    name: '视频+文字',
    value: 1,
    tip_titles: ['视频素材', '文本素材'],
    tips: ['请选择 1 个视频素材', '请选择 1 个文本素材'],
    groupIds: ['1', '5'], // 对应的素材类型ID
  },
  {
    name: '模型+文字',
    value: 2,
    tip_titles: ['模型素材', '文本素材'],
    tips: ['请选择 1 个模型素材', '请选择 1 个文本素材'],
    groupIds: ['4', '5'],
  },
  {
    name: '语音+文字',
    value: 3,
    tip_titles: ['语音素材', '文本素材'],
    tips: ['请选择 1 个语音素材', '请选择 1 个文本素材'],
    groupIds: ['2', '5'],
  },
  {
    name: '图片+文字',
    value: 4,
    tip_titles: ['图片素材', '文本素材'],
    tips: ['请选择1-9个图片素材（建议格式大小相同）', '请选择 1 个文本素材'],
    groupIds: ['3', '5'],
  },
  {
    name: '图片组合',
    value: 5,
    tip_titles: ['图片素材'],
    tips: ['请选择2-9个图片素材（建议格式大小相同）'],
    groupIds: ['3'],
  },
];

// AR场景菜单配置
export const arMenus: any = [
  {
    name: '空间AR',
    sceneType: 1,
    scenePlatform: 3,
  },
  {
    name: '平面AR',
    sceneType: 2,
    scenePlatform: 3,
  },
  {
    name: '图像AR',
    sceneType: 3,
    scenePlatform: 3,
  },
  {
    name: '身体AR',
    sceneType: 5,
    scenePlatform: 3,
  },
  {
    name: '人脸AR',
    sceneType: 6,
    scenePlatform: 3,
  },
  {
    name: '手势AR',
    sceneType: 7,
    scenePlatform: 3,
  },
  {
    name: '单场景AR',
    sceneType: 8,
    scenePlatform: 3,
  },
];

// 登录错误信息映射
export const loginErrorText: any = {
  1: '当前账号被授权使用登录了混空微信小程序',
  2: '当前账号在另一个浏览器登录，账号被迫退出',
  3: '当前账号在另一个浏览器登录，账号被迫退出',
  4: '当前账号在另一个浏览器登录，账号被迫退出',
  5: '当前账号在另一个浏览器登录，账号被迫退出',
  6: '当前账号首次登录并匹配眼镜端或移动端',
};

// 路由名称映射 - 用于面包屑导航
export const routeNameMap: any = {
  '/': '首页',
  '/home': '首页',
  '/experience_home': '首页',
  '/item_list': '我的项目',
  '/source_material': '素材管理',
  '/experience_material': '素材管理',
  '/default_material': '素材管理',
  '/spacelist': '空间管理',
  '/customer': '客户管理',
  '/devicelist': '设备管理',
  '/package': '套餐管理',
  '/square': '模板广场',
  '/path_navigation': '路径导航',
};

// 场景平台选项卡配置
export const scenePlatformTabs = [
  {
    name: '微信小程序',
    value: 3,
  },
  {
    name: '眼镜端',
    value: 1,
  },
  {
    name: '移动端',
    value: 2,
  },
];

// 场景平台筛选选项 - 包含"所有平台"选项
export const scenePlatforms = [
  {
    name: '所有平台',
    value: 0,
  },
  {
    name: '微信小程序',
    value: 3,
  },
  {
    name: '移动端',
    value: 2,
  },
  {
    name: '眼镜端',
    value: 1,
  },
];

// 场景类型列表
export const sceneTypeList = [
  {
    name: '空间AR',
    value: 1,
  },
  {
    name: '平面AR',
    value: 2,
  },
  {
    name: '图像AR',
    value: 3,
  },
  {
    name: '单场景AR',
    value: 8,
  },
  {
    name: '人脸AR',
    value: 6,
  },
  {
    name: '身体AR',
    value: 5,
  },
  {
    name: '手势AR',
    value: 7,
  },
];

// 场景类型列表 - 包含"所有类型"选项
export const sceneTypeListAll = [
  {
    name: '所有类型',
    value: 0,
  },
  {
    name: '空间AR',
    value: 1,
  },
  {
    name: '平面AR',
    value: 2,
  },
  {
    name: '图像AR',
    value: 3,
  },
  {
    name: '单场景AR',
    value: 8,
  },
  {
    name: '人脸AR',
    value: 6,
  },
  {
    name: '身体AR',
    value: 5,
  },
  {
    name: '手势AR',
    value: 7,
  },
];

// 场景类型映射 - 数字ID到名称的映射
export const sceneTypeMap: any = {
  1: '空间AR',
  2: '平面AR',
  3: '图像AR',
  5: '身体AR',
  6: '人脸AR',
  7: '手势AR',
  8: '单场景AR',
};

// AI大模型类型枚举
export const AIModelType = {
  MODEL: 1, // 模型
  IMAGE: 2, // 图片
  VIDEO: 3, // 视频
  AUDIO: 4, // 声音
} as const;

// AI大模型类型映射 - 数字ID到名称的映射
export const aiModelTypeMap: any = {
  [AIModelType.MODEL]: '模型',
  [AIModelType.IMAGE]: '图片',
  [AIModelType.VIDEO]: '视频',
  [AIModelType.AUDIO]: '声音',
};

// AI大模型类型选项 - 用于下拉框选择
export const aiModelTypeOptions = [
  {
    name: '模型',
    value: AIModelType.MODEL,
  },
  {
    name: '图片',
    value: AIModelType.IMAGE,
  },
  {
    name: '视频',
    value: AIModelType.VIDEO,
  },
  {
    name: '声音',
    value: AIModelType.AUDIO,
  },
];

// AI任务状态枚举
export const AiTaskStatus = {
  SUBMITED_DB: 1, // 已提交到数据库
  SUBMITED_MQ: 2, // 已提交到mq
  PROCESSING: 3, // 处理中
  FINISHED: 4, // 处理完毕
  CANCEL: 5, // 手动取消
  SUPPLIER_ERROR: 6, // ai供应商报错
  INNER_ERROR: 7, // 系统内部报错
  SUBMITED_SUPPLIER: 8, // 提交任务到ai供应商
} as const;

// AI任务状态映射
export const AiTaskStatusMap: Record<number, string> = {
  1: '已提交到数据库',
  2: '已提交到mq',
  3: '处理中',
  4: '处理完毕',
  5: '手动取消',
  6: 'ai供应商报错',
  7: '系统内部报错',
  8: '提交任务到ai供应商',
};
